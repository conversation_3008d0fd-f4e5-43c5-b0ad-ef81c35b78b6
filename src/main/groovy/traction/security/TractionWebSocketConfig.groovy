package traction.security

import grails.plugin.springsecurity.rest.token.AccessToken
import grails.plugin.springwebsocket.GrailsSimpAnnotationMethodMessageHandler
import grails.plugin.springwebsocket.GrailsWebSocketAnnotationMethodMessageHandler
import groovy.transform.CompileStatic
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.messaging.Message
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.SubscribableChannel
import org.springframework.messaging.simp.SimpMessageSendingOperations
import org.springframework.messaging.simp.config.ChannelRegistration
import org.springframework.messaging.simp.config.MessageBrokerRegistry
import org.springframework.messaging.support.ChannelInterceptor
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.web.socket.config.annotation.AbstractWebSocketMessageBrokerConfigurer
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker
import org.springframework.web.socket.config.annotation.StompEndpointRegistry

@CompileStatic
@Configuration
@EnableWebSocketMessageBroker
class TractionWebSocketConfig extends AbstractWebSocketMessageBrokerConfigurer {

    final static List<String> PUBLIC_DESTINATIONS_PREFIXES = [
            "/topic/communicationchannel/"
    ]

    @Override
    void configureMessageBroker(MessageBrokerRegistry messageBrokerRegistry) {
        messageBrokerRegistry.enableSimpleBroker "/queue", "/topic"
        messageBrokerRegistry.setApplicationDestinationPrefixes "/app"
    }

    @Override
    void registerStompEndpoints(StompEndpointRegistry stompEndpointRegistry) {
        stompEndpointRegistry.addEndpoint("/stomp").withSockJS()
        /* Grails 3
        stompEndpointRegistry
                .addEndpoint("/stomp")
                .setAllowedOrigins("*")
                .withSockJS()
                .setClientLibraryUrl("https://cdn.jsdelivr.net/npm/sockjs-client@1.3.0/dist/sockjs.min.js")
         */
    }

    @Bean
    GrailsSimpAnnotationMethodMessageHandler grailsSimpAnnotationMethodMessageHandler(
            SubscribableChannel clientInboundChannel,
            MessageChannel clientOutboundChannel,
            SimpMessageSendingOperations brokerMessagingTemplate
    ) {
        def handler = new GrailsSimpAnnotationMethodMessageHandler(clientInboundChannel, clientOutboundChannel, brokerMessagingTemplate)
        handler.destinationPrefixes = ["/app"]
        return handler
    }

    @Bean
    GrailsWebSocketAnnotationMethodMessageHandler grailsWebSocketAnnotationMethodMessageHandler(
            SubscribableChannel clientInboundChannel,
            MessageChannel clientOutboundChannel,
            SimpMessageSendingOperations brokerMessagingTemplate
    ) {
        def handler = new GrailsWebSocketAnnotationMethodMessageHandler(clientInboundChannel, clientOutboundChannel, brokerMessagingTemplate)
        handler.destinationPrefixes = ["/app"]
        return handler
    }

    @Override
    void configureClientInboundChannel(ChannelRegistration registration) {
        registration.interceptors(new ChannelInterceptor() {
            @Override
            Message<?> preSend(Message<?> message, MessageChannel channel) {
                String simpDestination = message.getHeaders().get("simpDestination")
                if (!simpDestination) return message
                def tempUser = message.getHeaders().get("simpUser")
                boolean canSend = false
                if(tempUser instanceof UsernamePasswordAuthenticationToken) {
                    UsernamePasswordAuthenticationToken simpUser = tempUser
                    canSend = simpUser?.principal != null
                } else if (tempUser instanceof AccessToken) {
                    AccessToken simpUser = tempUser
                    canSend = simpUser?.principal != null
                }
                if (!canSend) {
                    PUBLIC_DESTINATIONS_PREFIXES.each {
                        if (simpDestination.startsWith(it)) {
                            canSend = true
                        }
                    }
                }
                return canSend ? message : null
            }

            @Override
            void postSend(Message<?> message, MessageChannel channel, boolean sent) {
            }

            @Override
            void afterSendCompletion(Message<?> message, MessageChannel channel, boolean sent, Exception ex) {
            }

            @Override
            boolean preReceive(MessageChannel channel) {
                return true
            }

            @Override
            Message<?> postReceive(Message<?> message, MessageChannel channel) {
                return message
            }

            @Override
            void afterReceiveCompletion(Message<?> message, MessageChannel channel, Exception ex) {
            }
        })
    }
}
