package traction

import traction.opportunity.OpportunityService

class ReassignOpportunitiesJob {
    OpportunityService opportunityService

    static concurrent = false
    static triggers = {
        simple startDelay: 300000, repeatInterval: 900000, repeatCount: -1 // Every 15 minutes
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> ReassignOpportunitiesJob start"
        opportunityService.reassignOpportunities()
        log.info "=====================> ReassignOpportunitiesJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
    }
}
