package traction

import product.ManagerProductConfig
import traction.item.Item
import traction.vehicle.Vehicle
import traction.vehicle.VehicleOption
import traction.vehicle.VehicleSpecification
import traction.vehicle.VehicleText
import traction.vehicle.VehicleTranslatedText

import java.util.stream.Collectors

class CreateVehicleTranslatedTextJob {

    def vehicleTranslatedTextService
    def configService

    static triggers = {
        cron name:"CreateVehicleTranslatedTextJob", startDelay:10000, cronExpression: "0 0 0 * * ?" //Midnight every day
    }

    def execute() {
        if(configService.get("GEMINI", "gemini.activated") == "1") {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> CreateVehicleTranslatedTextJob start"
            List<String> propertyName = ["category", "description", "warrantyDescription", "magentoData.metaDescription", "magentoData.metaKey", "magentoData.metaKey", "magentoData.htmlH1", "magentoData.htmlH2", "customText1", "customText2", "customText3", "options", "options2", "exteriorColor"]
            List<String> allVehicleText = []
            propertyName.each {
                String property = it
                allVehicleText += Vehicle.createCriteria().list { projections { distinct(property) } }
            }
            List<String> listSpecsName = VehicleSpecification.createCriteria().list { projections { distinct("name") } } as List<String>
            List<String> listSpecsValue = VehicleSpecification.createCriteria().list { projections { distinct("value") } } as List<String>
            List<String> listTextName = VehicleText.createCriteria().list { projections { distinct("name") } } as List<String>
            List<String> listTextDescription = VehicleText.createCriteria().list { projections { distinct("description") } } as List<String>
            List<String> listItemDescription = Item.createCriteria().list { projections { distinct("description") } } as List<String>
            List<String> listOptionText = []
            ["name","description"].each {
                String property = it
                if (property == "description") {
                    VehicleOption.createCriteria().list { projections { distinct(property) } }.each {
                        listOptionText += it?.split(",") as List<String>
                    }
                }else {
                    listOptionText +=  VehicleOption.createCriteria().list { projections { distinct(property) } } as List<String>
                }
            }
            List<VehicleTranslatedText> translatedText = VehicleTranslatedText.findAll()
            allVehicleText += listSpecsName + listSpecsValue + listTextName + listTextDescription + listOptionText + listItemDescription
            allVehicleText.removeAll(Arrays.asList("", null))
            List<String> differences = allVehicleText.stream()
                    .filter({ element -> !translatedText.text.contains(element) })
                    .collect(Collectors.toList())
            differences.removeAll(Arrays.asList("", null))
            List<ManagerProductConfig> configs = ManagerProductConfig.createCriteria().list {
                eq("name", "MULTI LOCAL")
                eq("category", "DEFAULT")
            }
            String defaultLang = ManagerProductConfig.findByNameAndCategory("DEFAULT LOCAL", "DEFAULT").value
            if (differences.size() > 0) {
                differences.each {
                    def stringText = it
                    configs.each {
                        if (it.value != defaultLang) {
                            if (!VehicleTranslatedText.findByTextAndLang(stringText, it.value)) {
                                VehicleTranslatedText vehicleTranslatedText = new VehicleTranslatedText()
                                vehicleTranslatedText.text = stringText
                                vehicleTranslatedText.translated = "TO_TRANSLATE"
                                vehicleTranslatedText.lang = it.value
                                vehicleTranslatedTextService.saveTranslatedText(vehicleTranslatedText)
                            }
                        }
                    }
                }
            }
            log.info "=====================> CreateVehicleTranslatedTextJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        }
    }
}
