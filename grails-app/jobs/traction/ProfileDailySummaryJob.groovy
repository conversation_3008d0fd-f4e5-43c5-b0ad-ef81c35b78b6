package traction

import traction.category.CategoryMailTemplate
import traction.form.FormEvent
import traction.form.FormProfile
import traction.security.User
import traction.security.UserAttribute

class ProfileDailySummaryJob {

    def mailSenderService
    def userService
    def configService
    def mailTemplateService

    static concurrent = false

    static triggers = {
        cron name: "ProfileDailySummaryJob", startDelay: 10000, cronExpression: "0 0 0 * * ?" //Midnight every day
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> ProfileDailySummaryJob start"

        List<FormProfile> profiles = FormProfile.findAllByDailySummary(true)
        log.debug "ProfileDailySummaryJob profiles : " + profiles
        for (FormProfile profile : profiles) {
            log.debug "ProfileDailySummaryJob events : " + profile.events
            for (FormEvent event : profile.events) {
                if (event.toDate >= new Date() && event.fromDate <= new Date()) {
                    log.debug "Notified user : " + event.notificationUsers
                    for (User user : event.notificationUsers) {
                        log.debug "ProfileDailySummary User : " + user
                        UserAttribute attribute = userService.getUserAttribute(user, "language")
                        if (attribute) {
                            String language = attribute.value
                            log.debug "ProfileDailySummary attribute found : " + language

                            MailTemplate template = MailTemplate.findByCategoryAndLang(CategoryMailTemplate.SUMMARY_DAILY_MAIL.message, language)
                            if (template) {
                                log.debug "ProfileDailySummary template found..."
                                Map<String, String> custom = new LinkedHashMap<>()
                                CategoryMailTemplate.SUMMARY_DAILY_MAIL.variables.each {

                                    switch (it) {
                                        case "[[dailySummary.url]]":
                                            log.debug "ProfileDailySummary replacing..."
                                            Calendar cal = Calendar.getInstance()
                                            cal.setTime(new Date())
                                            cal.add(Calendar.DATE, -1)
                                            custom.put(it, configService.get("SERVER", "server.traction.base") + "/formData/list?date=" + cal.getTime().format("YYYY-MM-dd") + "&event=" + event.id);
                                            break
                                    }
                                }
                                Map map = mailTemplateService.replaceDailySummaryMailTemplate(template, custom)
                                if (!mailSenderService.sendMail(configService.get("GOOGLE", "google.systemuser"), user.email, map.subject, map.body)) {
                                    log.warn "Cannot send email to ${user}"
                                }
                            } else {
                                log.info "No template found for category \"" + CategoryMailTemplate.SUMMARY_DAILY_MAIL.message + "\" and language \"" + language + "\""
                            }
                        }
                    }
                }
            }
        }

        log.info "=====================> ProfileDailySummaryJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
    }
}
