package traction

import groovy.json.JsonSlurper

class GoogleGmailWatchJob {
    def configService
    def googleAppService

    static concurrent = false

    static triggers = {
        simple startDelay: 10000, repeatInterval: 600000l // executeJob job once in 10min
    }

    def execute() {
        def isON = configService.get("GOOGLE", "google.gsuite").toBoolean()
        if (isON && configService.get("GENERAL", "GoogleGmailWatchJob.status").toBoolean()) {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> GoogleGmailWatchJob start"
            List<String> emails = []
            try {
                emails = new JsonSlurper().parseText(configService.get("GOOGLE", "google.watch.emails"))
            } catch (Exception ex) {
                AntiSpamLogger.error "Error parsing the Google scope, link to google api not working : ${ex}"
                return null
            }
            emails.each { e ->
                log.info "Watching email: ${e}"
                googleAppService.watch(e)
            }
            log.info "=====================> GoogleGmailWatchJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        } else {
            log.info "=====================> GoogleGmailWatchJob is off"
        }
    }
}
