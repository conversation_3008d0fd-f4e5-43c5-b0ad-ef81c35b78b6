package traction.notification

class NotificationJob {

    NotificationSubscriberService notificationSubscriberService

    static concurrent = false
    static triggers = {
        simple startDelay: 300000, repeatInterval: 900000, repeatCount: -1 // Every 15m
        //simple startDelay: 30000, repeatInterval: 30000, repeatCount: -1 // 30 sec
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> NotificationJob start"
        notificationSubscriberService.executeJob()
        log.info "=====================> NotificationJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
    }
}
