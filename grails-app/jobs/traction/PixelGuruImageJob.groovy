package traction

import product.PixelGuruService

class PixelGuruImageJob {
    PixelGuruService pixelGuruService
    ConfigService configService
    static concurrent = false
    static triggers = {
        cron name: "Accept Every PixelGuru Images ", startDelay: 10000, cronExpression: "0 0 21 * * ?" //9pm every day
    }

    def execute() {
        if(configService.get("CONFIG","pixelguru.job") == "1") {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> PixelGuruImageJob start"
            pixelGuruService.acceptAll()
            log.info "=====================> PixelGuruImageJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        }
    }
}
