package traction

import grails.gorm.transactions.NotTransactional
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.hibernate.Session
import org.hibernate.SessionFactory
import traction.client.Client
import traction.client.ClientAttributes
import traction.client.ClientService
import traction.client.IndividualClient
import traction.communication.email.EmailUtils
import traction.communication.PhoneUtils
import traction.cotation.CotationService
import traction.vehicle.VehicleDms

import java.nio.charset.StandardCharsets

class SyncLarsClientJob {
    ConfigService configService
    SessionFactory sessionFactory
    CotationService cotationService
    ClientService clientService
    DmsRulesService dmsRulesService

    static concurrent = false

    static triggers = {
        cron name: "SyncLarsJob", startDelay: 10000, cronExpression: "0 0 1 * * ?" //1h AM every day
    }

    def execute() {
        if (configService.get("LARS", "lars.sync.status.client").equals("1") || configService.get("LARS", "lars.sync.status.clientVehicle").equals("1")) {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> SyncLarsClientJob start"
            String FILES_FOLDER = configService.get("LARS", "FOLDER")
            File fs = new File(FILES_FOLDER)

            def workFolder = new File(FILES_FOLDER + "/work/")
            if (!workFolder.exists()) {
                workFolder.mkdir()
            }

            // Map containing all files an filename of files to process
            try {
                fs.eachFileMatch(~/.*.csv/) { file ->
                    cleanLarsClientCsv(file, workFolder)
                }
            } catch (Exception ex) {
                AntiSpamLogger.error "No Lars file found: ${fs} : ${ex}"
            }

            workFolder.eachFileMatch(~/.*.csv/) { file ->
                DMS dms = DMS.findByDmsIdAndSource(file.name, DMS.Source.LARS)
                if (dms) {
                    if (configService.get("LARS", "lars.sync.status.client").equals("1")) importLARSClientCsv(file, dms)
                    if (configService.get("LARS", "lars.sync.status.clientVehicle").equals("1")) syncVehicle(file.name, dms)
                }
            }


            log.info "=====================> SyncLarsClientJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        } else {
            log.info "=====================> SyncLarsClientJob is off"
        }
    }

    def syncVehicle(String filename, DMS dms) {
        VehicleDms curVehicleDMS = VehicleDms.findByUserAndToken(dms.dmsId, DMS.Source.LARS.name)
        if (curVehicleDMS) {
            curVehicleDMS.filepath = filename
            curVehicleDMS.dmsId = dms?.id
            if (dmsRulesService.saveDMS(curVehicleDMS)) {
                dmsRulesService.importDMS(curVehicleDMS)
            }
        }
    }

    def cleanLarsClientCsv(file, workFolder) {
        FileWriter csvWriter = new FileWriter(workFolder.path + "/" + file.name)
        file.eachLine { line, index ->
            if (index != 1) {
                String[] splitted = line.split(",")
                try {
                    Long extID = Long.valueOf(splitted[1])
                    csvWriter.append(line + "\n")
                } catch (Exception ex) {
                    csvWriter.append(line.replaceFirst(",", "|") + "\n")
                }
            } else {
                csvWriter.append(line + "\n")
            }
        }
        csvWriter.flush()
        csvWriter.close()
        file.delete()
    }

    def importLARSClientCsv(File file, DMS dms) {
        if (!dms) {
            AntiSpamLogger.error "No DMS defined for import LARS file : ${file.name}"
            return
        }

        Long lastUpdatedIndex = Long.parseLong(configService.get("LARS", "lars.sync.lastUpdated").toString())
        CSVFormat format = CSVFormat.Builder.create(CSVFormat.DEFAULT).
                setHeader()
                .setSkipHeaderRecord(true)
                .setEscape('\\' as char)
                .build()
        Iterable<CSVRecord> records = CSVParser.parse(file, StandardCharsets.UTF_8, format)

        Session session = sessionFactory.getCurrentSession()
        session.beginTransaction()
        List<Client> clientsDone = []
        int i = 0
        for (CSVRecord line : records) {
            try {
                Long extID = Long.valueOf(line.get("NUMERO"))
                if (lastUpdatedIndex < extID) {
                    i++
                    println "Doing extID: ${extID}"

                    String lastname = getOrDefault(line, "NOM")
                    String firstname = ""
                    int coma = lastname.indexOf('|')
                    if (coma != -1) {
                        firstname = lastname.substring(coma + 1, lastname.length()).trim()
                        lastname = lastname.substring(0, coma).trim()
                    }
                    String address = getOrDefault(line, "RUE")
                    String city = getOrDefault(line, "VILLE")
                    String province = getOrDefault(line, "PROVINCE")
                    String postal = getOrDefault(line, "POSTAL")
                    String email = EmailUtils.getValidOrNull(getOrDefault(line, "COURRIEL 1"))
                    String altEmail = EmailUtils.getValidOrNull(getOrDefault(line, "COURRIEL 2"))
                    if (!email) {
                        email = altEmail
                        altEmail = ""
                    }
                    String phone = PhoneUtils.getValidPhoneNumber(getOrDefault(line, "MAISON"))
                    String phonework = PhoneUtils.getValidPhoneNumber(getOrDefault(line, "BUREAU"))
                    String phonecell = PhoneUtils.getValidPhoneNumber(getOrDefault(line, "CELL"))
                    String fax = getOrDefault(line, "FAX")

                    Client client = Client.findByExtID(extID.toString(), dms)
                    boolean doSessionFlush = false
                    // Find the client by email
                    boolean larsValidEmail = EmailUtils.isValid(email)
                    boolean setEmailOfNewClient = false
                    if (larsValidEmail) {
                        setEmailOfNewClient = true
                        Client sameEmail = Client.findByEmail(email)
                        log.debug "sameEmail:" + sameEmail
                        if (sameEmail) {
                            if (!client && !sameEmail.getExtID(dms)) {
                                client = sameEmail
                                log.debug "Takes the sameEmail client"
                            } else {
                                setEmailOfNewClient = false
                            }
                        } else if (client && EmailUtils.isNoMail(client.email)) {
                            client.email = email
                            doSessionFlush = true
                            log.debug "Set the email"
                        }
                    }
                    if (!client) {
                        List<Client> sameTel = clientService.findAllByPhoneWithoutExtID([phone, phonecell], dms)
                        Client sameTelSameName = sameTel.find { it.asIndividualClient()?.firstname?.toLowerCase() == firstname.toLowerCase() && it.asIndividualClient()?.lastname?.toLowerCase() == lastname.toLowerCase() }
                        if (!sameTelSameName) {
                            sameTelSameName = sameTel.find { it.asIndividualClient()?.firstname?.toLowerCase() == firstname.toLowerCase() || it.asIndividualClient()?.lastname?.toLowerCase() == lastname.toLowerCase() }
                        }
                        if (sameTelSameName) {
                            client = sameTelSameName
                        } else if (sameTel) {
                            client = sameTel.first()
                        }
                        if (!client) {
                            client = new IndividualClient()
                            if (setEmailOfNewClient) {
                                client.email = email
                                doSessionFlush = true
                            }
                            client.media = Media.DMS
                        } else if (larsValidEmail && EmailUtils.isNoMail(client.email) && setEmailOfNewClient) {
                            client.email = email
                            doSessionFlush = true
                        }
                    }

                    if (!client.getExtID(dms, extID.toString())) {
                        client.addToExternalIdentifiers(new ExternalIdentifier(extId: extID.toString(), dms: dms))
                        doSessionFlush = true
                    }

                    IndividualClient individualClient = client.asIndividualClient()
                    if (individualClient) {
                        if (!individualClient.firstname && firstname) {
                            individualClient.firstname = firstname
                        }
                        if (!individualClient.lastname && lastname) {
                            individualClient.lastname = lastname
                        }
                        if (!individualClient.phonecell && phonecell) {
                            individualClient.phonecell = phonecell
                        }
                        /* A voir:
                        if (!client.phonework && phonework) {
                            client.phonework = phonework
                        }
                        */
                    }
                    if (!client.phone && phone) {
                        client.phone = phone
                    }
                    if (!client.altEmail && altEmail) {
                        client.altEmail = altEmail
                    }
                    if (!client.fax && fax) {
                        client.fax = fax
                    }
                    if (!client.address.state && province) {
                        client.address.state = province
                    }
                    if (!client.address.city && city) {
                        client.address.city = city
                    }
                    if (!client.address.line1 && address) {
                        client.address.line1 = address
                    }
                    if (!client.address.postalCode && postal) {
                        client.address.postalCode = postal
                    }
                    if (clientService.save(client, true, session)) {
                        if (doSessionFlush) {
                            session.flush()
                        }
                        clientsDone.add(client)

                        String couleur1 = getOrDefault(line, "COULEUR1")
                        String couleur2 = getOrDefault(line, "COULEUR2")
                        String odometre = getOrDefault(line, "ODOMETRE")
                        String description = getOrDefault(line, "DESCRIPTION")
                        String make = getOrDefault(line, "MARQUE")
                        String model = getOrDefault(line, "MODELE")
                        String year = getOrDefault(line, "ANNEE")
                        String stockNumber = getOrDefault(line, "No. STOCK")
                        String serialNumber = getOrDefault(line, "SERIE")

                        /*
                        if (!client.trades.any { it.make == make && it.model == model && it.year.toString() == year && it.serialnumber == serialNumber && !it.deleted }) {
                            cotationService.saveTradeFromParams([
                                    trade_make          : make,
                                    trade_model         : model,
                                    trade_year          : year,
                                    trade_interest      : '0',
                                    trade_stocknumber   : stockNumber,
                                    trade_serialnumber  : serialNumber,
                                    trade_kilometers    : odometre,
                                    trade_exterior_color: couleur1,
                                    trade_interior_color: couleur2,
                                    trade_description   : description,
                                    clientId            : client.id,
                            ], Locale.ENGLISH, session)
                        }
                         */
                    } else {
                        log.warn "Client not saved! ${client}"
                    }
                    if (i % 20 == 0) {
                        log.debug "DONE : ${i}"
                        session.flush()
                        session.clear()
                        clientsDone.clear()
                        session.getTransaction().commit()
                        session.beginTransaction()
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace()
            }
        }
        session.flush()
        session.getTransaction().commit()
    }

    String getOrDefault(CSVRecord line, String property) {
        try {
            return line.get(property)
        } catch (Exception ex) {
        }
        return ""
    }
}
