package traction

import groovy.json.JsonSlurper
import traction.mail.MailReceiver
import traction.vendor.GoogleAppService
import traction.vendor.MailgunService

class SyncEmailJob {
    ConfigService configService
    GoogleAppService googleAppService
    MailgunService mailgunService

    static concurrent = false

    static triggers = {
        cron name: "SyncEmailJob", startDelay: 10000, cronExpression: "0 0 4 * * ?" // every day at 4AM
    }

    def execute() {
        String receiver = configService.get("EMAIL", "email.receiver")
        if (receiver == MailReceiver.GMAIL && configService.get("GENERAL", "GoogleGmailWatchJob.status").equals("1")) {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> SyncEmailJob Google start"
            List<String> emails
            try {
                emails = new JsonSlurper().parseText(configService.get("GOOGLE", "google.watch.emails"))
            } catch (Exception ex) {
                AntiSpamLogger.error "Error parsing the Google scope, link to google api not working : ${ex}"
                return null
            }
            emails.each { e ->
                googleAppService.syncEmail(e, 500)
            }
            log.info "=====================> SyncEmailJob Google stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        }

        if (receiver == MailReceiver.MAILGUN) {
            long globalStartTime = System.currentTimeMillis()
            log.info "=====================> SyncEmailJob Mailgun start"
            mailgunService.getStoredEvents(1)
            log.info "=====================> SyncEmailJob Mailgun stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        }
    }
}
