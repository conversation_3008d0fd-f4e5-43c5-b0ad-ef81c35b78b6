package traction

import groovy.time.TimeDuration
import traction.api.MobileApiService
import traction.mobile.MobileConnection

/**
 * Job to remove mobile connections older than 2 months
 */
class MobileConnectionJob {

    MobileApiService mobileApiService

    static concurrent = false

    static triggers = {
        cron name: "MobileConnectionJob", startDelay: 10000, cronExpression: "0 30 23 * * ?" // every day at 11:30pm
        //simple startDelay: 0, repeatInterval: 30000, repeatCount: -1 // 30 sec
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> MobileConnectionJob start"
        MobileConnection.getAll().each { connection ->
            boolean isOld = false
            use(groovy.time.TimeCategory) {
                Long duration = (new Date() - connection.timestamp).toMilliseconds()
                isOld = duration >= new TimeDuration(60, 0, 0, 0, 0).toMilliseconds()
            }
            if (isOld) {
                mobileApiService.removeMobileConnection(connection)
            }
            log.info "=====================> MobileConnectionJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
        }
    }
}
