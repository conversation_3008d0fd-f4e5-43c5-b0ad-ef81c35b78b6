package traction

import groovy.sql.Sql
import traction.history.History
import traction.opportunity.Opportunity

class IntegrityCheckJob {
    def dataSource

    static concurrent = false

    static triggers = {
        cron name: "IntegrityCheckJob", startDelay: 10000, cronExpression: "0 0 0 * * ?" //0h every day
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> IntegrityCheckJob start"
        Sql sql = new Sql(dataSource)
        try {
            StringBuilder error = new StringBuilder()
            Checks.values().each {
                List<Long> errorIds = it.listErrors(sql)
                if (errorIds) {
                    error.append("${errorIds.size()} errors for ${it.name()}: ${errorIds} \n")
                }
            }
            if (error.length()) {
                log.info "Data Integrity errors : " + error.toString()
                log.warn "Data Integrity errors : read the previous log for all the information"
            }
        }
        catch (Exception e) {
            AntiSpamLogger.error "IntegrityCheckJob not working:" + e.getMessage()
            e.printStackTrace()
        }
        sql.close()
        log.info "=====================> IntegrityCheckJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
    }

    enum Checks {

        INVALID_PHONE({ Sql sql ->
            return sql.rows("""
SELECT 
    id,
    phone 
FROM 
    client 
WHERE 
    phone != '' AND 
    phone NOT REGEXP '^[0-9]#?[0-9]'
""").collect { it.id }
        }),

        INVALID_PHONECELL({ Sql sql ->
            return sql.rows("""
SELECT 
    id,
    phonecell 
FROM 
    client 
WHERE 
    phonecell != '' AND
    phonecell NOT REGEXP '^[0-9]#?[0-9]'
""").collect { it.id }
        }),

        HISTORY_WITH_OPPORTUNITY_AND_NULL_CLIENT({ Sql sql ->
            return sql.rows("""
SELECT
    id 
FROM 
    history 
WHERE 
    opportunity_id IS NOT NULL AND
    client_id IS NULL
""").collect { it.id }
        }),

        CLIENT_WITHOUT_CREATION_HISTORY({ Sql sql ->
            return sql.rows("""
SELECT 
    client.id 
FROM 
    client 
WHERE 
    client.id NOT IN (
        SELECT 
            history.client_id
        FROM 
            history 
        WHERE 
            history.status in (?, ?)
    )
""",
                    [History.Status.CREATECLIENT.id, History.Status.CREATENOEMAILCLIENT.id]).collect { it.id }
        }),

        OPPORTUNITY_WITHOUT_CREATION_HISTORY({ Sql sql ->
            return sql.rows("""
SELECT 
    opportunity.id 
FROM 
    opportunity 
WHERE 
    opportunity.id NOT IN (
        SELECT 
            history.opportunity_id
        FROM 
            history 
        WHERE 
            history.status IN (?, ?)
    )
""",
                    [History.Status.NEWOPPORTUNITY.id, History.Status.NEW_POTENTIAL_OPPORTUNITY.id]).collect { it.id }
        }),

        WORKFLOW_DATA_WITHOUT_OPPORTUNITY({ Sql sql ->
            return sql.rows("""
SELECT
    workflow_data.id
FROM
    workflow_data
WHERE
    NOT EXISTS (
        SELECT 
            opportunity.id
        FROM 
            opportunity 
        WHERE 
            opportunity.workflow_data_id = workflow_data.id
    )
""").collect { it.id }
        }),

        OPPORTUNITY_WITHOUT_NAME({ Sql sql ->
            return sql.rows("""
SELECT 
    id 
FROM 
    opportunity 
WHERE 
    name = ''
""").collect { it.id }
        }),

        COTATION_WITH_OPPORTUNITY_STATUS_NOT_MATCHING({ Sql sql ->
            return sql.rows("""
SELECT
    cotation.id
FROM 
    cotation 
WHERE 
    cotation.sold_status IS NOT NULL AND 
    EXISTS (
        SELECT 
            cotation_element.id
        FROM 
            cotation_element
        LEFT JOIN 
            opportunity ON cotation_element.opportunity_id = opportunity.id
        WHERE 
            cotation_element.cotation_id = cotation.id AND
            cotation_element.opportunity_id IS NOT NULL AND
            opportunity.status != cotation.sold_status
    )
""").collect { it.id }
        }),

        OPPORTUNITY_WITH_MULTIPLE_SOLD_HISTORY({ Sql sql ->
            return sql.rows("""
SELECT
    history.opportunity_id
FROM
    history
WHERE
    history.status = ${History.Status.OPPORTUNITYSOLD.id}
GROUP BY
    history.opportunity_id
HAVING
    COUNT(*) > 1
""").collect { it.opportunity_id }
        }),

        OPPORTUNITY_WITH_DATE_SOLD_WITHOUT_HISTORY_SOLD({ Sql sql ->
            return sql.rows("""
SELECT
    opportunity.id
FROM
    opportunity
WHERE
    opportunity.datesold IS NOT NULL AND
    NOT EXISTS (
        SELECT
            history.id
        FROM
            history
        WHERE
            history.opportunity_id = opportunity.id AND
            history.status = ${History.Status.OPPORTUNITYSOLD.id}
    )
""").collect { it.id }
        }),

        HISTORY_SOLD_WITHOUT_OPPORTUNITY_DATESOLD({ Sql sql ->
            return sql.rows("""
SELECT
    history.id
FROM
    history
WHERE
    history.status = ${History.Status.OPPORTUNITYSOLD.id} AND
    NOT EXISTS (
        SELECT
            opportunity.id
        FROM
            opportunity
        WHERE
            opportunity.id = history.opportunity_id AND
            opportunity.datesold IS NOT NULL
    )
""").collect { it.id }
        }),

        OPPORTUNITY_MISSING_DATESOLD({ Sql sql ->
            return sql.rows("""
SELECT
    opportunity.id
FROM
    opportunity
WHERE
    opportunity.category = ${Opportunity.Category.SOLD.id} AND
    opportunity.datesold IS NULL
""").collect { it.id }
        }),

        OPPORTUNITY_MISSING_CATEGORY_SOLD({ Sql sql ->
            return sql.rows("""
SELECT
    opportunity.id
FROM
    opportunity
WHERE
    opportunity.datesold IS NOT NULL AND
    opportunity.category != ${Opportunity.Category.SOLD.id}
""").collect { it.id }
        }),

        private final Closure<List> closure

        Checks(Closure<List> closure) {
            this.closure = closure
        }

        List listErrors(Sql sql) {
            return this.closure(sql)
        }
    }

}
