package traction

class PotentialOpportunitiesQueueJob {
    PotentialOpportunityService potentialOpportunityService
    static concurrent = false
    static triggers = {
        simple startDelay: 300000, repeatInterval: 300000, repeatCount: -1 // Every 5 minutes
    }

    def execute() {
        long globalStartTime = System.currentTimeMillis()
        log.info "=====================> PotentialOpportunitiesQueueJob start"
        potentialOpportunityService.createPotentialOpportunities()
        log.info "=====================> PotentialOpportunitiesQueueJob stop, executed in ${System.currentTimeMillis() - globalStartTime} ms"
    }
}
