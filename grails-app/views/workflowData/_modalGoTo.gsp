<div class="modal-dialog modal-dialog-scrollable modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold"><g:message code="workflowdata.for" args="[vehicle ? vehicle.serialNumber + ' - ' + vehicle.makeModelYear() : opportunity?.name ?: client?.fullName]"/></h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

            </button>
        </div>

        <div class="modal-body">
            <g:if test="${hasWorkflowRole}">
                <g:if test="${workflowDatas}">
                    <table>
                        <g:each var="workflowData" in="${workflowDatas}">
                            <tr>
                                <td>
                                    <a href="<g:createLink controller="workflow" action="index"
                                                           params="[id: workflowData.workflowBoard.workflowMaster.id, opportunityId: workflowData.opportunity.id]"/>">
                                        <traction:card opportunity="${workflowData.opportunity}"/>
                                    </a>
                                </td>
                                <td>
                                    <a href="<g:createLink controller="workflow" action="index"
                                                           params="[id: workflowData.workflowBoard.workflowMaster.id, opportunityId: workflowData.opportunity.id]"/>">
                                        <div>
                                            <g:if test="${workflowData.opportunity.isService()}">
                                                <g:message code="service.opportunity"/>
                                            </g:if>
                                            <g:else>
                                                <g:message code="${workflowData.opportunity.category}"/>
                                                <i class="fas fa-long-arrow-alt-right"></i>
                                                <g:message code="${workflowData.opportunity.status}"/>
                                            </g:else>
                                        </div>
                                        <h6>
                                            ${workflowData.workflowBoard.workflowMaster.name + "/" + workflowData.workflowBoard.name}
                                        </h6>
                                        <span class="mdi mdi-open-in-new">
                                            <g:message code="soldboard.goto.workflow"/>
                                        </span>
                                    </a>
                                </td>
                            </tr>
                        </g:each>
                    </table>
                </g:if>
                <g:else>
                    <div class="alert alert-info">
                        <g:message code="no.workflowdata"/>
                    </div>
                </g:else>
            </g:if>
            <g:else>
                <div class="alert alert-danger">
                    <g:message code="error.role"/>
                </div>
            </g:else>
        </div>
        <div class="modal-footer d-block">
            <g:if test="${client}">
                <a class="mdi mdi-open-in-new float-left" href="<g:createLink controller="client" action="index" id="${client.id}"/>">
                    <g:message code="soldboard.goto.client"/>
                </a>
            </g:if>
            <button type="button" class="btn btn-secondary float-right" data-bs-dismiss="modal"><g:message code="cancel"/></button>
        </div>
    </div>
</div>

