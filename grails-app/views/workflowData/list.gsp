<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title>${title}</title>
    <asset:javascript src="leads/OpportunityDataTable.js"/>
    <asset:stylesheet src="leads/LeadsTable.css"/>
    <script>
        var leadsTable
        $(document).ready(function () {
            leadsTable = new OpportunityDataTable({
                title: '${title}',
                id: 'workflowdatalist${workflowMaster?.id}',
                divId: 'leads',
                modifyLiveFilterJson: function (filter) {
                    filter.workflowMasterId = '${workflowMaster?.id}';
                    filter.workflowDataOnly = true;
                    return filter;
                }
            });
        });
    </script>
</head>

<body>
<div class="p-4">
    <div id="leads"></div>
</div>
</body>
</html>
