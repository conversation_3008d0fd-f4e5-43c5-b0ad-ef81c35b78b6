<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="dms"/></title>
</head>

<body>
<div class="clearfix m-4">
    <div class="d-flex justify-content-between">
        <h2 ><g:message code="dms"/></h2>
        <button type="button" class="btn btn-primary d-flex align-items-center"
        onclick="TractionModal.show({url: '/dms/edit'});"><i class="fa-solid fa-circle-plus me-2"></i><g:message code="create"/></button>
    </div>
</div>

<div class="m-4">
    <table class="table table-hover dataTable w-100" id="dmsTable">
        <thead>
        <tr>
            <th><g:message code="dms.name"/></th>
            <th><g:message code="dms.dmsId"/></th>
            <th><g:message code="dms.source"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="dms" in="${dmsList}">
            <tr class="cursor" onclick="TractionModal.show({url: '/dms/edit', data: {id: ${dms.id}}});">
                <td>${dms.name}</td>
                <td>${dms.dmsId}</td>
                <td><g:message code="${dms.source.name}"/></td>
            </tr>
        </g:each>
        </tbody>
    </table>
    <script>
        TractionDataTable.init('#dmsTable', {
            id: 'dmsTable',
            dataTableOptions: {
                serverSide: false,
                searching: false,
                buttons: []
            }
        });
    </script>
</div>
</body>
</html>
