<div class="col-12">
    <div id="tableMenu" class="d-flex flex-row-reverse  border p-2">
        <button class="btn btn-primary me-2 btn-sm" type="button"
                onclick="resetTable();"><g:message code="export.client.clearfilter"/>
        </button>

        <div class="dropdown me-2">
            <button class="btn btn-primary dropdown-toggle btn-sm" type="button" id="column" data-bs-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="false">
                <g:message code="export.client.columns"/>
                <span id="badgeColumn" class="badge bg-body-tertiary"></span>
            </button>

            <div class="dropdown-menu dropdown-menu-end">
                <div class="checkbox dropdown-item">
                    <label class="w-100">
                        <input id="formDataResetFilter" type="checkbox" class="option_all" value="allfilter" onclick="resetVisibility();">
                        <g:message code="export.client.filter.selectAll"/>
                    </label>
                </div>

                <div id="columnVisMenu" class="row ms-1 me-1" style="width: 700px;">
                </div>
            </div>
        </div>
    </div>
</div>
