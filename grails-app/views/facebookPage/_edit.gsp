<asset:javascript src="facebookPage/index.js"/>
<div class="modal-dialog modal-dialog-scrollable modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title"><g:message
                    code="facebookpage.${facebookPage ? 'edit' : 'add'}"/> ${facebookPage ? "| " + facebookPage.name : ""}</h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"></button>
        </div>

        <div class="modal-body">
            <div class="row mb-5">
                <input type="hidden" id="facebookId" value="${facebookPage?.id}"/>

                <div class="mb-3 col-6">
                    <label for="facebookPageName"><g:message code="chatwidget.name"/></label>
                    <input class="form-control" id="facebookPageName" value="${facebookPage?.name}">
                </div>

                <div class="mb-3 col-6">
                    <label><g:message code="associated.origin"/></label>
                    <select class="form-select bg-body" id="origin">
                        <option value=""><g:message code="none"/></option>
                        <g:each var="o" in="${origins}">
                            <option value="${o.id}" ${o.id == facebookPage?.originId ? 'selected' : ''}>${o.name}</option>
                        </g:each>
                    </select>
                </div>

                <div class="mb-3 col-6">
                    <label for="facebookPageId"><g:message code="facebookPage.pageId"/></label>
                    <input class="form-control" id="facebookPageId" value="${facebookPage?.pageId}"/>
                </div>

                <div class="mb-3 col-6">
                    <label for="accessToken"><g:message code="facebookPage.accessToken"/></label>
                    <input class="form-control" id="accessToken" value="${facebookPage?.accessToken}"/>
                </div>

                <div class="mb-3 col-12">
                    <label><g:message code="chatwidget.lists"/></label>

                    <div>
                        <div class="w-100">
                            <container:userSelect2 selectedUsers="${facebookPage?.users}" id="users" class="form-select"
                                                   name="users"/>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer p-0 d-flex justify-content-end">
                <button class="btn btn-primary rounded-pill" onclick="saveFacebookPage();">
                    <g:message code="default.button.save.label"/>
                </button>
            </div>
        </div>
    </div>
</div>