<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message code="vehicle.dms.rules"/></title>
    <asset:javascript src="vehicle/vehicleDMSImport.js"/>
    <asset:stylesheet src="lib/codeMirror/codemirror.css"/>
    <asset:javascript src="lib/codeMirror/codemirror.js"/>

    <asset:javascript src="lib/codeMirror/addon/display/autorefresh.js"/>
    <asset:javascript src="lib/codeMirror/addon/edit/matchbrackets.js"/>
    <asset:javascript src="lib/codeMirror/addon/lint/json-lint.js"/>
    <asset:javascript src="lib/codeMirror/addon/lint/lint.css"/>
    <asset:javascript src="lib/codeMirror/addon/lint/lint.js"/>

    <script>
        var columnsList = [];
        var columnsListMessage = [];
        var columnsListCheckBox = [];
        var columnsListStepList = [];
        var columnsListDone = [];
        var columnsListFlush = [];
        var columnsListMessageFlush = [];
        var columnsListCheckBoxFlush = [];
        var columnsListStepListFlush = [];
        var columnsListDoneFlush = [];
        var selectedDMSRule = ${select};

        $(document).ready(function () {
            <g:each var="column" in="${columns}">
            columnsList.push("${column.column}");
            columnsListMessage.push("<g:message code="vehicle.${column.column}"/>");
            columnsListStepList.push("${column.page}");
            columnsListCheckBox.push("");
            columnsListDone.push(0);

            columnsListFlush.push("${column.column}");
            columnsListMessageFlush.push("<g:message code="vehicle.${column.column}"/>");
            columnsListStepListFlush.push("${column.page}");
            columnsListCheckBoxFlush.push("");
            columnsListDoneFlush.push(0);
            </g:each>
        });
    </script>
</head>

<body>
<div class="bg-body-tertiary border p-5">
    <h4 class="text-uppercase mb-5">
        <g:message code="vehicle.dms.rules"/>
    </h4>

    <div class="row">
        <div class="col-xs-12 col-lg-8">
            <div class="row">
                <div class="col-xs-12 col-lg-8 d-flex flex-column">
                    <div class="h6 text-uppercase"><g:message code="vehicle.dms.rules.create"/></div>
                    <div class="fw-bold my-1"><g:message code="vehicle.dms.rules.step.1"/></div>
                    <div class="row w-100 input-group-text border-secondary ms-0 my-1 bg-body">
                        <select class="form-select form-select-sm mode-edit border-0 fw-normal border-0 w-100" style="flex: 1 1 0;" id="dms_name" onchange="">
                            <g:each var="utils" in="${utilsMap}">
                                <option value="${utils}"><g:message code="${utils}"/></option>
                            </g:each>
                        </select>
                        <a class="text-primary nolink" style="display:contents" href="javascript:void(0);" onclick="VehicleDMSIndexSave('');"><i class="fa-solid fa-plus-large"></i></a>
                    </div>
                    <ul id="DMSImportList" class="border border-secondary my-1 rounded p-0" style="list-style-type: none;height: 260px;overflow-y: auto;">
                        <g:render template="/vehicleDMSImport/dMSImportList" model="${[dmsAll: dmsAll,selected: select]}"/>
                    </ul>
                </div>
                <div class="col-xs-12 col-lg-4">
                    <div class="align-items-center d-flex mt-5 mb-1">
                        <div class="fw-bold"><g:message code="vehicle.dms.rules.step.2"/></div>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_user_h">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_creds_user" value="${selectDms?.user}" placeholder="<g:message code="vehicle.dms.rules.step.2.user"/>"></input>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_mdp_h">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_creds_mdp" value="${selectDms?.password}"  placeholder="<g:message code="vehicle.dms.rules.step.2.mdp"/>"></input>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_filepath_h">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_creds_filepath" value="${selectDms?.filepath}"  placeholder="<g:message code="vehicle.dms.rules.step.2.filepath"/>"></input>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_url_h">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_creds_url" value="${selectDms?.url}"  placeholder="<g:message code="vehicle.dms.rules.step.2.url"/>"></input>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_token_h">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_creds_token" value="${selectDms?.token}" placeholder="<g:message code="vehicle.dms.rules.step.2.token"/>"></input>
                    </div>
                    <div class="row w-100 input-group-text border-secondary bg-body mb-2" id="opt_creds_dept_h">
                        <select class="form-select form-select-sm mode-edit border-0 fw-normal border-0 w-100" style="flex: 1 1 0;" id="opt_creds_dept" onchange="">
                            <g:each var="depts" in="${deptsMap}">
                                <g:if test="${selectDms?.department?.id?.equals(depts?.id)}">
                                    <option value="${depts?.id}" selected>${depts?.name}</option>
                                </g:if>
                                <g:else>
                                    <option value="${depts?.id}">${depts?.name}</option>
                                </g:else>
                            </g:each>
                        </select>
                    </div>
                    <button type="button" class="row w-100 btn btn-sm btn-primary w-100 fw-bold border-secondary py-2" onclick="searchStock()">
                        <i class="fa-regular fa-link w-auto"></i><g:message code="vehicle.dms.search.stock"/>
                    </button>
                    <p id="date-file-dms" class="fw-bold text-center"></p>
                </div>
            </div>

            <div class="fw-bold my-1"><g:message code="vehicle.dms.rules.step.3"/></div>
            <div class="mb-3">
                <textarea id="dmsconfig" class="form-control" rows="25" style="font-size: 12px">${config}</textarea>
            </div>
        </div>
        <div class="col-xs-12 col-lg-4">
            <div class="w-100 h-100 border-start">
                <div style="flex-direction: column;margin-left: 24px;">
                    <div class="d-flex">
                        <div>
                            <div class="h6 text-uppercase"><g:message code="vehicle.dms.rules.column"/></div>
                            <div class="fw-bold my-1"><g:message code="vehicle.dms.rules.step.4"/></div>
                        </div>
                        <div class="ms-auto" style="padding-top: 10px; padding-bottom: 10px;">
                            <button class="btn btn-primary fw-normal all_select" id="opt_columns_all" onclick="VehicleDMSIndexSelectAll();"><g:message code="vehicle.dms.rules.step.4.all.select"/></button>
                        </div>
                    </div>
                    <div class="row w-100 input-group-text border-secondary ms-0 my-1 bg-body">
                        <div class="col-xs-12 col-lg-5">
                            <select class="form-select form-select-sm mode-edit border-0" style="flex: 1 1 0;" id="step3.page" onchange="VehicleDMSIndexSearch();">
                                <option value="tous"><g:message code="vehicle.dms.rules.step.4.page_all"/></option>
                                <option value="price"><g:message code="vehicle.dms.rules.step.4.page_price"/></option>
                                <option value="date"><g:message code="vehicle.dms.rules.step.4.page_date"/></option>
                                <option value="specs"><g:message code="vehicle.dms.rules.step.4.page_specs"/></option>
                                <option value="data"><g:message code="vehicle.dms.rules.step.4.page_data"/></option>
                                <option value="info"><g:message code="vehicle.dms.rules.step.4.page_info"/></option>
                            </select>
                        </div>
                        <div class="col-xs-12 col-lg-4 opt_dms_column_section_actif">
                            <select class="form-select form-select-sm mode-edit border-0" style="flex: 1 1 0;" id="step3.actif" onchange="VehicleDMSIndexSearch();">
                                <option value="tous"><g:message code="vehicle.dms.rules.step.4.actif_all"/></option>
                                <option value="actif"><g:message code="vehicle.dms.rules.step.4.actif"/></option>
                                <option value="inactif"><g:message code="vehicle.dms.rules.step.4.inactif"/></option>
                            </select>
                        </div>
                        <div class="col-xs-12 col-lg-3 opt_dms_column_section_flush">
                            <select class="form-select form-select-sm mode-edit border-0" style="flex: 1 1 0;" id="step3.flush" onchange="VehicleDMSIndexswitchFlush();">
                                <option value="created"><g:message code="vehicle.dms.rules.step.4.replaced"/></option>
                                <option value="flush"><g:message code="vehicle.dms.rules.step.4.flush"/></option>
                            </select>
                        </div>
                    </div>
                    <div class="w-100 input-group-text border-secondary bg-body">
                        <input type="text" class="fw-normal border-0 w-100" id="opt_search-ul" onkeyup="VehicleDMSIndexSearch();" placeholder="<g:message code="vehicle.dms.rules.step.4.search"/>"></input>
                        <div>
                            <i class="text-primary fa-light fa-magnifying-glass"></i>
                        </div>
                    </div>

                    <ul name="dms_columns" id="dms_columns" class="border border-secondary my-1 w-100 list-group" style="list-style-type: none;height: 700px;overflow-y: auto;" onchange="">
                    </ul>
                    <ul name="dms_columns_flush" id="dms_columns_flush" class="border border-secondary my-1 w-100 list-group" style="display:none;list-style-type: none;height: 700px;overflow-y: auto;" onchange="">
                    </ul>
                    <div class="d-flex justify-content-end mt-3">
                        <button type="button" class="btn btn-primary me-2" onclick="VehicleDMSIndexSave('update');"><g:message code="save"/></button>
                        <button type="button" class="btn btn-outline-primary" onclick="VehicleDMSIndexTest();"><g:message code="vehicle.dms.rules"/></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>