import traction.config.CardType
import traction.config.Icon
import traction.service.LautopakPartReservation
import traction.service.ServiceSchedule
import traction.service.WorkOrder
import traction.service.WorkOrderJob

model {
    List<Icon> icons
    List<Icon> iconsJobs
    List<CardType> cardTypes
}

json {
    serviceSchedule {
        status ServiceSchedule.Status.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        icon: it.icon,
                        color: it.color
                ]
        ] }
    }
    workOrder {
        icons icons.collectEntries { [
                (it.id): [
                        icon: it.icon,
                        color: it.color,
                        title: it.title
                ]
        ] }
        status WorkOrder.Status.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        closed: it.closed
                ]
        ] }
        type WorkOrder.Type.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        icon: it.icon
                ]
        ] }
    }
    workOrderJob {
        icons iconsJobs.collectEntries { [
                (it.id): [
                        icon: it.icon,
                        color: it.color,
                        title: it.title
                ]
        ] }
        status WorkOrderJob.Status.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message)
                ]
        ] }
        type WorkOrderJob.AuthorizationStatus.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        icon: it.icon,
                        color: it.color
                ]
        ] }
        statusTech WorkOrderJob.StatusTech.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        dotColor: it.dotColor
                ]
        ] }
        assignmentStatus WorkOrderJob.AssignmentStatus.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        icon: it.icon,
                        iconColor: it.iconColor,
                        borderColor: it.borderColor
                ]
        ] }
    }
    lautopakPartReservation {
        status LautopakPartReservation.Status.values().collectEntries { [
                (it.name()): [
                        message: g.message(code: it.message),
                        icon: it.icon,
                        iconColor: it.iconColor
                ]
        ] }
    }
    workflowData {
        typeOfCard cardTypes.collectEntries { [
                (it.id): [
                        color: it.color,
                        title: it.title
                ]
        ] }
    }
}