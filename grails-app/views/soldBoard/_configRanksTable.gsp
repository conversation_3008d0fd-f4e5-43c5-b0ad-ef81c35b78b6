<div class="h-100 overflow-scroll  border rounded-2">
    <table class="table p-0 m-0" id="soldboard-table">
        <thead>
        <tr class="border-bottom">
            <th scope="col" style="width: 48%;"><g:message code="soldboard.goals.ranks.rate"/></th>
            <th scope="col" style="width: 48%;"><g:message code="soldboard.goals.ranks.color"/></th>
            <th scope="col" style="width: 4%;"><i class="fa-solid fa-circle-plus text-primary" onclick="TractionModal.show({ url: '/config/modaladdusergoalsrank'});"></i></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="rank" in="${ranks}">
            <tr>
                <th scope="row">${rank.rate}</th>
                <td>
                    <div class="d-flex align-items-center gap-2">
                        <div class="rounded-circle" style="height: 16px; width: 16px; background-color: ${rank.color};"></div>${rank.color}
                    </div>
                </td>
                <td>
                    <g:if test="${!hideDelete}">
                        <i onClick="deleteGoalsRank(${rank.rate});" class="fa-light fa-trash text-primary" data-toggle="tooltip" title="<g:message code="default.button.delete.label"/>"></i>
                    </g:if>
                </td>
            </tr>
        </g:each>
        </tbody>
    </table>
</div>
<style>
    #soldboard-table td, #soldboard-table th {
        padding: 12px 24px;
    }
</style>