<div class="col-12 pt-2">
    <table id="provider-list" class="table table-hover" style="width: 100%">
        <thead>
        <tr>
            <th><g:message code="lautopakprovider.extId"/></th>
            <th><g:message code="lautopakprovider.name"/></th>
            <th><g:message code="lautopakprovider.dms"/></th>
            <th><i class="mdi mdi-truck-delivery" title="<g:message code="lautopakprovider.daysForReception"/>" data-toggle="tooltip"></i></th>
            <th><g:message code="lautopakprovider.order.count30"/></th>
            <th><g:message code="lautopakprovider.order.count"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="p" in="${providers}">
            <tr class="cursor" onclick="LautopakProvider.edit(${p.id});">
                <td>${p.extId}</td>
                <td>${p.name}</td>
                <td>${p.dms.name}</td>
                <td>${p.daysForReception}</td>
                <td>${orderCount30[p.id] ?: 0}</td>
                <td>${orderCount[p.id] ?: 0}</td>
            </tr>
        </g:each>
        </tbody>
    </table>
</div>
<script>
    $(document).ready(function () {
        TractionDataTable.init('#provider-list', {
            id: "provider-list",
            enableState: true,
            dataTableOptions: {
                serverSide: false,
                buttons: [
                    TractionDataTable.buttonCreateFunction('lautopakprovider.create', function () {
                        LautopakProvider.edit();
                    })
                ]
            }
        });
    });
</script>