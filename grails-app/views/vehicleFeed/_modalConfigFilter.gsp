<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2  id="title">
                <g:message code="managerproductfeed.configs"/>
            </h2>
            <button type="button" id="modaldismiss" class="btn-close opacity-100" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
            <form id="userCommunicationTransferForm" class="form">
            <input type="hidden" name="userId" value=""/>

            <div class="form-group">
                <label class="">
                    <g:message code="productmanager.feeds.listadd"/>:
                </label>

                <div class="row">
                    <div class="col col-md-6">
                        <select id="availableConfig" class="form-select">
                            <g:each var="available" in="${availableConfigs}">
                                <option value="${available.id}">${available.name}</option>
                            </g:each>
                        </select>
                    </div>
                    <div class="col col-md-6">
                        <select id="availableFilter" class="form-select">
                            <g:each var="filter" in="${availableFilters}">
                                <option value="${filter.id}">
                                    ${filter.name}
                                </option>
                            </g:each>
                        </select>
                    </div>
                </div>


                <div class="p-2 d-flex">
                    <button id="addButtonConfig" type="button" class="btn btn-primary rounded-pill me-2" onclick="_addConfigFilter()"><g:message code="add"/></button>
                    <button id="delButtonConfig" type="button" class="btn btn-link me-2" onclick="_removeConfigFilter()" disabled="disabled">
                        <i class="fa-regular fa-trash"></i>
                    </button>
                    <button id="orderUpButtonConfig" type="button" class="btn btn-link me-2" onclick="_orderConfigFilter('up')" disabled="disabled">
                        <span class="">⇈</span>
                    </button>
                    <button id="orderDownButtonConfig" type="button" class="btn btn-link" onclick="_orderConfigFilter('down')" disabled="disabled">
                        <span class="">⇊</span>
                    </button>
                </div>
                <div>
                    <select id="currentConfig" style="width: 100%;" size="5">
                        <g:each var="current" in="${currentConfigs}">
                            <option value="${current.id}">${current.conf.name} &nbsp  (<g:message code="vehicle.feed.filter"/>: ${current.filter.name})</option>
                        </g:each>
                    </select>
                </div>

            </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.getElementById('currentConfig').addEventListener('change', function () {
        if (this.value !== '') {
            $('#delButtonConfig').removeAttr('disabled');
            $('#orderUpButtonConfig').removeAttr('disabled');
            $('#orderDownButtonConfig').removeAttr('disabled');
        } else {
            $('#delButtonConfig').setAttribute('disabled', 'disabled');
            $('#orderUpButtonConfig').setAttribute('disabled', 'disabled');
            $('#orderDownButtonConfig').setAttribute('disabled', 'disabled');
        }

    })

    document.getElementById('currentFilter').addEventListener('change', function () {
        if (this.value !== '') {
            $('#delButtonFilter').removeAttr('disabled');
            $('#orderUpButtonFilter').removeAttr('disabled');
            $('#orderDownButtonFilter').removeAttr('disabled');
        } else {
            $('#delButtonFilter').setAttribute('disabled', 'disabled');
            $('#orderUpButtonFilter').setAttribute('disabled', 'disabled');
            $('#orderDownButtonFilter').setAttribute('disabled', 'disabled');
        }

    })

    function _addConfigFilter() {
        const selectedConfigToAdd = $("#availableConfig").val();
        const selectedFilterToAdd = $("#availableFilter").val();

        $.ajax({
            url: tractionWebRoot + "/vehicleFeed/addConfigAndFilter",
            type: "POST",
            data: {
                id: ${id},
                conf: selectedConfigToAdd,
                filter: selectedFilterToAdd
            },
            success: function (data) {
                _reloadModal();
                if(data.success == true)
                    $.notify(data.message, 'success')
                else
                    $.notify(data.message, 'error')
            }
        });
    }

    function _removeConfigFilter() {
        const selectedConfigToRemove = $("#currentConfig").val();

        $.ajax({
            url: tractionWebRoot + "/vehicleFeed/removeConfigAndFilter",
            type: "POST",
            data: {
                id: ${id},
                mapping: selectedConfigToRemove,
            },
            success: function (data) {
                _reloadModal();
                if(data.success == true)
                    $.notify(data.message, 'success')
                else
                    $.notify(data.message, 'error')
            }
        });
    }

    function _orderConfigFilter(order) {
        const selectedConfigToMove = $("#currentConfig").val();

        $.ajax({
            url: tractionWebRoot + "/vehicleFeed/reorderConfigAndFilter",
            type: "POST",
            data: {
                id: ${id},
                order: order,
                mapping: selectedConfigToMove,
            },
            success: function (data) {
                _reloadModal();
                if(data.success == true)
                    $.notify(data.message, 'success')
                else
                    $.notify(data.message, 'error')
            }
        });

    }

    function _reloadModal() {
        TractionModal.show({
            url: '/vehicleFeed/modalConfigFilter/',
            data: {
                id: ${id},
            }
        });
    }
</script>