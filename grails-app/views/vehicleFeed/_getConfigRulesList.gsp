<g:each var="rule" in="${list}">
    <li class="${selected == rule ? "bg-info-subtle" : ""} px-sm-2 py-2"
        onclick="VehicleFeedConfig.getConfigRulesList(${rule?.feedConfigId}, ${rule.id});">
        ${rule.rule}
        <i class="text-primary my-1 mx-sm-2 fa-light fa-trash float-right"
           onclick="VehicleFeedConfig.deleteConfigRule(${rule.id}, ${rule.feedConfigId});"></i>
        <i style="" class="text-primary my-1 mx-sm-2 ${rule.quoted ? "fa-solid" : "fa-light"} fa-quote-right float-right"
           onclick="VehicleFeedConfig.toggleRuleQuoted(${rule.id}, ${rule.feedConfigId});"></i>
    </li>
</g:each>
<script>
    <g:if test="${selected}">
        VehicleFeedConfig.getConfigRuleActionsList(${selected.id});
    </g:if>
</script>