<h4 class="text-uppercase"><g:message code="vehicle.feed.create"/></h4>

<div class="fw-bold my-1"><g:message code="vehicle.feed.create.name"/></div>
<div class="col-xs-12 input-group-text border mb-2 bg-body">
    <input id="feedName" type="text" class="fw-normal border-0 w-100" placeholder="<g:message code="vehicle.feed.create.name.holder"/>" value="${vehicleFeedsConfig?.name}"/>
</div>

<div class="fw-bold my-1"><g:message code="vehicle.feed.create.file"/></div>
<div class="col-xs-12 input-group-text border bg-body">
    <input id="feedFile" type="text" class="fw-normal border-0 w-100" placeholder="<g:message code="vehicle.feed.create.file.holder"/>" value="${vehicleFeedsConfig?.file}"/>
</div>

<div class="row m-0">
    <div class="col-xs-12 col-lg-4 mt-5">
        <div class="row">
            <div class="col-xs-12 col-lg-11 p-0">
                <div class="fw-bold my-1"><g:message code="vehicle.feed.choose.rule"/></div>
                <select class="form-select" style="flex: 1 1 0;" id="feedRulesFonction" onchange="">
                    <g:each var="rule" in="${configRules}">
                        <option value="${rule}">${rule}</option>
                    </g:each>
                </select>
            </div>
            <div class="col-xs-12 col-lg-1">
                <div class="h-100 align-items-center d-flex my-2">
                    <div class="fs-6 mt-xxl-3 mt-3">
                        <a class="text-primary nolink" href="javascript:void(0);" onclick="VehicleFeedConfig.createConfigRule(${vehicleFeedsConfig.id});"><i class="fa-solid fa-plus-large fs-5"></i></a>
                    </div>
                </div>
            </div>
            <ul name="feedRulesList" id="feedRulesList" class="border my-1 w-100 list-group" style="list-style-type: none;height: 500px;overflow-y: auto;" onchange="">
            </ul>
            <script>
                VehicleFeedConfig.getConfigRulesList(${vehicleFeedsConfig.id});
            </script>
            <div class="my-3 w-100" style="">
                <a class="text-primary text-decoration-underline nolink" href="javascript:void(0);" onclick="VehicleFeedConfig.choosePreviewVehicle();"><g:message code="vehicle.feed.select.vehicle"/></a>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-lg-8 mt-5">
        <div class="fw-bold my-1"><g:message code="vehicle.feed.add.action"/></div>
        <div class="d-flex">
            <div id="value-modifier-form" class="flex-grow-1"></div>
            <div class="fs-6 px-3 mt-auto mb-3">
                <a class="text-primary nolink" href="javascript:void(0);" onclick="VehicleFeedConfig.createConfigRuleAction();"><i class="fa-solid fa-plus-large"></i></a>
            </div>
        </div>
        <script>
            VehicleFeedConfig.getValueModifierForm()
        </script>

        <ul name="feedActionsList" id="feedActionsList" class="border my-1 w-100 list-group" style="list-style-type: none;height: 450px;overflow-y: auto;" onchange="">
        </ul>
        <div class="my-3 w-100 d-flex justify-content-end" style="">
            <button type="button" class="btn btn-primary float-right me-2" onclick="VehicleFeedConfig.saveConfig(${vehicleFeedsConfig.id});"><g:message code="vehicle.feed.save"/></button>
            <button type="button" class="btn btn-primary float-right" onclick="VehicleFeedConfig.duplicateConfig(${vehicleFeedsConfig.id});"><g:message code="vehicle.feed.duplicate"/></button>
        </div>
    </div>
</div>
