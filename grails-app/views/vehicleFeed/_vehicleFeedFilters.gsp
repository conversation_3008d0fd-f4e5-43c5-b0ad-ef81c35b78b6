<asset:stylesheet src="filter/index.css"/>
<script>
    $(document).ready(function () {
        elem = document.getElementById("select-filters");
        const event = new Event("change");
        elem.dispatchEvent(event);
    });

    function getFilterCount(id) {
        $.get({
            url: tractionWebRoot + '/filter/getCount/' + id,
            dataType: 'text',
            success: function (data) {
                alert(data);
            }
        });
    }
    function saveFilterModal(type) {
        var name = $('#name').val();
        var id = $('#id').val();
        var result = $('#builder-basic').queryBuilder('getRules');
        if (result) {
            $.post({
                url: tractionWebRoot + '/filter/save?type=' + type + '&name=' + name + '&id=' + id,
                contentType: 'application/json',
                data: JSON.stringify(result),
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        $.post({
                            url: tractionWebRoot + '/vehicleFeed/filterTab',
                            success: function(data) {
                                $("#vehicleFeedFilters").html(data)
                            }
                        })

                    } else {
                        $.notify(data.message, 'error');
                    }
                }
            });
        }else {
            $.notify(I18N.messages['invalid.filter'], 'error');
        }
    }

    function deleteFilterModal(type) {
        var id = $('#id').val();
        if (id) {
            $.post({
                url: tractionWebRoot + '/filter/delete/' + id,
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        $.post({
                            url: tractionWebRoot + '/vehicleFeed/filterTab',
                            success: function (data) {
                                $.post({
                                    url: tractionWebRoot + '/vehicleFeed/filterTab',
                                    success: function(data) {
                                        $("#vehicleFeedFilters").html(data)
                                    }
                                })
                            }
                        })
                    } else {
                        $.notify(data.message, 'error');
                    }
                }
            });
        }
    }
</script>
<div>
    <input type="hidden" id="id" value="">
    <div class="row">
        <div class="col">
            <label class="fw-bold"><g:message code="filter.select.to.load"/></label>
            <select id="select-filters" class="form-select">
                <option value=""><g:message code="filter.create"/></option>
                <g:each var="filter" in="${filters}">
                    <option value="${filter.id}">${filter.name}</option>
                </g:each>
            </select>
        </div>
        <div class="mb-3 col">
            <label class="fw-bold"><g:message code="filter.name"/></label>
            <input class="form-control" id="name" value="">
        </div>
    </div>

    <div id="builder-container" class="mb-3">
        <label><g:message code="filter.queryBuilder"/></label>
        <div class="query-builder-filter"></div>
    </div>
    <div class="d-flex gap-2">
        <button data-toggle="tooltip" title="<g:message code="filter.get.count"/>" class="btn btn-primary btn-count" onclick="">
            <i class="mdi mdi-counter"></i>
        </button>
        <a data-toggle="tooltip" title="<g:message code="filter.duplicate"/>" class="btn btn-primary btn-duplicate" onclick="">
            <i class="mdi mdi-content-duplicate"></i>
        </a>
        <button class="btn btn-primary" onclick="saveFilterModal('${type.id}')"><g:message code="save"/></button>
        <button class="btn btn-outline-primary" onclick="deleteFilterModal('${type.id}')"><g:message code="default.button.delete.label"/></button>
    </div>
</div>
<script>
    $("#select-filters").on("change", function() {
        $("#name").val($(this).find("option:selected").text());
        $("#id").val($(this).val());
            $.get({
                url: tractionWebRoot + '/filter/editFilter',
                data: {
                    id: $(this).val(),
                    type: ${type?.id}
                },
                success: function (html) {
                    $(".query-builder-filter").html(html);
                    $(".btn-count").on("click", function() {
                        getFilterCount($("#id").val());
                    });
                }
            })
    });
	
	$(".btn-duplicate").on("click", function() {
		$.get({
			url: tractionWebRoot + '/filter/editFilter',
			data: {
				id: $("#id").val(),
				duplicate: true
			},
			success: function () {
				$.post({
					url: tractionWebRoot + '/vehicleFeed/filterTab',
					success: function(data) {
						$("#vehicleFeedFilters").html(data)
					}
				})
			}})
	});
	
</script>