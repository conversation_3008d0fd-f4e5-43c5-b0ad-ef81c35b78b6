<div class="row">
    <div class="col-xs-12 col-lg-7 border-bottom border-end bg-body-tertiary">
        <p class="fw-bold border-2 p-2 px-1 w-100 mb-0 ms-3 text-capitalize py-1"><g:message code="feed.config.action"/></p>
    </div>
    <div class="col-xs-12 col-lg-5 border-bottom bg-body-tertiary">
        <p class="fw-bold border-2 p-2 px-0 w-100 mb-0 text-capitalize py-1"><g:message code="feed.config.preview"/></p>
    </div>
</div>
<g:set var="previewvalue" value=""/>
<g:each var="action" in="${list}">
    <div class="row">
        <div class="col-xs-12 col-lg-7">
        <li class="px-sm-3 py-2 ${action.active ? "" : "text-secondary"}" onclick="VehicleFeedConfig.getValueModifierForm(${action.id});">
            ${action.valueModifierType}/${action.value}
        </li>
        </div>
    <div class="col-xs-12 col-lg-5 py-2">
        <g:set var="previewvalue" value="${action.getValueModifierPreview(previewVehicle, previewvalue)}"/>
        ${previewvalue}
        <i style="" class="text-primary FAConfig_list_icons_${action.id} py-1 px-sm-2 fa-light fa-trash float-right"
           onclick="VehicleFeedConfig.deleteRuleAction('${action.id}');"></i>
        <i style="display: block;"
           class="text-primary my-1 mx-sm-1 ${action.active ? 'fa-solid fa-toggle-on' : 'fa-light fa-toggle-off'} float-right"
           onclick="VehicleFeedConfig.toggleRuleActionActive(${action.id});"></i>
        <span style="display: block;" class="text-primary mx-sm-1 float-right"
              onclick="VehicleFeedConfig.reorderRuleAction('${action.id}','down');"><i
                class="fa-solid fa-down-long"></i><i class="fa-solid fa-down-long"></i></span>
        <span style="display: block;" class="text-primary mx-sm-1 float-right"
              onclick="VehicleFeedConfig.reorderRuleAction('${action.id}','up');"><i class="fa-solid fa-up-long"></i><i
                class="fa-solid fa-up-long"></i></span>

    </div>
    </div>
</g:each>