<script>
    $(document).ready(function () {

        if (window['chart-bar-request']) {
            window['chart-bar-request'].abort();
        }
        window['chart-bar-request'] = $.get(tractionWebRoot + '/salesClosingRate/getBarChart?medias=${mediaIds}&statusId=${status?.id}&userIds=${users.id.join(",")}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}', function (data) {
            var chartBar = document.getElementById('chart-bar');
            var datasets = [{
                label: '<g:message code="sales.ratio"/>',
                borderColor: '#000000',
                backgroundColor: '#00000088',
                borderWidth: 2,
                data: data.ratio
            }, /*{
                label: '<g:message code="enter.status"/>',
                borderColor: '#0000ff',
                backgroundColor: '#0000ff88',
                borderWidth: 2,
                data: data.enter
            }, {
                label: '<g:message code="exit.status"/>',
                borderColor: '#ff0000',
                backgroundColor: '#ff000088',
                borderWidth: 2,
                data: data.exit
            }, */{
                label: '<g:message code="sold.status"/>',
                borderColor: '#00ff00',
                backgroundColor: '#00ff0088',
                borderWidth: 2,
                data: data.sold
            }];
            window.usersBarChart = new Chart(chartBar.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    tooltips: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top',
                        onHover: function (event, legendItem) {
                            chartBar.style.cursor = 'pointer';
                        },
                        onLeave: function (event, legendItem) {
                            chartBar.style.cursor = '';
                        },
                        onClick: function (event, legendItem) {
                            window.assignedChartSort = legendItem.datasetIndex;
                            window.usersBarChart.options.sort = legendItem.datasetIndex;
                            window.usersBarChart.update();
                        }
                    },
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
            window.usersBarChart.options.sort = 0;
            window.usersBarChart.update();
            $(chartBar).closest('.divLoader').removeClass('divLoader');
        });
    });

    function setChartDatasetSortIndex(index) {
        window.assignedChart.options.sort = index;
        window.assignedChart.update();
    }
</script>

<div class="leaders row">
    <div class="col-12">
        <h2><g:message code="leaders.salesratio" /></h2>
    </div>
    <g:each var="leadersEntry" in="${leaders}">
        <div class="card-block-big card-visitor-block col-md-4">
            <h5>
                ${leadersEntry.days} <g:message code="day${leadersEntry.days != 1 ? 's' : ''}"/>
            </h5>
            <div class="row">
                <div class="col-6 pe-1">
                    <a class="card" data-toggle="tooltip" title="${leadersEntry.leaders[0].user?.fullName}">
                        <div class="div-img">
                            <g:if test="${leadersEntry.leaders[0].user?.image}">
                                <img src="<g:createLink absolute="true" controller="web" action="getFile" id="${leadersEntry.leaders[0].user?.image.id}"/>" alt="${leadersEntry.leaders[0].user?.username}"/>
                            </g:if>
                            <g:else>
                                <asset:image src="avatar-blank.jpg" alt="Blank Avatar"/>
                            </g:else>
                        </div>
                        <span class="points">${leadersEntry.leaders[0].ratio}</span>
                    </a>
                </div>

                <div class="col-6 ps-1 text-left">
                    <div class="container d-flex flex-column h-100 p-0">
                        <a class="card" data-toggle="tooltip" title="${leadersEntry.leaders[1].user?.fullName}">
                            <div class="div-img">
                                <g:if test="${leadersEntry.leaders[1].user?.image}">
                                    <img src="<g:createLink absolute="true" controller="web" action="getFile" id="${leadersEntry.leaders[1].user?.image.id}"/>" alt="${leadersEntry.leaders[1].user?.username}"/>
                                </g:if>
                                <g:else>
                                    <asset:image src="avatar-blank.jpg" alt="Blank Avatar"/>
                                </g:else>
                            </div>
                            <span class="points">${leadersEntry.leaders[1].ratio}</span>
                        </a>
                        <a class="card" data-toggle="tooltip" title="${leadersEntry.leaders[2].user?.fullName}">
                            <div class="div-img">
                                <g:if test="${leadersEntry.leaders[2].user?.image}">
                                    <img src="<g:createLink absolute="true" controller="web" action="getFile" id="${leadersEntry.leaders[2].user?.image.id}"/>" alt="${leadersEntry.leaders[2].user?.username}"/>
                                </g:if>
                                <g:else>
                                    <asset:image src="avatar-blank.jpg" alt="Blank Avatar"/>
                                </g:else>
                            </div>
                            <span class="points">${leadersEntry.leaders[2].ratio}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </g:each>
</div>

    <div class="col-12 pb-2">
        <h3><g:message code="salesratio.for.period" /></h3>
    </div>
    <div class="col-12">
        <div class="user-history-chart-container divLoader">
            <div class="loader"></div>
            <canvas id="chart-bar"></canvas>
        </div>
    </div>

    <div class="col-12 pb-2">
        <h3><g:message code="category.opportunity.active" /></h3>
    </div>
    <div class="user-history-chart-container">
        <canvas id="chart-category"></canvas>
    </div>

    <div class="col-12 pb-2">
        <h2 class="bd-bottom-grey"><g:message code="opportunities.followup"/> <small>(<g:message code="last.x.months" args="${[12]}"/>)</small></h2>
    </div>
    <div class="user-history-chart-container">
        <canvas id="status-following"></canvas>
    </div>

    <div class="col-12 pb-2">
        <h3><g:message code="salesratio.by.user" /></h3>
    </div>
    <div class="user-history-chart-container">
        <canvas id="chart-users"></canvas>
    </div>

<g:each var="media" in="${medias ?: allMedias}">     
    <div class="col-12 pb-2">
            <h3><g:message code="salesratio.by.user" /> (<g:message code="opportunity.media"/> <g:message code="${media.message}"/> <g:message code="only"/>)</h3>
        </div>
        <div class="user-history-chart-container">
            <canvas id="chart-users${media.id}"></canvas>
        </div>
</g:each>

    <div class="col-12 pb-2">
        <h3><g:message code="salesratio.by.status" /></h3>
    </div>
    <div class="user-history-chart-container">
        <canvas id="chart-status"></canvas>
    </div>

    <div class="col-12 pb-2">
        <h3><g:message code="sales.by.status"/></h3>
    </div>
    <div class="user-history-chart-container">
        <canvas id="chart-sold-status"></canvas>
    </div>

<g:each var="status" in="${statusList}">
        <div class="col-12 pb-2">
            <h3><g:message code="${status.message}" /></h3>
        </div>
        <div class="user-history-chart-container">
            <canvas id="chart-status-${status.id}"></canvas>
        </div>
</g:each>


<script>
    $(document).ready(function () {
        function makeSimpleCharts(labels, datasets, statusId, isCategory) {
            var data = [{
                label: '<g:message code="sales.ratio"/>',
                borderColor: 'black',
                backgroundColor: 'black',
                fill: false,
                data: datasets.ratio,
                yAxisID: 'y-axis-2',
            }, {
                label: '<g:message code="enter.status"/>',
                borderColor: '#0000ff',
                backgroundColor: '#0000ff88',
                fill: false,
                data: datasets.enter,
                yAxisID: 'y-axis-1',
            }, {
                label: '<g:message code="exit.status"/>',
                borderColor: '#ff0000',
                backgroundColor: '#ff000088',
                fill: false,
                data: datasets.exit,
                yAxisID: 'y-axis-1',
            }, {
                label: '<g:message code="sold.status"/>',
                borderColor: '#00ff00',
                backgroundColor: '#00ff0088',
                fill: false,
                data: datasets.sold,
                yAxisID: 'y-axis-1',
            }];
            console.log(data);
            new Chart(document.getElementById(isCategory ? 'chart-category' : 'chart-status-' + statusId).getContext('2d'), {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: data
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    tooltips: {
                        mode: 'index',
                        intersect: false,
                    },
                    hover: {
                        mode: 'nearest',
                        intersect: true
                    },
                    scales: {
                        xAxes: [{
                            display: true,
                            scaleLabel: {
                                display: true
                            }
                        }],
                        yAxes: [{
                            type: 'linear', // only linear but allow scale type registration. This allows extensions to exist solely for log scale for instance
                            display: true,
                            position: 'left',
                            id: 'y-axis-1',
                            scaleLabel: {
                                display: true,
                                labelString: "<g:message code="opportunity.count"/>"
                            },
                            ticks: {
                                suggestedMin: 0
                            }
                        }, {
                            display: true,
                            position: 'right',
                            id: 'y-axis-2',
                            gridLines: {
                                drawOnChartArea: false, // only want the grid lines for one axis to show up
                            },
                            scaleLabel: {
                                display: true,
                                labelString: '<g:message code="sales.ratio"/>'
                            },
                            ticks: {
                                suggestedMin: 0,
                                suggestedMax: 100,
                                callback: function (value) {
                                    return value + '%';
                                },
                            }
                        }]
                    }
                }
            });
        }

        $.get({
            url: tractionWebRoot + '/salesClosingRate/getStatusAndActiveLines?medias=${mediaIds}&userIds=${users.id.join(",")}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}&interval=${interval}',
            success: function (json) {
                console.log(json);
                if (json.labels) {
                    for (var statusId in json.datasetsMap) {
                        var datasets = json.datasetsMap[statusId];
                        console.log(statusId, datasets);
                        makeSimpleCharts(json.labels, datasets, statusId, false);
                    }
                    makeSimpleCharts(json.labels, json.datasetsActive, null, true);
                }
            }
        });
        function createUserLineChart(stacked, medias, elementId) {
            $.get({
                url: tractionWebRoot + '/salesClosingRate/getUserLineChartData?stacked=' + stacked + '&medias=' + medias + '&statusId=${status?.id}&userIds=${users.id.join(",")}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}&interval=${interval}',
                success: function (json) {
                    window.chartUsersElement = document.getElementById(elementId);
                    window.chartUsers = new Chart(window.chartUsersElement.getContext('2d'), {
                        type: 'line',
                        data: {
                            datasets: json.datasets,
                            labels: json.labels
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            tooltips: {
                                mode: 'index',
                                intersect: false,
                                itemSort: (a, b) => b.yLabel - a.yLabel
                            },
                            hover: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                xAxes: [{
                                    display: true,
                                    scaleLabel: {
                                        display: true
                                    }
                                }],
                                yAxes: [{
                                    display: !stacked,
                                    stacked: stacked,
                                    scaleLabel: {
                                        display: true,
                                        labelString: '<g:message code="sales.ratio"/>'
                                    },
                                    ticks: {
                                        suggestedMin: 0,
                                        suggestedMax: 100,
                                        callback: function (value) {
                                            return value + '%';
                                        }
                                    }
                                }]
                            }
                        }
                    });
                }
            });
        }
        createUserLineChart(false, '${mediaIds}', 'chart-users');
        <g:each var="m" in="${medias ?: allMedias}">
            createUserLineChart(true, '${m.id}', 'chart-users${m.id}');
        </g:each>
        $.get({
            url: tractionWebRoot + '/salesClosingRate/getStatusLineChartData?medias=${mediaIds}&userIds=${users.id.join(",")}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}&interval=${interval}',
            success: function (json) {
                json.datasets.forEach(function (dataset) {
                    dataset.data = dataset.ratio;
                });
                window.chartStatusElement = document.getElementById('chart-status');
                window.chartStatus = new Chart(window.chartStatusElement.getContext('2d'),  {
                    type: 'line',
                    data: {
                        datasets: json.datasets,
                        labels: json.labels
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        tooltips: {
                            mode: 'index',
                            intersect: false,
                        },
                        hover: {
                            mode: 'nearest',
                            intersect: true
                        },
                        scales: {
                            xAxes: [{
                                display: true,
                                scaleLabel: {
                                    display: true
                                }
                            }],
                            yAxes: [{
                                display: true,
                                scaleLabel: {
                                    display: true,
                                    labelString: '<g:message code="sales.ratio"/>'
                                },
                                ticks: {
                                    suggestedMin: 0,
                                    suggestedMax: 100,
                                    callback: function (value) {
                                        return value + '%';
                                    }
                                }
                            }]
                        }
                    }
                });
                json.datasets.forEach(function (dataset) {
                    dataset.data = dataset.sold;
                });
                window.chartSoldStatusElement = document.getElementById('chart-sold-status');
                window.chartSoldStatus = new Chart(window.chartSoldStatusElement.getContext('2d'),  {
                    type: 'line',
                    data: {
                        datasets: json.datasets,
                        labels: json.labels
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        tooltips: {
                            mode: 'index',
                            intersect: false,
                        },
                        hover: {
                            mode: 'nearest',
                            intersect: true
                        },
                        scales: {
                            xAxes: [{
                                display: true,
                                scaleLabel: {
                                    display: true
                                }
                            }],
                            yAxes: [{
                                display: true,
                                scaleLabel: {
                                    display: true,
                                    labelString: '<g:message code="main.sales"/>'
                                },
                                ticks: {
                                    suggestedMin: 0
                                }
                            }]
                        }
                    }
                });
            }
        });

        $.get({
            url: tractionWebRoot + '/source/getTotalSourceChartData?medias=${mediaIds}&userIds=${users.id.join(",")}&months=12',
            success: function (json) {
                console.log('getTotalSourceChartData', json);
                window.statusFollowingElement = document.getElementById('status-following');
                console.log("Creating the chart");
                window.sourceBarTotalChart = new Chart(window.statusFollowingElement.getContext('2d'), {
                    type: 'bar',
                    data: json,
                    options: {
                        tooltips: {
                            mode: 'index',
                            intersect: false
                        },
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            yAxes: [
                                {
                                    type: 'linear', // only linear but allow scale type registration. This allows extensions to exist solely for log scale for instance
                                    display: true,
                                    position: 'left',
                                    id: 'y-axis-bar',
                                    scaleLabel: {
                                        display: true,
                                        labelString: I18N.messages['opportunity.count']
                                    },
                                    ticks: {
                                        suggestedMin: 0
                                    },
                                },
                                {
                                    type: 'linear', // only linear but allow scale type registration. This allows extensions to exist solely for log scale for instance
                                    display: true,
                                    position: 'right',
                                    id: 'y-axis-ratio',
                                    gridLines: {
                                        drawOnChartArea: false
                                    },
                                    ticks: {
                                        suggestedMin: 0,
                                        callback: function (value, index, values) {
                                            return value + '%';
                                        }
                                    },
                                    scaleLabel: {
                                        display: true,
                                        labelString: I18N.messages['sales.ratio'],
                                    }
                                }

                            ],
                        },
                        legend: {
                            position: 'bottom',
                            onHover: function (event, legendItem) {
                                window.statusFollowingElement.style.cursor = 'pointer';
                            },
                            onLeave: function (event, legendItem) {
                                window.statusFollowingElement.style.cursor = '';
                            }
                        }
                    }
                });
            }
        });
    });
</script>