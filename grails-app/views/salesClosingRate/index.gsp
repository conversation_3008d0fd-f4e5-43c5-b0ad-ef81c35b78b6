<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message code="main.salesClosingRate"/></title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0"></script>
    <asset:javascript src="libTraction/chartjs-sort-plugin.js"/>

    <asset:stylesheet src="container/openmultiselect.css"/>

    <asset:stylesheet src="pointReport/index.css"/>

    <asset:javascript src="salesClosingRate/index.js"/>
</head>

<body>
<div class="m-4">
    <h4 class="text-uppercase mb-5"><g:message code="main.salesClosingRate"/></h4>

    <div class="d-flex flex-row">
        <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_OPPORTUNITY_ADMIN]">
            <container:departmentGroupUsersSelector departmentGroups="${departmentGroups}" selectedUsers="${users}" onchange="setUsersSelected"/>
        </permission:ifHasPermissions>
        <div class="p-2 ms-auto d-flex flex-wrap gap-2">
            <div>
                <select id="medias" class="form-select h-fit-content me-2" multiple placeholder="<g:message code="opportunity.media"/>">
                    <g:each var="s" in="${medias}">
                        <option value="${s.id}"><g:message code="${s.message}"/></option>
                    </g:each>
                </select>
            </div>
            <div data-toggle="tooltip" title="<g:message code="date"/>">
                <input id="date" value="${fromToInput}" class="form-control">
            </div>
            <div class="open-multi-select" data-toggle="tooltip" title="<g:message code="display"/>">
                <select id="select-interval">
                    <option value="week"><g:message code="report.filter.week"/></option>
                    <option value="month"><g:message code="date.month"/></option>
                </select>
            </div>
            <select id="status" class="form-select h-fit-content w-auto">
                <g:each var="s" in="${status}">
                    <option value="${s.id}"><g:message code="${s.message}"/></option>
                </g:each>
            </select>
            <button onclick="applyFilter()" class="btn btn-primary fw-bold rounded h-fit-content float-right" data-toggle="tooltip"
                    title="<g:message code="filtermenu.apply"/>"><i class="fa-solid fa-filter me-2"></i><g:message code="apply"/></button>
            <button onclick="resetFilters()" class="btn bg-body text-primary border border-primary rounded h-fit-content fw-bold float-right me-2" data-toggle="tooltip"
                    title="<g:message code="filtermenu.reset"/>"><i class="fa-solid fa-arrows-rotate me-2"></i><g:message code="datatable.resetState"/></button>
        </div>
    </div>
</div>

<div class="m-4" id="stats-ratio-users">

</div>

</body>
</html>
