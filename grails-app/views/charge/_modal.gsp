<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold">
                <g:message code="stripecharge.details"/>
            </h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
            <div class="row">
                <div class="col-4">
                    <g:message code="stripecharge.date"/>:
                </div>
                <div class="col-8">
                    ${stripeCharge.date.format(g.message(code: 'dateFormatWithTime'))}
                </div>

                <div class="col-12 mt-2">
                    <h5 class="bd-bottom-grey"><g:message code="stripecharge.outcome"/></h5>
                </div>
                <g:if test="${stripeCharge.outcomeRiskLevel && stripeCharge.outcomeRiskScore}">
                    <div class="col-4">
                        <g:message code="stripecharge.outcomeRisk"/>:
                    </div>
                    <div class="col-8" style="color: ${stripeCharge.outcomeRiskLevel.color};">
                        <span class="risk-score" style="background-color: ${stripeCharge.outcomeRiskLevel.color}33;">
                            ${stripeCharge.outcomeRiskScore}
                        </span>
                        <g:message code="${stripeCharge.outcomeRiskLevel.message}"/>
                    </div>
                </g:if>
                <g:if test="${charge.outcome.networkStatus}">
                    <div class="col-4">
                        <g:message code="stripecharge.outcome.networkStatus"/>:
                    </div>
                    <div class="col-8">
                        <g:message code="stripecharge.outcome.networkStatus.${charge.outcome.networkStatus}"/>
                    </div>
                </g:if>
                <g:if test="${charge.outcome.reason}">
                    <div class="col-4">
                        <g:message code="stripecharge.outcome.reason"/>:
                    </div>
                    <div class="col-8">
                        <g:message code="stripecharge.outcome.reason.${charge.outcome.reason}"/>
                    </div>
                </g:if>
                <g:if test="${charge.outcome.type}">
                    <div class="col-4">
                        <g:message code="stripecharge.outcome.type"/>:
                    </div>
                    <div class="col-8">
                        <g:message code="stripecharge.outcome.type.${charge.outcome.type}"/>
                    </div>
                </g:if>
                <g:if test="${charge.outcome.sellerMessage}">
                    <div class="col-4">
                        <g:message code="stripecharge.outcome.sellerMessage"/>:
                    </div>
                    <div class="col-8">
                        ${charge.outcome.sellerMessage}
                    </div>
                </g:if>
                <div class="col-12 mt-3">
                    <h5 class="bd-bottom-grey"><g:message code="stripecharge.billingDetails"/></h5>
                </div>
                <div class="col-4">
                    <g:message code="stripecharge.billingDetails.name"/>:
                </div>
                <div class="col-8">
                    ${charge.billingDetails.name}
                </div>
                <div class="col-4">
                    <g:message code="stripecharge.billingDetails.email"/>:
                </div>
                <div class="col-8">
                    ${charge.billingDetails.email}
                </div>
                <g:if test="${charge.billingDetails.phone}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.phone"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.phone}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.country}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.country"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.country}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.state}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.state"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.state}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.city}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.city"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.city}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.line1}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.line1"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.line1}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.line2}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.line2"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.line2}
                    </div>
                </g:if>
                <g:if test="${charge.billingDetails.address.postalCode}">
                    <div class="col-4">
                        <g:message code="stripecharge.billingDetails.address.postalCode"/>:
                    </div>
                    <div class="col-8">
                        ${charge.billingDetails.address.postalCode}
                    </div>
                </g:if>
            </div>
        </div>
    </div>
</div>