<asset:stylesheet src="callNotes/CallNoteDataTable.css"/>
<search:clientInit/>

<g:if test="${callNotesIds}">
    <style>
    #filter-menu-div {
        display: none;
    }
    </style>
</g:if>
<script>
    function addNote${id}(client) {
        ${id}.addNote(client);
        TractionModal.hide();
    }
</script>

<h4 class="text-uppercase mb-5">
    <g:message code="main.callnotes"/>
    <g:if test="${callNotesIds}">
        <button onclick="window.location.reload()" class="float-right btn btn-primary btn-sm mdi mdi-refresh"><g:message
                code="callmanager.reloadwithallnotes"/></button>
    </g:if>
</h4>

<table class="table callnotes-table w-100" id="${id}">
    <thead>
    <tr>
        <th class="completed"><g:message code="callmanager.status"/></th>
        <th class="date"><g:message code="date"/></th>
        <th class="departmentGroup"><g:message code="callmanager.group"/></th>
        <th class="user"><g:message code="client.assignation"/></th>
        <th class="client"><g:message code="client"/></th>
        <th class="phone"><g:message code="client.phone"/></th>
        <th class="text"><g:message code="callmanager.message"/></th>
        <th class="comment"><g:message code="callmanager.comment"/></th>
        <th class="record"><g:message code="callmanager.record"/></th>
        <th class="actions"><i class="fa-solid fa-ellipsis-v"></i></th>
    </tr>
    </thead>
    <tbody></tbody>
</table>

<style>

    #notesDatatable_wrapper .form-select ,#notesDatatable_wrapper .form-control,  #notesDatatable_wrapper td,#notesDatatable_wrapper .select2-custom  {
        font-size: 14px!important;
    }
</style>

