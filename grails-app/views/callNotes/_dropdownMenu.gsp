<div>
    <div class="d-flex justify-content-between align-items-center px-4 py-3">
        <h5 class="text-uppercase m-0">
            <g:message code="dashboard.mycallnotes"/>
        </h5>
        <div class="d-flex filter-div align-items-center gap-2">
            <i class="fa-light fa-filter f-24 me-2" style="color: #8D9FB0"></i>
            <button type="button" class="btn btn-outline-primary active rounded-pill" id="showNotesRetard">
                <g:message code="dashboard.late"/> (<span id="retardCount">0</span>)
            </button>
            <button type="button" class="btn btn-outline-primary active rounded-pill" id="showNotesfrozen">
                <g:message code="communication.completion.frozen"/> (<span id="frozenCount">0</span>)
            </button>
            <button type="button" class="btn btn-outline-primary active rounded-pill" id="showNotesTodo">
                <g:message code="dashboard.todo"/> (<span id="todoCount">0</span>)
            </button>
        </div>
    </div>

    <div style="overflow-y: auto; max-height: 350px;" class="no-scrollbar">
        <table class="table table-sm table-hover m-0" id="callnote-table">
            <thead>
            <th class="status min-w-200"><g:message code="communication.status"/></th>
            <th class="client min-w-150"><g:message code="client"/></th>
            <th class="phone min-w-150"><g:message code="client.phone"/></th>
            <th class="user min-w-250"><g:message code="communication.assignedTo"/></th>
            <th class="message min-w-300"><g:message code="callmanager.message"/></th>
            <th class="comment min-w-200"><g:message code="callnote.comment"/></th>
            </thead>

            <tbody>
            <g:if test="${notesRetard}">
                <g:each var="note" in="${notesRetard}">
                    <g:render template="dropdownMenuRow" model="${[note: note, notesid: notesid]}"/>
                </g:each>
            </g:if>

            <g:if test="${notesTodo}">
                <g:each var="note" in="${notesTodo}">
                    <g:render template="dropdownMenuRow" model="${[note: note, notesid: notesid]}"/>
                </g:each>
            </g:if>

            <g:if test="${notesAllUser}">
                <g:each var="note" in="${notesAllUser}">
                    <g:render template="dropdownMenuRow" model="${[note: note, notesid: notesid]}"/>
                </g:each>
            </g:if>
            </tbody>
        </table>
    </div>
    <div class="px-4 py-3 border-top">
        <a class="btn btn-outline-primary fw-bold" style="line-height: 19px; padding-top: 10px; padding-bottom: 10px; border-radius: 4px;" href="<g:createLink absolute="true" controller="callNotes" action="index"/>">
            <i class="fa-regular fa-arrow-up-right-from-square me-2"></i> <g:message code="call.note.see.all"/>
        </a>
    </div>
</div>
<script>
    $(document).ready(function() {
        let headerCallNoteDatatable = TractionDataTable.init('#callnote-table', {
            id: 'callnote-table',
            enableScrollBar: true,
            dataTableOptions: {
                "serverSide": false,
                "order": [[1, "desc"]],
                "paging": false,
                "fixedHeader": {
                    header: true,
                },
                "columns": [
                    {"name": "status"},
                    {"name": "client"},
                    {"name": "phone",
                        "width": "200px",
                    },
                    {"name": "user"},
                    {"name": "message"},
                    {"name": "comment"}
                ],
                buttons: []
            }
        });

        function updateCounts() {
            $('#retardCount').text($('.notesRetardRow').length);
            $('#todoCount').text($('.notesTodoRow').length);
            $('#frozenCount').text($('.notesFrozenRow').length);
        }

        updateCounts();

        $('#showNotesRetard').on('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            $(this).toggleClass('btn-outline-primary btn-outline-secondary');
            const isActive = $(this).hasClass('btn-outline-primary');
            console.log('showNotesRetard clicked:', isActive);
            const rows = $('.notesRetardRow');
            rows.each(function() {
                console.log('Changing display for notesRetardRow:', this);
                $(this).css('display', isActive ? '' : 'none');
            });
        });

        $('#showNotesTodo').on('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            $(this).toggleClass('btn-outline-primary btn-outline-secondary');
            const isActive = $(this).hasClass('btn-outline-primary');
            console.log('showNotesTodo clicked:', isActive);
            const rows = $('.notesTodoRow');
            rows.each(function() {
                console.log('Changing display for notesTodoRow:', this);
                $(this).css('display', isActive ? '' : 'none');
            });
        });
        $('#showNotesfrozen').on('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            $(this).toggleClass('btn-outline-primary btn-outline-secondary');
            const isActive = $(this).hasClass('btn-outline-primary');
            const rows = $('.notesFrozenRow');
            rows.each(function() {
                console.log('Changing display for notesFrozenRow:', this);
                $(this).css('display', isActive ? '' : 'none');
            });
        });
    });
</script>
<style>
#callnote-table_wrapper .dt-scroll {
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
}
#callnote-table_wrapper .dt-scroll-body {
    max-height: 350px;
}
#callnote-table_wrapper .dt-scroll-body td{
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}
#callnote-table_wrapper .dt-scroll-head th{
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}
</style>