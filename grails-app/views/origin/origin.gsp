<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="origin.title"/> | ${origin?.name}</title>
    <asset:javascript src="lib/tagsinput.js"/>
    <asset:stylesheet src="lib/tagsinput.css"/>
    <asset:javascript src="lib/jscolor/jscolor.js"/>
</head>

<body>
<style>
.disabledDiv input, .disabledDiv [data-role="remove"] {
    display: none;
}

.disabledDiv .bootstrap-tagsinput {
    min-height: 39px;
    cursor: default;
    background-color: #efefef;
}
</style>

<div class="m-4 d-flex">
    <g:if test="${origin}">
        <h1><g:message code="origin.edit"/> (${origin.id})</h1>
    </g:if>
    <g:else>
        <h1><g:message code="origin.create"/></h1>
    </g:else>
    <div class="d-flex flex-row-reverse ms-auto">
            <button onclick="saveOrigin();" class="btn btn-primary">
                <g:message code="client.save"/>
            </button>

        <g:if test="${origin}">
                <button class="btn btn-outline-primary me-2" onclick="deleteOrigin();">
                    <g:message code="default.button.delete.label"/>
                </button>
        </g:if>
    </div>
</div>

<div class="m-4">
    <div class="mb-3">
        <label><g:message code="origin.name"/></label>
        <input class="form-control h-39" value="${origin?.name}" id="name" placeholder="<g:message code="origin.name"/>"/>
        <input type="hidden" value="${origin?.id}" id="id"/>
    </div>

    <div class="mb-3">
        <label><g:message code="origin.receptionEmail"/> (<g:message code="origin.receptionEmail.hint"/>)</label>
        <input class="form-control h-39" value="${origin?.receptionEmail}" id="receptionEmail"
               placeholder="<g:message code="origin.receptionEmail"/>"/>
    </div>

    <div class="row">

        <div class="col-md-6 mb-3">
            <label><g:message code="icon.color"/></label>
            <input class="form-control jscolor h-39" value="${origin?.color}" id="color"/>
        </div>

        <div class="col-md-6 mb-3">
            <label><g:message code="config.emailParser"/></label>
            <select class="form-select h-39" id="emailParser">
                <option value="none"><g:message code="none"/></option>
                <g:each var="emailParser" in="${emailParsers}">
                    <option value="${emailParser.id}" ${origin?.emailParser?.id == emailParser.id ? "selected" : ""}>${emailParser.name}</option>
                </g:each>
            </select>
        </div>
        <g:each var="type" in="${types}">
            <div class="col-md-6 mb-3">
                <label><g:message code="${type.message}"/></label>
                <input class="form-control h-39 source rounded-1" id="source-${type.id}" data-id="${type.id}" type="text"
                       value="${origin?.getSourcesValues(type)?.join(",")}"/>
            </div>
        </g:each>
        <div class="col-md-6 mb-3 disabledDiv">
            <label><g:message code="form.menu.profile"/></label>
            <input disabled class="form-control h-39 init-tagsinput rounded-1" type="text"
                   value="${origin?.formProfiles?.name?.join(",")}"/>
        </div>

        <div class="col-md-6 mb-3 disabledDiv">
            <label><g:message code="departments"/></label>
            <input disabled class="form-control h-39 init-tagsinput rounded-1" type="text"
                   value="${origin?.departments?.name?.join(",")}"/>
        </div>

        <div class="col-md-6 mb-3 disabledDiv">
            <label><g:message code="chatwidgetgroup.list"/></label>
            <input disabled class="form-control h-39 init-tagsinput rounded-1" type="text"
                   value="${origin?.getChatWidgetGroupNames()?.join(",")}"/>
        </div>
    </div>
    <hr>
    <div class="mb-3">
        <label><g:message code="origin.timer"/></label>
        <input class="form-control h-39" id="timer" type="number" min="30" value="${origin?.timer}"/>
        <small><g:message code="origin.timer.info"/></small>
    </div>

    <div class="mb-3">
        <label><g:message code="origin.timerFromTo"/></label>
        <table class="table w-100 border" id="originInfoTable">
            <thead>
            <tr class="text-center">
                <th><g:message code="config.sunday"/></th>
                <th><g:message code="config.monday"/></th>
                <th><g:message code="config.tuesday"/></th>
                <th><g:message code="config.wednesday"/></th>
                <th><g:message code="config.thursday"/></th>
                <th><g:message code="config.friday"/></th>
                <th><g:message code="config.saturday"/></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToSunday" type="text"
                           value="${timerJson.timerFromToSunday ? timerJson.timerFromToSunday.start + ' - ' + timerJson.timerFromToSunday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToMonday" type="text"
                           value="${timerJson.timerFromToMonday ? timerJson.timerFromToMonday.start + ' - ' + timerJson.timerFromToMonday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToTuesday" type="text"
                           value="${timerJson.timerFromToTuesday ? timerJson.timerFromToTuesday.start + ' - ' + timerJson.timerFromToTuesday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToWednesday" type="text"
                           value="${timerJson.timerFromToWednesday ? timerJson.timerFromToWednesday.start + ' - ' + timerJson.timerFromToWednesday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToThursday" type="text"
                           value="${timerJson.timerFromToThursday ? timerJson.timerFromToThursday.start + ' - ' + timerJson.timerFromToThursday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToFriday" type="text"
                           value="${timerJson.timerFromToFriday ? timerJson.timerFromToFriday.start + ' - ' + timerJson.timerFromToFriday.end : '00:00 - 23:59'}"/>
                </td>
                <td>
                    <input class="form-control timerFromTo" id="timerFromToSaturday" type="text"
                           value="${timerJson.timerFromToSaturday ? timerJson.timerFromToSaturday.start + ' - ' + timerJson.timerFromToSaturday.end : '00:00 - 23:59'}"/>
                </td>
            </tr>
            </tbody>
        </table>
        <script>
            TractionDataTable.init('#originInfoTable', {
                id: 'originInfoTable',
                enableState: true,
                dataTableOptions: {
                    serverSide: false,
                    buttons: [],
                    searching: false,
                    paging: false
                }
            });
        </script>
    </div>
</div>
<script>
    $(document).ready(function () {

        $('.timerFromTo').daterangepicker({
            timePicker: true,
            timePicker24Hour: true,
            timePickerIncrement: 1,
            timePickerSeconds: true,
            locale: DateRangePickerUtils.LOCALE_ONLY_TIME
        }).on('show.daterangepicker', function (ev, picker) {
            picker.container.find(".calendar-table").hide();
        });
        $('.init-tagsinput').tagsinput({
            trimValue: true
        });
        // DEALERS
        $('#source-2').tagsinput({
            trimValue: true,
            onTagExists: null,
        });

        // EMAILS
        $('#source-1').tagsinput({
            trimValue: true,
            onTagExists: null,
        });
        $('#source-1').on('beforeItemAdd', function (event) {
            if (!/(.+)@(.+){2,}\.(.+){2,}/.test(event.item)) {
                $.notify("<g:message code="source.invalid.email"/>", 'error');
                event.cancel = true;
            }
        });

        // PHONES
        $('#source-0').tagsinput({
            trimValue: true,
            onTagExists: null,
        });

        // TEXT
        $('#source-3').tagsinput({
            trimValue: true,
            onTagExists: null,
        });
    });

    function deleteOrigin() {
        confirmDelete('<g:message code="origin.delete.confirm"/>', function () {
            location.href = tractionWebRoot + '/origin/delete?id=' + $('#id').val();
        });
    }

    function saveOrigin() {
        var name = $('#name').val();
        if (name) {
            var jsonTimer = {}
            $('.timerFromTo').each(function () {
                let data = $(this).data('daterangepicker');
                jsonTimer[$(this).attr('id')] = {
                    start: data.startDate.format('HH:mm'),
                    end: data.endDate.format('HH:mm')
                }
            })
            var json = {
                id: $('#id').val(),
                name: name,
                receptionEmail: $('#receptionEmail').val(),
                color: $('#color').val(),
                emailParser: $('#emailParser').val(),
                timer: $('#timer').val(),
                timerFromTo: jsonTimer
            };
            $('.source').each(function () {
                var arr = $(this).tagsinput('items');
                var id = $(this).data('id');
                json['type-' + id] = arr;
            });
            console.log(json);
            $.post({
                url: tractionWebRoot + '/origin/save',
                data: JSON.stringify(json),
                contentType: "application/json",
                success: function (data) {
                    if (data.success) {
                        location.href = tractionWebRoot + '/origin/index';
                    } else {
                        $.notify(data.message, 'error');
                    }
                }
            });
        } else {
            $('#name').focus();
        }
    }
</script>
</body>
</html>