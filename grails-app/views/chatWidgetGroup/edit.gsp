<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message code="chatwidgetgroup.${chatWidgetGroup ? 'edit' : 'create'}"/></title>
</head>

<body>
<div class="m-4">
    <div class="d-flex">
        <h2 class="mb-3 me-auto">
            <g:message code="chatwidgetgroup.${chatWidgetGroup ? 'edit' : 'create'}"/>
        </h2>
        <div class="mb-3 clearfix d-flex">
            <button class="btn btn-primary" onclick="saveChatWidgetGroup()">
                <g:message code="save"/>
            </button>
            <g:if test="${chatWidgetGroup && !chatWidgetGroup.isDefault()}">
                <button class="btn btn-outline-primary ms-2" onclick="deleteChatWidgetGroup()">
                    <g:message code="default.button.delete.label"/>
                </button>
            </g:if>
        </div>
    </div>

    <div class="mb-3">
        <label><g:message code="chatwidgetgroup.name"/></label>
        <input class="form-control" id="name" value="${chatWidgetGroup?.name}"
               <g:if test="${chatWidgetGroup?.isDefault()}">disabled</g:if>/>
    </div>

    <div class="mb-3">
        <label><g:message code="chatwidgetgroup.origin"/></label>
        <select class="form-control" id="origin" <g:if test="${chatWidgetGroup?.isDefault()}">disabled</g:if>>
            <option value="0">----</option>
            <g:each var="origin" in="${origins}">
                <option value="${origin.id}" ${chatWidgetGroup?.originId == origin.id ? 'selected' : ''}>
                    ${origin.name}
                </option>
            </g:each>
        </select>
    </div>

    <div class="mb-3 form-check">
        <label class="form-check-label">
            <input id="createPotentialOpportunity" class="form-check-input"
                   type="checkbox" ${chatWidgetGroup?.createPotentialOpportunity ? 'checked' : ''}
                   <g:if test="${chatWidgetGroup?.isDefault()}">disabled</g:if>>
            <g:message code="chatwidgetgroup.createPotentialOpportunity"/>
        </label>
    </div>

    <div class="mb-3">
        <label><g:message code="chatwidgetgroup.users"/></label>
        <container:userSelect2 id="users" selectedUsers="${chatWidgetGroup?.users}"></container:userSelect2>
    </div>

    <div class="mb-3 <g:if test="${chatWidgetGroup?.isDefault()}">d-none</g:if>">
        <label><g:message code="chatwidgetgroup.displayRules"/></label>
        <chatTagLib:chatQueryBuilder id="displayRules" rules="${chatWidgetGroup?.displayRules}"/>
    </div>
    <script>
        function deleteChatWidgetGroup() {
            confirmDelete("<g:message code="chatwidgetgroup.confirm.delete"/>",function() {
                $.post({
                    url: tractionWebRoot + '/chatWidgetGroup/delete',
                    data: {
                        id: '${chatWidgetGroup?.id}'
                    },
                    complete: hideBigLoader,
                    success: function (data) {
                        if (data.success) {
                            location.href = tractionWebRoot + '/chatWidgetGroup/index';
                        } else {
                            $.notify(data.message, 'error');
                        }
                    }
                });
            })
        }

        function saveChatWidgetGroup() {
            if (!displayRules.getRules()) return;
            $.post({
                url: tractionWebRoot + '/chatWidgetGroup/save',
                data: {
                    id: '${chatWidgetGroup?.id}',
                    name: $('#name').val(),
                    createPotentialOpportunity: $('#createPotentialOpportunity').prop('checked'),
                    users: $('#users').val(),
                    origin: $('#origin').val(),
                    displayRules: JSON.stringify(window.displayRules.getRules())
                },
                complete: hideBigLoader,
                success: function (data) {
                    if (data.success) {
                        location.href = tractionWebRoot + '/chatWidgetGroup/index';
                    } else {
                        $.notify(data.message, 'error');
                    }
                }
            });
        }
    </script>
</div>
</body>
</html>