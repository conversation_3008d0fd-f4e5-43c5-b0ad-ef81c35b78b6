<div class="modal-dialog modal-lg">
    <form id="createOpportunityForm">
        <input type="hidden" name="clientId" value="${client.id}">

        <div class="modal-content" style="max-height: 800px !important;">
            <div class="modal-header border-0 pb-5">
                <h2 class="modal-title fw-bold" id="createOpportunityModalLabel">
                    <g:message code="create.opportunity"/>
                </h2>
                <button type="button" id="modaldismiss" class="btn-close opacity-100" data-bs-dismiss="modal"
                        aria-label="Close" onclick="TractionModal.hide()">
                </button>
            </div>

            <div class="modal-body">
                <div class="row align-items-center">
                    <div class="mb-4">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input cursor-pointer" type="radio" name="flexRadioDefault"
                                   id="flexRadioDefault1">
                            <label class="form-check-label cursor-pointer" for="flexRadioDefault1">
                                <g:message code="opportunity.client.create.no-vehicle"/>
                            </label>
                        </div>

                        <div class="form-check form-check-inline">
                            <input class="form-check-input cursor-pointer" type="radio" name="flexRadioDefault"
                                   id="createFromVehicle" checked>
                            <label class="form-check-label cursor-pointer text-info-secondary-emphasis"
                                   for="createFromVehicle">
                                <g:message code="opportunity.client.create.vehicle"/>
                            </label>
                        </div>
                    </div>

                    <div id="opp-name-div" class="col-12 d-none">
                        <div class="col">
                            <div class="input-group">
                                <input id="oppNameInput" type="text"
                                       class="form-control"
                                       placeholder="${g.message(code: 'opportunity.client.input')}">
                            </div>
                        </div>


                        <div class="d-flex justify-content-end mt-3">
                            <button type="button" class="btn btn-primary rounded-pill fw-bold"
                                    id="createOpportunityButton" disabled>
                                <g:message code="opportunity.create"/>
                            </button>
                        </div>
                    </div>

                    <div id="opp-vehicle-div">
                        <search:vehicleInit/>
                        <search:vehicle type="text" callback="createOpportunity" placeholder="${g.message(code: 'vehicle.search')}"/>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


<script>
    $(document).ready(function () {
        let selectedVehicle = null

        // Gestion des changements des boutons radio
        $('#createFromVehicle, #flexRadioDefault1').on('change', function () {

            if ($('#createFromVehicle').is(':checked')) {
                // Affiche le tableau des vehicles, cache la recherche
                $('#opp-vehicle-div').removeClass('d-none');
                $('#opp-name-div').addClass('d-none');
                $("#flexRadioDefault1 + .form-check-label, #createFromVehicle + .form-check-label").toggleClass("text-info-secondary-emphasis");
            } else if ($('#flexRadioDefault1').is(':checked')) {
                // Cache le tableau des vehicles, affiche la recherche
                $('#opp-vehicle-div').addClass('d-none');
                $('#opp-name-div').removeClass('d-none');
                $("#flexRadioDefault1 + .form-check-label, #createFromVehicle + .form-check-label").toggleClass("text-info-secondary-emphasis");
            }
        });

        $("#oppNameInput").on("keyup", function () {
            if ($(this).val() != "") {
                $("#createOpportunityButton").attr("disabled", false);
            } else {
                $("#createOpportunityButton").attr("disabled", true);
            }
        })

        // Bouton de création d'opportunité
        $('#createOpportunityButton').on('click', function (e) {
            e.preventDefault();  //Pour éviter le refresh de la page
            createOpportunity(selectedVehicle);
        });
    });

    // Fonction appelée lorsque l'on clique sur une ligne de la datatable
    function onVehicleRowClick(vehicle) {
        selectedVehicle = vehicle; // Mise à jour du véhicule sélectionné
    }

    function createOpportunity(vehicle) {

        var vehicleId = vehicle ? vehicle.id : null;

        var oppName = vehicleId ? "" : ($('#oppNameInput').val() || '').trim();
        var returnMethod = '${returnMethod}'
        if (vehicleId || oppName) {
            showBigLoader();
            $.post({
                url: tractionWebRoot + "/opportunity/create",
                data: {
                    'clientId': '${client.id}',
                    'cotationId': '${cotationId}',
                    'vehicleId': vehicleId,
                    'opportunity_name': oppName
                },
                success: function (data) {
                    if (data.success) {
                        if (returnMethod === 'clientCotationCreate') {
                            TractionModal.show({
                                url: '/client/modalClientCotationCreate',
                                data: {
                                    clientId: '${client.id}',
                                    selectedOppId: data.id
                                },
                                bigLoader: true,
                            });
                        } else {
                            $.notify(data.message, 'success');
                            window.location.href = data.url;
                        }
                    } else {
                        $.notify(data.message, 'error');
                    }
                },
                error: function (xhr, status, error) {
                    $.notify(error, 'error');
                    hideBigLoader();
                }
            });
        } else {
            $.notify("<g:message code='opportunity.error.name'/>", "error");
        }
    }
</script>