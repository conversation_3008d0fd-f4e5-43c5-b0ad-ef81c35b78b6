<style>
.select2 {
    width: auto !important;
}
</style>
<ul class="gap-1 p-0">
    <input type="hidden" value="${opportunity.id}" id="opportunityAssignId"/>
    <div class="d-flex flex-row fw-bold mb-3">
        <div class="w-25">
            <g:message code="product.department"/>
        </div>
        <div class="ms-2 ps-1 w-75">
            <g:message code="opportunity.assign.user"/>
        </div>
    </div>

    <g:each var="assignation" in="${assignations.sort { it.userGroup }}">
        <div class="input-group mb-3 row mx-0">

            <span class="input-group-text w-25 rounded bg-secondary-subtle" style="border-color: #CCC;"><g:message
                    code="${assignation.userGroup}"/> ${assignation.isPrimary ? '' : '2'}</span>
            <select id="dropdown-user-${assignation.userGroup}${assignation.isPrimary ? '' : '2'}" class="form-control pe-0">
                <g:if test="${assignOpportunity || assignation.userGroup != traction.UserGroup.SALES || !assignation.isPrimary}">
                    <option value="0"><g:message code="no.user"/></option>
                </g:if>
                <g:each var="user" in="${assignation.replacementUsers.sort { it.fullName }}">
                    <option value="${user.id}" ${assignation.user?.id == user.id ? "selected" : ""}>
                        ${user.fullName}
                    </option>
                </g:each>
            </select>
        </div>

        <style>
            div[id*="assignUsers"] .select2.select2-container {
                padding-right: 0px !important;
            }
            div[id*="assignUsers"] .select2.select2-container .select2-selection {
                border-color: #CCC !important;
            }
        </style>

    <script>
            $("[id='dropdown-user-${assignation.userGroup}${assignation.isPrimary ? '' : '2'}']").select2({
                theme: 'bootstrap-5',
                dropdownParent: $('#traction-modal')
            });
            $("[id='dropdown-user-${assignation.userGroup}${assignation.isPrimary ? '' : '2'}']").on('change', function () {
                opportunityAssign('${assignation.id}', ${opportunity.id}, $("[id='dropdown-user-${assignation.userGroup}${assignation.isPrimary ? '' : '2'}']").val());
            });
        </script>
    </g:each>

    <search:userInit/>
    <search:searchUser callback="assignUserSelected" type="search" class="mx-0" id="searchTaskUser" autocomplete="off"
                       placeholder="${g.message(code: "opportunity.assign.user.search")}"/>

</ul>

<script>
    function opportunityAssign( assignationId,opportunityId , userId) {
        $.post({
            url: tractionWebRoot + "/opportunity/opportunityAssign",
            data: {
                "opportunityAssignedId": assignationId,
                "opportunityId": opportunityId,
                "userId": userId,
            },
            success: function (data) {
                if (data.messageError) {
                    $.notify(data.messageError, "error");
                } else {
                    $.notify(I18N.m('form.success'), "success");
                    TractionModal.reload();
                }
            }
        });
    }
    function assignUserSelected(item) {
        opportunityAssign('', $('#opportunityAssignId').val(), item.id);

    }
</script>

