import traction.opportunity.Opportunity

model {
    List<Opportunity> opportunities = []
}

json {
    status true
    error null
    data {
        opportunities opportunities.collect {
            [
                    id: it.id,
                    name: it.name,
                    vehicle: [
                            id: it.vehicle?.id,
                            name: it.vehicle?.makeModelYear()
                    ],
                    client: [
                            id: it.client.id,
                            fullName: it.client.fullName,
                            email: it.client.email
                    ]
            ]
        }
    }
}