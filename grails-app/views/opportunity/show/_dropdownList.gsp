<%@ page import="traction.DateUtils" %>
<ul class="dropdown-menu border-primary-subtle py-0 my-1 overflow-auto"
    aria-labelledby="dropdownOpportunity"
    style="${opportunityList.size() > 3 ? 'height: 60vh' : ''}">
    <g:each var="opp" in="${opportunityList}">
        <li class="dropdown-item py-3 px-0 lh-sm" role="button"
            onclick="location.href = tractionWebRoot + '/opportunity/index/' + ${opp.id}">
            <div class="d-flex px-3">
                <p class="fw-bold me-2">${opp.toString()}</p>

                <div class="d-flex ms-auto">
                    <g:each var="assign" in="${opp.assigned}">
                        <communication:avatar user="${assign.user}"
                                              class="communication-avatar-xs-sm align-items-baseline ${opp.assigned.last() != assign ? 'me-2' : ' me-3'}"/>
                    </g:each>
                    <i class="fa-solid fa-person-walking me-2 h-fit-content text-center text-body-tertiary ${opp.compiledData.lastWalkinDate && opp.compiledData.lastWalkinDate < new Date() ? 'text-primary' : 'text-body-tertiary'}"
                       style="width: 20px;"
                       data-toggle="tooltip" title="<g:message code="opportunity.lastWalkin"/>"></i>
                    <i class="fa-regular fa-chart-simple me-2 h-fit-content position-relative text-center ${opp.excluded ? 'text-primary' : 'text-body-tertiary'}"
                       style="width: 20px;"
                       data-toggle="tooltip" title="<g:message code="opportunity.excluded"/>"><span
                            style="width: 20px;height: 1.5px;display: block;background: ${opp.excluded ? 'var(--bs-primary)' : '#B3B3B3'};position: absolute;rotate: 45deg;top: 6px;left: 0px;"></span>
                    </i>
                    <i class="fa-regular fa-box-archive h-fit-content text-center text-body-tertiary ${opp.archived ? 'text-primary' : 'text-body-tertiary'}"
                       style="width: 20px;"
                       data-toggle="tooltip" title="<g:message code="opportunity.archived"/>"></i>
                </div>
            </div>

            <div class="d-flex mb-3 px-3">
                <img src="${opp?.getVehicleFirstPictureUrl() ?: "/traction/assets/image-missing.jpg"}"
                     class="img-fluid border h-fit-content me-3" style="width: 100px;"
                     alt="No image">

                <div class="me-3 lh-1 w-50">
                    <p class="d-flex w-fit-content mb-2">
                        <label class="me-2 text-truncate"
                               style="width: 100px;"><g:message
                                code="vehicle.serialNumber.number"/></label>
                        <span style="width: 150px;"
                              class="text-truncate d-block align-content-center">${opp.vehicle?.serialNumber ?: '-'}</span>
                    </p>

                    <p class="d-flex w-fit-content mb-2">
                        <label class="me-2 text-truncate"
                               style="width: 100px;"><g:message
                                code="vehicle.stockNumber.number"/></label>
                        <span style="width: 150px;"
                              class="text-truncate d-block align-content-center">${opp.vehicle?.stockNumber ?: '-'}</span>
                    </p>

                    <p class="d-flex w-fit-content mb-2">
                        <label class="me-2 text-truncat"
                               style="width: 100px;"><g:message
                                code="managerproduct.modelCode"/></label>
                        <span style="width: 150px;"
                              class="text-truncate d-block align-content-center">${opp.vehicle?.modelCode ?: '-'}</span>
                    </p>

                    <p class="d-flex w-fit-content mb-0">
                        <label class="me-2 text-truncate"
                               style="width: 100px;"><g:message
                                code="vehicle.localisation"/></label>
                        <span style="width: 150px;"
                              class="text-truncate d-block align-content-center">${opp.vehicle?.location ?: '-'}</span>
                    </p>
                </div>

                <div class="lh-1" style="width: 32.5%;">
                    <p class="mb-2 d-flex">
                        <label class="me-2 text-truncate"
                               style="width: 50px;"><g:message
                                code="vehicle.msrp"/></label>
                        <span style="width: 150px;"
                              class="text-truncate align-content-center">${opp.vehicle?.msrp ?: '-'} $</span>
                    </p>

                    <p class="mb-2 d-flex">
                        <label class="me-2 text-truncate"
                               style="max-width: 50px; width: 50px;"><g:message
                                code="vehicle.odometer"/></label>
                        <span style="width: 150px;"
                              class="text-truncate align-content-center">${opp.vehicle?.odometer ?: '-'}</span>
                    </p>

                    <p class="mb-0 d-flex">
                        <label class="me-2 text-truncate"
                               style="max-width: 50px; width: 50px;"><g:message
                                code="vehicle.weight"/></label>
                        <span style="width: 150px;"
                              class="text-truncate align-content-center">${opp.vehicle?.weight ?: '-'}</span>
                    </p>
                </div>
            </div>

            <div class="d-flex px-3 gap-2">
                <span class="badge badge-outline-primary">
                    <g:message code="${opp?.category?.message}"/>
                </span>
                <span class="badge badge-outline-primary">
                    <g:message code="${opp?.status?.message}"/>
                </span>
                <span class="badge badge-outline-secondary-emphasis text-uppercase">
                    <g:message code="${opp?.vehicle?.status?.message}"/>
                </span>
                <span class="badge badge-outline-secondary-emphasis">
                    ${DateUtils.getReallyShortDurationString(opp?.date)}
                </span>
            </div>
        </li>
        <g:if test="${opp != opportunityList.last()}">
            <div class="mx-3 border-bottom border-info-subtle"
                 style="height: 1px;"></div>
        </g:if>
    </g:each>
</ul>