<%@ page import="traction.opportunity.Opportunity" %>
<asset:javascript src="client/clientServiceMultipleStepForm.js" />
<div class="modal-dialog modal-xl modal-dialog-scrollable">
    <form id="createOpportunityForm">
        <input type="hidden" name="clientId" value="${client.id}">

        <div class="modal-content" style="max-height: 800px !important;">
            <div class="modal-header border-0 pb-5">
                <h2 class="modal-title fw-bold" id="createOpportunityModalLabel">
                    <g:message code="create.opportunity"/>
                </h2>
                <button type="button" id="modaldismiss" class="btn-close opacity-100" data-bs-dismiss="modal"
                        aria-label="Close">
                </button>
            </div>

            <div class="modal-body overflow-y-scroll">
                <div id="form-multiple-step-containers">
                    <div id="view-step-1">
                        <div class="d-flex flex-row fw-bold mb-3 f-16 line-height-19">
                            <span>1. <g:message code="select.trade"/></span>
                        </div>
                        <g:each var="vehicle" in="${client.vehicles}">
                            <vehicleTagLib:tradeCard vehicle="${vehicle}" selectable="true"/>
                        </g:each>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="d-flex flex-row-reverse m-0">
                    <button id="submit-btn" class="btn btn-primary rounded-5 fw-bold"><g:message
                            code="create"/></button>
                    <button id="next-step-btn" class="btn btn-primary rounded-5 d-flex fw-bold"><g:message
                            code="next.step"/><i class="mdi mdi-chevron-right ms-1 lh-sm f-20"></i></button>
                </div>
            </div>
        </div>
    </form>
</div>


<script>
    $(document).ready(function () {
        window.serviceMultiStepForm = new ServiceMultipleStepForm(${client.id});

        $('.vehicle-selection').on('change', function () {
            if ($(this).is(':checked')) {
                $('.vehicle-selection:not([value="' + $(this).val() + '"])').prop('checked', false);
                window.serviceMultiStepForm.formData.vehicleId = $(this).val();
                window.serviceMultiStepForm.handleCheckedStep();
            } else {
                console.log('Checkbox with ID ' + $(this).attr('id') + ' is unchecked');
                window.serviceMultiStepForm.formData.vehicleId = null;
                window.serviceMultiStepForm.handleCheckedStep();
            }
        });

        // Bouton pour l'étape suivante
        $('#next-step-btn').on('click', () => {
            window.serviceMultiStepForm.nextStep();
        });

        $('#submit-btn').on('click', () => window.serviceMultiStepForm.submit());
    });
</script>