<tr class="${disabled ? "bg-body-secondary" : ""}">
    <td>
        <g:message code="usernotificationsettings.${name}"/>
    </td>
    <td>
        <div class="form-check form-switch f-18">
            <input onchange="NotificationMenu.updateSetting(this);"
                   class="form-check-input cursor"
                   type="checkbox"
                   name="web.${name}"
                ${userSettings.webNotificationSettings.getProperty(name) ? 'checked' : ''}
                ${disabled ? 'disabled': ''}
            />
        </div>
    </td>
    <td>
        <div class="form-check form-switch f-18">
            <input onchange="NotificationMenu.updateSetting(this);"
                   class="form-check-input cursor"
                   type="checkbox" name="mobile.${name}"
                ${userSettings.mobileNotificationSettings.getProperty(name) ? 'checked' : ''}
                ${disabled ? 'disabled': ''}
            />
        </div>
    </td>
</tr>