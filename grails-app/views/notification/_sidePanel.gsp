<div id="notifications-menu" class="modal-dialog modal-dialog-scrollable" style="min-width: 594px;">
    <div id="notifications-menu-content" class="modal-content">
        <div class="modal-header p-0 mx-4 mt-4 mb-0 mb-5 d-flex justify-content-between align-items-center" style="height: 38px;">
            <h2 class="text-uppercase mb-0">
                <g:message code="my.notif"/>
            </h2>
            <button type="button" class="f-32" data-bs-dismiss="modal" aria-label="Close" style="border: none; background: none; color: #4DAAF5">
                <i class="fa-solid fa-xmark"></i>
            </button>
        </div>
        <div class="modal-header px-4 py-0 mb-3">
            <div class="d-flex align-items-center justify-content-between w-100" style="gap: 8px;">
                <div class="dropdown">
                    <a class="dropdown-toggle f-16 gap-2 d-flex align-items-center" style="padding: 10px 12px; border: 1px solid #CCCCCC; border-radius: 4px; color:black; width: 151px; display: inline-block;" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fa-regular fa-filter f-16" style="width: 20px; color: #5F7991; text-align: center;"></i>
                        <span id="notification-menu-filter-title" class="f-16" style="line-height:17px; width: 80px; text-align: left;"></span>
                        <i class="fa-regular fa-chevron-down f-12 chevron-icon" style="margin-left: auto;"></i>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                        <li>
                            <span class="dropdown-item" onclick="NotificationMenu.setFilter('read')">
                                <g:message code="notification.filter.read"/>
                            </span>
                        </li>
                        <li>
                            <span class="dropdown-item" onclick="NotificationMenu.setFilter('unread')">
                                <g:message code="notification.filter.unread"/>
                            </span>
                        </li>
                        <li>
                            <span class="dropdown-item" onclick="NotificationMenu.setFilter('important')">
                                <g:message code="notification.filter.important"/>
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="d-flex align-items-center ms-auto" style="gap: 8px;">
                    <a class="btn btn-outline-primary f-16 fw-bold" style="line-height:17px; box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15); padding:10px 11.5px; border: 1px solid var(--bs-primary); border-radius: 4px;" onclick="NotificationMenu.readAll()">
                        <g:message code="notification.set.all.read"/>
                    </a>
                    <button type="button" class="btn btn-primary f-20 d-flex justify-content-center align-items-center p-0 line-height-19" style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15); width:39px; height:39px; " onclick="NotificationMenu.openSettings();">
                        <i class="fa-solid fa-cog"></i>
                    </button>
                </div>
            </div>
        </div>

        <div id="notification-list" class="modal-body p-0">
        </div>
        <div class="modal-footer">
            <button id="notification-load-more" class="btn btn-link btn-sm w-100 text-center text-primary" onclick="NotificationMenu.loadMore()" style="box-shadow: none;">
                <g:message code="notifications.load.more"/>
            </button>
        </div>
    </div>
    <div id="notifications-menu-settings" class="h-100"></div>
</div>

<script>
    $(function () {
        NotificationMenu.init();
    });
    $(document).ready(function () {
        $('#dropdownMenuLink').on('click', function (event) {
            event.stopPropagation();
            var chevronIcon = $(this).find('.chevron-icon');
            if (chevronIcon.hasClass('fa-chevron-down')) {
                chevronIcon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                chevronIcon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        });

        $(document).on('click', function (event) {
            if (!$(event.target).closest('#dropdownMenuLink').length) {
                var chevronIcon = $('#dropdownMenuLink').find('.chevron-icon');
                if (chevronIcon.hasClass('fa-chevron-up')) {
                    chevronIcon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                }
            }
        });
    });
</script>
<style>
#dropdownMenuLink::after {
    display: none;
}
</style>