<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="m-0 fw-bold">
                <g:message code="vehicle.${propertyName}"/><br>
                <small class="fw-normal">(${vehicle})</small>
            </h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
            <g:set var="formId" value="vehicle-interests-modal-form"/>
            <form id="${formId}">
                <select id="${formId}-select" name="interests" multiple="multiple" class="form-select form-select-sm">
                    <g:each var="interest" in="${allInterests}">
                        <option value="${interest.id}" ${vehicle?.interests?.contains(interest) ? 'selected' : ''}>${interest.name}</option>
                    </g:each>
                </select>
                <script>
                    $('#${formId}-select').select2({
                        theme: 'bootstrap-5'
                    });
                </script>
                <input type="hidden" name="id" value="${vehicle.id}"/>

                <div class="row flex-fill">
                    <div class="col align-self-end text-end">
                        <div class="d-flex justify-content-end pt-3">
                            <button type="button" class="btn btn-sm btn-outline-secondary w-auto me-2" data-bs-dismiss="modal"
                                    onclick="TractionModal.hide()"><g:message code="cancel"/></button>
                            <button type="button" class="btn btn-sm btn-primary w-auto"
                                    onclick="vehicleUpdateClientInterestsValue()"><g:message code="save"/></button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    function vehicleUpdateClientInterestsValue() {
        let form = $('#${formId}');
        $.post({
            url: tractionWebRoot + '/vehicle/save',
            data: form.serialize(),
            success: function (res) {
                console.log('response:', res);
                if (res.hasErrors) {
                    console.log('errors:', res.hasErrors);
                    form.find('.invalid-feedback').remove();
                    form.find('.is-invalid').removeClass('is-invalid');
                    res.errors.errors.forEach(function (error) {
                        form.find('[name="' + error.field + '"]')
                            .addClass('is-invalid')
                            .after('<div class="invalid-feedback">' + error.message + '</div>');
                    });
                    form.find('.is-invalid:first').focus();
                } else {
                    TractionModal.hide();
                }
            }
        })
    }
</script>