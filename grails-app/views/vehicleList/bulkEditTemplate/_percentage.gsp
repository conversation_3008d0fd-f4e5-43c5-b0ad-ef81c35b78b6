<div class="d-flex flex-row">
    <div class="d-flex flex-column w-100">
        <div class="d-flex flex-row">
            <label class="${column}-text"><g:message code="vehicle.${column}"/></label>
            <g:if test="${nullable == true}">
                <div class="ms-auto">
                    <label class="${column}-text me-2">
                        <g:message code="edit.bulk.empty.value" />
                    </label>
                    <input type="checkbox" name="${column}-empty" onchange="handleEmptyCheck(this, '${column}')">
                </div>
            </g:if>
        </div>
        <input type="text" class="form-control ${column}-input" name="${column}" oninput="handlePercentageInput(this)" placeholder="0.0 - 100.0 %">
    </div>
</div>

<script>
    function handlePercentageInput(input){
        input.value = input.value.replace(/[^0-9.,%]/g, '');
        let matches = input.value.match(/[.,]/g);
        if (matches && matches.length > 1) {
            input.value = input.value.slice(0, -1);
        }
        let numericValue = parseFloat(input.value.replace(/,/g, '.').replace('%', ''));
        if (!isNaN(numericValue) && (numericValue < 0 || numericValue > 100)) {
            input.value = '0';
        }
    }
</script>
