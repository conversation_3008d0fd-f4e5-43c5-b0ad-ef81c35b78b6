<div class="d-flex flex-row">
    <div class="d-flex flex-column w-100">
        <div class="d-flex flex-row">
            <label class="${column}-text"><g:message code="vehicle.${column}"/></label>
            <g:if test="${nullable == true}">
                <div class="ms-auto">
                    <label class="${column}-text me-2">
                        <g:message code="edit.bulk.empty.value" />
                    </label>
                    <input type="checkbox" name="${column}-empty" onchange="handleEmptyCheck(this, '${column}')">
                </div>
            </g:if>
        </div>
        <div class="${column}-input">
            <select class="form-control form-control-sm select2-${column} " multiple="multiple" name="${column}"></select>
        </div>

    </div>
</div>

<script>
    if(window.vehicledatatable.options['${column}']) {
        $(".select2-${column}").html(getSelectOptionsHtml(window.vehicledatatable.options['${column}']));
        $(".select2-${column}").select2({
            theme: 'bootstrap-5'
        })
    }

    function getSelectOptionsHtml(options) {
        console.log("options: ", options)

        return $.map(sortByLabel(options), function (option) {
            console.log("option: ", option)
            let datasHtml = option.datas ? $.map(option.datas, function (key, value) {
                return 'data-' + key + '="' + value + '"';
            }) : '';
            if (option.children) {
                var childrenHTML = $.map(filter.sort ? sortByLabel(option.children) : option.children, function (children) {
                    return getSelectOptionHtml(children, datasHtml);
                }).join('\n');
                return `<optgroup label="` + option.label + `">` + childrenHTML + `</optgroup>`
            }
            return getSelectOptionHtml(option, datasHtml);
        }).join('\n');
    }

    function getSelectOptionHtml(option, datasHtml) {
        console.log("2 option: ", option)
        console.log("2 datasHtml: ", datasHtml)

        return `<option value="` + option.value + `" data-content="` + option.label + `">` + option.label + `</option>`;
    }

    function sortByLabel(list) {
        return list.sort(function (a, b) {
            let textA = a.label.toUpperCase();
            let textB = b.label.toUpperCase();
            return (textA < textB) ? -1 : (textA > textB) ? 1 : 0;
        });
    }
</script>