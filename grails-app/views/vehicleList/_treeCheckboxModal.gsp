<div class="modal-dialog modal-dialog-scrollable" style="height: 80vh;overflow: auto;">
    <div class="modal-content">
        <div class="modal-header pb-5 position-fixed bg-body" style="width: 492px;z-index: 1;">
            <h2 class="m-0 modal-title lh-1">
                <g:message code="vehicle.${propertyName}"/><br>
                <small class="fw-normal">(${vehicle})</small>
            </h2>
            <button type="button" class="btn-close opacity-100" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body" style="margin-top: 96px;">
            <h5 class="text-uppercase"><g:message code="vehicle.magentoData_category.select"/></h5>
            <vehicleTagLib:treeCheckbox opened="true" id="${vehicle.id}" value="${vehicle.getNestedProperty(propertyName)}" propertyName="${propertyName}" onchange="vehicleUpdateTreeCheckboxValue"/>
        </div>
    </div>
</div>
<script>
    function vehicleUpdateTreeCheckboxValue(id, values) {
        $.post({
            url: tractionWebRoot + '/vehicleList/updateTreeCheckboxValue',
            data: {
                id: id,
                values: values,
                propertyName: '${propertyName}'
            },
            success: function (data) {
                if (data.success) {
                    //$.notify(data.message, 'success');
                    TractionModal.reload();
                } else {
                    $.notify(data.message, 'error');
                }
            }
        });
    }
</script>