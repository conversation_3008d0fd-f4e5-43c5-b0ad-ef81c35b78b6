<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold" id="title"><g:message code="productmanager.removefrommagento"/></h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" id="modaldismiss" aria-label="Close">

            </button>
        </div>
        <script>
            function occurrences(string, subString, allowOverlapping) {
                string += "";
                subString += "";
                if (subString.length <= 0) return (string.length + 1);

                var n = 0,
                    pos = 0,
                    step = allowOverlapping ? 1 : subString.length;

                while (true) {
                    pos = string.indexOf(subString, pos);
                    if (pos >= 0) {
                        ++n;
                        pos += step;
                    } else break;
                }
                return n;
            }

            var currentid;

            function magentoloop2(currentid, idsplit, method, removelist) {
                $.ajax({
                    url: tractionWebRoot + '/vehicleList/datatableremovefrommagentogroupupdate',
                    type: "POST",
                    data: {
                        "idlist": idsplit[0],
                        "currentid": currentid,
                        "method": "",
                        "type": method
                    },
                    success: function (data) {
                        if (data) {
                            if (data.statut) {
                                var vartype = 0;
                                if (data.statut === "Product found") {
                                    vartype = 1;
                                }
                                if (data.statut === "No method found") {
                                    vartype = 2;
                                }
                                if (data.statut === "No product found") {
                                    vartype = 2;
                                }
                                if (data.statut === "Access Denied") {
                                    vartype = 2;
                                }
                                if (data.statut === "REMOVE MAGENTO NOK") {
                                    vartype = 3;
                                }
                                if (data.statut === "REMOVE MAGENTO DONE") {
                                    vartype = 4;
                                }
                                if (vartype === 1) {
                                    var label = document.createElement("DIV");
                                    label.innerHTML = "<label>Removing id:" + data.curid + " " + data.product + "..." + "</label>";
                                    label.id = "removemagento_" + data.curid;
                                    label.name = "removemagento_" + data.curid;
                                    label.setAttribute("style", "font-weight:bold;");
                                    label.setAttribute("class", "");
                                    removelist.appendChild(label);
                                    magentoloop2(currentid, idsplit, "REMOVE MAGENTO", removelist);
                                }
                                if (vartype === 2) {
                                    var label = document.createElement("DIV");
                                    label.innerHTML = "<label>Cancelling id:" + data.curid + "=>" + data.statut + "</label>";
                                    label.id = "removemagento_" + data.curid;
                                    label.name = "removemagento_" + data.curid;
                                    label.setAttribute("style", "font-weight:bold;");
                                    label.setAttribute("class", "");
                                    removelist.appendChild(label);
                                    currentid = currentid + 1;
                                    var count = occurrences(idsplit, ",", false) + 1
                                    if (currentid < count) {
                                        magentoloop2(currentid, idsplit, "GETPRODUCT", removelist);
                                    } else {
                                        var label = document.createElement("DIV");
                                        label.innerHTML = "<label>REMOVE FINISH!" + "</label>";
                                        label.id = "removemagento_finish_" + data.curid;
                                        label.name = "removemagento_finish_" + data.curid;
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        removelist.appendChild(label);
                                    }
                                }
                                if (vartype === 3) {
                                    var label = document.getElementById("removemagento_" + data.curid);
                                    label.innerHTML = label.innerHTML + '<i class="mdi mdi-close-box" style="color:red;"></i>NOK ! ' + data.message;
                                    label.setAttribute("style", "font-weight:bold;");
                                    label.setAttribute("class", "");
                                    currentid = currentid + 1;
                                    var count = occurrences(idsplit, ",", false) + 1
                                    if (currentid < count) {
                                        magentoloop2(currentid, idsplit, "GETPRODUCT", removelist);
                                    } else {
                                        var label = document.createElement("DIV");
                                        label.innerHTML = "<label>REMOVE FINISH!" + "</label>";
                                        label.id = "removemagento_finish_" + data.curid;
                                        label.name = "removemagento_finish_" + data.curid;
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        removelist.appendChild(label);
                                    }
                                }
                                if (vartype === 4) {
                                    var label1 = document.getElementById("removemagento_" + data.curid);
                                    label1.innerHTML = label1.innerHTML + '<i class="mdi mdi-checkbox-marked" style="color:green;"></i>OK !';
                                    label1.setAttribute("style", "font-weight:bold;");
                                    label1.setAttribute("class", "");

                                    currentid = currentid + 1;
                                    var count = occurrences(idsplit, ",", false) + 1
                                    if (currentid < count) {
                                        magentoloop2(currentid, idsplit, "GETPRODUCT", removelist);
                                    } else {
                                        var label = document.createElement("DIV");
                                        label.innerHTML = "<label>REMOVE FINISH!" + "</label>";
                                        label.id = "removemagento_finish_" + data.curid;
                                        label.name = "removemagento_finish_" + data.curid;
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        removelist.appendChild(label);
                                    }
                                }
                            }
                        }
                    }
                });
            }

            function datatableremovefrommagentogroupupdate() {
                var removelist = document.getElementById("progressremove");
                while (removelist.firstChild) {
                    removelist.removeChild(removelist.firstChild);
                }

                var idsplit = [${idlist}];
                currentid = 0;
                magentoloop2(currentid, idsplit, "GETPRODUCT", removelist);
            }

        </script>

        <p><g:message code="productmanager.verifyremovemagento" args="${idlist.size()}"/></p>
        <div id="progressremove" name="progressremove">
        </div>

        <div class="d-flex flex-row-reverse">
            <g:if test="${idlist.size() > 0}">
                <div class="p-2">
                    <button type="button" class="btn btn-danger pull-right"
                            onclick="datatableremovefrommagentogroupupdate();"><g:message
                            code="productmanager.removefrommagento"/></button>
                </div>
            </g:if>
        </div>
    </div>
</div>