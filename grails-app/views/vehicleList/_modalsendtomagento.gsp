<div class="modal-dialog modal-dialog-scrollable modal-lg">
    <div class="modal-content" style="overflow-y: visible;">
        <div class="modal-header">
            <h2 class="modal-title fw-bold" id="title"><g:message code="productmanager.sendtomagento"/></h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" id="modaldismiss" aria-label="Close">

            </button>
        </div>
        <div class="modal-body">
            <script>
                var currentid;

                function magentoloop(currentid, idsplit, method, exportlist) {
                    $.ajax({
                        url: tractionWebRoot + '/vehicleList/datatablesendtomagentogroupupdate',
                        type: "POST",
                        data: {
                            "idlist": idsplit[currentid],
                            "method": "",
                            "type": method
                        },
                        success: function (data) {
                            if (data) {
                                if (data.statut) {
                                    var vartype = 0
                                    if (data.statut === "Product found") {
                                        vartype = 1
                                    }
                                    if (data.statut === "No method found") {
                                        vartype = 2
                                    }
                                    if (data.statut === "No product found") {
                                        vartype = 2
                                    }
                                    if (data.statut === "Access Denied") {
                                        vartype = 2
                                    }
                                    if (data.statut === "BUILD DONE") {
                                        vartype = 5
                                    }
                                    if (data.statut === "BUILD NOK") {
                                        vartype = 6
                                    }
                                    if (data.statut === "MAGENTO DONE") {
                                        vartype = 7
                                    }
                                    if (data.statut === "MAGENTO NOK") {
                                        vartype = 8
                                    }
                                    if (vartype === 1) {
                                        var label = document.createElement("DIV");
                                        label.innerHTML = "<label>Exporting id:" + data.idlist + " " + data.product + "..." + "</label>";
                                        label.id = "exportmagento_" + data.idlist;
                                        label.name = "exportmagento_" + data.idlist;
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        exportlist.appendChild(label);
                                        var label2 = document.createElement("DIV");
                                        label2.innerHTML = '<table class="mt-3 table table-striped table-bordered" cellspacing="0"> <thead> <tr> <th>BUILD</th><th>MAGENTO</th> </tr> </thead> <tbody> <tr> <td id="exportmagento_build_' + data.idlist + '" name="exportmagento_build_' + data.idlist + '"></td><td id="exportmagento_magento_' + data.idlist + '" name="exportmagento_magento_' + data.idlist + '"></td> </tr> </tbody> </table>';
                                        label2.setAttribute("style", "font-weight:bold;");
                                        label2.setAttribute("class", "");
                                        exportlist.appendChild(label2);
                                        magentoloop(currentid, idsplit, "BUILD", exportlist);
                                    }

                                    if (vartype === 2) {
                                        var label = document.createElement("DIV");
                                        label.innerHTML = "<label>Cancelling id:" + data.idlist + "=>" + data.statut + "</label>";
                                        label.id = "exportmagento_" + data.idlist;
                                        label.name = "exportmagento_" + data.idlist;
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        exportlist.appendChild(label);
                                        currentid = currentid + 1;
                                        if (currentid < idsplit.length) {
                                            magentoloop(currentid, idsplit, "GETPRODUCT", exportlist);
                                        } else {
                                            var label = document.createElement("DIV");
                                            label.innerHTML = "<label>EXPORT FINISH!" + "</label>";
                                            label.id = "exportmagento_finish_" + data.idlist;
                                            label.name = "exportmagento_finish_" + data.idlist;
                                            label.setAttribute("style", "font-weight:bold;");
                                            label.setAttribute("class", "");
                                            exportlist.appendChild(label);
                                        }
                                    }
                                    if (vartype === 5) {
                                        var label = document.getElementById("exportmagento_build_" + data.idlist);
                                        label.innerHTML = '<i class="mdi mdi-checkbox-marked" style="color:green;"></i>OK !';
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        magentoloop(currentid, idsplit, "MAGENTO", exportlist);
                                    }
                                    if (vartype === 6) {
                                        var label = document.getElementById("exportmagento_build_" + data.idlist);
                                        label.innerHTML = '<i class="mdi mdi-close-box" style="color:red;"></i>NOK !';
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        currentid = currentid + 1;
                                        if (currentid < idsplit.length) {
                                            magentoloop(currentid, idsplit, "GETPRODUCT", exportlist);
                                        } else {
                                            var label = document.createElement("DIV");
                                            label.innerHTML = "<label>EXPORT FINISH!" + "</label>";
                                            label.id = "exportmagento_finish_" + data.idlist;
                                            label.name = "exportmagento_finish_" + data.idlist;
                                            label.setAttribute("style", "font-weight:bold;");
                                            label.setAttribute("class", "");
                                            exportlist.appendChild(label);

                                            var label2 = document.createElement("DIV");
                                            label2.innerHTML = '<i class="mdi mdi-alert" style="color:red;"></i>' + data.message + '</label>';
                                            label2.id = "exportmagento_error_" + data.idlist;
                                            label2.name = "exportmagento_error_" + data.idlist;
                                            label2.setAttribute("style", "font-weight:bold;");
                                            label2.setAttribute("class", "");
                                            exportlist.appendChild(label2);
                                        }
                                    }
                                    if (vartype === 7) {
                                        var label = document.getElementById("exportmagento_magento_" + data.idlist);
                                        label.innerHTML = '<i class="mdi mdi-checkbox-marked" style="color:green;"></i>OK !';
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");
                                        currentid = currentid + 1;
                                        if (currentid < idsplit.length) {
                                            magentoloop(currentid, idsplit, "GETPRODUCT", exportlist);
                                        } else {
                                            var label = document.createElement("DIV");
                                            label.innerHTML = "<label>EXPORT FINISH!" + "</label>";
                                            label.id = "exportmagento_finish_" + data.idlist;
                                            label.name = "exportmagento_finish_" + data.idlist;
                                            label.setAttribute("style", "font-weight:bold;");
                                            label.setAttribute("class", "");
                                            exportlist.appendChild(label);
                                        }
                                    }
                                    if (vartype === 8) {
                                        var label = document.createElement("DIV");
                                        if (data.message.includes("Produit avec l’id ")) {
                                            if (data.message.includes("ne contient pas l’attribut obligatoire")) {
                                                if (data.message.includes("color")) {
                                                    data.message = "Couleur manquante sur un des variants !";
                                                }
                                                if (data.message.includes("mpp_size_ts")) {
                                                    data.message = "SizeMagento manquant sur un des variants !";
                                                }
                                                if (data.message.includes("mpp_gender_ld")) {
                                                    data.message = "GenderMagento manquant sur un des variants !";
                                                }
                                                if (data.message.includes("mpp_model_ld")) {
                                                    data.message = "ModelMagento manquant sur un des variants !";
                                                }
                                                if (data.message.includes("mpp_style_ms")) {
                                                    data.message = "StyleMagento manquant sur un des variants !";
                                                }
                                            }
                                        }
                                        var label = document.getElementById("exportmagento_magento_" + data.idlist);
                                        label.innerHTML = '<i class="mdi mdi-close-box" style="color:red;"></i>NOK !';
                                        label.setAttribute("style", "font-weight:bold;");
                                        label.setAttribute("class", "");

                                        var label2 = document.createElement("DIV");
                                        label2.innerHTML = '<i class="mdi mdi-alert" style="color:red;"></i>' + data.message + '</label>';
                                        label2.id = "exportmagento_error_" + data.idlist;
                                        label2.name = "exportmagento_error_" + data.idlist;
                                        label2.setAttribute("style", "font-weight:bold;");
                                        label2.setAttribute("class", "");
                                        exportlist.appendChild(label2);

                                        currentid = currentid + 1;
                                        if (currentid < idsplit.length) {
                                            magentoloop(currentid, idsplit, "GETPRODUCT", exportlist);
                                        } else {
                                            var label = document.createElement("DIV");
                                            label.innerHTML = "<label>EXPORT FINISH!" + "</label>";
                                            label.id = "exportmagento_finish_" + data.idlist;
                                            label.name = "exportmagento_finish_" + data.idlist;
                                            label.setAttribute("style", "font-weight:bold;");
                                            label.setAttribute("class", "");
                                            exportlist.appendChild(label);
                                        }
                                    }
                                }
                            }
                        }
                    });
                }

                function datatablesendtomagentogroupupdate() {
                    var exportlist = document.getElementById("progressexport");
                    while (exportlist.firstChild) {
                        exportlist.removeChild(exportlist.firstChild);
                    }

                    var idsplit = ${idlist};
                    currentid = 0;

                    $.ajax({
                        url: tractionWebRoot + '/vehicleList/cleanmagentocache',
                        type: "POST",
                        data: {},
                        success: function (data) {
                            magentoloop(currentid, idsplit, "GETPRODUCT", exportlist);
                        }
                    });
                }

            </script>

            <p><g:message code="productmanager.verifysendmagento" args="${idlist.size()}"/></p>
            <div id="progressexport" name="progressexport">
            </div>

            <div class="d-flex flex-row-reverse">
                <g:if test="${idlist.size() > 0}">
                    <div class="p-2">
                        <button type="button" class="btn btn-danger pull-right"
                                onclick="datatablesendtomagentogroupupdate();"><g:message
                                code="productmanager.sendtomagento"/></button>
                    </div>
                </g:if>
            </div>
        </div>

    </div>
</div>