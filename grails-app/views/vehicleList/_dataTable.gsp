<g:if test="${statistics}">
    <div class="statistics-container card p-2 pt-4">
        <div class="${id}-global-filters d-flex mb-3 gap-3">
            <div class="flex-grow-1">
                <h3 class="emphasis-medium ms-3"><g:message code="datatable.statistics.filter"/></h3>
            </div>
            <div>
                <label>
                    <g:message code="vehicle.type"/>:
                </label>
                <select id="globalType${id}" class="form-control form-control-sm" multiple="multiple">
                    <g:each var="type" in="${types}">
                        <option value="${type.name()}">
                            <g:message code="${type.message}"/>
                        </option>
                    </g:each>
                </select>
            </div>
            <div>
                <label>
                    <g:message code="vehicle.department"/>:
                </label>
                <select id="globalDepartment${id}" class="form-control form-control-sm" multiple="multiple">
                    <g:each var="department" in="${departments}">
                        <option value="${department.id}">
                            ${department.name}
                        </option>
                    </g:each>
                </select>
            </div>
            <div>
                <label>
                    <g:message code="date"/>:
                </label>
                <select id="globalDate${id}" class="form-control form-control-sm" multiple="multiple">
                    <option value="dateCreatedLast7"><g:message code="vehicle.filters.dateCreatedLast7"/></option>
                    <option value="dateCreatedLast30"><g:message code="vehicle.filters.dateCreatedLast30"/></option>
                    <option value="dateModifiedLast7"><g:message code="vehicle.filters.dateModifiedLast7"/></option>
                    <option value="dateModifiedLast30"><g:message code="vehicle.filters.dateModifiedLast30"/></option>
                </select>
            </div>

            <div data-bs-toggle="collapse"
                 data-bs-target="#top-statistics-collapse"
                 onclick="$(this).find('.mdi').toggleClass('mdi-chevron-up mdi-chevron-down')">
                <span class="mdi mdi-chevron-up f-28 pe-2"
                      style="cursor: pointer;"
                      data-toggle="tooltip"
                      title="<g:message code="leads.showHideTable"/>">
                </span>

            </div>
        </div>

        <div id="top-statistics-collapse" class="collapse show">

        </div>
    </div>

    <div id="statistics-container" class="mb-3">
        <div class="loader-bg"></div>

        <div class="loader"></div>

        <div class="statistics-datatable-container shadow border rounded mt-3 p-3 position-relative">
            <div style="position: absolute;right:1em;top:0.5em;z-index: 100;"
                 data-bs-toggle="collapse"
                 data-bs-target="#statistics-collapse"
                 onclick="$(this).find('.mdi').toggleClass('mdi-chevron-up mdi-chevron-down')">
                <span class="mdi mdi-chevron-up f-28"
                      style="cursor: pointer;"
                      data-toggle="tooltip"
                      title="<g:message code="leads.showHideTable"/>">
                </span>
            </div>

            <h3 class="mb-3"><g:message code="datatable.grouped.view"/></h3>

            <div id="statistics-collapse" class="collapse show">
                <table class="table table-sm table-hover" id="statistics-datatable">
                    <thead>
                    <tr>
                        <th colspan="2"><g:message code="leads.groupings"/></th>
                        <th><g:message code="all"/></th>
                        <th colspan="4"><g:message code="vehicle.stat.vehicleNoOpportunity"/></th>
                        <th colspan="4"><g:message code="vehicle.stat.vehicleAvgOpportunity"/></th>
                        <th colspan="2"></th>
                    </tr>
                    <tr>
                        <th class="expand noVis"></th>
                        <th class="PROPERTY_LABEL noVis">
                            <div class="group-property-selects">
                                <select name="groupProperty1" onclick="event.stopPropagation()">
                                    <g:render template="dataTableGroupPropertyOptions"/>
                                </select>
                                <div class="d-flex pt-1">
                                    <div class="w-25 text-center">
                                        <div class="mdi mdi-subdirectory-arrow-right" style="
                                        font-size: 22px;
                                        margin: -6px 0px -10px 0px;
                                        "></div>
                                    </div>
                                    <div class="w-75">
                                        <select name="groupProperty2" onclick="event.stopPropagation()">
                                            <g:render template="dataTableGroupPropertyOptions" model="[selectSecond: true]"/>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </th>
                        <th class="total" data-toggle="tooltip" title="<g:message code="report.total"/>">
                            <g:message code="report.total"/>
                        </th>
                        <th class="newOpportunityLast30" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleNoOpportunityLast30.tooltip"/>">
                            <g:message code="vehicle.stat.30days"/>
                        </th>
                        <th class="newOpportunityLast90" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleNoOpportunityLast90.tooltip"/>">
                            <g:message code="vehicle.stat.90days"/>
                        </th>
                        <th class="newOpportunityLast180" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleNoOpportunityLast180.tooltip"/>">
                            <g:message code="vehicle.stat.180days"/>
                        </th>
                        <th class="newOpportunityAll" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleNoOpportunity.tooltip"/>">
                            <g:message code="vehicle.stat.alltime"/>
                        </th>
                        <th class="newOpportunityLast30Count" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleAvgOpportunityLast30.tooltip"/>">
                            <g:message code="vehicle.stat.30days"/>
                        </th>
                        <th class="newOpportunityLast90Count" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleAvgOpportunityLast90.tooltip"/>">
                            <g:message code="vehicle.stat.90days"/>
                        </th>
                        <th class="newOpportunityLast180Count" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleAvgOpportunityLast180.tooltip"/>">
                            <g:message code="vehicle.stat.180days"/>
                        </th>
                        <th class="newOpportunityAllCount" data-toggle="tooltip" title="<g:message code="vehicle.stat.vehicleAvgOpportunity.tooltip"/>">
                            <g:message code="vehicle.stat.alltime"/>
                        </th>
                        <th class="daysInStock" data-toggle="tooltip" title="<g:message code="vehicle.stat.avgDaysInStock.tooltip"/>">
                            <g:message code="vehicle.stat.avgDaysInStock"/>
                        </th>
                        <th class="listingStatusAll">
                            <g:message code="vehicle.listingStatus"/> - <g:message code="Vehicle.ListingStatus.ALL"/>
                        </th>
                    </tr>
                    </thead>
                </table>
                <style>
                table.table-sm > thead > tr[role="row"] th,
                table.table-sm > thead > tr th,
                table.table-sm > tbody > tr td {
                    padding: 12px !important;
                }
                table.table-sm > thead > tr th {
                    font-size: 12px !important;
                }
                table.table-sm > tbody > tr td {
                    padding-top: 8px !important;
                    padding-bottom: 8px !important;
                }
                table.table-sm > tbody > tr td {
                    align-content: center;
                }
                table.table-sm > thead > tr[role="row"] th[class*="dt-orderable-asc"] .dt-column-order {
                    width: 5px;
                }
                </style>
            </div>
        </div>
    </div>
</g:if>

<div id="leads-table-container">
    <div class="${id} shadow border rounded p-3 position-relative">
        <div class="loader-bg"></div>
        <div class="loader"></div>

        <div style="position: absolute; right:1em; top:0.5em; z-index: 100;"
             data-bs-toggle="collapse"
             data-bs-target="#${id}_container"
             onclick="$(this).find('.mdi').toggleClass('mdi-chevron-up mdi-chevron-down')">
            <span class="f-28 mdi mdi-chevron-up"
                  style="cursor: pointer;"
                  data-toggle="tooltip"
                  title="<g:message code="leads.showHideTable"/>">
            </span>
        </div>

        <h4 class="text-uppercase mb-5"><g:message code="vehicle.list"/></h4>

        <div id="${id}_container" class="collapse show">
            <table class="table vehicle-datatable table-hover border" style="width: 100%;" id="${id}">
                <thead>
                <tr>
                    <th class="check" data-dt-order="disable"><i class="mdi mdi-format-list-checks"></i></th>
                    <th class="id min-w-150"><g:message code="id"/></th>
                    <th class="preview min-w-200"><g:message code="vehicle.preview"/></th>
                    <th class="stateList min-w-250"><g:message code="vehicle.stateList"/></th>
                    <th class="firstPicture min-w-200"><g:message code="vehicle.firstPicture"/></th>
                    <th class="stockNumber min-w-200"><g:message code="vehicle.stockNumber"/></th>
                    <th class="type min-w-200"><g:message code="vehicle.type"/></th>
                    <th class="listingStatus min-w-200"><g:message code="vehicle.listingStatus"/></th>
                    <th class="group min-w-250"><g:message code="vehicle.group"/></th>
                    <th class="rootVehicle min-w-200"><g:message code="vehicle.rootVehicle"/></th>
                    <th class="rootChilds min-w-150"><g:message code="vehicle.rootChilds"/></th>
                    <th class="displayYear min-w-150"><g:message code="vehicle.displayYear"/></th>
                    <th class="year min-w-150"><g:message code="vehicle.year"/></th>
                    <th class="make min-w-150"><g:message code="vehicle.make"/></th>
                    <th class="modelCode min-w-150"><g:message code="vehicle.modelCode"/></th>
                    <th class="model min-w-150"><g:message code="vehicle.model"/></th>
                    <th class="isUsed min-w-200"><g:message code="vehicle.isUsed"/></th>
                    <th class="serialNumber min-w-150"><g:message code="vehicle.serialNumber"/></th>
                    <th class="serialNumber2 min-w-200"><g:message code="vehicle.serialNumber2"/></th>
                    <th class="licenseNumber"><g:message code="vehicle.licenseNumber"/></th>
                    <th class="category min-w-250"><g:message code="vehicle.category"/></th>
                    <th class="categoryKijiji min-w-250"><g:message code="vehicle.categoryKijiji"/></th>
                    <th class="categoryTrader min-w-250"><g:message code="vehicle.categoryTrader"/></th>
                    <th class="magentoData_category min-w-500"><g:message code="vehicle.magentoData_category"/></th>
                    <th class="interests min-w-200"><g:message code="vehicle.interests"/></th>
                    <th class="exteriorColor min-w-200"><g:message code="vehicle.exteriorColor"/></th>
                    <th class="subCategory min-w-200"><g:message code="vehicle.subCategory"/></th>
                    <th class="line min-w-150"><g:message code="vehicle.line"/></th>
                    <th class="status min-w-150"><g:message code="vehicle.status"/></th>
                    <th class="reserved min-w-150"><g:message code="vehicle.reserved"/></th>
                    <th class="clearance min-w-150"><g:message code="vehicle.clearance"/></th>
                    <th class="featured min-w-150"><g:message code="vehicle.featured"/></th>
                    <th class="location min-w-150"><g:message code="vehicle.location"/></th>
                    <th class="keyLocation min-w-150"><g:message code="vehicle.keyLocation"/></th>
                    <th class="weight min-w-150"><g:message code="vehicle.weight"/></th>
                    <th class="odometer min-w-200"><g:message code="vehicle.odometer"/></th>
                    <th class="floorPlanPicture min-w-200"><g:message code="vehicle.floorPlanPicture"/></th>
                    <th class="pictures min-w-150"><g:message code="vehicle.pictures"/></th>
                    <th class="specifications min-w-150"><g:message code="vehicle.specifications"/></th>
                    <th class="height min-w-150"><g:message code="vehicle.height"/></th>
                    <th class="length min-w-150"><g:message code="vehicle.length"/></th>
                    <th class="width min-w-150"><g:message code="vehicle.width"/></th>
                    <th class="diameter min-w-150"><g:message code="vehicle.diameter"/></th>
                    <th class="magentoData_visibility min-w-200"><g:message code="vehicle.magentoData_visibility"/></th>
                    <th class="magentoData_sku min-w-200"><g:message code="vehicle.magentoData_sku"/></th>
                    <th class="description min-w-150"><g:message code="vehicle.description"/></th>
                    <th class="shortDescription min-w-150"><g:message code="vehicle.shortDescription"/></th>
                    <th class="options min-w-150"><g:message code="vehicle.options"/></th>
                    <th class="options2 min-w-150"><g:message code="vehicle.options2"/></th>
                    <th class="optionsPrice min-w-150"><g:message code="vehicle.optionsPrice"/></th>
                    <th class="optionsPrice2 min-w-150"><g:message code="vehicle.optionsPrice2"/></th>
                    <th class="videoUrl min-w-200"><g:message code="vehicle.videoUrl"/></th>
                    <th class="showroomUrl min-w-250"><g:message code="vehicle.showroomUrl"/></th>
                    <th class="promoManufacturerAmount min-w-150"><g:message code="vehicle.promoManufacturerAmount"/></th>
                    <th class="promoManufacturerStartDate min-w-350"><g:message code="vehicle.promoManufacturerStartDate"/></th>
                    <th class="promoManufacturerEndDate min-w-300"><g:message code="vehicle.promoManufacturerEndDate"/></th>
                    <th class="promoInternalAmount"><g:message code="vehicle.promoInternalAmount"/></th>
                    <th class="promoInternalStartDate min-w-300"><g:message code="vehicle.promoInternalStartDate"/></th>
                    <th class="promoInternalEndDate min-w-300"><g:message code="vehicle.promoInternalEndDate"/></th>
                    <th class="promoText"><g:message code="vehicle.promoText"/></th>
                    <th class="promoTextStartDate min-w-300"><g:message code="vehicle.promoTextStartDate"/></th>
                    <th class="promoTextEndDate min-w-250"><g:message code="vehicle.promoTextEndDate"/></th>
                    <th class="watermark min-w-150"><g:message code="vehicle.watermark"/></th>
                    <th class="promoWatermark min-w-200"><g:message code="vehicle.promoWatermark"/></th>
                    <th class="compiledData_minimumPrice min-w-150"><g:message code="vehicle.compiledData_minimumPrice"/></th>
                    <th class="compiledData_displayPrice min-w-150"><g:message code="vehicle.compiledData_displayPrice"/></th>
                    <th class="compiledData_barredPrice min-w-150"><g:message code="vehicle.compiledData_barredPrice"/></th>
                    <th class="msrp"><g:message code="vehicle.msrp"/></th>
                    <th class="promoSpecialPrice"><g:message code="vehicle.promoSpecialPrice"/></th>
                    <th class="dealerInvoice min-w-250"><g:message code="vehicle.dealerInvoice"/></th>
                    <th class="dealerNet min-w-200"><g:message code="vehicle.dealerNet"/></th>
                    <th class="holdback min-w-150"><g:message code="vehicle.holdback"/></th>
                    <th class="preparationCost min-w-200"><g:message code="vehicle.preparationCost"/></th>
                    <th class="preparationDetail min-w-200"><g:message code="vehicle.preparationDetail"/></th>
                    <th class="transportCost min-w-200"><g:message code="vehicle.transportCost"/></th>
                    <th class="transportDetail min-w-200"><g:message code="vehicle.transportDetail"/></th>
                    <th class="surchargeAmount min-w-150"><g:message code="vehicle.surchargeAmount"/></th>
                    <th class="loanRate"><g:message code="vehicle.loanRate"/></th>
                    <th class="cost min-w-150"><g:message code="vehicle.cost"/></th>
                    <th class="costGL min-w-150"><g:message code="vehicle.costGL"/></th>
                    <th class="dateCreated"><g:message code="vehicle.dateCreated"/></th>
                    <th class="dateAvailability min-w-150"><g:message code="vehicle.dateAvailability"/></th>
                    <th class="dateOrder min-w-150"><g:message code="vehicle.dateOrder"/></th>
                    <th class="dateModified min-w-150"><g:message code="vehicle.dateModified"/></th>
                    <th class="dateReceipted min-w-150"><g:message code="vehicle.dateReceipted"/></th>
                    <th class="dateRegistered min-w-150"><g:message code="vehicle.dateRegistered"/></th>
                    <th class="dateSold min-w-150"><g:message code="vehicle.dateSold"/></th>
                    <th class="datePossessed min-w-150"><g:message code="vehicle.datePossessed"/></th>
                    <th class="dateManufactured min-w-150"><g:message code="vehicle.dateManufactured"/></th>
                    <th class="dateCommissioning min-w-150"><g:message code="vehicle.dateCommissioning"/></th>
                    <th class="dateImport min-w-150"><g:message code="vehicle.dateImport"/></th>
                    <th class="dateDelivery min-w-150"><g:message code="vehicle.dateDelivery"/></th>
                    <th class="dateDue min-w-150"><g:message code="vehicle.dateDue"/></th>
                    <th class="warrantyPrice min-w-200"><g:message code="vehicle.warrantyPrice"/></th>
                    <th class="warrantyTerm min-w-200"><g:message code="vehicle.warrantyTerm"/></th>
                    <th class="warrantyDescription min-w-150"><g:message code="vehicle.warrantyDescription"/></th>
                    <th class="demoDiscountAmount"><g:message code="vehicle.demoDiscountAmount"/></th>
                    <th class="loanTermMonths min-w-200"><g:message code="vehicle.loanTermMonths"/></th>
                    <th class="compiledData_monthlyPayment min-w-150"><g:message code="vehicle.compiledData_monthlyPayment"/></th>
                    <th class="loanTermWeeks min-w-200"><g:message code="vehicle.loanTermWeeks"/></th>
                    <th class="magentoData_attrSet min-w-150"><g:message code="vehicle.magentoData_attrSet"/></th>
                    <th class="configuratorData_model min-w-250"><g:message code="vehicle.configuratorData_model"/></th>
                    <th class="configuratorData_genre min-w-250"><g:message code="vehicle.configuratorData_genre"/></th>
                    <th class="configuratorData_style min-w-200"><g:message code="vehicle.configuratorData_style"/></th>
                    <th class="configuratorData_color min-w-250"><g:message code="vehicle.configuratorData_color"/></th>
                    <th class="configuratorData_colorSet min-w-250"><g:message code="vehicle.configuratorData_colorSet"/></th>
                    <th class="configuratorData_size min-w-200"><g:message code="vehicle.configuratorData_size"/></th>
                    <th class="configuratorData_material min-w-250"><g:message code="vehicle.configuratorData_material"/></th>
                    <th class="configuratorData_decor min-w-200"><g:message code="vehicle.configuratorData_decor"/></th>
                    <th class="configuratorData_interior min-w-250"><g:message code="vehicle.configuratorData_interior"/></th>
                    <th class="magentoData_vehicleNavAll min-w-250"><g:message code="vehicle.magentoData_vehicleNavAll"/></th>
                    <th class="magentoData_vehicleNavCur min-w-250"><g:message code="vehicle.magentoData_vehicleNavCur"/></th>
                    <th class="magentoData_htmlH1 min-w-150"><g:message code="vehicle.magentoData_htmlH1"/></th>
                    <th class="magentoData_htmlH2 min-w-150"><g:message code="vehicle.magentoData_htmlH2"/></th>
                    <th class="magentoData_crsell min-w-250"><g:message code="vehicle.magentoData_crsell"/></th>
                    <th class="magentoData_upsell min-w-250"><g:message code="vehicle.magentoData_upsell"/></th>
                    <th class="configuratorData_configurable min-w-300"><g:message code="vehicle.configuratorData_configurable"/></th>
                    <th class="iprofile min-w-150"><g:message code="vehicle.iprofile"/></th>
                    <th class="suffix min-w-150"><g:message code="vehicle.suffix"/></th>
                    <th class="department min-w-150"><g:message code="vehicle.department"/></th>
                    <th class="powergoid min-w-150"><g:message code="vehicle.powergoid"/></th>
                    <th class="compiledData_formulaPrice1 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice1" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice2 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice2" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice3 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice3" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice4 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice4" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice5 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice5" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice6 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice6" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice7 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice7" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPrice8 min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice8" preventConfigure="true"/></th>
                    <th class="compiledData_formulaPdiPrice min-w-150"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPdiPrice" preventConfigure="true"/></th>
                    <th class="compiledData_formulaFreightPrice min-w-200"><vehicleTagLib:customizableFieldLabel property="compiledData_formulaFreightPrice" preventConfigure="true"/></th>
                    <th class="compiledData_tractionPrice min-w-150"><g:message code="vehicle.compiledData_tractionPrice"/></th>
                    <th class="compiledData_optionsFormulaPrice min-w-150"><g:message code="vehicle.compiledData_optionsFormulaPrice"/></th>
                    <th class="compiledData_costRealPrice min-w-150"><g:message code="vehicle.compiledData_costRealPrice"/></th>
                    <th class="compiledData_estimatedLoanAmount min-w-250"><g:message code="vehicle.compiledData_estimatedLoanAmount"/></th>
                    <th class="compiledData_formulaDescription min-w-300"><g:message code="vehicle.compiledData_formulaDescription"/></th>
                    <th class="compiledData_formulaShortDescription min-w-300"><g:message code="vehicle.compiledData_formulaShortDescription"/></th>
                    <th class="compiledData_formulaTitre min-w-200"><g:message code="vehicle.compiledData_formulaTitre"/></th>
                    <th class="compiledData_formulaDescriptionKijiji min-w-300"><g:message code="vehicle.compiledData_formulaDescriptionKijiji"/></th>
                    <th class="compiledData_formulaDescriptionTrader min-w-300"><g:message code="vehicle.compiledData_formulaDescriptionTrader"/></th>
                    <th class="compiledData_weeklyPayment min-w-150"><g:message code="vehicle.compiledData_weeklyPayment"/></th>
                    <th class="customPrice1 min-w-150"><vehicleTagLib:customizableFieldLabel property="customPrice1" preventConfigure="true"/></th>
                    <th class="customPrice2 min-w-150"><vehicleTagLib:customizableFieldLabel property="customPrice2" preventConfigure="true"/></th>
                    <th class="customPrice3 min-w-150"><vehicleTagLib:customizableFieldLabel property="customPrice3" preventConfigure="true"/></th>
                    <th class="customText1 min-w-200"><vehicleTagLib:customizableFieldLabel property="customText1" preventConfigure="true"/></th>
                    <th class="customText2 min-w-200"><vehicleTagLib:customizableFieldLabel property="customText2" preventConfigure="true"/></th>
                    <th class="customText3 min-w-200"><vehicleTagLib:customizableFieldLabel property="customText3" preventConfigure="true"/></th>
                    <th class="pixelguru min-w-150"><g:message code="vehicle.pixelguru"/></th>
                    <th class="actions min-w-150"><g:message code="actions"/></th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script>
    $(function () { restoreCollapseStates(); });
</script>


