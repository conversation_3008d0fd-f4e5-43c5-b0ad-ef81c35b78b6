<%@ page import="traction.I18N; traction.client.ClientInterest;" %>
<asset:javascript src="/bank/bank.js"/>
<style>
#bankTable_wrapper col[data-dt-column="2"] {
    min-width: revert !important;
    width: 90px !important;
}

#bankTable_wrapper {
    margin-top: 8px;
}
</style>

<div class="h-100 overflow-scroll  border rounded-2 p-4">
    <h3 class="text-emphasis fw-bold f-18 text-uppercase mb-3">
        <g:message code="config.financing.banks"/>
    </h3>
    <inputTagLib:select
            required="required"
            id="bankSelect"
            name="bank"
            class="form-select h-39"
            options="${[['label': I18N.m('config.select.bank'), 'value': '']] + availableBanks.collect{ [label: I18N.m(it.message), value: it.id] }}"
    />
    <table id="bankTable" class="table dataTable w-100">
        <thead>
        <tr>
            <th><g:message code="bank"/></th>
            <th><g:message code="bank.vehicle.type"/></th>
            <th><g:message code="actions"/></th>
        </tr>
        </thead>
        <tbody id="addedBanksTable">
        <g:each in="${addedBanks}" var="bank">
            <tr id="bankRow-${bank.id}">
                <td><g:message code="${bank.selectedBank.message}"/></td>
                <td id="vehicle-type-${bank.id}">
                    <g:if test="${bank.vehicleType}">
                        <div id="read-vehicle-type-${bank.id}" class="d-flex flex-wrap gap-1">
                            <g:each in="${bank.vehicleType}" var="vehicleType">
                                <span class="badge-outline-secondary-emphasis"><g:message code="${vehicleType.name}"/></span>
                            </g:each>
                        </div>
                    </g:if>
                    <div id="edit-vehicle-type-${bank.id}" class="w-100 d-none">
                        <inputTagLib:select id="select-vehicle-type-${bank.id}" name="select-vehicle-type-${bank.id}" class="form-select form-select-sm" multiple="multiple" values="${bank.vehicleType.collect{it.id}}" options="${ClientInterest.getAll().collect{ [label: it.name, value: it.id]}}"/>
                    </div>
                    <script>
                        $(document).ready(function () {
                            $('#save-button-${bank.id}').on('click', function () {
                                updateVehicleType(${bank.id}, $('#select-vehicle-type-${bank.id}').val());
                            });
                        });
                    </script>
                </td>
                <td>
                    <div class="d-flex align-items-center justify-content-between">
                        <i class="fa-light fa-pen f-16 text-primary cursor" id="edit-button-${bank.id}"
                            data-toggle="tooltip" title="<g:message code="edit"/>"
                           onclick="toggleEdit(${bank.id})"></i>
                        <i class="fa-light fa-trash f-16 text-primary cursor" id="delete-button-${bank.id}"
                            data-toggle="tooltip" title="<g:message code="delete"/>"
                           onclick="deleteBank(${bank.id})"></i>
                        <i class="fa-solid fa-pen-slash f-16 text-primary d-none cursor" id="read-button-${bank.id}"
                            data-toggle="tooltip" title="<g:message code="cancel"/>"
                           onclick="toggleEdit(${bank.id})"></i>
                        <i class="fa-solid fa-save f-16 text-primary cursor d-none" id="save-button-${bank.id}" data-toggle="tooltip" title="<g:message code="save"/>"></i>
                    </div>
                </td>
            </tr>
        </g:each>
        </tbody>
    </table>
</div>