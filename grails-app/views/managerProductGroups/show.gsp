<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message code="productmanager.groups"/></title>
    <asset:javascript src="managerProductGroups/ManagerProductGroups.js"/>
    <asset:stylesheet src="managerProduct/ManagerProductGroups.css"/>
</head>
<body>
<div class="m-6 p-6 mt-0 mb-0 pt-4">
    <div class="d-flex flex-row">
        <div class="col-10">
            <h2><g:message code="productmanager.groups" /></h2>
        </div>
        <div class="col-2 btn-group">
%{--            <button class="btn btn-secondary buttons-collection dropdown-toggle btn-sm btn-light rounded border ms-auto d-flex align-items-center me-2 multilocal fw-regular 466298" tabindex="0" aria-controls="vehicledatatable" type="button" aria-haspopup="dialog" aria-expanded="false">--}%
%{--                <span>--}%
%{--                    <i class="fa-solid fa-language me-2"></i>--}%
%{--                    <span class="f-16 fa-roboto fw-normal">French</span>--}%
%{--                </span>--}%
%{--            </button>--}%
%{--            <button class="btn btn-outline-primary rounded bg-white h-100">--}%
%{--                <g:message code="actions" />--}%
%{--            </button>--}%
        </div>
    </div>
    <div id="group-selection" class="mt-4">
        <g:render template="components/groupSelection" model="[selectedGroup: group, allGroups: allGroups, AVBLS: AVBLS]"/>
    </div>
    <div id="group-edit">
        <g:include action="editGroupCard" />
    </div>
</div>
</body>
</html>