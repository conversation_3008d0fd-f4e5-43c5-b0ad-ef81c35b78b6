<asset:stylesheet src="lib/codeMirror/codemirror.css"/>
<asset:javascript src="lib/codeMirror/codemirror.js"/>
<asset:javascript src="managerProductGroups/codemirror-group.js"/>
<asset:javascript src="lib/codeMirror/mode/xml/xml.js"/>
<asset:javascript src="lib/codeMirror/addon/display/autorefresh.js"/>
<asset:javascript src="lib/codeMirror/addon/search/searchcursor.js"/>
<asset:javascript src="managerProductGroups/prdgroup.js"/>
<asset:javascript src="managerProductGroups/modalFormula.js"/>

<script>
    var groupId = ${group.id};
    var propertyName = '${property}';
    var isFormula = ${isFormula};

    $(document).ready(function() {
        if (!isFormula) {
            VehicleGroupFormulaModal.init(groupId, propertyName);
            $('#saveFormulaButton').show();
            $('#saveTextButton').hide();
        } else {
            VehicleGroupTextModal.init(groupId, propertyName);
            $('#saveFormulaButton').hide();
            $('#saveTextButton').show();
        }
    });
</script>

<div class="modal-dialog modal-xl" style="max-width: 75%">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title text-uppercase" id="title">
                <g:message code="productmanager.Modify.group"/> ${property}</h2>
        </div>

        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <g:if test="${!isFormula}">
                        <div class="mb-2">
                            <h5 class="mb-3 text-uppercase"><g:message
                                    code="productmanager.groups.create.formula"/></h5>
                            <label class="fw-bold f-16 mb-2" style="color: black;"><g:message
                                    code="vehicle.formula.select"/></label>

                            <div class="dropdown mb-3">
                                <a class="dropdown-toggle h-39 w-100 f-16 d-flex align-items-center justify-content-between"
                                   style="padding: 10px 12px; border: 1px solid #CCCCCC; border-radius: 4px; color:black; width: 297px; display: inline-block;"
                                   type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                                   aria-expanded="false">
                                    <div>
                                        <i class="fa fa-plus text-primary me-2"></i>
                                        <g:message code="add.variable.to.formula"/>
                                    </div>
                                    <i class="fa-regular fa-chevron-down f-12 chevron-icon"></i>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="overflow-y: scroll; height:500px;">
                                    <g:each var="field" in="${fields}">
                                        <li><a class="dropdown-item" href="#"
                                               onclick="VehicleGroupFormulaModal.appendToFormula('${field}');"><g:message code="vehicle.${field}"/></a>
                                        </li>
                                    </g:each>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <textarea class="form-control mb-3" rows="5" id="vehicleGroupFormula">${formula}</textarea>
                        </div>
                    </g:if>
                    <g:else>
                        <div class="mb-2">
                            <h5 class="mb-3 text-uppercase"><g:message
                                    code="productmanager.groups.create.formula"/> ${property}</h5>
                            <label class="fw-bold f-16 mb-2" style="color: black;"><g:message
                                    code="vehicle.formula.select"/></label>

                            <div class="dropdown mb-3">
                                <a class="dropdown-toggle h-39 w-100 f-16 d-flex align-items-center justify-content-between"
                                   style="padding: 10px 12px; border: 1px solid #CCCCCC; border-radius: 4px; color:black; width: 297px; display: inline-block;"
                                   type="button" id="dropdownMenuButtonText" data-bs-toggle="dropdown"
                                   aria-expanded="false">
                                    <div>
                                        <i class="fa fa-plus text-primary me-2"></i>
                                        <g:message code="add.variable.to.formula"/>
                                    </div>
                                    <i class="fa-regular fa-chevron-down f-12 chevron-icon"></i>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtonText" style="overflow-y: scroll; height: 500px;" >
                                    <g:each var="text" in="${texts}">
                                        <li><a class="dropdown-item" href="#"
                                               onclick="VehicleGroupTextModal.insertText('${text.id}');">${text.id}: ${text.name}</a>
                                        </li>
                                    </g:each>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <textarea class="form-control" style="height: 200px;" id="vehicle-group-text-editor"
                                      name="body">${group.getProperty(property)}</textarea>
                        </div>
                    </g:else>
                </div>

                <div class="col-md-6">
                    <h5 class="mb-3 text-uppercase"><g:message
                            code="productmanager.groups.preview"/></h5>
                    <label class="fw-bold f-16 mb-2" style="color: black;"><g:message
                            code="vehicle.formula.select.vehicle"/></label>
                    <search:vehicleInit/>
                    <search:vehicle class="mb-3" type="text" callback="${!isFormula? 'VehicleGroupFormulaModal.previewFormula':'VehicleGroupTextModal.previewText'}"
                                    placeholder="${g.message(code: 'vehicle.search')}"/>
                    <div id="vehicleGroupPreview" class="border rounded mb-3 p-2 overflow-y-scroll"></div>
                </div>
            </div>

            <div class="d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-outline-primary shadow-sm fw-bold"
                        data-bs-dismiss="modal"><g:message code="cancel"/></button>
                <button id="saveFormulaButton" onclick="VehicleGroupFormulaModal.updateFormula();" type="button"
                        class="btn btn-primary shadow-sm fw-bold"><g:message code="client.save"/></button>
                <button id="saveTextButton" type="button" class="btn btn-primary shadow-sm fw-bold"
                        onclick="VehicleGroupTextModal.updateText();"><g:message code="save"/></button>
            </div>
        </div>
    </div>
</div>
<style>
#dropdownMenuButton::after {
    display: none;
}

#dropdownMenuButtonText::after {
    display: none;
}
</style>