<g:if test="${statistics}">
    <div class="statistics-container card p-2 pt-4 mb-4">
        <div
             class="d-flex flex-row-reverse"
             data-bs-toggle="collapse"
             data-bs-target="#top-statistics-collapse"
             onclick="$(this).find('.mdi').toggleClass('mdi-chevron-up mdi-chevron-down')">
            <span class="mdi mdi-chevron-up f-28 pe-2"
                  style="cursor: pointer;"
                  data-toggle="tooltip"
                  title="<g:message code="leads.showHideTable"/>">
            </span>
        </div>
        <div id="top-statistics-collapse" class="collapse show"></div>
    </div>
</g:if>

<div id="${id}_container" class="collapse show">
    <div class=" border p-4">
        <h2 class="mb-3 text-emphasis fw-bold"><g:message code="salestracking"/></h2>
        <table class="table table-hover w-100 border" id="${id}">
            <thead>
            <tr>
                <th colspan="9"><g:message code="cotation"/></th>
                <th rowspan="3" class="type">
                    <g:message code="cotationelement.type"/>
                    <div id="type-ratio" data-toggle="tooltip" title="<g:message code="cotationelement.type.ratio"/>"></div>
                </th>
                <th rowspan="3" class="label"><g:message code="label"/></th>
                <th colspan="3" rowspan="2"><g:message code="user"/></th>
                <th colspan="9" rowspan="2"><g:message code="client.vehicle"/></th>
                <th colspan="11"><g:message code="prices"/></th>
                <th colspan="2"><g:message code="salestracking.profit"/></th>
                <th colspan="2"><g:message code="salestracking.commission"/></th>
                <th colspan="2"><g:message code="cotation.summary.opportunityAccessories"/></th>
                <th rowspan="3" class="comment min-w-150"><g:message code="client.comment"/></th>
                <th colspan="12"><g:message code="cotationelement.fniProfit"/></th>
                <th rowspan="3" class="followUp min-w-150"><g:message code="cotationelement.followup"/></th>
                <th colspan="4" rowspan="2"><g:message code="main.workflow"/></th>
            </tr>
            <tr class="total-label">
                <th rowspan="2" class="cotation"><g:message code="id"/></th>
                <th colspan="2"><g:message code="client"/></th>
                <th rowspan="2" class="date"><g:message code="cotation.date"/></th>
                <th rowspan="2" class="dateSold"><g:message code="cotation.dateSold"/></th>
                <th rowspan="2" class="department"><g:message code="cotation.department"/></th>
                <th colspan="2" data-toggle="tooltip" title="<g:message code="salestracking.finance.tooltip"/>"
                    class="bg-warning text-center" style="float: initial;">
                    <g:message code="salestracking.finance"/>
                    <div id="founded-files"></div>
                </th>
                <th rowspan="2" class="exportedToLautopak min-w-200"><g:message code="cotation.exported"/></th>
                <th id="costPrice-sum"></th>
                <th id="opportunityTradePriceToAbsorb-sum"></th>
                <th id="opportunityMrsp-sum"></th>
                <th id="opportunityOtherFee-sum"></th>
                <th id="opportunityPotentialPrice-sum"></th>
                <th id="sellingPriceNoAcc-sum" class="min-w-300"></th>
                <th class="min-w-200">
                    <span id="opportunityPotentialPriceLack-sum"></span>
                    <small id="opportunityPotentialPriceLack-percent"></small>
                </th>
                <th id="opportunityFinalMinimumPrice-sum"></th>
                <th id="opportunityPromo-sum"></th>
                <th id="sellingPrice-sum"></th>
                <th rowspan="2" class="opportunityPromoDate me-1 min-w-200"><g:message code="vehicle.promovalid"/></th>
                <th colspan="2" id="profit-sum" data-toggle="tooltip" title="<g:message code="salestracking.profit.percent.total"/>"></th>
                <th colspan="2" id="commission-sum" data-toggle="tooltip" title="<g:message code="salestracking.commissionPercent.total"/>"></th>
                <th colspan="2" id="opportunityAccessories-sum" data-toggle="tooltip" title="<g:message code="cotation.summary.accessories.margin"/> <g:message
                        code="cotation.summary.accessories.margin.tooltip"/>"></th>
                <th id="fniProfitReserveFinance-sum"></th>
                <th id="fniProfitLifeInsurance-sum"></th>
                <th id="fniProfitCriticalIllnessInsurance-sum"></th>
                <th id="fniProfitExtendedPlan-sum"></th>

                <th id="fniProfitAestheticProtection-sum"></th>
                <th id="fniProfitOthers-sum"></th>
                <th id="fniProfitDisabilityInsurance-sum"></th>
                <th id="fniProfitReplacementInsurance-sum"></th>
                <th id="fniProfitRentalLeaseUsury-sum"></th>
                <th id="fniProfitChemicals-sum"></th>
                <th colspan="2" id="fniProfitTotal-sum"></th>
            </tr>
            <tr>
                <th class="firstname"><g:message code="client.firstname"/></th>
                <th class="lastname"><g:message code="client.lastname"/></th>
                <th class="soldStatus">
                    <g:message code="opportunity.status"/>
                </th>
                <th class="lastStatusChange" data-toggle="tooltip" title="<g:message code="opportunity.lastStatusChange"/>">
                    <g:message code="date"/>
                </th>
                <th class="userSales">
                    <g:message code="client.posstatus.sales"/>
                </th>
                <th class="userFni">
                    <g:message code="client.posstatus.fni"/>
                </th>
                <th class="userBdc">
                    <g:message code="client.posstatus.bdc"/>
                </th>
                <th class="make">
                    <g:message code="vehicle.make"/>
                </th>
                <th class="model">
                    <g:message code="vehicle.model"/>
                </th>
                <th class="year">
                    <g:message code="vehicle.year"/>
                </th>
                <th class="stocknumber">
                    <g:message code="opportunity.stocknumber"/>
                </th>
                <th class="modelcode min-w-200">
                    <g:message code="vehicle.modelCode"/>
                </th>
                <th class="interest">
                    <g:message code="client.interests"/>
                </th>
                <th class="daysInStock min-w-200">
                    <g:message code="vehicle.daysInStock"/>
                </th>
                <th class="opportunityIsUsed min-w-150">
                    <g:message code="vehicle.isUsed"/>
                </th>
                <th class="serialnumber min-w-200">
                    <g:message code="vehicle.serialNumber"/>
                </th>
                <th class="costPrice" data-toggle="tooltip" title="<g:message code="salestracking.costPrice.tooltip"/>">
                    <g:message code="salestracking.costPrice"/>
                </th>
                <th class="opportunityTradePriceToAbsorb" data-toggle="tooltip"
                    title="<g:message code="cotation.summary.tradePriceToAbsord"/>">
                    <g:message code="trade"/>
                </th>
                <th class="opportunityMrsp" data-toggle="tooltip"
                    title="<g:message code="cotation.summary.opportunityMrsp.tooltip"/>">
                    <g:message code="cotation.summary.opportunityMrsp"/>
                </th>
                <th data-toggle="tooltip" title="<g:message code="cotation.summary.opportunityOtherFee.tooltip"/>"
                    class="opportunityOtherFee">
                    <g:message code="cotation.summary.opportunityOtherFee"/>
                </th>
                <th class="opportunityPotentialPrice" data-toggle="tooltip"
                    title="<g:message code="salestracking.opportunityPotentialPrice.tooltip"/>">
                    <g:message code="salestracking.opportunityPotentialPrice"/>
                </th>
                <th class="sellingPriceNoAcc" data-toggle="tooltip"
                    title="<g:message code="salestracking.sellingPriceNoAcc.tooltip"/>">
                    <g:message code="salestracking.sellingPriceNoAcc"/>
                </th>
                <th class="opportunityPotentialPriceLack" data-toggle="tooltip"
                    title="<g:message code="salestracking.opportunityPotentialPriceLack.tooltip"/>">
                    <g:message code="salestracking.opportunityPotentialPriceLack"/>
                </th>
                <th class="opportunityFinalMinimumPrice" data-toggle="tooltip"
                    title="<g:message code="salestracking.opportunityFinalMinimumPrice.tooltip"/>">
                    <g:message code="salestracking.opportunityFinalMinimumPrice"/>
                </th>
                <th data-toggle="tooltip" title="<g:message code="cotation.summary.opportunityPromo.tooltip"/>"
                    class="opportunityPromo">
                    <g:message code="cotation.summary.opportunityPromo"/>
                </th>
                <th class="sellingPrice" data-toggle="tooltip"
                    title="<g:message code="salestracking.sellingPrice.tooltip"/>">
                    <g:message code="salestracking.sellingPrice"/>
                </th>
                <th class="profit" data-toggle="tooltip"
                    title="= <g:message code="salestracking.sellingPrice"/> - <g:message
                            code="salestracking.costPrice"/> - <g:message code="trade"/>">
                    $ <span class="d-none"><g:message code="salestracking.profit"/></span>
                </th>
                <th class="profitPercent" data-toggle="tooltip"
                    title="= <g:message code="salestracking.profit"/> / <g:message code="salestracking.sellingPrice"/>">
                    % <span class="d-none"><g:message code="salestracking.profit"/></span>
                </th>
                <th class="commission" data-toggle="tooltip" title="= <g:message code="salestracking.profit"/> * <g:message
                        code="salestracking.commissionPercent"/>">
                    $ <span class="d-none"><g:message code="salestracking.commission"/></span>
                </th>
                <th class="commissionPercent" data-toggle="tooltip"
                    title="= <g:message code="salestracking.commission"/> / <g:message code="salestracking.profit"/>">
                    % <span class="d-none"><g:message code="salestracking.commission"/></span>
                </th>
                <th data-toggle="tooltip" title="<g:message code="salestracking.opportunityAccessories"/>"
                    class="opportunityAccessories">
                    $ <span class="d-none"><g:message code="salestracking.opportunityAccessories"/></span>
                </th>
                <th data-toggle="tooltip" title="<g:message code="cotation.summary.accessories.margin"/> <g:message
                        code="cotation.summary.accessories.margin.tooltip"/>" class="opportunityAccessoriesMarginPercent">
                    % <span class="d-none"><g:message code="salestracking.opportunityAccessories"/></span>
                </th>
                <th class="fniProfitReserveFinance"><g:message code="cotationelement.fniProfitReserveFinance"/></th>
                <th class="fniProfitLifeInsurance"><g:message code="cotationelement.fniProfitLifeInsurance"/></th>
                <th class="fniProfitCriticalIllnessInsurance min-w-250"><g:message code="cotationelement.fniProfitCriticalIllnessInsurance"/></th>
                <th class="fniProfitExtendedPlan min-w-250"><g:message code="cotationelement.fniProfitExtendedPlan"/></th>
                <th class="fniProfitAestheticProtection min-w-200"><g:message code="cotationelement.fniProfitAestheticProtection"/></th>
                <th class="fniProfitOthers"><g:message code="cotationelement.fniProfitOthers"/></th>
                <th class="fniProfitDisabilityInsurance min-w-200"><g:message code="cotationelement.fniProfitDisabilityInsurance"/></th>
                <th class="fniProfitReplacementInsurance min-w-250"><g:message code="cotationelement.fniProfitReplacementInsurance"/></th>
                <th class="fniProfitRentalLeaseUsury min-w-200"><g:message code="cotationelement.fniProfitRentalLeaseUsury"/></th>
                <th class="fniProfitChemicals"><g:message code="cotationelement.fniProfitChemicals"/></th>
                <th class="fniProfitTotal"><g:message code="cotationelement.fniProfitTotal"/></th>
                <th class="fniProfitTotalPercent min-w-200"><g:message code="cotationelement.fniProfitTotalPercent"/></th>
                <th class="workflowData_workflowMaster min-w-150">
                    <g:message code="workflowboard.workflowMaster"/>
                </th>
                <th class="workflowData_workflowBoard min-w-250">
                    <g:message code="workflowboard"/>
                </th>
                <th class="workflowDataDateEnd">
                    <g:message code="workflowdata.dateEnd"/>
                </th>
                <th class="workflowData_workflowTypeOfCard min-w-200">
                    <g:message code="workflowdata.typeOfCard"/>
                </th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>