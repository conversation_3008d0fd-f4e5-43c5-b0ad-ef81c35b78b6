<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="m-0 fw-bold">
                <g:message code="managerproduct.categoryMagento"/>
            </h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

            </button>
        </div>

        <div class="modal-body">
            <container:treeCheckbox2 opened="true" id="${managerProductConfig.id}" value="${managerProductConfig.valuereplace}" configCategory="categorymagentosite_cat" profile="${managerProductConfig.profile}" onchange="updateConfigCategoryMagento"/>
        </div>
    </div>
</div>
<script>
    function updateConfigCategoryMagento(id, values) {
        console.log('id, values', id, values);
        $.post({
            url: tractionWebRoot + '/managerProductConfig/updateCategoryMagento',
            data: {
                id: id,
                values: values
            },
            success: function (data) {
                console.log(data);
            }
        });
    }
</script>