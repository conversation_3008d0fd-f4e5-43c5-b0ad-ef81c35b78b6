<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="vehicleselectionoption.manage"/></title>
    <asset:javascript src="vehicleSelectionOption/index.js"/>
    <asset:stylesheet src="vehicleSelectionOption/index.css"/>
</head>
<body>
<div class="container p-4">
    <h4 class="text-uppercase mb-5"><g:message code="vehicleselectionoption.manage"/></h4>
    <div class="row">
        <div class="col-3">
            <div class="mb-2 fw-bold line-height-19">
                <g:message code="vehicleselectionoption.select.property"/>
            </div>
            <div id="property-selector" class="border py-2" style="height: 75vh; overflow: scroll; border-radius: 4px; scrollbar-width: none;">
                <g:each var="prop" in="${properties}" status="index">
                    <div class="property-item cursor px-3 ${index == 0 ? 'active' : ''}" onclick="VehicleSelectionOptionIndex.select(this)" data-property="${prop}">
                        ${prop}
                    </div>
                </g:each>
            </div>
        </div>
        <div class="col-9" id="property-options-editor">

        </div>
    </div>

</div>

</body>
</html>
<style>
#property-selector {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}
#property-selector::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
}
</style>