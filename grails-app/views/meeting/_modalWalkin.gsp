<div class="modal-dialog modal-dialog-scrollable modal-xl">
    <div class="modal-content">

        <div class="modal-header pb-5">
            <h2 class="modal-title fw-bold"><g:message code="task.category.walkin"/> (${opportunity})</h2>
        </div>

        <div class="modal-body">
            <div class="d-flex flex-row align-items-center">
                <div class="mb-3 w-100">
                    <div class="d-flex flex-row">
                        <label class="col-2 align-content-center"><g:message code="date"/></label>
                        <div class="col d-flex align-items-center me-4">
                            <div class="position-relative me-5">
                            <input class="form-control w-auto date-picker" placeholder="YYYY-MM-dd HH:mm:ss"
                                   value="${date.format("yyyy-MM-dd HH:mm:ss")}"
                                   max="${date.format("yyyy-MM-dd HH:mm:ss")}"
                                   id="meeting-expectedDate"/>
                            </div>
                            <label class="w-fit-content me-3"><g:message code="meeting.duration"/></label>
                            <div class="d-flex">
                                <input type="number" class="form-control border-end-0 rounded-end-0" min="0" step="10" value="30"
                                   id="meeting-duration" style="width: 100px;" autofocus/>
                                <p class="border border-start-0 rounded-end mb-0 align-content-center" style="color: #808080;padding-right: 12px;">m</p>
                            </div>
                        </div>

                    </div>

                </div>

            </div>

            <div class="d-flex flex-row">
                <div class="col-2">
                    <label>
                        <g:message code="meeting.note"/>
                    </label>
                </div>

                <div class="col">
                    <textarea id="meeting-note" rows="4" class="form-control"></textarea>
                </div>
            </div>

            <g:if test="${visitLogs}">
                <div class="mb-3">
                    <strong><g:message code="appointment.visitlog"/>:</strong>
                    <select class="form-control" id="meeting-visit-log">
                        <option value="">----</option>
                        <g:each var="v" in="${visitLogs}">
                            <option value="${v.id}">${v.date.format("yyyy-MM-dd HH:mm")}: ${v.client?.fullName ?: v.noteOnClient} ${v.user ? '' : '(' + v.departmentGroup.department.name + ' ' + g.message(code: v.departmentGroup.userGroup.message) + ' - ' + g.message(code: "visitlog.nulluser") + ')'}</option>
                        </g:each>
                    </select>
                </div>
            </g:if>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-outline-primary shadow-sm fw-bold" onclick="TractionModal.hide()">
                <g:message code="cancel"/>
            </button>
            <button type="button" class="btn btn-primary shadow-sm fw-bold" onclick="addWalkin()">
                <g:message code="client.save"/>
            </button>
        </div>
    </div>
</div>
<script>
    $(function() {
        $("#meeting-expectedDate").daterangepicker({
            opens: 'left',
            timePicker: true,
            singleDatePicker: true,
            locale: DateRangePickerUtils.LOCALE_WITH_TIME
        }).on('cancel.daterangepicker', function (ev, picker) {
            $(this).val('');
            $(this).attr("value", '');
        });
        $("#meeting-expectedDate").each(function () {
            if ($(this).attr('value') == '') $(this).trigger("cancel.daterangepicker");
        });
    });

    function addWalkin() {
        var note = $('#meeting-note').val().trim();
        if (note == '') {
            $('#meeting-note').focus();
            $.notify(I18N.m('meeting.emptynote.error'), 'error');
            return;
        }
        showBigLoader();
        $.post({
            url: tractionWebRoot + "/meeting/addWalkin",
            data: {
                "opportunityId": ${opportunity.id},
                "duration": $('#meeting-duration').val(),
                "visitLogId": $('#meeting-visit-log').val(),
                "expectedDate": $('#meeting-expectedDate').val(),
                "note": note
            },
            success: function (data) {
                if (data.success) {
                    $.notify(data.message, 'success');
                    TractionModal.hide();
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    }
</script>
