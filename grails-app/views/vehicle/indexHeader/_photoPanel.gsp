<%@ page import="traction.DateUtils" %>
<div class="text-center">
    <g:if test="${vehicle}">
        <g:set var="pictures" value="${vehicle?.asInheritedVehicle()?.getInheritedPictures()}"/>
        <g:set var="firstImage" value="${pictures?.getFirstWatermarkedPicture()}"/>
        <div class="border position-relative">
            <!-- Problème quand image est supprimer -->
            <g:if test="${firstImage}">
                <img src="<g:createLink absolute="true" controller="web" action="getFile" id="${firstImage.id}"/>" class="img-fluid w-100" alt="" style="max-width: 300px; max-height: 300px; width: 100%;">
            </g:if>
            <g:else>
                <asset:image src="image-missing.jpg" class="img-fluid w-100" alt=""/>
            </g:else>
            <div style="position: absolute; bottom: 6px; right: 6px;" class="text-body bg-body bg-opacity-75 px-2 py-1 rounded">
                ${pictures.getOrderableList()?.size()} <i class="fa-regular fa-camera"></i>
            </div>
        </div>
    </g:if>
    <g:else>
        <i class="fa-solid fa-camera text-primary pt-6 pb-6" style="font-size: 4rem; --bs-text-opacity: .5;"></i>
    </g:else>
</div>
<div class="d-flex justify-content-between mx-2 my-1">
    <div class="d-flex flex-wrap me-auto gap-2">
        <span class="badge badge-outline-secondary-emphasis text-uppercase">
            <g:message code="${vehicle?.status?.message}"/>
        </span>
        <g:if test="${vehicle.getDaysInStockString()}">
            <span class="badge badge-outline-secondary-emphasis text-uppercase">${vehicle.getDaysInStockString()}</span>
        </g:if>
        <g:if test="${vehicle?.reserved}">
            <span class="badge badge-outline-danger text-uppercase">
                <g:message code="vehicle.reserved"/>
            </span>
        </g:if>
    </div>
    <g:if test="${vehicleOpportunityDanger || vehicleOpportunityWarning || vehicleOpportunitySecondary}">
        <div class="cursor align-content-center d-flex gap-2" onclick="TractionModal.show({
            url: '/vehicle/modalOpportunities',
            data: {id: ${vehicle?.id}}
        });">
            <g:if test="${vehicleOpportunityDanger}">
                <span data-toggle="tooltip"
                      title="<g:message code="vehicle.opportunities.sold"/>"
                      class="badge badge-outline-danger">${vehicleOpportunityDanger}</span>
            </g:if>
            <g:if test="${vehicleOpportunityWarning}">
                <span data-toggle="tooltip"
                      title="<g:message code="vehicle.opportunities.active"/>"
                      class="badge badge-outline-warning">${vehicleOpportunityWarning}</span>
            </g:if>
            <g:if test="${vehicleOpportunitySecondary}">
                <span data-toggle="tooltip"
                      title="<g:message code="vehicle.opportunities.other"/>"
                      class="badge badge-outline-secondary-emphasis">${vehicleOpportunitySecondary}</span>
            </g:if>
        </div>
    </g:if>
</div>