<div class="d-flex justify-content-between align-items-center">
  <h6 class="text-uppercase"><g:message code="client"/></h6>
    <g:if test="${vehicle?.client?.id}">
        <div>
            <i class="fa-solid fa-comments text-primary f-16 me-3 cursor" onclick="event.preventDefault(); event.stopPropagation(); TractionModal.show({
                url: '/communication/modal',
                bigLoader: true,
                data: {
                    clientId: ${vehicle?.client?.id},
                }
            });"></i>

            <i class="fa-solid fa-list-check text-primary f-16 cursor" onclick="TaskSidePanel.show({
                clientId: ${vehicle?.client?.id},
                vehicleId: ${vehicle?.id},
                userId: ${currentUser.id},
            });"></i>
        </div>
    </g:if>
</div>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-user",
        iconMessage: "client.fullname",
        value: vehicle?.client?.fullName
]}"/>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-hashtag",
        iconMessage: "client.id",
        value: vehicle?.client?.id
]}"/>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-phone",
        iconMessage: "client.phone",
        value: vehicle?.client?.phone
]}"/>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-mobile",
        iconMessage: "client.phonecell",
        value: vehicle?.client?.phonecell
]}"/>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-envelope",
        iconMessage: "client.email",
        value: vehicle?.client?.email
]}"/>
<g:render template="/vehicle/indexHeader/panelRow" model="${[
        iconClass: "fa-scale-balanced",
        iconMessage: "client.balance",
        value: vehicle?.client?.balance
]}"/>
