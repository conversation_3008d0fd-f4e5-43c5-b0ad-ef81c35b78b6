<%@ page import="traction.department.Department; traction.opportunity.Opportunity" %>
<style>
.note-editable {
    height: 150px !important;
}
.note-status-output, .note-resizebar {
    display: none !important;
}
#evaluationsTable .selected-row {
    background-color: #F2F9FE !important;
}
.rowDisabled {
    opacity: 0.5;
    pointer-events: none;
    user-select: none;
}
#traction-modal #evaluationsTable {
    width: 90vw;
    max-width: 1250px;
}
</style>
<script>
    var EvaluationEdit = {
        data: {vehicleId: ${vehicleId}, selectable: ${selectable ? true : false}},
        hasChanged: false,
        readOnly: ${readOnly ? true : false},

        enterEditMode() {
            this.hasChanged = true;
            TractionModal.options.data.hasChanged = true;
            FormModeUtils.edit('#evaluationsTable');
        },

        addData(input) {
            if (input) {
                $('button.evaluation-save').prop('disabled', false);
                let self = this;
                EvaluationEdit.data[$(input).attr('name').replace('_' + self.id, '')] = $(input).val();
            }
        },

        update() {
            EvaluationEdit.data.departmentId = $('input[type="radio"].department-input:checked').val();
            showBigLoader();
            $.post({
                url: tractionWebRoot + "/vehicle/saveEvaluation",
                data: EvaluationEdit.data,
                success: function (data) {
                    if (data.success) {
                        $.notify(data.message, 'success');
                        EvaluationEdit.hasChanged = true;
                        TractionModal.options.data.hasChanged = true;
                        TractionModal.hide();
                    } else {
                        $.notify(data.message, 'error');
                    }
                    hideBigLoader();
                }
            })
        },

        close() {
            TractionModal.options.data.hasChanged = this.hasChanged;
            TractionModal.hide();
        }
    };
</script>

<div id="evaluationsTable" class="modal-dialog modal-dialog-scrollable modal-xl form-mode-read">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold" id="title"><g:message code="vehicle.evaluation"/></h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
            <table class="table w-100 dataTable" id="pricePerDealer-table">
                <thead>
                <tr>
                    <g:if test="${selectable}"><th class="active min-w-100"><g:message code="action.active"/></th></g:if>
                    <th class="departement min-w-200"><g:message code="department"/></th>
                    <th class="priceAsked min-w-150"><g:message code="vehicle.priceAsked"/></th>
                    <th class="priceAccepted min-w-150"><g:message code="vehicle.priceAccepted"/></th>
                    <th class="priceLink min-w-150"><g:message code="vehicle.priceLink"/></th>
                    <th class="priceReal min-w-150"><g:message code="vehicle.priceReal"/></th>
                    <th class="priceDisplay min-w-150"><g:message code="vehicle.priceDisplay"/></th>
                </tr>
                </thead>
                <g:each var="department" in="${departments}">
                    <g:if test="${currentUser.getAuthorities().any { it.authority == "ROLE_SALESMANAGER" } || currentUser.currentDepartment == department}">
                    <tr>
                    </g:if>
                    <g:else>
                    <tr class="rowDisabled">
                    </g:else>
                        <g:if test="${selectable}">
                            <td style="vertical-align: middle;">
                                <div class="mode-read">
                                    <input type="radio" class="form-check-input" value="${department.id}" ${evaluations.find { it.department == department && it.isActive } ? 'checked' : ''} disabled>
                                </div>
                                <div class="mode-edit">
                                    <input type="radio" class="form-check-input department-input" name="departmentId" value="${department.id}"  ${evaluations.find { it.department == department && it.isActive } ? 'checked' : ''}>
                                </div>
                            </td>
                        </g:if>
                        <td style="vertical-align: middle;">${department.name}</td>
                        <td>
                            <div class="mode-read w-100">
                                <format:currency number="${evaluations.find { it.department == department }?.priceAsked ?: 0}"/>
                            </div>

                            <div class="mode-edit w-125-px input-group flex-nowrap">
                                <input type="text" class="form-control text-end border-end-0 ps-3 pe-1"
                                       id="evaluation_${department.id}_priceAsked"
                                       name="evaluation_${department.id}_priceAsked"
                                       onchange="EvaluationEdit.addData(this)"
                                       value="<format:currencyNumber number="${evaluations.find { it.department == department }?.priceAsked ?: 0}"/>"/>
                                <span class="input-group-text ps-0 border-start-0 text-body-tertiary bg-white"
                                      style="padding-right:0.6em !important; height:100%; border:1px solid #ced4da;">$</span>
                            </div>
                        </td>
                        <td>
                            <div class="mode-read w-100">
                                <format:currency number="${evaluations.find { it.department == department }?.priceAccepted ?: 0}"/>
                            </div>

                            <div class="mode-edit w-125-px input-group flex-nowrap">
                                <input type="text" class="form-control text-end border-end-0 ps-3 pe-1"
                                       id="evaluation_${department.id}_priceAccepted"
                                       name="evaluation_${department.id}_priceAccepted"
                                       onchange="EvaluationEdit.addData(this)"
                                       value="<format:currencyNumber number="${evaluations.find { it.department == department }?.priceAccepted ?: 0}"/>">
                                <span class="input-group-text ps-0 border-start-0 text-body-tertiary bg-white"
                                      style="padding-right:0.6em !important; height:100%; border:1px solid #ced4da;">$</span>
                            </div>
                        </td>
                        <td>
                            <div class="mode-read w-100">
                                <format:currency number="${evaluations.find { it.department == department }?.priceLink ?: 0}"/>
                            </div>

                            <div class="mode-edit w-125-px input-group flex-nowrap">
                                <input type="text" class="form-control text-end border-end-0 ps-3 pe-1"
                                       id="evaluation_${department.id}_priceLink"
                                       name="evaluation_${department.id}_priceLink"
                                       onchange="EvaluationEdit.addData(this)"
                                       value="<format:currencyNumber number="${evaluations.find { it.department == department }?.priceLink ?: 0}"/>">
                                <span class="input-group-text ps-0 border-start-0 text-body-tertiary bg-white"
                                      style="padding-right:0.6em !important; height:100%; border:1px solid #ced4da;">$</span>
                            </div>
                        </td>
                        <td>
                            <div class="mode-read w-100">
                                <format:currency number="${evaluations.find { it.department == department }?.priceReal ?: 0}"/>
                            </div>

                            <div class="mode-edit w-125-px input-group flex-nowrap">
                                <input type="text" class="form-control text-end border-end-0 ps-3 pe-1"
                                       id="evaluation_${department.id}_priceReal"
                                       name="evaluation_${department.id}_priceReal"
                                       onchange="EvaluationEdit.addData(this)"
                                       value="<format:currencyNumber number="${evaluations.find { it.department == department }?.priceReal ?: 0}"/>"/>
                                <span class="input-group-text ps-0 border-start-0 text-body-tertiary bg-white"
                                      style="padding-right:0.6em !important; height:100%; border:1px solid #ced4da;">$</span>
                            </div>
                        </td>
                        <td>
                            <div class="mode-read w-100">
                                <format:currency number="${evaluations.find { it.department == department }?.priceDisplay ?: 0}"/>
                            </div>

                            <div class="mode-edit w-125-px input-group flex-nowrap">
                                <input type="text" class="form-control text-end border-end-0 ps-3 pe-1"
                                       id="evaluation_${department.id}_priceDisplay"
                                       name="evaluation_${department.id}_priceDisplay"
                                       onchange="EvaluationEdit.addData(this)"
                                       value="<format:currencyNumber number="${evaluations.find { it.department == department }?.priceDisplay ?: 0}"/>">
                                <span class="input-group-text ps-0 border-start-0 text-body-tertiary bg-white"
                                      style="padding-right:0.6em !important; height:100%; border:1px solid #ced4da;">$</span>
                            </div>
                        </td>
                    </tr>
                </g:each>
            </table>
        </div>

        <g:if test="${!readOnly}">
            <div class="modal-footer">
                <div class="mode-read d-flex justify-content-end">
                    <button type="button" class="btn btn-link"
                            onclick="EvaluationEdit.enterEditMode()">
                        <g:message code="modify"/>
                    </button>
                </div>
                <div class="mode-edit d-flex justify-content-end">
                    <button type="button" class="btn btn-outline-primary rounded-pill fw-bold me-2"
                            onclick="EvaluationEdit.close()">
                        <g:message code="cancel"/>
                    </button>
                    <button type="button" class="btn btn-primary text-white rounded-pill fw-bold evaluation-save"
                            onclick="EvaluationEdit.update()">
                        <g:message code="save"/>
                    </button>
                </div>
            </div>
        </g:if>
    </div>
</div>
<script>
    $(document).ready(function () {
        TractionDataTable.init('#pricePerDealer-table', {
            id: 'pricePerDealer-table',
            enableScrollBar: true,

            dataTableOptions: {
                paging: false,
                serverSide: false,
                ordering: true,
                searching: true,
                buttons: []
            }
        });

        // Add event listener for radio buttons
        $('#pricePerDealer-table').on('change', 'input[type="radio"]', function () {
            // Remove background color from all rows
            $('#pricePerDealer-table tr').removeClass('selected-row');
            // Add background color to the selected row
            $(this).closest('tr').addClass('selected-row');
        });
        // Check which radio button is checked on page load and set background color
        $('#pricePerDealer-table input[type="radio"]:checked').closest('tr').addClass('selected-row');
    });

</script>

