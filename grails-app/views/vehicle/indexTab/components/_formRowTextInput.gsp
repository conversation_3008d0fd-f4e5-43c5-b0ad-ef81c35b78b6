<g:set var="label" value="${property == 'serialNumber' || property == 'serialNumber2' || property == 'stockNumber' ? message(code: 'vehicle.' + property + '.number') : message(code: 'vehicle.' + property)}"/>
<div class="row my-read-2-edit-1">
    <div class="col ${classColLabel} py-2 ${property in ['magentoData_crsell', 'magentoData_upsell', 'magentoData_vehicleNavAll', 'magentoData_vehicleNavCur'] ? 'text-truncate' : ''}" onclick="$(this).toggleClass('text-truncate');">
        <g:if test="${property in ['magentoData_crsell', 'magentoData_upsell', 'magentoData_vehicleNavAll', 'magentoData_vehicleNavCur']}">
            <label data-toggle="tooltip" title="${label}">${label}</label>
        </g:if>
        <g:else>
            <label for="${property}">${label}</label>
        </g:else>
    </div>
    <div class="col ${classColValue} mode-read align-self-center text-truncate">
        <vehicleTagLib:inheritedValue inheritedVehicle="${inheritedVehicle}" property="${property}"/>
    </div>
    <div class="col ${classColValue} mode-edit align-self-center">
        <g:if test="${property == "magentoData_sku"}">
            <p class="text-body-tertiary mb-0" style="padding-left: 12px;">${inheritedVehicle.magentoData.sku ?: '-'}</p>
        </g:if>
        <g:else>
        <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="${property}">
            <input id="${property}" name="${property}" type="text" class="form-control" value="${vehicle.getNestedProperty(property)}" placeholder="${label}" ${disabled ? 'disabled' : ''}/>
        </vehicleTagLib:inheritedField>
        </g:else>
    </div>
</div>