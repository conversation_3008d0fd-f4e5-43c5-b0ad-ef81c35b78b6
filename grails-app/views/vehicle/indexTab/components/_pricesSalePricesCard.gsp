<g:set var="formId" value="vehicle-salePrices-form"/>
<form id="${formId}" class="card p-4 mb-4  form-mode-read flex-grow-1">
    <h5 class="card-title mb-2 text-uppercase"><g:message code="vehicle.salePrices"/></h5>
    
    <g:each in="${['demoDiscountAmount', 'optionsPrice2', 'customPrice1', 'customPrice2', 'customPrice3']}" var="property">
        <g:render template="indexTab/components/formRowPriceInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: property]"/>
    </g:each>

    <input type="hidden" name="id" value="${vehicle.id}"/>

    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0 text-primary"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Prices')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>