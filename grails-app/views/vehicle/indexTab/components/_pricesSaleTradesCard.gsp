<g:set var="formId" value="vehicle-saleTrades-form"/>
<form id="${formId}" class="card p-4 mb-4  form-mode-read flex-grow-1">
    <h5 class="card-title mb-4 text-uppercase"><g:message code="vehicle.trade.prices"/></h5>

    <g:each in="${['priceAsked', 'priceAccepted', 'priceLink', 'priceReal', 'priceDisplay']}" var="property">
        <div class="mb-4">
            <g:render template="indexTab/components/formRowPriceInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: property]"/>
        </div>
    </g:each>

    %{--TODO estimated repair VehicleOptions... DV1-T670 --}%

    <input type="hidden" name="id" value="${vehicle.id}"/>

    <div class="row flex-fill mt-1">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-2">
                <div class="mode-read">
                    <button type="button" class="btn btn-sm btn-link w-auto"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-sm btn-link-subtle w-auto"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-sm btn-link w-auto"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Trades')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>