<div class="row my-read-2-edit-1">
    <div class="col-3 py-2 d-flex justify-content-between align-items-center">
        <label for="${property}"><vehicleTagLib:customizableFieldLabel property="${property}"/></label>
        <vehicleTagLib:inheritedIcon inheritedVehicle="${inheritedVehicle}" property="${property}"/>
    </div>
    <div class="col-9 mode-read align-items-center" style="padding-right: 12px !important;">
        <vehicleTagLib:inheritedValue inheritedVehicle="${inheritedVehicle}" property="${property}" type="currency" hideDollarIfEmpty="${hideDollarIfEmpty}"/>
    </div>
    <div class="col-9 mode-edit align-items-center">
        <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="${property}" type="currency">
        <div class="input-group">
            <input id="${property}" name="${property}" type="number" min="0" step="0.01" pattern="^\d+(?:\.\d{1,2})?$" class="form-control rounded"
                style="padding-right: 2rem !important;" value="<g:formatNumber number='${vehicle.getNestedProperty(property)}' format='#.00' locale='US'/>" placeholder="0.00"/>
            <div class="input-group-append rounded-0 float-right">
                <span class="input-group-text text-body-tertiary">
                    $
                </span>
            </div>
        </div>
        </vehicleTagLib:inheritedField>
    </div>
</div>