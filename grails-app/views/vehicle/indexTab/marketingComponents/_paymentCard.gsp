<g:set var="formId" value="vehicle-payment-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h5 class="card-title text-uppercase"><g:message code="vehicle.payment"/></h5>

    <div class="row my-2">
        <div class="col-3 align-self-center pe-0" style="width: 180px;margin-right: 12px;">
            <label><g:message code="vehicle.compiledData_estimatedLoanAmount"/></label>
        </div>

        <div class="col-9 ps-0 align-self-center pe-0" style="width: calc(100% - 204px);">
            <vehicleTagLib:inheritedValue vehicle="${vehicle}" inheritedVehicle="${inheritedVehicle}"
                                          property="compiledData_estimatedLoanAmount" type="currency"/>
        </div>
    </div>

    <div><g:render template="indexTab/components/formRowNumberInput"
                                model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: 'loanRate', units: '%', step: '0.01']"/></div>

    <div><g:render template="indexTab/components/formRowNumberInput"
                                model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: 'loanTermMonths', units: g.message(code: 'month')]"/></div>

    <div><g:render template="indexTab/components/formRowNumberInput"
                                model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: 'loanTermWeeks', units: g.message(code: 'week')]"/></div>

    <g:each in="${['monthlyPayment', 'weeklyPayment']}" var="property">
        <div class="row my-2">
            <div class="col-3 py-2 align-self-center" style="width: 180px;margin-right: 12px;">
                <label><vehicleTagLib:customizableFieldLabel property="${property}"/></label>
            </div>

            <div class="col-9 ps-0 align-self-center pe-0" style="width: calc(100% - 204px);">
                <vehicleTagLib:compiledDataPrice inheritedVehicle="${inheritedVehicle}" property="${property}"/>
            </div>
        </div>
    </g:each>
    <input type="hidden" name="id" value="${vehicle.id}"/>

    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-4">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>

                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>
