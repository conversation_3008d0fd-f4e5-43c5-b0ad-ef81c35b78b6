<div class="card p-4 rounded-1 flex-grow-1">
    <h5 class="card-title text-uppercase"><g:message code="vehicle.groupFormulas"/></h5>

    <div class="row">
        <div class="col pe-4">
            <g:each in="${(1..8)}" var="i">
                <div class="row my-2 py-2 d-inline-flex w-100 mx-0">
                    <div class="col align-self-center ps-0">
                        <label><vehicleTagLib:customizableFieldLabel property="compiledData_formulaPrice${i}"/></label>
                    </div>
                    <div class="col text-end pe-0">
                        <vehicleTagLib:compiledDataPrice inheritedVehicle="${inheritedVehicle}" property="formulaPrice${i}"/>
                    </div>
                </div>
            </g:each>
        </div>
        <div class="col border-start border-primary-subtle ps-4">
            <g:each in="${['formulaPdiPrice', 'formulaFreightPrice', 'tractionPrice', 'minimumPrice', 'optionsFormulaPrice', 'costRealPrice']}" var="property">
                <div class="row my-2 py-2 d-inline-flex w-100 mx-0">
                    <div class="col align-self-center ps-0">
                        <label>
                            <vehicleTagLib:customizableFieldLabel property="compiledData_${property}"/>
                        </label>
                    </div>
                    <div class="col text-end">
                        <vehicleTagLib:compiledDataPrice inheritedVehicle="${inheritedVehicle}" property="${property}"/>
                    </div>
                </div>
            </g:each>
        </div>
    </div>
    <div class="text-end mt-4">
        <a onclick="TractionModal.show({
            url: '/vehicle/modalGroupFormulas?vehicleId=${vehicle.id}',
            backdrop: 'static'
        });" class="icon-link icon-link-hover icon-link-hover-move-up-right small f-16 text-decoration-underline" role="button">
            <g:message code="details"/>
        </a>
    </div>
</div>