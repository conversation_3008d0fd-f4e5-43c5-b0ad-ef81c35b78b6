<g:set var="formId" value="vehicle-warranty-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h6 class="card-title mb-3 text-uppercase"><g:message
            code="vehicle.magentoData_crsell"/></h6>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>

    <div class="row my-3">
        <div class="col-6">ID</div>
    </div>
    <input type="hidden" name="id" value="${vehicle.id}"/>

    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>

                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link  w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
