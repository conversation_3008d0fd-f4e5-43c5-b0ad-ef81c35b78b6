<%@ page import="traction.vehicle.Vehicle" %>
<g:set var="formId" value="vehicle-websites-export-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h5 class="card-title text-uppercase"><g:message code="vehicle.websitesExport"/></h5>

    <div class="row my-read-2-edit-1">
        <div class="col-6 d-flex justify-content-between align-items-center py-2">
            <label for="categoryKijiji"><g:message code="vehicle.categoryKijiji"/></label>
            <vehicleTagLib:inheritedIcon inheritedVehicle="${inheritedVehicle}" property="categoryKijiji"/>
        </div>
        <div class="col-6 mode-read align-self-center">
            <vehicleTagLib:selectedValueLabel value="${inheritedVehicle.categoryKijiji ?: '-'}" propertyName="categoryKijiji"/>
        </div>
        <div class="col-6 mode-edit align-self-center">
            <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="categoryKijiji">
                <vehicleTagLib:select id="categoryKijiji" name="categoryKijiji" class="form-select" value="${vehicle.categoryKijiji}" propertyName="categoryKijiji"/>
            </vehicleTagLib:inheritedField>
        </div>
    </div>

    <div class="row my-read-2-edit-1">
        <div class="col-6 d-flex justify-content-between align-items-center py-2 text-truncate" data-toggle="tooltip" title="<g:message code="vehicle.categoryTrader"/>" onclick="$(this).toggleClass('text-truncate');">
            <label for="categoryTrader"><g:message code="vehicle.categoryTrader"/></label>
            <vehicleTagLib:inheritedIcon inheritedVehicle="${inheritedVehicle}" property="categoryTrader"/>
        </div>
        <div class="col-6 mode-read align-self-center">
            <vehicleTagLib:selectedValueLabel value="${inheritedVehicle.categoryKijiji ?: '-'}" propertyName="categoryTrader"/>
        </div>
        <div class="col-6 mode-edit align-self-center">
            <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="categoryTrader">
                <vehicleTagLib:select id="categoryTrader" name="categoryTrader" class="form-select" value="${vehicle.categoryTrader}" propertyName="categoryTrader"/>
            </vehicleTagLib:inheritedField>
        </div>
    </div>

    <g:if test="${!vehicle.isRoot()}">
        <g:render template="indexTab/components/formRowTextInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'powergoid']"/>
    </g:if>
    
    <div class="row my-read-2-edit-1">
        <div class="col-6 align-self-center py-2">
            <label for="iprofile"><g:message code="vehicle.iprofile"/></label>
        </div>
        <div class="col-6 mode-read align-self-center">
            ${vehicle.iprofile}
        </div>
        <div class="col-6 mode-edit align-self-center">
            <vehicleTagLib:select id="iprofile" name="iprofile" class="form-select" value="${vehicle.iprofile}" propertyName="iprofile"/>
        </div>
    </div>

    <g:render template="indexTab/components/formRowSelectInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'listingStatus', values: Vehicle.ListingStatus.values()]"/>
    <g:if test="${!vehicle.isRoot()}">
        <g:render template="indexTab/components/formRowBooleanInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'pixelguru']"/>
    </g:if>
    <g:render template="indexTab/components/formRowBooleanInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'clearance']"/>
    <g:render template="indexTab/components/formRowBooleanInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'featured']"/>
    <g:if test="${!vehicle.isRoot()}">
        <g:render template="indexTab/components/formRowBooleanInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, classColLabel: 'col-6', classColValue: 'col-6', property: 'reserved']"/>
    </g:if>

    <input type="hidden" name="id" value="${vehicle.id}"/>
    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link  w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>