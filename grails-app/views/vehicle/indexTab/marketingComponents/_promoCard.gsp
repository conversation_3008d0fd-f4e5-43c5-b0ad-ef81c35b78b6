<div class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h6 class="card-title text-uppercase"><g:message code="vehicle.promotions"/></h6>

    <div class="row my-2">
        <div class="text-info-secondary-emphasis fw-bold" style="width: 17.5%;"><g:message code="vehicle.type"/></div>
        <div class="text-info-secondary-emphasis fw-bold text-center" style="width: 10%;"><g:message code="vehicle.status"/></div>
        <div class="text-info-secondary-emphasis fw-bold" style="width: 20%;"><g:message code="vehicle.promoStartDate"/></div>
        <div class="text-info-secondary-emphasis fw-bold" style="width: 20%;"><g:message code="vehicle.promoEndDate"/></div>
        <div class="text-info-secondary-emphasis fw-bold" style="width: 32.5%;"><g:message code="vehicle.promo"/></div>
    </div>

    <div class="row my-2 py-2">
        <div style="width: 17.5%;">
            <label>
                <g:message code="vehicle.promoManufacturerAmount"/>
            </label>
        </div>
        <div style="width: 10%;" class="text-center">
            <i class="${inheritedVehicle.getPromoManufacturerStatus().icon}" data-toggle="tooltip" title="<g:message code="${inheritedVehicle.getPromoManufacturerStatus().message}"/>"></i>
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoManufacturerStartDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoManufacturerEndDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 32.5%;">
            <g:if test="${inheritedVehicle.promoManufacturerAmount != null}">
                <g:formatNumber number="${inheritedVehicle.promoManufacturerAmount}" type="number" minFractionDigits="2" maxFractionDigits="2" format="#,##0.00"/>
            </g:if>

            <g:else>
                -
            </g:else>
            <span class="text-body-tertiary float-right">$</span>
        </div>
    </div>
    <div class="row my-2 py-2">
        <div style="width: 17.5%;">
            <label>
                <g:message code="vehicle.promoInternalAmount"/>
            </label>
        </div>
        <div style="width: 10%;" class="text-center">
            <i class="${inheritedVehicle.getPromoInternalStatus().icon}" data-toggle="tooltip" title="<g:message code="${inheritedVehicle.getPromoInternalStatus().message}"/>"></i>
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoInternalStartDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoInternalEndDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 32.5%;">
            <g:if test="${inheritedVehicle.promoInternalAmount != null}">
                <g:formatNumber number="${inheritedVehicle.promoInternalAmount}" type="number" minFractionDigits="2" maxFractionDigits="2" format="#,##0.00"/>
            </g:if>
            <g:else>
                -
            </g:else>
            <span class="text-body-tertiary float-right">$</span>
        </div>
    </div>
    <div class="row my-2 py-2">
        <div style="width: 17.5%;">
            <label>
                <g:message code="vehicle.promoText"/>
            </label>
        </div>
        <div style="width: 10%;" class="text-center">
            <i class="${inheritedVehicle.getPromoTextStatus().icon}" data-toggle="tooltip" title="<g:message code="${inheritedVehicle.getPromoTextStatus().message}"/>"></i>
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoTextStartDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 20%;">
            ${inheritedVehicle.promoTextEndDate?.format('MM-dd-yyyy') ?: '-'}
        </div>
        <div style="width: 32.5%;">
            ${inheritedVehicle.promoText ?: '-'}<span class="text-body-tertiary float-right">$</span>
        </div>
    </div>

    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <a onclick="TractionModal.show({
                url: '/vehicle/modalPromotions?vehicleId=${vehicle.id}',
                backdrop: 'static'
            });" class="icon-link icon-link-hover icon-link-hover-move-up-right small f-16 text-decoration-underline" role="button">
                <g:message code="vehicle.managePromotions"/>
            </a>
        </div>
    </div>
</div>
