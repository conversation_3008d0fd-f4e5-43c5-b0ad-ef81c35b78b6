<g:set var="displayPrice" value="${vehicle?.compiledData?.displayPrice ?: 0}"/>
<g:set var="costGL" value="${inheritedVehicle.getNestedProperty('costGL') ?: 0}"/>
<g:set var="formId" value="vehicle-profit-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h5 class="card-title text-uppercase"><g:message code="vehicle.profitMargin"/></h5>

    <div class="row">
        <div class="col">
            <div class="row">
                <!--<div class="col-12 align-self-center">
                    <g:render template="indexTab/components/formRowPriceInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: 'costGL']"/>
                </div>-->
                <div class="col-3 py-2 align-self-center" style="height: 39px; width: 180px;margin-right: 12px;">
                    <label><g:message code="vehicle.costGL"/></label>
                </div>
                <div class="col-9 mode-read align-content-center" style="width: calc(100% - 204px); padding-left: 12px !important;">
                    <g:formatNumber number="${costGL}" type="number" currencySymbol="" minFractionDigits="2" maxFractionDigits="2" format="#,##0.00" /><span class="float-right text-body-tertiary"> $</span>
                </div>
                <div class="col-9 mode-edit align-content-center" style="width: calc(100% - 204px);">
                    <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="costGL" type="currency">
                        <div class="input-group">
                            <input id="costGL" name="costGL" type="number" min="0" step="100" pattern="^\d+(?:\.\d{1,2})?$" class="form-control rounded"
                                   style="padding-right: 2rem !important;" value="<g:formatNumber number='${vehicle.getNestedProperty("costGL")}' minFractionDigits="2" maxFractionDigits="2" format="#,##0.00" locale='US'/>" placeholder="0.00"/>
                            <div class="input-group-append rounded-0 float-right">
                                <span class="input-group-text text-body-tertiary">
                                    $
                                </span>
                            </div>
                        </div>
                    </vehicleTagLib:inheritedField>                </div>
            </div>
            <i class="fa fa-dash d-block text-primary text-end" style="height: 16px; width: 164px;"></i>
            <div class="row mb-3">
                <div class="col-3 py-2 align-self-center" style="height: 39px; width: 180px;margin-right: 12px;">
                    <label><g:message code="vehicle.displayPrice"/></label>
                </div>
                <div class="col-9 align-content-center px-0" style="width: calc(100% - 204px); padding-left: 12px !important;">
                    <g:formatNumber number="${displayPrice}" type="number" currencySymbol="" minFractionDigits="2" maxFractionDigits="2" format="#,##0.00" /><span class="float-right text-body-tertiary"> $</span>
                </div>
            </div>
            <div class="border-bottom border-primary-subtle my-3" style="height: 1px;"></div>
            <div class="row my-3">
                <div class="col-3 py-2 align-self-center" style="width: 180px;margin-right: 12px;">
                    <label><g:message code="vehicle.profitMargin"/> ($)</label>
                </div>
                <div class="col-9 align-content-center px-0" style="width: calc(100% - 204px); padding-left: 12px !important;">
                    <g:formatNumber number="${displayPrice - costGL}" type="number" currencySymbol=""  minFractionDigits="2" maxFractionDigits="2" format="#,##0.00"/><span class="float-right text-body-tertiary"> $</span>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-3 py-2 align-self-center" style="width: 180px;margin-right: 12px;">
                    <label><g:message code="vehicle.profitMargin"/> (%)</label>
                </div>
                <div class="col-9 align-content-center px-0" style="width: calc(100% - 204px); padding-left: 12px !important;">
                    <g:formatNumber number="${displayPrice > 0 ? (displayPrice - costGL) / displayPrice * 100 : 0}" type="number" minFractionDigits="2" maxFractionDigits="2" format="#,##0.00"/> <span class="float-right text-body-tertiary">%</span>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="id" value="${vehicle.id}"/>
    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn  btn-link w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>