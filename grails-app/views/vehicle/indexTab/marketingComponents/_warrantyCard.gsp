<g:set var="formId" value="vehicle-warranty-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h6 class="card-title text-uppercase"><g:message code="vehicle.warranty"/></h6>

    <div class="row my-2">
        <div class="col-5 py-2" style="width: 180px;margin-right: 12px;"><label># <g:message code="vehicle.warranty"/></label></div>
        <div class="col-7 align-self-center px-0" style="width: calc(100% - 204px);">12345</div>
    </div>
    <div class="row my-read-2-edit-1">
        <div class="col-5 py-2 align-self-center">
            <label for="warrantyTerm">${g.message(code: 'vehicle.warrantyTerm')}</label>
        </div>
        <div class="col-7 align-self-center mode-read" style="padding-right: 12px !important;">
            <vehicleTagLib:inheritedValue inheritedVehicle="${inheritedVehicle}" property="warrantyTerm" type="number" units="${g.message(code: 'month')}"/>
        </div>
        <div class="col-7 mode-edit align-self-center">
            <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="warrantyTerm" type="number">
                <div class="input-group">
                    <input id="'warrantyTerm'" name="'warrantyTerm'" type="number" min="0" step="${step}" pattern="^\d+(?:\.\d{1,2})?$" class="form-control rounded"
                           style="${units == '%' ? 'padding-right: 2rem !important;' : 'padding-right: 4rem !important;'}" value="${vehicle.getNestedProperty('warrantyTerm')}" placeholder="0.00"/>

                    <div class="input-group-append rounded-0 float-right">
                        <span class="input-group-text text-body-tertiary">${g.message(code: 'month')}</span>
                    </div>
                </div>
            </vehicleTagLib:inheritedField>
        </div>
    </div>
    <div class="row my-2">
        <div class="col-5 py-2" style="width: 180px;margin-right: 12px;"><label><g:message code="vehicle.warrantyKm"/></label></div>
        <div class="col-7 align-items-center d-flex px-0" style="width: calc(100% - 204px);">10 000 <span class="ms-auto text-body-tertiary">km</span></div>
    </div>

    <div class="border-bottom border-primary-subtle my-4" style="height: 1px;"></div>
    <g:render template="indexTab/components/formRowTextareaInput" model="[vehicle: vehicle, inheritedVehicle: inheritedVehicle, property: 'warrantyDescription']"/>
    <input type="hidden" name="id" value="${vehicle.id}"/>
    <div class="row flex-fill mx-0">
        <div class="col align-self-end text-end px-0">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link  w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
