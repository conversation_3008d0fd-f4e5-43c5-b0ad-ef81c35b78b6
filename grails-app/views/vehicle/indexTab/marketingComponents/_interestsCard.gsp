<g:set var="formId" value="vehicle-interests-form"/>
<form id="${formId}" class="card p-4 rounded-1 form-mode-read flex-grow-1">
    <h5 class="card-title text-uppercase"><g:message code="vehicle.interests"/></h5>

    <div id="vehicleInterests" class="pt-2 mode-read d-flex flex-column gap-2 w-100">
        <g:if test="${vehicle?.interests?.isEmpty()}">
            <g:if test="${!vehicle?.isRoot() && !vehicle?.asInheritedVehicle().getInterests()?.empty}">
                <vehicleTagLib:inheritedValue inheritedVehicle="${inheritedVehicle}" property="interests"/>
            </g:if>
            <g:else>
                <p class="text-body-tertiary"><g:message code="vehicle.noInterests"/></p>
            </g:else>
        </g:if>
        <g:else>
            <vehicleTagLib:inheritedValue inheritedVehicle="${inheritedVehicle}" property="interests"/>
        </g:else>
    </div>
    <div class="pt-2 mode-edit w-100">
        <vehicleTagLib:inheritedField inheritedVehicle="${inheritedVehicle}" property="interests">
            <!-- Hidden input to ensure 'interests' is sent even if no selection is made (to set empty interests) -->
            <input type="hidden" name="interests" value="" />
            <select id="vehicleInterests-select" name="interests" multiple="multiple" class="form-select form-select-sm">
                <g:each var="interest" in="${allInterests}">
                    <option value="${interest.id}" ${vehicle?.interests?.contains(interest) ? 'selected' : ''}>${interest.name}</option>
                </g:each>
            </select>
        </vehicleTagLib:inheritedField>
        <script>
            $('#vehicleInterests-select').select2({
                theme: 'bootstrap-5'
            });
        </script>
    </div>

    <input type="hidden" name="id" value="${vehicle.id}"/>
    <div class="row flex-fill">
        <div class="col align-self-end text-end">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="FormModeUtils.edit('#${formId}')"><g:message code="modify"/></button>
                </div>
                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3"
                            onclick="FormModeUtils.cancel('#${formId}')"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link w-auto p-0"
                            onclick="VehicleIndex.saveForm('#${formId}', 'Marketing')"><g:message code="save"/></button>
                </div>
            </div>
        </div>
    </div>
</form>