<%@ page import="traction.department.Department; traction.vehicle.Vehicle" contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="vehicle.new"/></title>
    <asset:javascript src="vehicle/index.js"/>
    <style>
    .select2-container--bootstrap-5.select2-container--disabled .select2-selection,
    .select2-container--bootstrap-5.select2-container--disabled.select2-container--focus .select2-selection {
        cursor: initial;
    }

    .btn-cancel:hover {
        transition: opacity 0.5s;
        opacity: 0.75;
    }

    .select2 {
        width: calc(100% - 192px) !important;
    }

    .select2, .select2-selection {
        height: 39px !important;
    }
    </style>
</head>

<body>
<div class="container-fluid p-4 mb-5 pb-5">
    <div class="d-flex justify-content-between mb-5">
        <h2 class="d-flex align-items-center"><!--<i class="fa-solid fa-circle-question f-18 me-2"></i>--><g:message
                code="vehicle.create"/></h2>

    </div>

    <g:render template="/vehicle/createBody" model="[returnMethod: returnMethod, cotationId: cotationId, makes: makes, categories: categories]"/>
</div>

<div class="d-flex justify-content-end p-4 position-fixed bg-body"
     style="box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.15);bottom: 0;width: calc(100% - 72px);">
    <div class="d-flex h-fit-content">
        <a role="button" class="btn btn-cancel text-primary border-primary rounded-pill py-2 px-3 lh-sm me-2 fw-bold"
           onclick="history.back()" style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"><g:message
                code="cancel"/></a>
        <a role="button" class="btn btn-save btn-primary rounded-pill fw-bold py-2 px-3 lh-sm" onclick="saveVehicle();"
           style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"><g:message code="save"/></a>
    </div>
</div>
</body>
</html>