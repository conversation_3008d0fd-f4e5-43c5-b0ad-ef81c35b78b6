<%@ page contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <asset:javascript src="lib/xterm/xterm.min.js"/>
    <asset:stylesheet src="lib/xterm/xterm.css"/>
    <asset:javascript src="terminal/tractionssh.js"/>
    <style>
    .bash {
        background-color: black;
        color: grey;
        font-family: Consolas, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace;
    }
    </style>
</head>

<body>
<div class=" border p-4 m-4">
    <h1 class="bd-bottom-grey">Terminal Lars</h1>
</div>
<div class=" border p-4 m-4">
    <div class="row">
        <div class="col-12">
            <div id="terminal"></div>
        </div>

        <div class="col">
            <div class="row">
                <div class="col-sm-2 mb-3">
                    <label class="required"><g:message code="host"/></label>
                    <input type="text" id="host" class="form-control" name="host" value="${externalLogin?.host}">
                </div>

                <div class="col-sm-2 mb-3">
                    <label class="required"><g:message code="port"/></label>
                    <input type="text" id="port" class="form-control" name="port" value="${externalLogin?.port}">
                </div>

                <div class="col-sm-2 mb-3">
                    <label class="required"><g:message code="user"/></label>
                    <input type="text" id="user" class="form-control" name="user" value="${externalLogin?.userSSH}">
                </div>

                <div class="col-sm-2 mb-3">
                    <label class="required"><g:message code="user.password"/></label>
                    <input type="text" id="password" class="form-control" name="password" value="${externalLogin?.passwordSSH}">
                </div>

                <div class="col-sm-2 mb-3">
                    <label><g:message code="delay"/></label>
                    <input type="text" id="delay" class="form-control" name="delay" value="${externalLogin?.delay}">
                </div>

                <div class="col-sm-2 mb-3">
                    <label><g:message code="server.host.key"/></label>
                    <input type="text" id="serverHostKey" class="form-control" name="serverHostKey" value="${externalLogin?.serverHostKey}">
                </div>

                <div class="col-sm-3 mb-3">
                    <button id="login" class="btn btn-primary"><g:message code="auth.login"/></button>
                    <button id="logout" class="btn btn-danger"><g:message code="auth.logout"/></button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

</html>