<html>
<head>
    <meta name="layout" content="mainAuth"/>
    <asset:link rel="icon" href="/skins/favicon/${grailsApplication.config.getProperty("traction.skin") ?: ''}.svg"
                type="image/png"/>
    <title><g:message code='auth.login'/></title>
</head>

<body>
<form action="${postUrl ?: '/login/authenticate'}" method="POST" id="loginForm">

    <div class="auth-box">

        <div class="header-section">
            <span class="f-24 text-uppercase fw-medium mb-3" style="color:#8D9FB0; height: 24PX;"><g:message
                    code="ensure.forward.motion.with"/></span>
            <asset:image src="/skins/login/traction.svg" alt="traction"
                         style="width: 380px; height: 80px; align-self: center; "/>
        </div>

        <g:if test='${flash.message}'>
            <div class="m-2">
                <small class="errorMessage pb-3">${flash.message}</small>
            </div>
        </g:if>
        <g:set var="configService" bean="configService"/>
        <g:if test="${configService.get("SERVER", "server.auth.warning") != ""}">
            <div class="alert alert-warning m-2">
                <small class="pb-3">${configService.get("SERVER", "server.auth.warning")}</small>
            </div>
        </g:if>
        <h3 class="text-center text-black f-16 fw-bold"><g:message code="auth.signin"/></h3>

        <input type="text" class="form-control" name="${usernameParameter ?: 'username'}"
               placeholder="<g:message code="user.username"/>" id="username" autofocus
               autocomplete="off"/>

        <div class="pt-2 d-flex align-items-center position-relative">
            <input type="password" class="form-control ps-3 pe-1" name="${passwordParameter ?: 'password'}"
                   placeholder="<g:message code="user.password"/>" id="password"
                   autocomplete="off"/>
            <i class="fa fa-eye float-right text-primary position-absolute" style="right:12px;" id="visibility-icon"
               onclick="toggleVisibility()"></i>
        </div>

        <div class="d-flex w-100 my-4">
            <input class="btn btn-primary btn-md shadow-sm fw-bold w-100" type="submit" id="submit"
                   value="${message(code: 'springSecurity.login.button')}"/>
        </div>

        <div class="d-flex align-items-center justify-content-center">
            <div class="w-50 me-4">
                <div class="d-flex align-items-center justify-content-end gap-2" id="remember_me_holder">
                    <input type="checkbox" class="form-check-input m-0 border-primary" value="true"
                           name="${rememberMeParameter ?: 'remember-me'}"
                           id="remember_me" <g:if test='${hasCookie}'>checked="checked"</g:if>/>
                    <label for="remember_me" class="text-black"><g:message
                            code='springSecurity.login.remember.me.label'/></label>
                </div>
            </div>

            <div class="w-50 text-start ms-4">
                <a href="<g:createLink absolute="true" controller="register" action="forgotPassword"/>"
                   class="f-16 text-decoration-underline" style="text-underline-offset: 1px;"><g:message
                        code="auth.forgotyourpassword"/></a>
            </div>
        </div>
    </div>
</form>
<script>
    function toggleVisibility() {
        let x = $('#password');
        if (x.attr('type') === 'password') {
            x.prop('type', 'text');
            $('#visibility-icon').addClass("fa-eye-slash")
            $('#visibility-icon').removeClass("fa-eye")
        } else {
            x.prop('type', 'password');
            $('#visibility-icon').removeClass("fa-eye-slash")
            $('#visibility-icon').addClass("fa-eye")
        }
    }
</script>
</body>
</html>