<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="${className}.list"/></title>
    <asset:stylesheet src="clientList/index.css"/>
    <asset:javascript src="clientList/index.js"/>
</head>

<body>
<script>
    <g:if test="${queryBuilder}">
    <g:if test="${filter}">
    window.history.pushState({}, document.title, window.location.pathname);
    UserStorage.set('client-table-filter-builder', ${raw(filter.json)});
    </g:if>

    function getCookieQueryBuilderRules() {
        return UserStorage.getJSON('client-table-filter-builder');
    }

    </g:if>
    ClientList.className = '${className}';
    window.clientListModal = {
        buttons: [
            <g:if test="${actions}">
            <g:each var="a" in="${actions}">
            {
                id: 'action${a.id}',
                label: `${a.name} <g:if test="${a.description}"><i class="mdi mdi-information" title="${a.description}" data-toggle="tooltip"></i></g:if>`,
                icon: 'mdi mdi-cogs',
                action: function () {
                    ClientList.executeAction(${a.id});
                }
            },
            </g:each>
            </g:if>
            <g:else>
            {
                id: 'createAction',
                label: 'action.create',
                icon: 'mdi mdi-plus',
                action() {
                    location.href = '<g:createLink absolute="true" controller="action" action="index"/>';
                }
            },
            </g:else>
            /*{
                id: 'edit',
                label: 'dataTable.edit.selected',
                icon: 'mdi mdi-pencil',
                action() {
                    if (ClientList.dataTable.rows({selected: true}).count()) {
                        ClientList.editor.edit({selected: true}, {
                            buttons: [
                                {
                                    label: I18N.m('cancel'), className: 'btn-secondary me-2', fn: function () {
                                        this.close();
                                    }
                                },
                                {
                                    label: I18N.m('dataTable.edit.selected'),
                                    className: 'btn-primary',
                                    fn: function () {
                                        this.submit();
                                    }
                                }
                            ]
                        });
                    } else {
                        $.notify(I18N.m('filtermenu.actions.noSelected'), 'error');
                    }
                }
            },*/
            {
                id: 'toggleAdvancedFilter',
                label: '${queryBuilder ? 'show.basic.filters' : 'show.advanced.filters'}',
                icon: 'mdi mdi-refresh',
                action() {
                    location.href = '?queryBuilder=${!queryBuilder}';
                }
            },
            {
                id: 'communication',
                label: 'filtermenu.communication',
                icon: 'mdi mdi-forum',
                action() {
                    ClientList.dataTable.selection.invokeIfNonEmpty(function () {
                        ClientList.openBulkCommunication();
                    })
                }
            }
        ]
    };
</script>

<g:if test="${queryBuilder}">
    <div id="selects-div" style="display: none;">
        <div class="w-100 clearfix">
            <select class="float-right w-50 form-control" id="builder-select"
                    onchange="ClientList.setQueryBuilderFilter()">
                <option value="0"><g:message code="filter.select.to.load"/></option>
                <g:each var="f" in="${filters}">
                    <option value="${f.id}">${f.name}</option>
                </g:each>
            </select>
        </div>

        <div class="w-100 pb-2">
            <container:queryBuilder id="builder" type="${traction.Filter.Type.CLIENT}"
                                    getInitialRulesFunction="getCookieQueryBuilderRules"/>
        </div>
    </div>
</g:if>

<div class="p-5">
    <div class="mb-4 d-flex justify-content-between">
        <h2>
            <g:message code="${className}.list"/>
            <small>(<g:message code="${queryBuilder ? 'advanced.filters' : 'basic.filters'}"/>)</small>
        </h2>
        <div class="align-self-center">
            <a onclick="TractionModal.show({
                url: '/client/modalNew?enterprise=${isEnterprise}',
                backdrop: 'static'
            });" class="btn border rounded-1 btn-primary-important fw-bold" role="button">
                <i class="bi fa-solid fa-circle-plus me-2"></i>
                <g:message code="add"/>
            </a>
        </div>
    </div>
    <table class="table table-striped border" style="width: 100%;" cellspacing="0" id="client-datatable">
        <thead>
        <tr>
            <th class="check"><i class="mdi mdi-format-list-checks"></i></th>
            <th class="Client_id min-w-100">
                <g:message code="id"/>
            </th>
            <th class="Client_externalIdentifiers min-w-200">
                <g:message code="extID"/>
            </th>
            <th class="Client_date">
                <g:message code="client.date"/>
            </th>
            <th class="Client_dateMOD">
                <g:message code="client.dateMOD"/>
            </th>
            <th class="Enterprise_name min-w-200">
                <g:message code="enterprise.name"/>
            </th>
            <th class="IndividualClient_firstname min-w-150">
                <g:message code="client.firstname"/>
            </th>
            <th class="IndividualClient_lastname">
                <g:message code="client.lastname"/>
            </th>
            <th class="Client_email min-w-100">
                <g:message code="client.email"/>
            </th>
            <th class="Client_emailValidationResult min-w-200">
                <g:message code="client.emailValidationResult"/>
            </th>
            <th class="Client_altEmail min-w-200">
                <g:message code="client.altEmail"/>
            </th>
            <th class="Client_phone min-w-100">
                <g:message code="client.phone"/>
            </th>
            <th class="Client_duplicatedPhone min-w-200">
                <g:message code="client.duplicatedPhone"/>
            </th>
            <th class="IndividualClient_phonecell min-w-150">
                <g:message code="client.phonecell"/>
            </th>
            <th class="IndividualClient_duplicatedPhonecell min-w-300">
                <g:message code="client.duplicatedPhonecell"/>
            </th>
            <th class="Client_interests">
                <g:message code="client.interests"/>
            </th>
            <th class="Client_status">
                <g:message code="client.status"/>
            </th>
            <th class="Client_balance min-w-150">
                <g:message code="client.balance"/>
            </th>
            <th class="Client_roopen min-w-150">
                <g:message code="client.roopen"/>
            </th>
            <th class="Client_media">
                <g:message code="client.media"/>
            </th>
            <th class="Client_origin">
                <g:message code="client.origin"/>
            </th>
        </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>
</body>
</html>
