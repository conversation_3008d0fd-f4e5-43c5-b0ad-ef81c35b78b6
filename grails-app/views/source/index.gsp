<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="report.source"/></title>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0"></script>
    <asset:javascript src="source/index.js"/>
    <asset:stylesheet src="source/index.css"/>
</head>

<body>

<div class="m-4 d-flex">
    <h4 class="text-uppercase mb-5 w-100"><g:message code="source.opportunity.report"/></h4>

    <div id="summary-source-filters" class="d-flex flex-row-reverse">
        <button id="updateFilterBtn" class="btn btn-primary fw-bold w-100" onclick="applyFilter()">
            <g:message code="filter.apply"/>
        </button>
        <select id="lastXMonthsSelectTotal" class="form-select me-2">
            <option value="6"><g:message code="last.x.months" args="${[6]}"/></option>
            <option value="12"><g:message code="last.x.months" args="${[12]}"/></option>
            <option value="18"><g:message code="last.x.months" args="${[18]}"/></option>
            <option value="24"><g:message code="last.x.months" args="${[24]}"/></option>
        </select>
        <container:filterMenu filterMenu="${filterMenu}"/>
    </div>
</div>

<div class="m-4" id="total-chart">
    <div class="source-chart-container divLoader">
        <div class="loader"></div>
        <canvas id="bar-total-origin"></canvas>
    </div>
</div>

<div class="m-4 d-flex">
    <h4 class="text-uppercase mb-5 w-100"><g:message code="source.opportunity.report.bysource"/></h4>

    <div class="d-flex flex-row-reverse">
        <select id="lastXMonthsSelect" onchange="applyFilterBySource();" class="form-control w-25 form-control-sm">
            <option value="6"><g:message code="last.x.months" args="${[6]}"/></option>
            <option value="12"><g:message code="last.x.months" args="${[12]}"/></option>
            <option value="18"><g:message code="last.x.months" args="${[18]}"/></option>
            <option value="24"><g:message code="last.x.months" args="${[24]}"/></option>
        </select>
    </div>
</div>

<div class="m-4" id="sourcesCharts">
    <g:each var="origin" in="${origins}">
        <h4 class="mb-3">${origin.name}</h4>
        <div class="bar-sold-chart-container">
            <container:sourceReport origin="${origin}" />
        </div>
    </g:each>
</div>
</body>
</html>