<%@ page import="traction.I18N" %>
<div class="p-2 mb-3 file-folder-content file-content rounded" id="file-content-${file.id}">


    <div class="d-flex bd-highlight">
        <div class="p-2 bd-highlight file-data-preview-image">
            <img alt="" src="<g:createLink absolute="true" controller="Web" action="getPreviewFile" id="${file.id}"/>"/>
        </div>

        <div class=" p-2 flex-grow-1 bd-highlight">
            <div class="document-file-title">
                <input type="text" class="folder-file-name form-control" id="file-name-${file.id}"
                       value="${file.name}" disabled>
            </div>

            <div class="document-file-subtitle">${file.author}</div>

            <div class="document-file-subtitle">${file.date.format(traction.I18N.m("dateFormatWithTime"))}</div>
        </div>

        <div class="p-2 bd-highlight f-16">

            <a class="file-view  btn-icon text-primary me-3 cursor" id="${file.id}">
                <i class="fas fa-eye"></i>
            </a>

            <a class="file-download  btn-icon text-primary me-3 cursor" id="file-download-${file.id}">
                <i class="fas fa-arrow-down-to-bracket"></i>
            </a>

            <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_DOCUMENT_WRITE]">
                <a class="file-rename  btn-icon text-primary me-3 cursor" id="file-rename-${file.id}">
                    <i id="file-rename-icon-${file.id}" class="fa-light fa-pen"></i>
                </a>
                <a class="file-delete  btn-icon text-primary cursor" id="file-delete-${file.id}">
                    <i class="fa-light fa-trash"></i>
                </a>
            </permission:ifHasPermissions>
        </div>
    </div>
</div>

