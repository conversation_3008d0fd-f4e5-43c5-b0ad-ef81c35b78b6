import traction.opportunity.Opportunity

model {
        Opportunity opportunity
}

json g.render(opportunity, [includes: ["id", "name"]]) {
        userSales tmpl.user(opportunity.getUserSales())
        userSales2 tmpl.user(opportunity.getUserSales2())
        userFni tmpl.user(opportunity.getUserFni())
        userService tmpl.user(opportunity.getUserService())
        userParts tmpl.user(opportunity.getUserParts())
        userShipping tmpl.user(opportunity.getUserShipping())
        userShop tmpl.user(opportunity.getUserShop())
        userBdc tmpl.user(opportunity.getUserBdc())
        userTech tmpl.user(opportunity.getUserTech())
}