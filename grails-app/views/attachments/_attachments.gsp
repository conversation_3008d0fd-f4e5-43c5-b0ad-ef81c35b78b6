<asset:javascript src="attachments/attachments.js"/>

<div id="${type}AttachmentContainer">
    <div class="container-fluid p-0">
        <div class="row mx-0">
            <div class="col-auto ps-0">
                <h4 class="mb-5 text-uppercase"><g:message code="client.documents"/></h4>
            </div>
            <div class="col text-end pe-0 d-flex justify-content-end">
                <a class="btn btn-outline-primary text-primary rounded-1 fw-bold me-2 py-2 px-3 bg-body h-fit-content" style="height: 39px;box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);" href="<g:createLink controller="fileData" action="downloadAttachments" params="[id: type == 'vehicle' ? vehicle?.id : (type == 'client' ? client?.id : (type == 'trade' ? trade?.id : (type == 'opportunityoptions' ? opportunityoptions?.id : opportunity?.id))), type: type]"/>" download>
                    <i class="fa-solid fa-arrow-down-to-bracket me-2"></i>
                    <g:message code="vehicle.pictures.download.all"/>
                </a>
                <div class="form-inline" id="uploadContainer_${type}AttachmentContainer">
                    <a class="btn btn-primary addAttachmentBtn fs-6" style="height: 39px;" role="button"
                       onclick="$('#uploadId_${type}AttachmentContainer').click()">
                        <div class="d-none spinner-border spinner-border-sm text-secondary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <i class="bi fa-solid fa-circle-plus me-2"></i><g:message code="add"/>
                    </a>
                    <input class="attachment-id" hidden value="${type == 'vehicle' ? vehicle?.id : (type == 'client' ? client?.id : (type == 'trade' ? trade?.id : (type == 'opportunityoptions' ? opportunityoptions?.id : opportunity?.id)))}"/>
                    <input class="attachment-type" hidden value="${type.encodeAsJavaScript()}"/>
                    <input class="d-none file" name="uploadId_${type}AttachmentContainer"
                           onchange="Attachments.addAttachments(${type == 'vehicle' ? vehicle?.id : (type == 'client' ? client?.id : (type == 'trade' ? trade?.id : (type == 'opportunityoptions' ? opportunityoptions?.id : opportunity?.id)))}, '${type.encodeAsJavaScript()}')" id="uploadId_${type}AttachmentContainer" type="file" multiple>
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex flex-column gap-4">
        <g:if test="${groupedFiles.size() > 0}">
            <g:each var="entry" in="${groupedFiles}">
                <g:if test="${entry.key}">
                    <attachmentCardTagLib:row month="${entry.key}" files="${entry.value}"/>
                </g:if>
            </g:each>
        </g:if>
        <g:else>
            <p class="text-body-tertiary mb-0"><g:message code="client.documents.none"/></p>
        </g:else>
    </div>
</div>

<script>
    function editAttachment(id) {
        $("div.edit-attachment-" + id).toggleClass('d-none d-flex');
        $("div.read-attachment-" + id).toggleClass('d-none d-flex');
        $("input.edit-attachment-" + id).toggleClass('d-none d-flex');
    }
</script>