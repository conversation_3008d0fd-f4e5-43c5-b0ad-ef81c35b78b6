import traction.DateUtils
import traction.service.ServiceSchedule

model {
    ServiceSchedule schedule
}

//System.out.println("WHAT schedule binding : $binding.variables")
json {
    id schedule.id
    //after talking with mobile team we think we should either test the rendering results
    labor schedule.labor
            ? g.render(template: "/v3_0_0/apiWorkOrder/labor", model: [labor: schedule.labor])
            : [job: [workOrder: [serialNumber: "", client: [id: schedule.pendingClientId, firstname: schedule.pendingClient?.firstname]], percentCompleted: 0.0]]
    overlapping schedule.overlapping
    outsideWorkSchedule schedule.outsideWorkSchedule
    color schedule.getColor()
    borderColor schedule.getBorderColor()
    pendingUserInitials schedule.pendingUser?.initial
    pendingUser schedule.pendingUser?.fullName
    pendingClientId schedule.pendingClient?.id
    pendingWorkOrderRoID schedule.pendingWorkOrderRoID
    pendingWorkOrderType schedule.pendingWorkOrderType?.id
    pendingJobNo schedule.pendingJobNo
    pendingClientName schedule.pendingClient?.fullName
    pendingNote schedule.pendingNote
    workOrderPromisedDate schedule.labor?.job?.workOrder?.promisedDate?.toString() ?: ""
    workOrderPromisedDate DateUtils.getCalendarBgColor(schedule.labor?.job?.workOrder?.promisedDate)
    status schedule.status?.id
}
