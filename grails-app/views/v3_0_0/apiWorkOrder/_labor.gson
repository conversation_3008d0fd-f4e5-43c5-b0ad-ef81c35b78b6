import traction.service.WorkOrderJobLabor

model {
    WorkOrderJobLabor labor
}

//System.out.println("WHAT labor binding : $binding.variables")
json {
    id labor.id
    job labor.job ? g.render(template: "/v3_0_0/apiWorkOrder/job", model: [job: labor.job]) : [
            workOrder: []
    ]
    hours labor.hours
    tech g.render(template: "/v3_0_0/apiUser/show", model: [user: labor.tech, expand: []])

    //description labor.description
    //note labor.note
    //tags g.render(template: "/v3_0_0/apiWorkOrder/tag", collection: labor.tags, var: "tag")
    //rate labor.rate
    //subtotal labor.subtotal
    //rateCost labor.rateCost
    //hoursCost labor.hoursCost
    //subtotalCost labor.subtotalCost
    //taxed labor.taxed
    //displayHours labor.displayHours
    //displayNote labor.displayNote
}
