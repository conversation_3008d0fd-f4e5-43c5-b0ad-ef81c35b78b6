import traction.ConfigService
import traction.cotation.CotationElement
import traction.opportunity.OpportunityService

//Template used to send the cotation element to the mobile app
model {
    CotationElement cotationElement
    List<String> expand
    ConfigService configService
    OpportunityService opportunityService
}

json g.render(cotationElement, [expand: [], excludes: ['trade', 'opportunity', 'cotation']]) {
    if (expand.contains("trade") && cotationElement.trade != null) {
        trade g.render(template: "/v3_0_0/apiTrade/show", model: [
                trade             : cotationElement.trade,
                configService     : configService,
                opportunityService: opportunityService,
                expanded          : ["vehicle"]
        ])
    } else {
        trade cotationElement.trade?.id
    }

    if (expand.contains("opportunity") && cotationElement.opportunity != null) {
        opportunity tmpl.'/v3_0_0/apiOpportunity/show'([
                opportunity       : cotationElement.opportunity,
                configService     : configService,
                opportunityService: opportunityService,
                expand            : ["workflowData", "card", "vehicle"]
        ])
    } else {
        opportunity cotationElement.opportunity?.id
    }

    if (expand.contains("cotation") && cotationElement.cotation != null) {
        cotation tmpl.'/v3_0_0/apiCotation/cotation'([
                cotation: cotationElement.cotation,
                expand  : []
        ])
    } else {
        cotation cotationElement.cotation?.id
    }

    if (expand.contains("surplusMinimumPrice")) {
        surplusMinimumPrice cotationElement.getSurplusMinimumPrice()
    }
}
