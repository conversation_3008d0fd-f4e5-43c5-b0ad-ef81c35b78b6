import traction.security.User

model {
    User user
}

String avatarString = user.image ? g.link(controller: "web", action: "getFile", id: user.image?.id, absolute: true) : ""

json {
    id user.id
    avatar avatarString
    firstname user.firstname
    lastname user.lastname
    username user.username
    color user.color
    email user.email
    userGroup user.userGroup?.id
    extensions user.extensions.collect { it.ext }
}
