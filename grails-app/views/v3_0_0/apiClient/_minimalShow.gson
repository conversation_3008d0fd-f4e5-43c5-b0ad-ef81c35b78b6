import traction.client.Client
import traction.client.IndividualClient

model {
    Client client
}

//System.out.println("WHAT client binding : $binding.variables")
String imageSrc = client?.image ? g.link(controller: "web", action: "getFile", id: client?.image?.id, absolute: true) : ""

//So that the client.show isnt recursive and could possibly end in a circular loop
json {
    type client.getClass().getSimpleName()
    id client.id
    phone client.phone
    email client.email
    roOpen client.roopen
    balance client.balance
    image imageSrc
    color client.getColor()
    accentColor client.getAccentColor()
    if (client.address) {
        address {
            city client.address.city
            country client.address.country
            line1 client.address.line1
            line2 client.address.line2
            postalCode client.address.postalCode
            state client.address.state
        }
    }

    if (client.isIndividualClient()) {
        IndividualClient individualClient = client.asIndividualClient()
        gender individualClient.gender.id
        status individualClient.status.id
        firstname individualClient.firstname
        lastname individualClient.lastname
        phonecell individualClient.phonecell
        birthday individualClient.birthday
        language individualClient.communicationLanguage.id
    }
}
