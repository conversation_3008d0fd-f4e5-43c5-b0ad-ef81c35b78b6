import traction.ColorUtils
import traction.client.Client
import traction.client.Enterprise
import traction.client.IndividualClient
import traction.client.Relationship
import traction.history.History

model {
    Client client
    List<String> expand
}

//System.out.println("WHAT client binding : $binding.variables")
Comparator<History> comparator = new Comparator<History>() {
    @Override
    int compare(History o1, History o2) {
        return o1.date.compareTo(o2.date)
    }
}
String dateLastAction = null
if (client.histories != null && client.histories.size() > 0) {
    dateLastAction = client.histories.max(comparator).date.toString()
}
String imageSrc = client?.image ? g.link(controller: "web", action: "getFile", id: client?.image?.id, absolute: true) : ""
Set<Relationship> relationShipList = client?.getRelationShips()

json {
    id client.id
    type client.getClass().getSimpleName()
    status client.status.id
    email client.email
    phone client.phone
    duplicatedPhone client.duplicatedPhone
    language client.communicationLanguage.id
    image imageSrc
    instagramLink client.instagramLink
    color client.getColor()
    accentColor client.getAccentColor()
    balance client.balance
    roOpen client.roopen
    creationDate client.date
    lastModificationDate client.dateMOD
    lastActionDate dateLastAction
    // emailValidationResult client.emailValidationResult.id //TODO remttre eventuellement byId avec v3.0.1
    emailValidationResult client.emailValidationResult

    if (client.address) {
        address {
            city client.address.city
            country client.address.country
            line1 client.address.line1
            line2 client.address.line2
            postalCode client.address.postalCode
            state client.address.state
        }
    }

    if (client.isIndividualClient()) {
        IndividualClient individualClient = client.asIndividualClient()
        firstname individualClient.firstname
        lastname individualClient.lastname
        driverLicense individualClient.driverLicense
        gender individualClient.gender.id
        phonecell individualClient.phonecell
        duplicatedCell individualClient.duplicatedPhonecell
        birthday individualClient.birthday
    }

    if (client.isEnterprise()) {
        Enterprise enterprise = client.asEnterprise()
        name enterprise.name
        provider enterprise.provider
        contacts enterprise.contacts.collect {
            [
                    id                   : it.id,
                    name                 : it.name,
                    title                : it.title,
                    email                : it.email,
                    emailValidationResult: it.emailValidationResult,
                    color                : it.getColor(),
                    accentColor          : ColorUtils.contrastColor(it.getColor()),
                    phone                : [
                            type  : it.phone?.type?.id,
                            number: it.phone?.number,
                            ext   : it.phone?.ext,
                    ]
            ]
        }
    }


    if (expand.contains("contactInformations") && client.contactInformations) {
        contactInformations client.contactInformations.collect {
            [
                    id          : it.id,
                    label       : it.label,
                    name        : it.name,
                    phone       : [
                            type  : it.phone?.type?.id,
                            number: it.phone?.number,
                            ext   : it.phone?.ext,
                    ],
                    fax         : [
                            type  : it.fax?.type?.id,
                            number: it.fax?.number,
                            ext   : it.fax?.ext,
                    ],
                    email       : it.email,
                    instructions: it.instructions,
                    address     : [
                            line1     : it.address?.line1,
                            line2     : it.address?.line2,
                            city      : it.address?.city,
                            state     : it.address?.state,
                            country   : it.address?.country,
                            postalCode: it.address?.postalCode,
                    ]
            ]
        }
    }

    if (expand.contains("relationships") && relationShipList) {
        relationships g.render(template: "/v3_0_0/apiClient/relationshipShow", collection: relationShipList ?: [], var: 'relationship')
    }

    if (expand.contains("opportunities")) {
        opportunities g.render(template: "/v3_0_0/apiOpportunity/show",
                collection: client.opportunities ?: [], var: 'opportunity',
                model: ['expand': ['status', 'category', 'assigned', 'lasts', 'origin']])
    } else if (client.opportunities?.size() > 0) {
        opportunities client.opportunities.collect {
            [id: it.id]
        }
    }

    if (expand.contains("interests")) {
        interests g.render(template: "/v3_0_0/apiClient/interest", collection: client.interests ?: [], var: "interest")
    }

    if (expand.contains("externalIdentifiers") && client.externalIdentifiers) {
        externalIdentifiers client.externalIdentifiers.collect {
            [
                    id         : it.id,
                    extId      : it.extId,
                    dms        : [
                            id         : it.dms.id,
                            name       : it.dms.name,
                            source     : it.dms.source.name,
                            dmsId      : it.dms.dmsId,
                            departments: it.dms.departments.collect { dept ->
                                [
                                        id  : dept.id,
                                        name: dept.name
                                ]
                            }
                    ],
                    client     : it.client?.id,
                    opportunity: it.opportunity?.id,
                    user       : it.user?.id,
                    vehicle    : it.vehicle?.id
            ]
        }
    }

    if (expand.contains('trades') && client.getActiveTrades()?.size() > 0) {
        trades client.getActiveTrades()?.collect {
            [id: it.id]
        }
    }
}
