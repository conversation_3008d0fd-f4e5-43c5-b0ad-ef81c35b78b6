import traction.vehicle.VehicleEvaluation

model {
    VehicleEvaluation vehicleEvaluation
}

json {
    id vehicleEvaluation?.id
    department {
        id vehicleEvaluation?.department?.id
        name vehicleEvaluation?.department?.name
    }
    vehicle {
        id vehicleEvaluation?.vehicle?.id
        stockNumber vehicleEvaluation?.vehicle?.stockNumber
    }
    isActive vehicleEvaluation?.isActive
    priceAccepted vehicleEvaluation?.priceAccepted
    priceAsked vehicleEvaluation?.priceAsked
    priceLink vehicleEvaluation?.priceLink
    priceReal vehicleEvaluation?.priceReal
    priceDisplay vehicleEvaluation?.priceDisplay
}
