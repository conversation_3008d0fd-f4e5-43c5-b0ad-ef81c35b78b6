import traction.file.FileData
import traction.vehicle.Vehicle

model {
    Vehicle vehicle
}

FileData firstWatermarkedPicture = vehicle.asInheritedVehicle().getInheritedPictures().getFirstWatermarkedPicture()?.asFileData()

json {
    id vehicle.id
    type vehicle.type?.name()
    status vehicle.status?.name()
    year vehicle.year
    if (vehicle.client != null) {
        client g.render(template: "/v3_0_0/apiClient/minimalShow", model: [client: vehicle.client, expand: []])
    }
    if (vehicle.rootVehicle) {
        rootVehicle {
            id vehicle.rootVehicle.id
        }
    }
    stockNumber vehicle.stockNumber
    serialNumber vehicle.serialNumber
    serialNumber2 vehicle.serialNumber2
    make vehicle.make
    model vehicle.model
    modelCode vehicle.modelCode

    //Location or POSITION
    if (vehicle.department) {
        department {
            id vehicle.department.id
            name vehicle.department.name
        }
    }
    location vehicle.location

    /**
     * Propriétés ci-dessous calculées par le backend, peuvent contenir des valeurs du vehicle root et/ou inherited
     *
     */
    if (firstWatermarkedPicture != null) {
        firstPicture {
            id firstWatermarkedPicture.id
        }
    }

}
