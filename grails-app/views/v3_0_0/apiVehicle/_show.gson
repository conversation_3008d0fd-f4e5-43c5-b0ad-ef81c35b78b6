import traction.file.FileData
import traction.vehicle.Vehicle

model {
    Vehicle vehicle
    List<String> expand
}

FileData firstWatermarkedPicture = vehicle.asInheritedVehicle().getInheritedPictures().getFirstWatermarkedPicture()?.asFileData()

json {
    deleted vehicle.deleted
    isUsed vehicle.isUsed
    id vehicle.id
    year vehicle.year
    displayYear vehicle.displayYear
    if (vehicle.client != null) {
        client g.render(template: "/v3_0_0/apiClient/minimalShow", model: [client: vehicle.client, expand: []])
    }
    if (expand != null && expand.contains("root") && vehicle.rootVehicle != null) {
        rootVehicle g.render(template: "/v3_0_0/apiVehicle/show", model: [vehicle: vehicle.rootVehicle, expand: []])
    }

    if (vehicle.group) {
        group g.render(template: "/v3_0_0/apiVehicle/groupShow", model: [group: vehicle.group])
    }
    /*
    //affichage vehicle.affichage
    stockType vehicle.stockType?.id
     */
    stockNumber vehicle.stockNumber
    serialNumber vehicle.serialNumber
    serialNumber2 vehicle.serialNumber2
    licenseNumber vehicle.licenseNumber
    category vehicle.category
    subCategory vehicle.subCategory
    make vehicle.make
    model vehicle.model
    modelCode vehicle.modelCode
    line vehicle.line
    suffix vehicle.suffix

    description vehicle.description
    options vehicle.options

    if (vehicle.pictures != null && vehicle.pictures.size() > 0) {
        List<FileData> pictureList = new ArrayList<FileData>()
        pictureList.addAll(vehicle.pictures)
        pictures g.render(template: "/v3_0_0/apiFileData/list", model: ["fileDataList": pictureList])
    }

    //Location or POSITION
    if (vehicle.department) {
        department {
            id vehicle.department.id
            name vehicle.department.name
            date vehicle.department.date
        }
    }
    location vehicle.location
    keyLocation vehicle.keyLocation

    //Aesthetic
    exteriorColor vehicle.exteriorColor
    if (vehicle.configuratorData) {
        configuratorData {
            style vehicle.configuratorData.style
            colorSet vehicle.configuratorData.colorSet
            color vehicle.configuratorData.color
            material vehicle.configuratorData.material
            configurable vehicle.configuratorData.configurable
            decor vehicle.configuratorData.decor
            model vehicle.configuratorData.model
            interior vehicle.configuratorData.interior
            genre vehicle.configuratorData.genre
            size vehicle.configuratorData.size
        }
    }
    if (vehicle.compiledData) {
        compiledData {
            formulaPrice1 vehicle.compiledData.formulaPrice1
            formulaPrice2 vehicle.compiledData.formulaPrice2
            formulaPrice3 vehicle.compiledData.formulaPrice3
            formulaPrice4 vehicle.compiledData.formulaPrice4
            formulaPrice5 vehicle.compiledData.formulaPrice5
            formulaPrice6 vehicle.compiledData.formulaPrice6
            formulaPrice7 vehicle.compiledData.formulaPrice7
            formulaPrice8 vehicle.compiledData.formulaPrice8
            formulaPdiPrice vehicle.compiledData.formulaPdiPrice
            formulaFreightPrice vehicle.compiledData.formulaFreightPrice
            minimumPrice vehicle.compiledData.minimumPrice
            tractionPrice vehicle.compiledData.tractionPrice
            optionsFormulaPrice vehicle.compiledData.optionsFormulaPrice
            costRealPrice vehicle.compiledData.costRealPrice
            barredPrice vehicle.compiledData.barredPrice
            formulaBarredPrice vehicle.compiledData.formulaBarredPrice
            displayPrice vehicle.compiledData.displayPrice
            formulaDisplayPrice vehicle.compiledData.formulaDisplayPrice
            weeklyPayment vehicle.compiledData.weeklyPayment
            monthlyPayment vehicle.compiledData.monthlyPayment
            estimatedLoanAmount vehicle.compiledData.estimatedLoanAmount
            formulaDescription vehicle.compiledData.formulaDescription
            formulaShortDescription vehicle.compiledData.formulaShortDescription
            formulaDescriptionKijiji vehicle.compiledData.formulaDescriptionKijiji
            formulaDescriptionTrader vehicle.compiledData.formulaDescriptionTrader
            formulaTitre vehicle.compiledData.formulaTitre
            formulaCategoryMagento vehicle.compiledData.formulaCategoryMagento
        }
    }
    if (vehicle.magentoData) {
        magentoData {
            crsell vehicle.magentoData.crsell
            upsell vehicle.magentoData.upsell
            vehicleNavAll vehicle.magentoData.vehicleNavAll
            vehicleNavCur vehicle.magentoData.vehicleNavCur
            category vehicle.magentoData.category
            attrSet vehicle.magentoData.attrSet
            visibility vehicle.magentoData.visibility
            sku vehicle.magentoData.sku
            metaDescription vehicle.magentoData.metaDescription
            metaTitle vehicle.magentoData.metaTitle
            metaKey vehicle.magentoData.metaKey
            htmlH1 vehicle.magentoData.htmlH1
            htmlH2 vehicle.magentoData.htmlH2
            onWebSite vehicle.magentoData.onWebsite
        }
    }

    // DATES
    dateModified vehicle.dateModified
    dateLastBuild vehicle.dateLastBuild
    dateCreated vehicle.dateCreated
    dateManufactured vehicle.dateManufactured
    dateReceipted vehicle.dateReceipted
    dateRegistered vehicle.dateRegistered
    dateCommissioning vehicle.dateCommissioning
    dateSold vehicle.dateSold

    // AVAILABILITY
    dateOrder vehicle.dateOrder
    dateAvailability vehicle.dateAvailability
    dateDelivery vehicle.dateDelivery
    dateDue vehicle.dateDue
    datePossessed vehicle.datePossessed
    dateImport vehicle.dateImport

    // MEASURES
    if (vehicle.odometer) {
        odometer {
            value vehicle.odometer.value
            unit vehicle.odometer.unit
        }
    }
    if (vehicle.height) {
        height {
            value vehicle.height.value
            unit vehicle.height.unit
        }
    }
    if (vehicle.length) {
        length {
            value vehicle.length.value
            unit vehicle.length.unit
        }
    }
    if (vehicle.width) {
        width {
            value vehicle.width.value
            unit vehicle.width.unit
        }
    }
    if (vehicle.diameter) {
        diameter {
            value vehicle.diameter.value
            unit vehicle.diameter.unit
        }
    }
    if (vehicle.weight) {
        weight {
            value vehicle.weight.value
            unit vehicle.weight.unit
        }
    }

    //PRICES
    transportCost vehicle.transportCost
    transportDetail vehicle.transportDetail
    preparationCost vehicle.preparationCost
    preparationDetail vehicle.preparationDetail
    surchargeAmount vehicle.surchargeAmount
    cost vehicle.cost
    costGL vehicle.costGL
    dealerInvoice vehicle.dealerInvoice
    dealerNet vehicle.dealerNet
    msrp vehicle.msrp
    holdback vehicle.holdback
    demoDiscountAmount vehicle.demoDiscountAmount
    optionsPrice vehicle.optionsPrice
    optionsPrice2 vehicle.optionsPrice2
    customPrice1 vehicle.customPrice1
    customPrice2 vehicle.customPrice2
    customPrice3 vehicle.customPrice3
    priceAccepted vehicle.priceAccepted
    priceAsked vehicle.priceAsked
    priceLink vehicle.priceLink
    priceReal vehicle.priceReal
    priceDisplay vehicle.priceDisplay

    // VEHICLE EVALUATION
    if (vehicle.vehicleEvaluations != null && vehicle.vehicleEvaluations?.size() > 0) {
        //potentiellement renommer pour vehicleEvaluations et changer le frontend mobile après que versions en prod seront sup. à 3.2.10
        vehicleEvaluationList g.render(template: "/v3_0_0/apiVehicle/vehicleEvaluationList", model: [vehicleEvaluations: vehicle.vehicleEvaluations?.toList()])

        //TODO: Supprimer vehicleEvaluations lorsque toutes les versions en prod seront sup. à 3.2.10 
        vehicleEvaluations g.render(template: "/v3_0_0/apiVehicle/vehicleEvaluations", model: [vehicleEvaluations: vehicle.vehicleEvaluations?.toList()])

        //TODO: Supprimer currentVehicleEvaluation lorsque toutes les versions en prod seront sup. à 3.2.10 
        def _currentVehicleEvaluation = vehicle.vehicleEvaluations?.find { it.isActive }
        if (_currentVehicleEvaluation != null) {
            currentVehicleEvaluation g.render(template: "/v3_0_0/apiVehicle/vehicleEvaluation", model: [vehicleEvaluation: _currentVehicleEvaluation])
        }
    }

    // INSPECTIONS
    // inspections vehicle.getInspections().collect { it.id }

    // INTERESTS
    interests g.render(template: "/v3_0_0/apiClient/interest", collection: vehicle.interests ?: [], var: "interest")

    // Loan/Payment
    loanTermWeeks vehicle.loanTermWeeks
    loanTermMonths vehicle.loanTermMonths
    loanRate vehicle.loanRate

    // Warranties
    warrantyPrice vehicle.warrantyPrice
    warrantyTerm vehicle.warrantyTerm
    warrantyDescription vehicle.warrantyDescription

    // Website
    categoryKijiji vehicle.categoryKijiji
    categoryTrader vehicle.categoryTrader
    powergoid vehicle.powergoid
    iprofile vehicle.iprofile
    pixelguru vehicle.pixelguru
    clearance vehicle.clearance
    featured vehicle.featured
    reserved vehicle.reserved
    type vehicle.type?.name()
    status vehicle.status?.name()
    listingStatus vehicle.listingStatus?.name()
    videoUrl vehicle.videoUrl
    showroomUrl vehicle.showroomUrl

    useMsrpAsBarredPrice vehicle.useMsrpAsBarredPrice
    promoSpecialPrice vehicle.promoSpecialPrice

    // PROMOTIONS
    promoInternalAmount vehicle.promoInternalAmount
    promoInternalStartDate vehicle.promoInternalStartDate
    promoInternalEndDate vehicle.promoInternalEndDate
    promoManufacturerAmount vehicle.promoManufacturerAmount
    promoManufacturerStartDate vehicle.promoManufacturerStartDate
    promoManufacturerEndDate vehicle.promoManufacturerEndDate
    promoText vehicle.promoText
    promoTextStartDate vehicle.promoTextStartDate
    promoTextEndDate vehicle.promoTextEndDate


    /**
     * Propriétés ci-dessous calculées par le backend, peuvent contenir des valeurs du vehicle root et/ou inherited
     *
     */
    if (firstWatermarkedPicture != null) {
        firstPicture g.render(template: "/v3_0_0/apiFileData/show", model: [fileData: firstWatermarkedPicture])
    }
}
