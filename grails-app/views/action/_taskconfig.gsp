<script>
    window.getConfigObject = function () {
        var description = $('#config-description').val();
        if ($('#config-days').val() == '') {
            $('#config-days').focus();
            return false;
        }
        if (description == '') {
            $('#config-description').focus();
            return false;
        }
        var userGroup = $('#config-userGroup').val();
        var conf = {
            description: description,
            days: parseInt($('#config-days').val()),
            assignIds: $.map($('#config-assign').val(), (i) => parseInt(i)),
            subscribeIds: $.map($('#config-subscribe').val(), (i) => parseInt(i)),
            category: $('[name="config-category"]:checked').val(),
        };
        if (userGroup != '') {
            conf.userGroup = userGroup;
        }
        return conf;
    };
</script>

<div class="mb-3">
    <h4 class="w-100 bd-bottom-grey pt-4">
        <g:message code="action.type.task.config"/>
    </h4>
    <textarea placeholder="<g:message code="task.description"/>" class="form-control" id="config-description">${config?.description}</textarea>
</div>
<g:if test="${isOpportunity || isWorkflowData}">
    <div class="mb-3">
        <strong><g:message code="task.userGroup"/></strong>
        <select class="form-control" id="config-userGroup">
            <option value="" ${config?.userGroup == null ? 'selected' : ''}>----</option>
            <g:each var="group" in="${userGroups}">
                <option value="${group.name()}" ${config?.userGroup == group ? 'selected' : ''}><g:message code="${group.message}"/></option>
            </g:each>
        </select>
    </div>
</g:if>
<div class="mb-3">
    <strong><g:message code="task.additional.users"/></strong>
    <div class="w-100">
        <container:userSelect2 id="config-assign" selectedUsers="${config?.getAssignUsers()}"/>
    </div>
</div>
<div class="mb-3">
    <strong><g:message code="task.subscriptions"/></strong>
    <div class="w-100">
        <container:userSelect2 id="config-subscribe" selectedUsers="${config?.getSubscribeUsers()}"/>
    </div>
</div>
<div class="mb-3">
    <strong><g:message code="task.days.todo"/> <i class="mdi mdi-calendar"/></strong>
    <input id="config-days" type="number" min="0" value="${config ? config.days : 1}" class="form-control"/>
</div>

<div class="row pt-2">
    <div class="col-6">
        <h5><g:message code="task.category"/></h5>

        <g:each var="category" in="${categories}">
            <div class="form-check">
                <input type="radio" name="config-category" class="form-check-input" role="switch"
                       value="${category.name()}" ${(config ? (config.category == category) : (category == categories.first())) ? 'checked' : ''}>
                <span>
                    <i class="${category.icon}"></i> <g:message code="${category.message}"/>
                </span>
            </div>
        </g:each>
    </div>
</div>
