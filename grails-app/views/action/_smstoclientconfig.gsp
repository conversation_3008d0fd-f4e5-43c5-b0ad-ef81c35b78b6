<script>
    window.getConfigObject${object} = function () {
        var body = $('#config-body${object}').val();
        if (body.length < 3) {
            $('#config-body${object}').summernote('focus');
            $('#config-body${object} + div')[0].scrollIntoView();
            window.scrollBy(0, -70);
        } else {
            var calendarEventModifyConfig;
            var calendarEventDeleteConfig;
            <g:if test="${object == ''}">
            calendarEventModifyConfig = window.getConfigObjectcalendarEventModifyConfig ? window.getConfigObjectcalendarEventModifyConfig() : null;
            calendarEventDeleteConfig = window.getConfigObjectcalendarEventDeleteConfig ? window.getConfigObjectcalendarEventDeleteConfig() : null;
            </g:if>
            if (calendarEventModifyConfig != false && calendarEventDeleteConfig != false) {
                console.log('calendarEventModifyConfig', calendarEventModifyConfig);
                var conf = {
                    twilioNumberId: parseInt($('#config-twilioNumberId${object}').val()),
                    calendarEventConfig: window.getCalendarEventConfigObject ? window.getCalendarEventConfigObject() : null,
                    calendarEventModifyConfig: calendarEventModifyConfig,
                    calendarEventDeleteConfig: calendarEventDeleteConfig,
                    body: body
                };
                return conf;
            }
        }
        return false;
    };
</script>

<div class="mb-3">
    <h4 class="w-100 bd-bottom-grey pt-4">
        <g:message code="action.type.smstoclient.config"/>
    </h4>
    <div class="mb-3">
        <strong><g:message code="task.userGroup"/></strong>
        <select class="form-control" id="config-twilioNumberId${object}">
            <g:each var="num" in="${numbers}">
                <option value="${num.id}" ${config?.twilioNumberId == num.id ? 'selected' : ''}>${num.getDescription()} - (${num.number})</option>
            </g:each>
        </select>
    </div>
    <container:summernote variables="${variables}" noHtml="true" value="${config?.body}" id="config-body${object}"/>
    <g:if test="${isCalendarEvent}">
        <h4 class="w-100 bd-bottom-grey pt-4">
            <g:message code="calendarEventModifyConfig"/>
        </h4>
        <g:render template="smstoclientconfig" model="[object: 'calendarEventModifyConfig', config: config?.calendarEventModifyConfig, variables: variables, numbers: numbers, isCalendarEvent: false]"/>

        <h4 class="w-100 bd-bottom-grey pt-4">
            <g:message code="calendarEventDeleteConfig"/>
        </h4>
        <g:render template="smstoclientconfig" model="[object: 'calendarEventDeleteConfig', config: config?.calendarEventDeleteConfig, variables: variables, numbers: numbers, isCalendarEvent: false]"/>

        <g:render template="calendareventconfig" model="[config: config?.calendarEventConfig, variables: variables]"/>
    </g:if>
</div>