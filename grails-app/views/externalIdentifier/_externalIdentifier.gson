import traction.ExternalIdentifier

model {
    ExternalIdentifier externalIdentifier
}
json g.render(externalIdentifier, [expand: [], excludes: ['dms']]) {
    if (externalIdentifier.dms) {
        dms {
            id externalIdentifier.dms.id
            dmsId externalIdentifier.dms.dmsId
            source externalIdentifier.dms.source.name
            dmsDealerIds externalIdentifier.dms.departments.dmsDealerId.join(',')
        }
    }
}