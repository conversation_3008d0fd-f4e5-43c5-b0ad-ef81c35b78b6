<div class="modal-dialog">
    <div class="modal-content">

        <div class="modal-header">
            <h2 class="modal-title"><g:message code="extID.add"/></h2>
        </div>

        <div class="modal-body">
            <form class="needs-validation">
                <input type="hidden" value="${client?.id}" name="clientId"/>
                <input type="hidden" value="${user?.id}" name="userId"/>

                <div class="mb-3">
                    <label for="dmsId" class="fw-bold mb-2"><g:message code="source.dms"/></label>
                    <select id="dmsId" class="form-select" name="dmsId" required>
                        <g:each var="dms" in="${dmsList}">
                            <option value="${dms.id}">${dms.name}</option>
                        </g:each>
                    </select>
                </div>

                <div class="required">
                    <label class="fw-bold mb-2"><g:message code="extID"/></label>
                    <g:if test="${client}">
                        <search:clientInit/>
                        <search:clientExtId onSubmit="submitTypeaheadClient" type="text"
                                            class="form-control form-control-sm" name="extId"
                                            callback="selectClient" required="true"/>
                    </g:if>
                    <g:else>
                        <input type="text" class="form-control form-control-sm" name="extId" required>
                    </g:else>
                </div>
            </form>
        </div>

        <div class="modal-footer">
            <div class="d-flex">
                <button type="button" onclick="TractionModal.hide();" class="btn btn-outline-primary shadow-sm fw-bold"><span><g:message
                        code="cancel"/></span></button>
                <button type="button" onclick="ExternalIdentifier.saveExtId();" class="btn btn-primary shadow-sm fw-bold"><g:message
                        code="save"/></button>
            </div>
        </div>
    </div>
</div>