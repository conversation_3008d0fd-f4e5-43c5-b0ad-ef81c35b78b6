<g:each var="rule" in="${rules}">
    <li id="li_inheritance_${rule?.id}" class="<g:if test="${selected.equals(rule?.id)}">opt_inherit_column_selected mx-sm-2 my-2 bg-info-subtle"</g:if><g:else>mx-sm-2 my-2</g:else>  <g:if test="${rule?.active.equals(true)}"></g:if><g:else>text-secondary</g:else>" onclick="VehicleInheritanceIndexUpdateName('${rule?.name}',${rule?.id});">${rule?.name}
        <i style="display: none;" class="text-primary inh_list_icons inh_list_icons_${rule?.id} my-1 mx-sm-2 fa-light fa-trash float-right" onclick="VehicleInheritanceIndexDelete('${rule?.id}');"></i>
        <i style="display: none;" class="text-primary inh_list_icons inh_list_icons_${rule?.id} my-1 mx-sm-1 fa-solid <g:if test="${rule?.active.equals(true)}">fa-toggle-on</g:if><g:else>fa-toggle-off</g:else> float-right" onclick="VehicleInheritanceIndexActive('${rule?.id}');"></i>
        <span style="display: none;" class="text-primary inh_list_icons inh_list_icons_${rule?.id} mx-sm-1 float-right" onclick="VehicleInheritanceIndexEditName('${rule?.id}');"><i class="fa-solid fa-pen-to-square"></i></span>
        <span style="display: none;" class="text-primary inh_list_icons inh_list_icons_${rule?.id} mx-sm-1 float-right" onclick="VehicleInheritanceIndexReorder('${rule?.id}','down');"><i class="fa-solid fa-down-long"></i><i class="fa-solid fa-down-long"></i></span>
        <span style="display: none;" class="text-primary inh_list_icons inh_list_icons_${rule?.id} mx-sm-1 float-right" onclick="VehicleInheritanceIndexReorder('${rule?.id}','up');"><i class="fa-solid fa-up-long"></i><i class="fa-solid fa-up-long"></i></span>
    </li>
</g:each>