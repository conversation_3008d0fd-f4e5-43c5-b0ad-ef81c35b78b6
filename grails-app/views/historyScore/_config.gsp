<asset:javascript src="historyScore/config.js"/>
<div class=" h-100 overflow-scroll border rounded-2 p-4 mb-4">
    <h5 class="text-emphasis fw-bold f-18 text-uppercase mb-3"><g:message code="config.history.score.title"></g:message></h5>

    <table class="table w-100"
           cellspacing="0" id="historyScores">
        <thead>
        <tr>
            <th class="button"><g:message code="button"/></th>
            <th class="action"><g:message code="actions"/></th>
            <th class="points"><g:message code="history.points"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="hs" in="${historyScores}">
            <tr>
                <td>
                    <div data-toggle="tooltip" class="btn me-2 ${hs.status.icon}"
                         style="color:white;background-color: ${hs.status.iconColor};" title="<g:message code="${hs.status.message}"/>">
                    </div>
                </td>
                <td>
                    <g:message code="${hs.status.message}"/>
                </td>
                <td>
                    <input onchange="editHistoryScore(this, ${hs.status.id});" class="form-control" type="number"
                           step="0.5" value="${hs.points}"/>
                </td>
            </tr>
        </g:each>
        </tbody>
    </table>
</div>
