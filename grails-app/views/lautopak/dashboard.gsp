<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="lautopak.workshop.dashboard"/></title>
    <asset:javascript src="lautopak/dashboard.js"/>
    <asset:stylesheet src="lautopak/dashboard.css"/>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0"></script>
</head>
<body>
<g:if test="${loadFilter}">
    <script>
        window.history.pushState({}, document.title, window.location.pathname);
        var load = UserStorage.getJSON('${loadFilter}');
        if (load) {
            console.log('loading', load);
            UserStorage.set('lautopakdashboard', load);
        }
    </script>
</g:if>
<div class="m-4">
    <div class="w-100 clearfix">
        <h2 class="m-0 float-left"><g:message code="lautopak.workshop.dashboard"/></h2>
        <div class="float-right dropdown ms-2">
            <span data-bs-toggle="dropdown">
                <span class="btn btn-primary mdi mdi-open-in-new" data-toggle="tooltip" title="<g:message code="lautopak.view.in.other"/>"></span>
            </span>
            <div class="dropdown-menu dropdown-menu-end">
                <a target="_blank" class="dropdown-item" href="<g:createLink absolute="true" controller="lautopak" action="index"/>?loadFilter=lautopakdashboard"><g:message code="workorderjob.list"/></a>
                <a target="_blank" class="dropdown-item" href="<g:createLink absolute="true" controller="lautopak" action="dashboard2"/>?loadFilter=lautopakdashboard"><g:message code="lautopak.workshop.dashboard.chart"/></a>
            </div>
        </div>
        <div id="filter-btns" class="float-right">
        </div>
    </div>
    <lautopak:jobFilter id="lautopakdashboard"/>
</div>

<div class="small-chart-container m-4">
    <div class="divLoader">
        <div class="loader"></div>
        <canvas id="bar-chart"></canvas>
    </div>
</div>

<div id="dashboard">
</div>
</body>
</html>