<asset:javascript src="lautopak/config.js"/>

<div class="h-100 overflow-y-scroll  border rounded-2 p-4 me-4" style="--bs-gutter-x: 0;">
    <h5 class="text-emphasis fw-bold f-18 text-uppercase mb-3"><g:message code="config.lautopak"></g:message></h5>
    <ul class="nav nav-tabs border-0 mb-3 cursor" role="tablist" id="lautopak-nav-tabs">
        <li class="nav-item">
            <span class="cursor nav-link border-0 p-0 me-4 active" data-bs-toggle="tab" onclick="LautopakCodeMenu.list();" role="tab"><g:message code="lautopakcodemenu.list"/></span>
            <div class="slide"></div>
        </li>
        <li class="nav-item">
            <span class="cursor nav-link border-0 p-0 me-4" data-bs-toggle="tab" onclick="LautopakSkill.list();" role="tab"><g:message code="lautopakskill.list"/></span>
            <div class="slide"></div>
        </li>
        <li class="nav-item">
            <span class="cursor nav-link border-0 p-0 me-4" data-bs-toggle="tab" onclick="LautopakViewer.list();" role="tab"><g:message code="lautopakviewer"/></span>
            <div class="slide"></div>
        </li>
        <li class="nav-item">
            <span class="cursor nav-link border-0 p-0 me-4" data-bs-toggle="tab" onclick="LautopakProvider.list();" role="tab"><g:message code="lautopakprovider.list"/></span>
            <div class="slide"></div>
        </li>
    </ul>
    <div id="lautopak-config">

    </div>
</div>
<style>
#lautopak-config tbody tr td:nth-child(2), #code-menu-list tbody tr td:nth-child(3), #code-menu-list tbody tr td:nth-child(4) {
    font-size: 14px;
}
#lautopak-config tbody td {
    padding: 0.2rem;
}
#lautopak-nav-tabs .nav-link.active{
    text-decoration: underline;
    color: #0086F0 !important;
}
#lautopak-nav-tabs .nav-link{
    color: #80C2F7 !important;
}
</style>