<%@ page import="traction.bank.Bank;traction.vehicle.VehicleSelectionOption; traction.client.ClientInterest; traction.financingScenario.FinancingScenarioValidation; traction.financingScenario.FinancingScenario" %>
<div class="modal-dialog modal-xl modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title text-emphasis fw-bold f-32" ><g:message code="modify.code.menu"/></h5>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            </button>
        </div>

        <div class="modal-body">

            <div class="d-flex align-items-center h-39 mb-3">
                <span class="text-emphasis f-16" style="min-width: 153px;"><g:message code="lautopakcodemenu"/></span>
                <input class="form-control" id="codeMenu" value="${codeMenu?.codeMenu}" ${codeMenu ? 'disabled' : ''}/>
            </div>
            <div class="d-flex align-items-center h-39 mb-3">
                <span class="text-emphasis f-16" style="min-width: 153px;"><g:message code="lautopakcodemenu.priority"/></span>
                <input class="form-control" type="number" id="priority" value="${codeMenu ? codeMenu.priority : 0}"/>
            </div>
            <div class="d-flex align-items-center h-39 mb-3">
                <span class="text-emphasis f-16" style="min-width: 153px;"><g:message code="lautopakskill"/></span>
                <select id="skill" class="form-select">
                    <g:each var="s" in="${skills}">
                        <option value="${s.id}" ${s.id == codeMenu?.skillId ? 'selected' : ''}>${s}</option>
                    </g:each>
                </select>
            </div>
            <div class="d-flex align-items-start mb-3">
                <span class="text-emphasis f-16" style="min-width: 153px;"><g:message code="client.posstatus.tech"/></span>
                <div class="border rounded-2 w-100" style="padding: 10px 12px;">
                    <div class="d-flex align-items-center mb-2">
                        <input type="checkbox" id="forEveryone" class="form-check-input border-primary  f-18 m-0 me-2" ${codeMenu?.forEveryone ? 'checked' : ''}>
                        <span><g:message code="lautopak.forEveryone"/></span>
                    </div>
                    <div id="users-container" class="w-100 mb-3">
                        <select id="users" class="form-control" multiple="multiple">
                            <g:each var="dg" in="${departmentGroupTech}">
                                <g:if test="${dg.users.any { !it.deleted }}">
                                    <g:each var="title" in="${dg.users.findAll { !it.deleted }.collect { it.attributes.find { it.name == 'title' }?.value }.unique()}">
                                        <optgroup label="${dg.department.name} ${title}">
                                            <g:each var="u" in="${dg.users.findAll { !it.deleted && it.attributes.find { it.name == 'title' }?.value == title }.sort { it.getExtID(dg.department.dms) }}">
                                                <option class="user" value="${u.id}" ${codeMenu?.users?.contains(u) ? "selected" : ""}>${u.getExtID(dg.department.dms) ? u.getExtID(dg.department.dms) + ": " : ""}${u.fullName}</option>
                                            </g:each>
                                        </optgroup>
                                    </g:each>
                                </g:if>
                            </g:each>
                        </select>
                        <script>
                            $(document).ready(function () {
                                $('#users').multipleSelect({
                                    filter: true,
                                    labelTemplate: function ($el) {
                                        return $el.attr('label') + '<i style="padding: 0px 14px;transform: scale(1.3);" class="mdi mdi-chevron-up float-right"></i>';
                                    }
                                });
                                $('#users + .ms-parent .mdi-chevron-up').on('click', function (event) {
                                    event.preventDefault();
                                    $(this).toggleClass('mdi-chevron-down mdi-chevron-up');
                                    var hide = $(this).hasClass('mdi-chevron-down');
                                    var $li = $(this).closest('li');
                                    while (true) {
                                        $li = $li.next();
                                        console.log($li);
                                        if ($li.length == 0 || $li.hasClass('group')) {
                                            break;
                                        }
                                        hide ? $li.hide() : $li.show();
                                    }
                                });
                            });
                        </script>
                        <style>
                        .ms-choice i {
                            display: none;
                        }
                        .ms-drop > ul > li.user {
                            padding-left: 20px;
                        }
                        </style>
                    </div>
                </div>
            </div>
            <div class="d-flex align-items-start mb-3">
                <span class="text-emphasis f-16" style="min-width: 153px;"><g:message code="main.workflow"/></span>
                <div class="d-flex flex-column w-100">
                    <select class="form-select mb-2" id="workflowMasterId">
                        <option value=""><g:message code="select.option"/></option>
                        <g:each var="m" in="${workflowMasters}">
                            <option value="${m.id}" ${m.id == codeMenu?.workflowMasterId ? 'selected' : ''}>${m.name}</option>
                        </g:each>
                    </select>
                    <div class="d-flex align-items-center border rounded-2 h-39 mb-2" style="padding: 0 12px;">
                        <input type="checkbox" id="createWorkflowData" class="form-check-input border-primary  f-18 m-0 me-2" ${codeMenu?.createWorkflowData ? 'checked' : ''}>
                        <span><g:message code="lautopak.createWorkflowData"/></span>
                    </div>
                    <div class="d-flex align-items-center border rounded-2 h-39 mb-2" style="padding: 0 12px;">
                        <input type="checkbox" id="createOpportunity" class="form-check-input border-primary  f-18 m-0 me-2" ${codeMenu?.createOpportunity ? 'checked' : ''}>
                        <span><g:message code="lautopak.createOpportunity"/></span>
                    </div>
                    <div class="d-flex align-items-center border rounded-2 h-39 mb-2" style="padding: 0 12px;">
                        <input type="checkbox" id="assignUser" class="form-check-input border-primary  f-18 m-0 me-2"  ${codeMenu?.assignUser ? 'checked' : ''}>
                        <span><g:message code="lautopak.assignUser"/></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-outline-primary shadow-sm fw-bold me-2" data-bs-dismiss="modal"><g:message code="default.button.cancel.label"/></button>
            <button type="submit" class="btn btn-primary shadow-sm fw-bold" onclick="LautopakCodeMenu.save('${codeMenu?.id}');"><g:message code="default.button.save.label"/></button>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        $('#skill').select2({
            theme: 'bootstrap-5'
        });
        $('#forEveryone').change(forEveryoneChange);
        forEveryoneChange();
    });

    function forEveryoneChange() {
        $('#users-container').toggle(!$('#forEveryone').prop('checked'));
    }
</script>