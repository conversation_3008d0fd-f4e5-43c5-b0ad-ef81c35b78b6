<asset:stylesheet src="vehicleList/treeCheckbox.css"/>
<script>
    $(function () {
        $('#tree${uuid} input').change(function () {
            var values = $('#tree${uuid} input:checked').map(function (elem) {
                console.log(this, elem);
                return this.value;
            }).get();
            var list = $(this).val().split('|');
            if ($(this).is(':checked')) {
                for (var i = 0; i < $(this).val().split('|').length - 1; i++) {
                    var list2 = list.pop();
                    if (!values.includes(list.join('|'))) {
                        values[values.length] = list.join('|');
                    }
                }
            }
            console.log('values', values);
            ${onchange}('${id}', values);
        });
    });
</script>
<div id="tree${uuid}" class="d-flex flex-column">
    <g:if test="${treeCheckbox.childrens.size() > 0}">
        <vehicleTagLib:treeCheckboxList treeCheckbox="${treeCheckbox}" disabled="${disabled}"/>
    </g:if>
    <g:else>
        <span class="text-body-tertiary"><g:message code="vehicle.magentoData_category.none"/></span>
    </g:else>
</div>