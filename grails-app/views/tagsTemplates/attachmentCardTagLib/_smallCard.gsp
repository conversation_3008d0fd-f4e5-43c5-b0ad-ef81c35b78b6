<form id="file-small-card-${file.id}" class="position-relative visible-hover-container d-block border rounded shadow-sm overflow-hidden" style="width: 140px; height: 140px;">
    <input type="hidden" name="id" value="${file.id}"/>
    <fileData:preview fileDataId="${file.id}" style="width: 100%; object-fit: cover; display:block; font-size: xxx-large; text-align: center"/>
    <div class="visible-hover position-absolute top-0 end-0 p-2 gap-3 d-flex">
        <a data-toggle="tooltip" title="<g:message code="download"/>" href="<g:createLink controller="web" action="getFile" id="${file.id}"/>" download="${file.name}">
            <i class="fa fa-regular fa-arrow-down-to-bracket text-primary"></i>
        </a>
        <a data-toggle="tooltip" title="<g:message code="delete"/>" href="javascript:void(0);" onclick="Attachments.delete('#file-small-card-${file.id}');">
            <i class="fas fa-x text-primary" role="button" data-toggle="tooltip" title="<g:message code="delete"/>" onclick=""></i>
        </a>
    </div>
</form>