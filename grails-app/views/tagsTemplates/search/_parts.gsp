<div class="typeahead__container">
    <div class="typeahead__field">
        <span class="typeahead__query">
            <input data-uuid="${uuid}" ${raw(attributes)} oninput="toggleMagnifyingGlass${uuid}(this)">
            <i class="fa-solid fa-magnifying-glass text-primary position-absolute" id="magnifyingGlass${uuid}" style="right: 1rem; top: 50%; transform: translateY(-50%);"></i>
        </span>
    </div>
</div>

<script>
    function toggleMagnifyingGlass${uuid}(input) {
        const magnifyingGlass = document.getElementById('magnifyingGlass${uuid}');
        if (input.value.length > 0) {
            magnifyingGlass.style.display = 'none';
        } else {
            magnifyingGlass.style.display = 'block';
        }
    }
    <g:if test="${addData}">
    window['addData${uuid}'] = ${addData};
    </g:if>
    <g:else>
    window['addData${uuid}'] = function () {
        return {};
    };
    </g:else>
    window['updateSearchParts${uuid}'] = ${callback};
</script>