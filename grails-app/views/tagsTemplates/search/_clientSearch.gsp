<div class="typeahead__container">
    <div class="typeahead__field" id="clientSearchDiv">
        <span class="typeahead__query">
            <input data-uuid="${uuid}" ${raw(attributes)} oninput="toggleMagnifyingGlass${uuid}(this)">
            <i class="fa-solid fa-magnifying-glass text-primary position-absolute" id="magnifyingGlass${uuid}" style="right: 1rem; top: 50%; transform: translateY(-50%);"></i>
        </span>
    </div>
</div>

<script>
    function toggleMagnifyingGlass${uuid}(input) {
        const magnifyingGlass = document.getElementById('magnifyingGlass${uuid}');
        if (input.value.length > 0) {
            magnifyingGlass.style.display = 'none';
        } else {
            magnifyingGlass.style.display = 'block';
        }
    }
    $(document).ready(function () {
        <g:if test="${callback}">
        window['updateClientSearch${uuid}'] = ${callback};
        </g:if>
        <g:if test="${onCancel}">
        window['onCancelClientSearch${uuid}'] = ${onCancel};
        </g:if>
    });

    function onLayoutBuiltBeforeClientSearch(node, query, result, resultHtmlList) {
        <g:if test="${onLayoutBuiltBefore}">
        ${onLayoutBuiltBefore}(node, query, result, resultHtmlList);
        </g:if>
    }

    function onSubmitClientSearch(node, form, item, event) {
        ${onSubmit ? onSubmit + '(node, form, item, event);' : ''}
    }
</script>
<style>
#search-client .typeahead__list {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>