<div class="d-flex flex-row conversation me-3 my-2 ${com.communication?.isUnread ? 'unread' : ''}"
     data-channel-id="${com.communication?.channelId}"
     data-client-id="${com.client?.id}" data-contact-id="${com.contact?.id}"
     data-timestamp="${com.communication?.date ? com.communication?.date.getTime() : ''}">
    <g:if test="${com.client}">
        <communication:avatar class="avatar-client" client="${com.client}"/>
    </g:if>
    <g:elseif test="${com.contact}">
        <communication:avatar class="avatar-client" contact="${com.contact}"/>
    </g:elseif>
    <g:else>
        <i class="fas fa-user-circle" style="font-size: 50px; margin-left: 6px"></i>
    </g:else>
    <div class="d-flex flex-column">
        <div class="conversation-name"
             data-fullname="${com.fullName ? com.fullName : com.communication?.channel?.webSession?.lastVisit?.pageUrl}">
            ${com.fullName ? com.fullName : com.communication?.channel?.webSession?.lastVisit?.pageUrl}
        </div>

        <div class="conversation-text ${com.communication?.error() ? 'text-danger' : ''}">
            <span class="cut">
                <i class="communication-icon ${com.communication?.getIcon()}" data-toggle="tooltip"
                   title="<g:message
                           code="communication.${com.communication?.outbound ? 'sent' : 'received'}.via"/>: <g:message
                           code="${com.communication?.class?.name}"/>"></i>
                ${com.isSentByCurrentUser ? g.message(code: 'you')+':' : '' } ${com.communication?.getPlainText()}
            </span>
        </div>
    </div>

    <div class="d-flex flex-column">
        <div class="justify-content-center text-end">
            <i class="${com.isImportant == true ? "fa-solid fa-star stars-color" : "fa-regular fa-star"} favorite-btn "
               style="color: var(--bs-tertiary-color)"
               role="button" onclick="event.preventDefault();
            event.stopPropagation();
            CommunicationInbox.setIsImportant($(this).closest('.conversation').data('client-id'), $(this).closest('.conversation').data('contact-id'));"
               data-is-important="${com.isImportant}"></i>

            <div class="d-flex flex-row ms-auto">
                <span class="conversation-timer text-secondary">${timer}</span>
                <span class="unread-point ms-1 mt-2"></span>
            </div>

        </div>
    </div>

        <g:if test="${com.communication?.channel && !com.communication?.user}">
            <div class="conversation-channel">
                <g:if test="${com.facebookMessage}">
                    ${com.facebookMessage.facebookPage?.name}
                </g:if>
                <g:elseif test="${com.chatMessage}">
                    ${com.chatMessage.channel.chatWidget?.name} - ${com.chatMessage.chatWidgetGroup?.name}
                </g:elseif>
            </div>
        </g:if>
</div>