<%@ page import="traction.I18N" %>
<div class="card m-2">
    <div class="card-header">
        <small class="text-body-tertiary float-right">${communication.date.format(I18N.m('dateFormatWithTime'))}</small>
        <h5>
            <g:message code="communication.${communication.outbound ? 'sent' : 'received'}.via"/>: <g:message
                    code="${communication.class.name}"/>
        </h5>
    <!--
                    <div class="row">
                        <div class="col-12">
                            <small><g:message code="user"/>: ${communication.user?.fullName}</small><br>
                            <small><g:message code="communication.userGroup"/>: <g:message
            code="${communication.userGroup?.message}"/></small><br>
                            <small><g:message code="communication.status"/>: <g:message
            code="${communication.status?.message}"/> <communication:eventsIcon communication="${communication}"/></small><br>
                            <small><g:message code="client"/>: ${communication.client?.fullName}</small><br>
                            <small><g:message code="opportunity"/>: ${communication.opportunity?.name}</small><br>
                        </div>
                    </div>
                    -->
        <g:if test="${mailMessage}">
            <div class="row">
                <div class="col-12">
                    <h4>${userCanAccess ? (mailMessage.subject ? mailMessage.subject : g.message(code: "mailmessage.nosubject")) : "***********"}</h4>
                    <small><g:message code="client.email.from"/>: ${mailMessage.emailFrom}</small><br>
                    <small><g:message code="client.email.to"/>: ${mailMessage.emailTo}</small><br>
                    <small><g:message code="client.email.cc"/>: ${mailMessage.emailCC}</small>
                </div>
            </div>
        </g:if>
        <g:if test="${smsMessage}">
            <div class="row">
                <div class="col-12">
                    <small>
                        <g:message
                                code="client.email.from"/>: ${smsMessage.fromNumber.replaceFirst("(\\d{1})(\\d{3})(\\d{3})(\\d+)", "\$1 (\$2) \$3-\$4")}
                        <g:if test="${smsMessage.outbound}">
                            <g:set var="twilioNumber"
                                   value="${traction.TwilioNumber.findByNumber(smsMessage.fromNumber)}"/>
                            ${twilioNumber ? "(${twilioNumber.getDescription()})" : ""}
                        </g:if>
                        <g:else>
                            (${communication.client.getFullName()})
                        </g:else>
                    </small>
                    <br>
                    <small>
                        <g:message
                                code="client.email.to"/>: ${smsMessage.toNumber.replaceFirst("(\\d{1})(\\d{3})(\\d{3})(\\d+)", "\$1 (\$2) \$3-\$4")}
                        <g:if test="${!smsMessage.outbound}">
                            <g:set var="twilioNumber"
                                   value="${traction.TwilioNumber.findByNumber(smsMessage.toNumber)}"/>
                            ${twilioNumber ? "(${twilioNumber.getDescription()})" : ""}
                        </g:if>
                        <g:else>
                            (${communication.client.getFullName()})
                        </g:else>
                    </small>
                    <br>
                </div>
            </div>
        </g:if>
        <div class="w-100 pt-2">
            <g:if test="${communication.isPrivate}">
                <span style="display: block;" class="mt-1 badge bg-warning"><g:message
                        code="communication.private"/></span>
            </g:if>
        </div>
    </div>

    <div class="card-body" style="max-height: 50vh; overflow-y: auto;">
        <g:if test="${userCanAccess}">
            <g:if test="${mailMessage}">
                <p class="card-text">
                    <iframe style="width: 100%;" frameborder="0" scrolling="no" onload="resizeIframe(this)"
                            src="<g:createLink absolute="true" controller="email" action="getContent"
                                               id="${mailMessage.id}" params="[original: true]"/>"></iframe>
                </p>
            </g:if>
            <g:elseif test="${callData}">
                <p class="card-text">
                    <container:playbox callData="${callData}"/>
                </p>
            </g:elseif>
            <g:elseif test="${chatMessage}">
                <g:each var="c" in="${chatMessageList}">
                    <div class="${chatMessage.id == c.id ? "pt-2 ps-2 pe-2 mb-4 mt-2 me-3 ms-3 shadow border rounded" : ""}">
                        <communication:item communication="${c}" hideIcons="true"/>
                    </div>
                </g:each>
            </g:elseif>
            <g:else>
                <communication:item communication="${communication}" hideIcons="true"/>
            </g:else>
        </g:if>
        <g:else>
            <g:message code="communication.isPrivate.content" args="[communication.user?.fullName]"/>
        </g:else>
    </div>

    <g:if test="${communication.fileDatas}">
        <div class="card-footer">
            <g:each var="f" in="${communication.fileDatas}">
                <a class="communication-file ${f.isDangerous() ? 'danger' : ''}"
                   href="${f.isDangerous() ? '#!' : g.createLink(absolute: true, controller: "web", action: "getFile", id: f.id)}"
                   download="${f.name}">
                    <g:if test="${f.status.contains('image')}">
                        <div class="communication-image border shadow-lg">
                            <img class="img-fluid" loading="lazy"
                                 src="${g.createLink(absolute: true, controller: "web", action: "getFile", id: f.id)}"/>
                        </div>
                    </g:if>
                    <span><i class="${f.icon}"></i> ${f.name}</span>
                </a>
            </g:each>
        </div>
    </g:if>
</div>
