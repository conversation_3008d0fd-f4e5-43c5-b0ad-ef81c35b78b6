<div class="card-task card w-auto ${irrelevant ? 'meeting-NO_SHOW' : ''}">
    <div data-toggle="tooltip"
         title="<g:message code="${visitLog.category.message}"/>"
         class="t1 ${visitLog.category.icon}"></div>

    <div data-toggle="tooltip" title="<g:message code="${visitLog.client ? 'client' : 'visitlog.noteOnClient'}"/>: ${client}"
         class="t2 ${visitLog.client ? 'mdi mdi-account' : 'mdi mdi-notebook'}">: ${visitLog.client ? visitLog.client.fullName : visitLog.noteOnClient}</div>

    <g:if test="${visitLog.opportunity}">
        <div data-toggle="tooltip" title="<g:message code="opportunity"/>: ${visitLog.opportunity?.name}"
             class="t3 mdi mdi-car">: ${visitLog.opportunity ? visitLog.opportunity.name : "----"}</div>
    </g:if>

    <div data-toggle="tooltip" class="t4"></div>

    <div class="t5">
        <span style="background-color: ${assignedColor}; color: <communication:contrastColor hex="${assignedColor}"/>"
              class="assigned ${irrelevant ? 'assigned-done mdi mdi-clipboard-check' : ''}"
              data-toggle="tooltip">
            ${assignedName}
            <g:if test="${isAssignedUser}">
                <span class="mdi mdi-account-tie"></span>
            </g:if>
        </span>
    </div>

    <div class="t6">
    </div>

    <div class="t7">
        <span class="f-10">${visitLog.creator ? "${g.message(code: "timeline.usercreated")} ${visitLog.creator.fullName}" : g.message(code: "timeline.systemcreated")}</span>
        <span class="badge badge-outline-secondary-emphasis">${visitLog.date.format("yyyy-MM-dd HH:mm")}</span>
    </div>
</div>
