<g:each var="f" in="${fileDatas}">
    <a class="task-file ${f.isDangerous() ? 'danger' : ''} ${f.category == traction.category.CategoryFile.DRAFT_ATTACHMENTS.message ? 'draft-file' : ''}"
       href="${f.isDangerous() ? '#!' : g.createLink(absolute: true, controller: "web", action: "getFile", id: f.id)}"
       download="${f.name}">
        <g:if test="${edit}">
            <i class="mdi mdi-circle" data-toggle="tooltip" title="<g:message
                    code="${f.category == traction.category.CategoryFile.DRAFT_ATTACHMENTS.message ? 'task.draft.file' : 'task.saved.file'}"/>"></i>
        </g:if>
        <g:if test="${f.status.contains('image')}">
            <div class="task-image border shadow-lg">
                <img class="img-fluid" loading="lazy"
                     src="${g.createLink(absolute: true, controller: "web", action: "getFile", id: f.id)}"/>
            </div>
        </g:if>
        <span>${f.name}</span>
        <g:if test="${edit && user == f.author}">
            <i class="mdi mdi-delete" data-toggle="tooltip"
               title="<g:message code="default.button.delete.label"/>"
               onclick="event.preventDefault();TaskSidePanel.deleteFile(this, ${f.id})"></i>
        </g:if>
    </a>
</g:each>
