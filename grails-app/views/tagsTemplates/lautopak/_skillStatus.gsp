<asset:javascript src="lautopak/LautopakSkillStatus.js"/>
<script>
    $(document).ready(function () {
        new LautopakSkillStatus('${id}', '${root1}', '${root2}', '${root3}');
    });
</script>
<h2 class="mb-3">
    ${root1}${root2 ? " / " + root2 : ""}${root3 ? " / " + root3 : ""}
    <div class="float-right text-black fw-medium">
        <small>
            <g:message code="display.on"/> <input id="limit${id}" style="width:60px;height:1.5em;display:inline-block;" class="form-control form-control-sm" min="0" value="30" type="number"> <g:message code="days"/>
        </small>
    </div>
</h2>
<div class="row">
    <div class="col-2 p-0">
        <div class="card p-1 mb-1" style="background-color: #85858577; border-color: #858585">
            <g:message code="workorderjob.estimatedTime"/>: ${estimatedTime}h
        </div>
        <div class="card p-1 mb-1" style="background-color: #30cf2777; border-color: #30cf27">
            <g:message code="workorderjob.timeWorked"/>: ${timeWorked}h
        </div>
        <div class="card p-1 mb-1" style="background-color: #cf8c2777; border-color: #cf8c27">
            <g:message code="workorderjob.exceeding"/>: ${exceeding}h
        </div>
        <div class="card p-1 mb-1" style="background-color: #cccf2777; border-color: #cccf27">
            <g:message code="workorderjob.timeLeft"/>: ${timeLeft}h
        </div>
        <div class="card p-1 mb-1" style="background-color: #cf272777; border-color: #cf2727">
            <g:message code="workorderjob.timeLate"/>: ${timeLate}h
        </div>
        <label><g:message code="workorderjob.divide.late.time"/></label>
        <div class="input-group mb-3">
            <input id="distributionTimeLate${id}" class="form-control" min="0" value="0" type="number">
                <span class="input-group-text"><g:message code="days"/></span>
        </div>
        <div class="form-check">
            <input type="checkbox" class="form-check-input border-primary" id="limitOverflow${id}">
            <label class="form-check-label" for="limitOverflow${id}">Limiter le depassement des ressources</label>
        </div>
    </div>
    <div class="col-2">
        <h5 class="mb-2"><g:message code="next.availability"/></h5>
        <div id="nextJobDate${id}" class="card bg-body-tertiary mb-3 next-job">
            ---
        </div>
        <h5 class="mb-2"><g:message code="service.ressource"/></h5>
        <div class="input-group mb-3">
            <input id="timeLate${id}" value="${timeLate}" type="hidden">
            <input id="timeLeft${id}" value="${timeLeft}" type="hidden">
            <input id="techCount${id}" class="tech-count form-control" min="0" value="0" type="number">
                <span class="input-group-text"><g:message code="workorderjob.techs"/></span>
        </div>

        <div class="input-group">
            <input id="capacity${id}" class="capacity form-control" min="0" value="0" type="number">
                <span class="input-group-text">%</span>
        </div>
    </div>
    <div class="small-chart-container col-8">
        <div class="divLoader">
            <div class="loader"></div>
            <canvas id="${id}"></canvas>
        </div>
    </div>
</div>