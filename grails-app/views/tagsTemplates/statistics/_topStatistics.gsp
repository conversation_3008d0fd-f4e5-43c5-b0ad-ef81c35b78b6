<div class="d-flex flex-column gap-2">
    <g:each var="rows" in="${topStatistics.cards}">
        <div class="row g-2">
            <g:each var="card" in="${rows}">
                <div class="${card.width ? "col-${card.width}" : "col"}">
                    <div class="border rounded-1 shadow-sm h-100 d-flex flex-column">
                        <g:each var="cardRow" in="${card.rows}">
                            <div class="d-flex flex-row flex-grow-1">
                                <g:each var="statisticCard" in="${cardRow.cells}">
                                    <statisticsTagLib:card statisticCard="${statisticCard}"/>
                                </g:each>
                            </div>
                        </g:each>
                    </div>
                </div>
            </g:each>
        </div>
    </g:each>
</div>

<style>
    /* TODO mettre dans un fichier css */
    .statistic-card-filter-toggle.cursor:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.04);
        box-shadow: var(--bs-box-shadow) !important;
    }
    .statistic-card-filter-toggle.activeFilter {
        border-color: var(--bs-primary) !important;
        background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
    }
    .statistic-card-filter-toggle.activeFilter .text-body-secondary {
        color: var(--bs-primary) !important;
    }
</style>
