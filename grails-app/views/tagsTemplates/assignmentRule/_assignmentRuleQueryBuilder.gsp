<asset:stylesheet src="container/queryBuilder.css"/>
<asset:javascript src="assignmentRule/AssignmentRuleQueryBuilder.js"/>

<asset:stylesheet src="lib/query-builder/query-builder.default.css"/>
<asset:javascript src="lib/query-builder/query-builder.standalone.js"/>
<asset:javascript src="lib/query-builder/i18n/query-builder.${request.locale.language}.js"/>

<div style="display: block;" id="${id}"></div>

<script>
    new AssignmentRuleQueryBuilder({
        id: '${id}',
        rules: ${rules ? raw(rules) : '[]'},
        language: '${request.locale.language}'
    });
</script>