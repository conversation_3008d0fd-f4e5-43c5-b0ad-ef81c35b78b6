<asset:javascript src="lib/summernote/summernote-lite.js"/>
<asset:javascript src="lib/summernote/tam-emoji/js/config.js"/>
<asset:javascript src="lib/summernote/tam-emoji/js/tam-emoji.js"/>
<asset:stylesheet src="lib/summernote/summernote-lite.css"/>
<asset:stylesheet src="lib/summernote/tam-emoji/css/emoji.css"/>
<style>
.note-dropdown-menu {
    max-height: 400px;
    overflow: auto;
}

.note-toolbar > .note-btn-group > .note-btn, .note-toolbar > .note-btn-group > .note-btn-group > .note-btn {
    background: initial !important;
    color: #B3B3B3;
    border-color: black;
}
.note-toolbar > .note-btn-group > .note-btn:hover, .note-toolbar > .note-btn-group > .note-btn-group > .note-btn:hover {
    color: white;
}
.note-btn.active {
    color: white;
}
.note-toolbar {
    background-color: #1A4062;
}
.note-toolbar > .note-btn-group > .note-btn, .note-toolbar > .note-btn-group > .note-btn-group > .note-btn {
    border: 0 !important;
    color: white;
}
.note-modal.fade:not(.show) {
    opacity: initial !important;
}
.note-editor .dropdown-toggle::after { all: unset; }
.note-editor .note-dropdown-menu { box-sizing: content-box; }
.note-editor .note-modal-footer { box-sizing: content-box; }
</style>

<textarea id="${id}" summernote-uuid="${uuid}" ${raw(attributes)}
          hidden>${noHtml ? value?.replace("\n", "<br>") : value}</textarea>
<g:if test="${noHtml}">
    <style>
    textarea[summernote-uuid="${uuid}"] + div.note-editor p {
        margin-bottom: 0;
    }
    </style>
</g:if>
<script>
    $(document).ready(function () {
        <g:if test="${isBracketVariables}">
        var variables = [
            <g:each var="s" in="${variables}">
            {value: '[[${s}]]', text: '${s}'},
            </g:each>
        ];
        </g:if>
        <g:else>
        var variables = [
            <g:each var="s" in="${variables}">
            {value: '$' + '{${s}}', text: '${s}'},
            </g:each>
        ];
        </g:else>
        document.emojiButton = 'fa-regular fa-face-smile fs-6'
        document.emojiType = 'unicode';
        document.emojiSource = tractionWebRoot + '/assets/lib/summernote/tam-emoji/img';
        var $summernote = $('textarea[summernote-uuid="${uuid}"]');
        //$summernote.summernote('destroy');
        $summernote.summernote({
            height: 200,
            dialogsInBody: true,
            dialogsFade: true,
            maxHeight: ${isSingleLine ? 45 : 1000},
            shortcuts: ${!noHtml},
            inheritPlaceholder: true,
            buttons: {
                variables: function (context) {
                    var ui = $.summernote.ui;
                    var button = ui.buttonGroup([
                        ui.button({
                            <g:if test="${isBracketVariables}">
                            contents: '<i class="mdi mdi-code-brackets"></i> <g:message code="insert.variables"/> <span class="caret"></span>',
                            </g:if>
                            <g:else>
                            contents: '<i class="mdi mdi-currency-usd" style="margin-right: -3px;"></i><i class="mdi mdi mdi-code-json"></i> <g:message code="insert.variables"/> <span class="caret"></span>',
                            </g:else>
                            className: "dropdown-toggle",
                            data: {
                                toggle: "dropdown",
                            }
                        }),
                        ui.dropdown({
                            className: "drop-default summernote-list",
                            contents: $.map(variables, function (v) {
                                return '<span class="dropdown-item cursor" data-value="' + v.value + '" role="listitem">' + v.text + '</span>';
                            }).join(''),
                            callback: function ($dropdown) {
                                $dropdown.find("span.dropdown-item").click(function () {
                                    context.invoke("editor.restoreRange");
                                    context.invoke("editor.focus");
                                    context.invoke("editor.insertText", $(this).data('value'));
                                });
                            },
                        }),
                    ]);
                    return button.render();
                }
            },
            toolbar: [
                    <g:if test="${variables}">['insert', ['variables']], </g:if>
                ['undoredo', ['undo', 'redo']],
                ['insert', ['emoji']],
                ['fontname', ['fontname']],
                <g:if test="${!noHtml}">
                , ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                // ['insert', ['link', 'picture']],
                ['view', ['codeview']]
                </g:if>
            ],
            hint: { // https://summernote.org/examples/#hint
                <g:if test="${isBracketVariables}">
                match: /\[\[([\w*\.?]*)$/,
                </g:if>
                <g:else>
                match: /\$\{(\w*)$/,
                </g:else>
                search: function (keyword, callback) {
                    callback($.grep(variables, function (item) {
                        return item.text.indexOf(keyword) == 0;
                    }));
                },
                content: function (item) {
                    setTimeout(function () {
                        setTextValue($summernote, $summernote.summernote('code'));
                    }, 100);
                    return item.value;
                },
                template: function (item) {
                    return item.text;
                }
            },
            callbacks: {
                <g:if test="${noHtml}">
                onPaste: function (e) {
                    var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                    e.preventDefault();
                    document.execCommand('insertText', false, bufferText);
                },
                </g:if>
                onInit: function () {
                    $summernote.summernote("removeModule", "autoSync");
                    <g:if test="${noHtml}">
                    setTextValue($summernote, $summernote.summernote('code'));
                    </g:if>
                    ${raw(onInit)}
                },
                onChange: function (contents) {
                    <g:if test="${noHtml}">
                    setTextValue($summernote, contents);
                    </g:if>
                    <g:else>
                    $summernote.val(contents);
                    </g:else>
                    $summernote.change();
                },
                onImageUpload: function (files) {
                    var formData = new FormData();
                    files.forEach(function (f) {
                        if (f.type.startsWith('image/')) {
                            formData.append('files', f);
                        } else {
                            $.notify(I18N.m('communication.drop.images.only'), 'error');
                        }
                    });
                    <g:if test="${noHtml}">
                    if (window.CommunicationBox) {
                        formData.append('clientId', CommunicationBox.clientId);
                        $.post({
                            url: tractionWebRoot + '/communication/addFiles',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (data) {
                                CommunicationBox.updateAttachedFiles();
                            },
                        });
                    }
                    </g:if>
                    <g:else>
                    $.post({
                        url: tractionWebRoot + '/container2/addDraft',
                        data: formData,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success: function (response) {
                            console.log('response', response);
                            response.forEach(function (f) {
                                $summernote.summernote('insertImage', f.url, f.filename);
                            });
                        }
                    });
                    </g:else>
                }
            }
        });

        function setTextValue($textarea, contents) {
            function getString($div) {
                var s = '';
                $div.contents().each(function () {
                    if (this.nodeType == 3) {
                        s += this.textContent.replaceAll('\n', '');
                    } else if (this.nodeType == 1) {
                        if (this.tagName == 'BR') {
                            s += '\n';
                        } else if (this.tagName == 'DIV' || this.tagName == 'P') {
                            if (s.length && !s.endsWith('\n')) {
                                s += '\n';
                            }
                            s += getString($(this));
                            if (!s.endsWith('\n')) {
                                s += '\n';
                            }
                        } else {
                            s += this.textContent.replaceAll('\n', '');
                        }
                    }
                });
                return s;
            }

            var value = getString($('<div>').html(contents));
            if (value.endsWith('\n')) {
                value = value.slice(0, -1);
            }
            $textarea.val(value);
        }
    });
</script>

