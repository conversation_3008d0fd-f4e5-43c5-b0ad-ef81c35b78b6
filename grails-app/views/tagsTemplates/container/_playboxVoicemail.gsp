<asset:stylesheet src="container/playbox.css"/>
<script>
    new PlayboxCall({
        uuid: '${uuid}',
        id: ${callNotes.id},
        url: '/web/getFile/${callNotes.voicemailFileDataId}',
        options: {
            waveColor: '#BAC6D0',
            progressColor: '#0086f0',
            height: 25,
            barWidth: 3,
            barGap: 3,
        },
    });
</script>

<div id="box-call-${uuid}">
    <div id='collapse-wave-call-${uuid}' class='divLoader'>
        <div class="loader"></div>

        <div id="wave-call-header-container-${uuid}" class="d-none d-flex pt-2 ps-1 pe-1 justify-content-between mb-2">
            <div id="wave-call-header-${uuid}" class="d-none d-flex">
                <g:if test="${callNotes.user}">
                    <div class="me-3">
                        <i class="fa-regular fa-phone-arrow-down-left me-1"></i>
                        <label>
                            ${callNotes.user.getFullName()}
                        </label>
                    </div>
                </g:if>
            </div>

            <div class="wave-call-buttons-${uuid} d-none">
                <a data-toggle="tooltip" title="<g:message code="download"/>"
                   class="fa-solid fa-arrow-down-to-bracket text-primary me-3 cursor" target="_blank"
                   href="<g:createLink absolute="true" controller="web" action="getFile"
                                       id="${callNotes.voicemailFileDataId}"/>">
                </a>
            </div>
        </div>

        <div class="waveform">
            <g:if test="${!callNotes?.userCanListen(currentUser)}">
                <div style="min-height:50px;"
                     id="wave-call-notallowed-${uuid}"
                     class="d-flex flex-column align-items-start justify-content-start badge text-white fs-1 py-3 text-emphasis bg-body-secondary">
                    <g:if test="${callNotes.isPrivate}">
                        <g:message code="call.private"/>
                    </g:if>
                    <g:else>
                        <span class="text-primary mb-2">
                            <i class="fa-solid fa-shield-keyhole"></i> <g:message code="call.listen.not.allowed"/>
                        </span>

                        <div>
                            <g:message code="call.listen.not.allowed.subtitle"/>
                        </div>
                    </g:else>
                </div>
            </g:if>
            <g:elseif test="${callNotes.voicemailFileDataId}">
                <div style="min-height:50px;"
                     id="wave-call-norecording-${uuid}"
                     class="cursor d-flex flex-column align-items-start justify-content-start badge text-white fs-1 py-3 text-emphasis bg-body-secondary ">
                    <span class="text-primary mb-2">
                        <i class="fa-solid fa-rotate"></i> <g:message code="no.recording"/>
                    </span>

                    <div>
                        <g:message code="no.recording.subtitle"/>
                    </div>
                </div>
            </g:elseif>

            <div class=""><div id="wave-call-${uuid}" class="cursor"></div></div>
        </div>

        <div class="wave-call-buttons-${uuid} d-none d-flex pt-2 ps-1">
            <i class="fa-solid fa-backward-fast text-primary me-3 cursor" id="changespeed-0-${uuid}"></i>
            <i class="fa-solid fa-play text-primary me-3 cursor" id="play-pause-${uuid}"></i>
            <i class="fa-solid fa-forward-fast text-primary me-3 cursor" id="changespeed-1-${uuid}"></i>
        </div>
    </div>
</div>