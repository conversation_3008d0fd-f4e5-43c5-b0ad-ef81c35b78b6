<script>
    $(document).ready(function () {

        if (window['chart-bar-today-request']) {
            window['chart-bar-today-request'].abort();
        }
        window['chart-bar-today-request'] = $.get(tractionWebRoot + '/callReport/getBarChart?isAllDepartments=${isAllDepartments}&departmentGroupIds=${departmentGroups.id.join(',')}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}', function (data) {
            var chartBar = document.getElementById('chart-bar-today');
            new Chart(chartBar.getContext('2d'), {
                type: 'horizontalBar',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    scales: {
                        yAxes: [{
                            barPercentage: 1.0,
                            categoryPercentage: 0.6
                        }]
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'top',
                        onHover: function (event, legendItem) {
                            chartBar.style.cursor = 'pointer';
                        },
                        onLeave: function (event, legendItem) {
                            chartBar.style.cursor = '';
                        }
                    }
                }
            });

            var chartPie = document.getElementById('chart-pie-today');
            console.log("report.calls", data.datasets.find(function (element) {
                return element.message == 'report.calls';
            }));
            var pieDataSet = data.datasets.find(function (element) {
                return element.message == 'report.calls';
            });
            pieDataSet.backgroundColor = pieDataSet.backgroundColorList;
            pieDataSet.borderColor = pieDataSet.borderColorList;
            var sum = pieDataSet.data.reduce((a, b) => a + b, 0);
            for (var i in data.labels) {
                var currentValue = pieDataSet.data[i];
                var percentage = 0;
                if (sum) {
                    percentage = Math.floor(((currentValue / sum) * 100) + 0.5);
                }
                data.labels[i] = data.labels[i] + ": " + currentValue + " (" + percentage + "%)";
            }

            new Chart(chartPie.getContext('2d'), {
                type: 'pie',
                data: {
                    labels: data.labels,
                    datasets: [
                        pieDataSet
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'left',
                        onHover: function (event, legendItem) {
                            chartPie.style.cursor = 'pointer';
                        },
                        onLeave: function (event, legendItem) {
                            chartPie.style.cursor = '';
                        }
                    },
                    hover: {
                        mode: 'nearest',
                        intersect: false
                    },
                    tooltips: {
                        mode: 'point',
                        intersect: true,
                        callbacks: {
                            label: function (tooltipItem, data) {
                                var label = data.labels[tooltipItem.index];
                                return label;
                            }
                        }
                    }
                }
            });

            $(chartPie).closest('.divLoader').removeClass('divLoader');
            $(chartBar).closest('.divLoader').removeClass('divLoader');
        });
    });
</script>

<div class="row">
    <div class="col-6">
        <div class="border rounded-1 ms-4 me-0 p-4">
            <div class="small-chart-container divLoader">
                <div class="loader"></div>
                <canvas id="chart-pie-today"></canvas>
            </div>
        </div>
    </div>
    <div class="col-6">
        <div class="border rounded-1 me-4 ms-0 p-4">
            <div class="small-chart-container divLoader">
                <div class="loader"></div>
                <canvas id="chart-bar-today"></canvas>
            </div>
        </div>
    </div>
</div>
