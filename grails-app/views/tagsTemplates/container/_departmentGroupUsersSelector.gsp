<button id="userSelectType${uuid}" class="m-2 btn btn-primary h-fit-content"><i class="fas fa-user${selectedUsers.size() != 1 ? 's' : ''}"></i></button>

<div id="user${uuid}" class="p-2" style="display: ${selectedUsers.size() != 1 ? 'none' : 'flex'};">
    <search:userInit/>
    <search:searchUser value="${selectedUsers.size() != 1 ? currentUser.username : selectedUsers.first().username}"
                       callback="updateUser${uuid}" type="search" class="form-control js-typeahead"
                       id="searchUser" autocomplete="off"/>
</div>

<div id="userSelect${uuid}" class="p-2" style="display: ${selectedUsers.size() != 1 ? 'flex' : 'none'}; width: 20em;">
    <container:departmentGroupUsersSelector2 id="multi-user-select-${uuid}" onchange="onMultiSelectChange${uuid}" departmentGroups="${departmentGroups}" selectedUsers="${selectedUsers}"/>
</div>

<script>
    window['user${uuid}'] = ['${selectedUsers.size() != 1 ? currentUser.id : selectedUsers.first().id}']; // userSearch id
    window['users${uuid}'] = $('#multi-user-select-${uuid}').val(); // List of user ids from multiselect
    function triggerOnchange${uuid}() {
        if ($('#user${uuid}').css('display') === "flex") {
            ${onchange}(window['user${uuid}']);
        }
        else {
            ${onchange}(window['users${uuid}']);
        }
    }
    triggerOnchange${uuid}();

    function updateUser${uuid}(user) {
        window['user${uuid}'] = [user.id];
        triggerOnchange${uuid}();
    }
    function onMultiSelectChange${uuid}() {
        window['users${uuid}'] = $('#multi-user-select-${uuid}').val();
        triggerOnchange${uuid}();
    }
    $(document).ready(function () {
        $('#userSelectType${uuid}').on('click', function () {
            if ($('#user${uuid}').css('display') === "flex") {
                $('#user${uuid}').css('display', 'none');
                $('#userSelect${uuid}').css('display', 'flex');
                $('#userSelectType${uuid}').html('<i class="fas fa-users"></i>');
                $('#multi-user-select-${uuid}').multipleSelect('refresh');
            } else {
                $('#user${uuid}').css('display', 'flex');
                $('#userSelect${uuid}').css('display', 'none');
                $('#userSelectType${uuid}').html('<i class="fas fa-user"></i>');
            }
            triggerOnchange${uuid}();
        });
    });
</script>