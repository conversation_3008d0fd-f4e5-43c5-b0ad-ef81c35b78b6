<iframe style="width: 100%;height: 100%;" frameborder="0" scrolling="auto" id="frame-${id}">
</iframe>

<script>
    $(document).ready(function() {
        var iframe = document.getElementById('frame-${id}');
        iframe.style.display = 'block';
        var iFrameDoc = iframe.contentWindow.document;
        //console.log("${raw(body.replace('"', '\\"').replace('/', '\\/').replace("\n", "").replace("\r", ""))}");
        iFrameDoc.write("${raw(body.replace('"', '\\"').replace('/', '\\/').replace("\n", "").replace("\r", ""))}");
        iFrameDoc.close();
        <g:if test="${triggerSelector}">
            $('${triggerSelector}').one('click', function() {
                setResize();
            });
        </g:if>
        <g:else>
            setResize();
        </g:else>

        function setResize() {
            var iframeInterval = setInterval(function () {
                if (iframe.contentDocument && iframe.contentDocument.body) {
                    if (iframe.contentDocument.body.scrollHeight != 0 && iframe.contentDocument.body.scrollWidth != 0) {
                        iframe.style.height = (iframe.contentDocument.body.scrollHeight + 1) + 'px';
                        clearInterval(iframeInterval);
                    }
                }
            }, 500);
        }
    });
</script>