<div id="${id}-select2-container" class="w-100">
    <select style="display: none; border-radius: 4px;" id="${id}" name="${name}" ${multiple ? 'multiple="multiple"' : ""} ${disabled ? 'disabled' : ''} onchange="${onchange}">
        <g:if test="${addNoneOption}">
            <option value="">-</option>
        </g:if>
        <g:each status="i" var="department" in="${traction.department.Department.getAll()}">
            <g:if test="${department.users.any { !it.deleted }}">
                <g:each status="j" var="departmentGroup" in="${department.groups.findAll { userGroup == null || it.userGroup == userGroup }}">
                    <g:if test="${departmentGroup.users.findAll { !it.deleted }}">
                        <optgroup label="${department.name} <g:message code="${departmentGroup.userGroup}"/>">
                            <g:each var="user" in="${departmentGroup.users.findAll { !it.deleted }}">
                                <option value="${user.id}" ${selectedUsers.contains(user) ? "selected" : ""}>${user.getFullName()}</option>
                            </g:each>
                        </optgroup>
                    </g:if>
                </g:each>
                <g:if test="${userGroup == null && department.users.any { !it.deleted && it.userGroup == null }}">
                    <optgroup label="${department.name} <g:message code="no.usergroup"/>">
                        <g:each var="user" in="${department.users.findAll { !it.deleted && it.userGroup == null }}">
                            <option value="${user.id}" ${selectedUsers.contains(user) ? "selected" : ""}>
                                ${user.getFullName()}
                            </option>
                        </g:each>
                    </optgroup>
                </g:if>
            </g:if>
        </g:each>
    </select>
</div>
<g:if test="${initSelect2}">
    <script>
        $(document).ready(function () {
            $('#${id}').select2({
                <g:if test="${dropdownParent}">
                dropdownParent: $('${dropdownParent}'),
                </g:if>
                theme: 'bootstrap-5'
            }).preventDuplicateSelect2Values();
        });
    </script>
</g:if>