<script>
    $('.treeboxgrid-dropdown-menu .removeclose').click(function(e) {
        e.stopPropagation();
    });
    
    // Close the dropdown if the user clicks outside of it
    window.onclick = function(event) {
      if (!event.target.matches('.treeboxgrid-dropbtn')) {

        var dropdowns = document.getElementsByClassName("treeboxgrid-dropdown-content");
        var i;
        for (i = 0; i < dropdowns.length; i++) {
          var openDropdown = dropdowns[i];
          if (openDropdown.classList.contains('show')) {
            openDropdown.classList.remove('show');
          }
        }
      }
    }
</script>

<div class="treeboxgrid-dropdown" style="display: contents;">
    <a href="javascript:void(0);" onclick="document.getElementById('${id}_${link}_${container}_dropdown').classList.toggle('show');" id="${id}_${link}_${container}_drop" class="treeboxgrid-dropbtn btn btn-primary btn-sm">${text.take(45)}...</a>
    <input type="hidden" id="${id}_${link}_${container}_droptext" name="${id}_${link}_${container}_droptext" value="${text}"/>
    <div id="${id}_${link}_${container}_dropdown" class="treeboxgrid-dropdown-content">
        <div class="" style="padding: 5px;">
            <g:each status="i" var="checkbox" in="${listcheck}">
                <label style='display: flex;align-items: center;' class="">${checkbox.level}<input type="checkbox" style="" class="form-check-inline" onclick="datatableupdatetreecheckbox('${id}_${link}_${container}_tagtemplate',${id},'${collection}','${type}','${id}_${link}_${container}_droptext','${link}','${container}','${id}_${link}_${container}_${checkbox.id}');" value="${checkbox.value}" id="${id}_${link}_${container}_${checkbox.id}" ${checkbox.checked}><a href="javascript:void(0);" onclick="datatabletreecheckboxshow('${id}_${link}_${container}_tagtemplate',${id},'${id}_${link}_${container}_${checkbox.id}','${collection}','${type}','${id}_${link}_${container}_droptext','${link}','${container}');">${checkbox.name} <i class="${checkbox.icon}}"></i></a></label>
            </g:each>
        </div>
    </div>
</div>
    