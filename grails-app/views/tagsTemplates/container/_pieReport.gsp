<div id="${pieReport.id}"></div>
<script>
    pieReport_${pieReport.id} = ${raw(pieReportJSON)};

    $(document).ready(function() {

        console.log('PieReport:', pieReport_${pieReport.id});

        google.charts.load("current", {packages: ["corechart"]});

        google.charts.setOnLoadCallback(function () {
            var data = google.visualization.arrayToDataTable(pieReport_${pieReport.id}.data);
/*
            if (params.isCash) {
                var formatter = new google.visualization.NumberFormat({
                    prefix: '$',
                    negativeColor: 'red',
                    negativeParens: true,
                    fractionDigits: 0
                });
                formatter.format(data, 1);
            }
*/
            /*
            var options = {
                title: json.title
            };

            if (params.pieHole) {
                options.pieHole = params.pieHole;
            }
            if (params.pieSliceText) {
                options.pieSliceText = params.pieSliceText;
            }
*/
            var chart = new google.visualization.PieChart(document.getElementById('${pieReport.id}'));

            chart.draw(data, pieReport_${pieReport.id}.options);
        });

    });
</script>