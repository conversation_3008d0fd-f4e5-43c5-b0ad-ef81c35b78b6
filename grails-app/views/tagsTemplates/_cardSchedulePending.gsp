<div class="work-order-job-card card shadow" onclick="event.stopPropagation()"
     style="border-left-color: ${schedule.getColor()}">
    <input type="text" value="${schedule.pendingClientId}" id="pendingClient" hidden/>
    <div>
        <div class="mb-3">
            <g:if test="${schedule.pendingClient}">
                <div class="bd-bottom-grey">
                    <communication:avatar client="${schedule.pendingClient}" class="communication-avatar-xs"/>
                    <div class="d-flex flex-row">
                    <a class="fw-bold mdi mdi-open-in-new" target="_blank" href="<g:createLink controller="client" action="index" id="${schedule.pendingClientId}"/>">
                        ${schedule.pendingClient?.getFullName()}
                    </a>
                    </div>
                    <g:if test="${schedule.pendingClientVehicle}">
                        <div class="d-flex flex-row">
                            <a class="fw-bold mdi mdi-open-in-new" target="_blank" href="<g:createLink controller="vehicle" action="index" id="${schedule.pendingClientVehicle.id}"/>">
                                <g:message code="vehicle"/>: ${schedule.pendingClientVehicle}
                            </a>
                        </div>

                    </g:if>
                </div>
            </g:if>
            <g:else>
                <search:clientInit/>
                <search:contactSearch id="search-contact-inputreservation" type="text"
                                     class="form-control"
                                     callback="selectPendingClient"
                                     placeholder="${g.message(code: "client.search")}"/>
            </g:else>
        </div>
        <div class="mb-3">
            <textarea class="form-control" rows="10" id="pendingNote"  onchange="editPending()">${schedule.pendingNote}</textarea>
        </div>
        <container:pendingColorSelect id="pendingColor" value="${schedule.pendingColor}" onchange="editPending()"/>
    </div>
    <script>
        function selectPendingClient(item) {
            $('#pendingClient').val(item.id);
            editPending();
        }
        function editPending() {
            console.log('editPending');
            showBigLoader();
            $.post({
                url: tractionWebRoot + '/serviceSchedule/editPending',
                data: {
                    id: '${schedule.id}',
                    pendingColor: $('#pendingColor').val(),
                    pendingNote: $('#pendingNote').val(),
                    pendingClient: $('#pendingClient').val()
                },
                success: function (data) {
                    if (data.success) {
                        window.ServiceScheduleIndexApp.$children[0].refresh();
                        $.notify(data.message, 'success');
                    } else {
                        $.notify(data.message, 'error');
                    }
                },
                complete: hideBigLoader
            });
        }
    </script>
</div>