<%@ page import="traction.DateUtils" %>
<div id="opportunity-card-${vehicle?.id}" class="card mb-2 form-mode-read rounded-1">
    <div class="card-body p-4">
        <div class="row mb-3 mx-0 mx-lg-0">
            <div class="col-xs-12 col-sm p-0">
                <h5 class="card-title mb-0 d-flex align-items-center">
                    <g:if test="${selectable == true}">
                        <input id="opportunity-${opportunity.id}" type="checkbox" class="fw-medium form-check-input m-0 opportunity-selection me-2 border-primary" value="${opportunity.id}" ${selected ? 'checked': ''}/>
                    </g:if>
                    <span>${opportunity.name}</span>
                </h5>
            </div>
            <div class="col-auto pe-0">
                <div class="d-flex flex-row">
                    <g:if test="${opportunity?.vehicle}">
                        <div class="d-flex justify-content-between">
                            <g:if test="${vehicleOpportunityDanger || vehicleOpportunityWarning || vehicleOpportunitySecondary}">
                                <div class="cursor border-end me-3 pe-3" onclick="TractionModal.show({
                                    url: '/vehicle/modalOpportunities',
                                    data: {id: ${opportunity.vehicleId}, opportunityId: ${opportunity.id}}
                                });">
                                    <g:if test="${vehicleOpportunityDanger}">
                                        <span data-toggle="tooltip"
                                              title="<g:message code="vehicle.opportunities.sold"/>"
                                              class="border border-1 border-danger text-uppercase text-danger p-2 py-1 fw-medium fs-7 rounded-pill align-content-center f-12 fw-bold ${vehicleOpportunityWarning ? 'me-2' : ''}">${vehicleOpportunityDanger}</span>
                                    </g:if>
                                    <g:if test="${vehicleOpportunityWarning}">
                                        <span data-toggle="tooltip"
                                              title="<g:message code="vehicle.opportunities.active"/>"
                                              class="border border-1 border-warning text-uppercase text-warning p-2 py-1 fw-medium fs-7 rounded-pill align-content-center f-12 fw-bold ${vehicleOpportunitySecondary ? 'me-2' : ''}">${vehicleOpportunityWarning}</span>
                                    </g:if>
                                    <g:if test="${vehicleOpportunitySecondary}">
                                        <span data-toggle="tooltip"
                                              title="<g:message code="vehicle.opportunities.other"/>"
                                              class="border border-1 border-secondary text-uppercase text-secondary p-2 py-1 fw-medium fs-7 rounded-pill align-content-center f-12 fw-bold">${vehicleOpportunitySecondary}</span>
                                    </g:if>
                                </div>
                            </g:if>
                            <div class="d-flex border-end me-3 pe-3 gap-2">
                                <span class="badge badge-outline-secondary-emphasis text-uppercase"><g:message
                                        code="${opportunity?.vehicle?.status?.message}"/></span>
                                <span class="badge badge-outline-secondary-emphasis text-uppercase">${DateUtils.getReallyShortDurationString(opportunity?.date)}</span>
                            </div>
                        </div>
                    </g:if>
                    <div class="d-flex gap-2">
                        <span class="badge badge-outline-primary text-uppercase"><g:message
                                code="${opportunity?.category?.message}"/></span>
                        <span class="badge badge-outline-primary text-uppercase"><g:message
                                code="${opportunity?.status?.message}"/></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex flex-row">
            <div class="d-flex flex-column me-4" style="width: 10%;">
                <div class="text-center h-100">
                    <g:if test="${opportunity}">
                        <g:set var="pictures" value="${opportunity.vehicle?.asInheritedVehicle()?.getInheritedPictures()}"/>
                        <g:set var="firstImage" value="${pictures?.getFirstWatermarkedPicture()}"/>
                        <div class="border rounded-1 position-relative align-content-center">
                            <g:if test="${firstImage}">
                                <img src="<g:createLink absolute="true" controller="web" action="getFile"
                                                        id="${firstImage.id}"/>" class="img-fluid h-100 object-fit-contain" alt="">
                            </g:if>
                            <g:else>
                                <asset:image src="image-missing.jpg" class="img-fluid" alt=""/>
                            </g:else>
                        </div>
                    </g:if>
                    <g:else>
                        <i class="fa-solid fa-camera text-primary pt-6 pb-6"
                           style="font-size: 4rem; --bs-text-opacity: .5;"></i>
                    </g:else>
                </div>
            </div>

            <div class="vehicleCard-col-lg-3 w-auto d-flex flex-column mx-0 me-lg-4 p-0 flex-grow-1">
                <div class="row g-3 mb-3 mt-0 mx-0  line-height-19">
                    <div class="col-3 mt-0 p-0" style="width: 138px;">
                        <label><g:message code="vehicle.modelCode" /></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.modelCode ?: '-'}
                    </div>
                </div>

                <div class="row g-3 mb-3 mt-0 mx-0 line-height-19">
                    <div class="col-3 mt-0 p-0" style="width: 138px;">
                        <label><g:message code="vehicle.serialNumber"/></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.serialNumber ?: "-"}
                    </div>
                </div>

                <div class="row g-3 mb-3 mt-0 mx-0 line-height-19">
                    <div class="col-3 mt-0 p-0 " style="width: 138px;">
                        <label><g:message code='vehicle.stockNumber'/></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.stockNumber ?: "-"}
                    </div>
                </div>

                <div class="row g-3 mt-0 mx-0  line-height-19">
                    <div class="col-3 mt-0 p-0" style="width: 138px;">
                        <label><g:message code='product.disponibilityStatus'/></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        <g:message code="${opportunity.vehicle?.status ?: '-'}"/>
                    </div>
                </div>
            </div>


            <div class="vehicleCard-col-lg-3 w-auto d-flex flex-column mx-0 mx-lg-2 ms-lg-0 p-0 flex-grow-1">
                <div class="row g-3 mb-3 mt-0 mx-0 line-height-19">
                    <div class="col-3 mt-0 p-0" style="width: 138px;">
                        <label><g:message code='vehicle.localisation'/></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.location ?: "-"}
                    </div>
                </div>

                <div class="row g-3 mb-3 mt-0 mx-0 line-height-19">
                    <div class="col-3 mt-0 p-0 " style="width: 138px;">
                        <label><g:message code="product.kilometers" /></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.odometer?.value ?: 0.00D}
                    </div>
                </div>

                <div class="row g-3 mb-3 mt-0 mx-0 line-height-19">
                    <div class="col-3 mt-0 p-0" style="width: 138px;">
                        <label><g:message code="product.hours" /></label>
                    </div>

                    <div class="mode-read col-9 ps-1 mt-0 align-content-center">
                        ${opportunity.vehicle?.odometer?.value ?: 0.00D}
                    </div>
                </div>
                <div class="d-flex flex-row g-3 mt-0 mx-0 line-height-19">
                    <span class="text-body-tertiary me-4"><g:message code="opportunity.created.on"/> ${opportunity.date?.format("MM-dd-yyyy") ?: '-'}</span>
                    <span class="text-body-tertiary me-4"><g:message code="opportunity.modified.on"/> ${opportunity.dateMOD?.format("MM-dd-yyyy") ?: '-'}</span>
                    <span class="text-body-tertiary"><g:message code="opportunity.sold.on"/> ${opportunity.dateSOLD?.format("MM-dd-yyyy") ?: '-'}</span>
                </div>
            </div>
        </div>
    </div>
</div>