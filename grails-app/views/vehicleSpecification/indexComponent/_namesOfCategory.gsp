<g:each var="name" in="${names}" status="count">
    <div id="specification-name-${count}" class="form-mode-read">
        <div class="mode-read d-flex justify-content-between align-items-center f-16 w-100" style="padding:5.5px 24px !important;  height: 35px; line-height: 24px;">
            <div class="col col-md-10" role="button" onclick="SpecificationTab.getValuesFromName('${category.replaceAll("\\'", "&quot")}', '${name.key.replaceAll("\\'", "&quot")}', this);  showButtons(this);">
                ${name.key} <a class="link" onclick="SpecificationTab.getSelectedVehicles('${category.replaceAll("\\'", "&quot")}', '${name.key.replaceAll("\\'", "&quot")}')">(${name.value})</a>
            </div>
            <div class="d-flex align-items-center">
                <div class="d-flex justify-content-center align-items-center me-2 btn-default d-none" role="button"
                     style="width: 24px; height: 24px;"
                     onclick="FormModeUtils.edit('#specification-name-${count}')">
                    <i class="fa-light fa-pen text-primary"></i>
                </div>

                <div class="d-flex justify-content-center align-items-center btn-default d-none" role="button"
                     style="width: 24px; height: 24px;"
                     onclick="SpecificationTab.bulkDeleteName('${category.replaceAll("\\'", "&quot")}', '${name.key.replaceAll("\\'", "&quot")}')">
                    <i class="fa-light fa-trash text-primary"></i>
                </div>
            </div>
        </div>
        <div class="mode-edit d-flex justify-content-between align-items-center f-16 w-100" style="padding:5.5px 24px !important;  height: 35px; line-height: 24px;">
            <input class="form-control form-control-sm me-2" type="text" value="${name.key}" placeholder="${name.key}">
            <div class="d-flex align-items-center">
                <div class="d-flex justify-content-center align-items-center me-2" role="button"
                     style="width: 24px; height: 24px;"
                     onclick="SpecificationTab.editNameCategory('${category.replaceAll("\\'", "&quot")}', this)">
                    <i class="fa-solid fa-floppy-disk text-primary"></i>
                </div>
                <div class="d-flex justify-content-center align-items-center" role="button"
                     style="width: 24px; height: 24px;"
                     onclick="FormModeUtils.cancel('#specification-name-${count}')">
                    <i class="fa-regular fa-pen-slash text-primary"></i>
                </div>
            </div>
        </div>
    </div>
</g:each>
