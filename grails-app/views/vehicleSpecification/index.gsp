<%@ page contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="vehicle.specification"/></title>
    <asset:javascript src="vehicleSpecification/specificationTab.js"/>
    <asset:javascript src="vehicleTranslatedText/VehicleTranslatedTextDataTable.js"/>
    <script>
        function showButtons(element) {
            const allButtons = document.querySelectorAll('.btn-default');
            allButtons.forEach(button => button.classList.add('d-none'));
            const modalbuttons = document.querySelectorAll('.link');
            modalbuttons.forEach(button => button.classList.remove('d-none'));

            const modalbutton = element.parentElement.querySelectorAll('.link');
            modalbutton.forEach(button => button.classList.add('d-none'));
            const buttons = element.parentElement.querySelectorAll('.btn-default');
            buttons.forEach(button => button.classList.remove('d-none'));

        }
    </script>
</head>

<body>
<div class="p-4">
    <h4 class="text-uppercase mb-5">
        <!--<i class="fa-solid fa-circle-question me-2 fs-5 lh-1" onclick="TractionHelp.getHelp();"></i>--->
        <g:message code="vehicle.specification"/>
    </h4>
    <h5 class="text-uppercase mb-2 line-height-19" >
        <g:message code="vehicle.create.specifications"/>
    </h5>
    <div class="d-flex mb-5">
        <div class="col-md-4 pe-4">
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.categories.filter"/></p>
            <input class="form-control" type="text" onkeyup="SpecificationTab.search($(this).val(), 'category')" />
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.categories.select"/></p>
            <div id="category" class="border overflow-y-auto rounded" style="height: 550px; padding-top: 10.5px;">
                <g:each var="cat" in="${categories}" status="count">
                    <div id="specification-category-${count}"
                         class="form-mode-read specification-category">
                        <div class="mode-read d-flex justify-content-between align-items-center f-16 w-100" style="padding:5.5px 24px !important;  height: 35px; line-height: 24px;">
                            <div class="col col-md-9" role="button"
                                 onclick="SpecificationTab.getNamesFromCategory('${cat.key.replaceAll("\\'", "&quot")}', this); showButtons(this)">
                                ${cat.key}
                            </div>

                            <div class="d-flex align-items-center">
                                <a class="link me-2" role="button"
                                   onclick="SpecificationTab.getSelectedVehicles('${cat.key.replaceAll("\\'", "&quot")}')">(${cat.value})</a>
                                <div class="d-flex justify-content-center align-items-center me-2 btn-default d-none" role="button"
                                     style="width: 24px; height: 24px;"
                                     onclick="FormModeUtils.edit('#specification-category-${count}')">
                                    <i class="fa-light fa-pen text-primary"></i>
                                </div>

                                <div class="d-flex justify-content-center align-items-center btn-default d-none" role="button"
                                     style="width: 24px; height: 24px;"
                                     onclick="SpecificationTab.bulkDeleteCategory('${cat.key.replaceAll("\\'", "&quot")}')">
                                    <i class="fa-light fa-trash text-primary"></i>
                                </div>
                            </div>
                        </div>

                        <div class="mode-edit d-flex justify-content-between align-items-center f-16 w-100" style="padding:5.5px 24px !important;  height: 35px; line-height: 24px;">
                            <input name="${cat.key}" class="form-control form-control-sm me-2" type="text"
                                   value="${cat.key}" placeholder="${cat.key}">
                            <div class="d-flex align-items-center">

                                <div class="d-flex justify-content-center align-items-center me-2" role="button"
                                     style="width: 24px; height: 24px;"
                                     onclick="SpecificationTab.editCategory(this, '#specification-category-${count}')">
                                    <i class="fa-solid fa-floppy-disk text-primary"></i>
                                </div>

                                <div class="d-flex justify-content-center align-items-center" role="button"
                                     onclick="FormModeUtils.cancel('#specification-category-${count}')">
                                    <i class="fa-regular fa-pen-slash text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </g:each>
            </div>
        </div>

        <div class="col-md-4 pe-4">
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.names.filter"/></p>
            <input class="form-control" type="text" onkeyup="SpecificationTab.search($(this).val(), 'namesFromCategory')" />
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.names.select"/></p>
            <div id="namesFromCategory" class="border overflow-y-auto w-100 rounded text-truncate" style="height: 550px; padding-top: 10.5px;">
            </div>
        </div>

        <div class="col-md-4">
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.values.filter"/></p>
            <input class="form-control" type="text" onkeyup="SpecificationTab.search($(this).val(), 'valuesFromName')" />
            <p class="fw-bold mb-1"><g:message code="vehicle.specification.values.select"/></p>
            <div id="valuesFromName" class="border overflow-y-auto w-100 rounded" style="height: 550px; padding-top: 10.5px;"></div>
        </div>
    </div>
    <hr class="m-0 p-0 mb-5">

    <h4 class="text-uppercase mb-3 line-height-19" >
        <g:message code="vehicle.specifications.preview"/>
    </h4>

    <div class="mb-4">
        <div class="d-flex mb-2">
            <p style="margin: auto 10px auto 0px;"><g:message code="vehicle.text.translated.show"/></p>
            <select class="form-control w-auto select-lang-translated-text me-2">
                <g:each var="lang" in="${langs}">
                    <g:if test="${lang.value != "FR"}">
                        <option value="${lang.value}">${lang.value}</option>
                    </g:if>
                </g:each>
            </select>
            <button class="btn btn-primary" onclick="SpecificationTab.getTranslatedTextByLang()">OK</button>
            <a class="btn btn-primary ms-auto" onclick="SpecificationTab.createVehicleTranslatedText()"><g:message code="vehicle.text.translated.update"/></a>
        </div>

        <div class="container-translated-text" id="container-translated-text"></div>

    </div>
</div>
</body>
</html>
