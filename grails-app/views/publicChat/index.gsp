<%@ page contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <asset:stylesheet src="communication/item.css"/>
    <asset:stylesheet src="publicChat/index.css"/>
    <asset:stylesheet src="lib/mdi/css/materialdesignicons.css"/>
    <asset:javascript src="lib/jquery-3.7.1.min.js"/>
    <asset:javascript src="lib/js.cookie.js"/>
    <asset:javascript src="spring-websocket"/>
</head>

<body>

<button id="start-chat-btn" class="primary-bg-color shadow-light-hover">
    <span class="label">
        <g:message code="chat.now" locale="${lang}"/>
    </span>
    <div>
        <i class="mdi mdi-bell-outline" style="display: none; "></i>
        <i class="mdi mdi-chat-processing-outline"></i>
    </div>
</button>

<div id="chat-window" class="shadow">
    <div id="close-confirm">
        <div style="padding-bottom: 10px;"><g:message code="chat.close.confirm" locale="${lang}"/></div>
        <button class="btn btn-no w-100 shadow-light-hover" onclick="ClientChatCommunication.closeCancel()">
            <g:message code="no" locale="${lang}"/>
        </button>
        <button class="btn btn-yes w-100 primary-bg-color shadow-light-hover" onclick="ClientChatCommunication.closeConfirm()">
            <g:message code="yes" locale="${lang}"/>
        </button>
    </div>
    <div class="header-form primary-bg-color">
        <div class="header-title">
            <g:message code="welcome" locale="${lang}"/>!
            <div class="right-icons">
                <i class="mdi mdi-bell-outline" style="display: none;"></i>
                <i class="mdi mdi-window-minimize" id="minimize"></i>
                <i class="mdi mdi-window-close" id="close" style="display: none;"></i>
            </div>
        </div>

        <hr/>
        <div id="connected-user">

        </div>
    </div>

    <div id="public-chat-div"></div>

    <div class="footer-form">
        <g:message code="powered.by" locale="${lang}"/>
        <asset:image src="traction-chat-logo.png" alt="Traction Logo" width="60"/>
    </div>
</div>

<script>
    window.tractionWebRoot = '${request.getContextPath()}';
    window.m = {
        <g:each var="m" in="[
            'chat.continue',
            'chat.widget.one.moment.please',
            'chat.widget.welcome',
            'chat.widget.welcome.client',
            'chat.notAvailable',
            'chat.widget.say',
            'client.fullname',
            'client.firstname',
            'client.lastname',
            'client.phonecell',
            'client.email',
            'chatwidgetgroup.select',
            'chatwidgetgroup.available.true',
            'chatwidgetgroup.available.false',
            'chat.widget.message',
            'chat.widget.message.placeholder',
            'chat.widget.start',
            ]">
        "${m}": "<g:message code="${m}" locale="${lang}"/>",
        </g:each>
    };
</script>
<asset:javascript src="publicChat/ClientChat.js"/>
</body>
</html>