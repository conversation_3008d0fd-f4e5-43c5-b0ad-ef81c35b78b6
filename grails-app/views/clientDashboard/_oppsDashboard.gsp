<g:each var="opp" in="${opportunities}">
    <a onclick="openFocusTab('<g:createLink absolute="true" controller="client" action="index" params="[id: opp.clientId, opporid: opp.id]"/>')"
       href="javascript:void(0)">
        <traction:card opportunity="${opp}"/>
    </a>
</g:each>
<g:if test="${loadMore}">
    <g:if test="${status}">
        <button class="btn btn-sm btn-primary mdi mdi-unfold-more-horizontal" style="margin: 0px 30% 10px 30%;width: 100%;" onclick="getOpportunitiesByStatus(${status.id}, true, this)"><g:message code="load.more"/> ...</button>
    </g:if>
    <g:elseif test="${category}">
        <button class="btn btn-sm btn-primary mdi mdi-unfold-more-horizontal" style="margin: 0px 30% 10px 30%;width: 100%;" onclick="getOpportunitiesByCategory(${category.id}, true, this)"><g:message code="load.more"/> ...</button>
    </g:elseif>
</g:if>