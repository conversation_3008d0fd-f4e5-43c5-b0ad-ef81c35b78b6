<div class="row">
    <div class="col-xs-12 col-lg-6">
        <select class="form-select" style="flex: 1 1 0;" id="value-modifier-param-value1" onchange="">
            <g:each var="field" in="${mapRedirectGeneralFields}">
                <option value="${field}" <g:if test="${field.equals(value1)}">selected</g:if>>${field}</option>
            </g:each>
        </select>
    </div>
    <div class="col-xs-12 col-lg-6">
        <button type="button" class="btn btn-sm btn-primary fw-normal border rounded p-1 w-100" onclick="VehicleGeneralImportIndexMapping('${action?.value2}');"><g:message code="vehicle.feed.map.redirection"/></button>
    </div>
</div>
