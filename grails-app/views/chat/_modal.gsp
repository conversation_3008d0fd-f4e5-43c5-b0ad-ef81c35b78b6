<asset:javascript src="/chat/modal.js"/>

<g:if test="${message != null}">
    <div class="modal-dialog modal-dialog-scrollable d-none">
        <div class="modal-content">
            <div class="modal-header">
            </div>

            <div class="modal-body">
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            setTimeout(function () {
                $.notify('${message}', 'error');
                TractionModal.hide()
            }, 600)
        });
    </script>
</g:if>
<g:else>
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title fw-bold">
                    <g:message code="client.transfer"/>
                </h2>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <div>
                    <p><span style="font-weight: bold"><g:message code="client"/>:</span> ${client?.fullName}</p>
                </div>

                <div id="transfer-chat">
                    <div class="mb-3">
                        <strong><g:message code="user"/></strong>
                        <select class="form-control" id="transfertselect-${channel?.id}-${client?.id}"
                                name="transferchatselect" onchange="">
                            <g:each var="usr" in="${userlist.sort { it.username }}">
                                <option value="${usr.id}">${usr.username}</option>
                            </g:each>
                        </select>
                    </div>

                    <button class="btn btn-primary float-right mt-2" onclick="transferChat()">
                        <g:message code="client.transfer"/>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            ChatModal.init({
                clientId: '${client?.id}',
                channelId: '${channel?.id}',
                userlist: '${userlist}'
            });
        });

        function transferChat() {
            var sel = document.getElementById("transfertselect-${channel?.id}-${client?.id}");
            showBigLoader();
            $.ajax({
                url: tractionWebRoot + "/chat/transfer",
                type: "POST",
                data: {
                    "channelId": ${channel?.id},
                    "clientId": ${client?.id},
                    "user": sel.value
                },
                success: function (data) {
                    if (data && data.success) {
                        $.notify(data.message ? data.message : 'success', 'success');
                    } else {
                        $.notify(data.message ? data.message : 'error', 'error');
                    }
                    TractionModal.hide();
                    hideBigLoader();
                    ChatManager.initLoop()
                }
            });
        }
    </script>
</g:else>