<html>
<head>
    <meta name="layout" content="publicClientLayout"/>
    <title><g:message code="calendarevent.confirmation"/> | ${client.fullName}</title>
    <g:if test="${opportunity?.getVehicleFirstPictureUrl()}">
        <meta property="og:image" content="${opportunity.getVehicleFirstPictureUrl()}">
    </g:if>
    <meta property="og:type" content="website" />
    <meta property="og:title" content="${config.title}" />
    <meta property="og:description" content="${event.start.format("yyyy-MM-dd")} - ${config.description}" />
    <meta property="og:url" content="${baseUrl}" />
    <meta property="og:site_name" content="${companyName}" />
    <meta property="article:modified_time" content="2021-03-19T15:15:49+00:00" />
</head>
<body>
<div class="w-100">
    <div class="d-flex justify-content-center">
        <div class="card m-5" style="width: 80%">
            <div class="card-header">
                <h5 class="card-title"><g:message code="calendarevent.confirmation.workflowData" args="[opportunity.name, client.fullName]"/></h5>
            </div>
            <div class="card-body">
                <h6 class="card-subtitle mb-2">${config.title}</h6>
                <p class="card-text">
                <pre>${config.description}</pre>
            </p>
                <g:if test="${config.location}">
                    <p class="mdi mdi-map-marker">${config.location}</p>
                </g:if>
                <p class="card-text">
                    <g:message code="date"/>: ${event.start.format("yyyy-MM-dd HH:mm")} - ${event.end.format("yyyy-MM-dd HH:mm")}<br>
                </p>
            </div>
            <div class="card-footer text-body-tertiary clearfix">
                <button class="btn btn-danger mdi mdi-cancel float-left" onclick="confirmEventCalendar(${traction.service.CalendarEvent.Status.REFUSED.id})"><g:message code="calendarevent.status.refused.label"/></button>
                <button class="btn btn-primary mdi mdi-pin float-right" onclick="confirmEventCalendar(${traction.service.CalendarEvent.Status.ACCEPTED.id})"><g:message code="calendarevent.status.accepted.label"/></button>
            </div>
        </div>
    </div>
</div>
<script>
    function confirmEventCalendar(status) {
        $.post({
            url: tractionWebRoot + '/calendarEvent/setStatus',
            data: {
                access: '${access}',
                id: ${event.id},
                actionId: ${action.id},
                status: status
            },
            success: function (data) {
                if (data.success) {
                    location.reload();
                }
                else {
                    $.notify('Error updating event status', 'error');
                }
            }
        });
    }
</script>
</body>
</html>
