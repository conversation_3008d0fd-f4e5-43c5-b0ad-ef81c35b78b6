<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <g:if test="${calendar}">
        <title>${calendar.name} | <g:message code="servicecalendar"/></title>
    </g:if>
    <g:else>
        <title><g:message code="servicecalendar.create"/></title>
    </g:else>
    <asset:javascript src="lib/jscolor/jscolor.js"/>
    <asset:javascript src="serviceCalendar/edit.js"/>
</head>

<body>
<div class="m-4 d-flex">
    <g:if test="${calendar}">
        <h1><g:message code="servicecalendar.edit"/> - ${calendar.name}</h1>
    </g:if>
    <g:else>
        <h1><g:message code="servicecalendar.create"/></h1>
    </g:else>
    <div class="d-flex flex-row-reverse ms-auto">
        <div class="p-2">
            <button onclick="saveCalendar('${calendar?.id}');" class="btn btn-primary fw-bold">
                <g:message code="client.save"/>
            </button>
        </div>

        <g:if test="${calendar}">
            <div class="p-2">
                <button class="btn btn-danger fw-bold" onclick="deleteCalendar(${calendar.id});">
                    <g:message code="default.button.delete.label"/>
                </button>
            </div>
        </g:if>
    </div>
</div>

<div class="m-4">

    <div class="row">
        <div class="col-12 mb-3">
            <label><g:message code="servicecalendar.name"/></label>
            <input class="form-control" value="${calendar?.name}" id="name"/>
        </div>
        <div class="col-12 mb-3">
            <label><g:message code="servicecalendar.color"/></label>
            <input id="color" class="jscolor form-control" value="${calendar?.color ? calendar.color.substring(1) : ''}"/>
        </div>
        <div class="col-12 mb-3">
            <label><g:message code="serviceresources"/></label>
            <select id="resources" class="form-control" multiple="true">
                <optgroup label="<g:message code="users"/>">
                    <g:each var="r" in="${resources.findAll { it.user }}">
                        <option value="${r.id}" ${calendar?.resources?.id?.contains(r.id) ? 'selected' :''}>${r.getTitle()}</option>
                    </g:each>
                </optgroup>
                <optgroup label="<g:message code="serviceresource.external"/>">
                    <g:each var="r" in="${resources.findAll { !it.user }}">
                        <option value="${r.id}" ${calendar?.resources?.id?.contains(r.id) ? 'selected' :''}>${r.getTitle()}</option>
                    </g:each>
                </optgroup>
            </select>
        </div>
    </div>
</div>
</body>
</html>