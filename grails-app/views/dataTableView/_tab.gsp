<div id="datatableview-table" class="h-100 overflow-scroll border rounded-2 p-4 me-4 mb-4">
    <h5 class="text-emphasis fw-bold f-18 text-uppercase mb-3"><g:message code="tableview.config.title"></g:message></h5>
    <button type="button" class="btn btn-primary shadow-sm fw-bold float-left" onclick="showDefaultView()" ><g:message code="datatable.view.set.default"/></button>
</div>
<asset:javascript src="datatable/DataTableViewTable.js"/>
<g:message code=""/>
<script>
    $(document).ready(function () {
        window.dataTableViews = new DataTableViewTable('datatableviews', 'datatableview-table');
    });

    function showDefaultView() {
        TractionModal.show({url: '/dataTableCookie/defaultModal'})
    }
</script>