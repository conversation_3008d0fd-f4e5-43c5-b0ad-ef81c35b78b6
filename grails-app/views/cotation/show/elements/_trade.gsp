<%@ page import="traction.DateUtils; traction.cotation.Cotation; traction.vehicle.enums.Odometer;" %>

<asset:javascript src="trade/show/TradeIndex.js"/>
<asset:stylesheet src="trade/show.css"/>

<script>
    $(function () {
        tradeIndex${trade.id} = new TradeIndex(${trade.id ?: 'null'});
    });
    document.addEventListener('DOMContentLoaded', function () {
        const tradeIndexSectionContent = document.getElementById('trade-index-section-content-${trade.id}');
        if (tradeIndexSectionContent && tradeIndexSectionContent.innerHTML.trim() !== '') {
            tradeIndexSectionContent.classList.add('mt-5');
        }
    });
</script>

<div data-id="${trade.id}" class="trade trade-${trade.id} border rounded-1 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-xl-8 col-lg-7 p-4">
                <div class="col-12 row mx-0">
                    <div class="col-4 ps-0 pe-3">
                        <img src="${trade?.vehicle?.pictures?.size() > 0 ? g.createLink(absolute: true, controller: "web", action: "getFile", params: [id: trade?.vehicle?.pictures?.first()]) : "/traction/assets/image-missing.jpg"}"
                             class="img-fluid border w-100 mb-2" alt="No image">
                    </div>

                    <div class="col-8 pe-0 ps-2">
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2" style="height: 19px;">
                                <div>
                                    <h6 class="text-body-secondary m-0">
                                        <g:message code="trade"/> #${status + 1}
                                    </h6>
                                </div>
                            </div>
                            <a href="<g:createLink controller="vehicle" action="index" id="${trade?.vehicleId}"/>" target="_blank"
                               class="text-uppercase fw-bold f-16 mb-3 line-height-19 d-block link-body-emphasis">${trade?.vehicle?.makeModelYear()} <i class="fa-regular fa-external-link text-primary"></i></a>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.make"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.make ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.line"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.line ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.model"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.model ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.year"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.year ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.serialNumber"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.serialNumber ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.stockNumber"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.stockNumber ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.location"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.location ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.interests"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.interests?.join(", ") ?: "-"}
                            </div>
                        </div>

                        <div class="row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.odometer"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${trade?.vehicle?.odometer?.toString() ?: "-"}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-lg-5 bg-body-tertiary border-start p-4" style="border-top-right-radius: 4px;">
                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message
                            code="vehicle.priceAsked"/></label>

                    <div class="col-6 py-0 text-end" style="padding-right: 12px;">
                        <format:currency number="${cotationElement.tradeAskedPrice}"/>
                    </div>
                </div>

                <div class="row mb-3 lh-sm align-content-center h-39">
                    <label class="col-6 py-0 align-content-center col-form-label"><g:message
                            code="trade.gross"/></label>

                    <div class="col-6 py-0 text-end" style="padding-right: 12px;">
                        <format:currency number="${cotationElement.tradeGross}"/>
                    </div>
                </div>

                <div class="row mb-3 lh-sm align-content-center h-39">
                    <label class="col-6 py-0 align-content-center col-form-label"><g:message
                            code="vehicle.priceLink"/></label>

                    <div class="col-6 py-0 text-end" style="padding-right: 12px;">
                        <format:currency number="${cotationElement.tradeLink}"/>
                    </div>
                </div>

                <div class="row mb-3 lh-sm align-content-center h-39">
                    <label class="col-6 py-0 align-content-center col-form-label"><g:message
                            code="trade.reparationEstimated"/></label>

                    <div class="col-6 text-end"
                         style="padding-right: 12px;">
                        <format:currency number="${cotationElement.tradeReparationEstimated}"/>
                    </div>
                </div>
                <button class="btn btn-link text-decoration-underline p-0"
                        onclick="TractionModal.show({
                            url: '/vehicle/modalVehicleEvaluations',
                            data: {
                                vehicleId: ${cotationElement.trade?.vehicle?.id},
                                selectable: true,
                                readOnly: ${cotationElement.cotation.category.isLocked() ? 'true' : 'false'},
                                hasChanged: false
                            },
                            onhide: function (event, modalData) {
                                if (modalData && modalData.hasChanged === true) {
                                    window.location.reload();
                                }
                            }
                        })"
                    style="pointer-events: auto">
                    <g:message code="vehicle.evaluation.price"/>
                </button>
            </div>
        </div>
    </div>

    <div class="trade-details border-top px-4 pt-4 pb-2 f-16">
        <ul id="trade-navigation"
            class="nav nav-tabs mb-3 md-tabs nav-7 d-flex align-items-center gap-0 nav-pills flex-column flex-sm-row"
            role="tablist">
            <li class="nav-item me-4 w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="tradeIndex${trade.id}.router.navigate('infoTab');"
                      data-navigation="infoTab"><g:message code="trade.infoTab"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item me-4 w-auto w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="tradeIndex${trade.id}.router.navigate('fileTab');"
                      data-navigation="fileTab"><g:message code="trade.files"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item me-4 w-auto w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="tradeIndex${trade.id}.router.navigate('inspectionsTab');"
                      data-navigation="inspectionsTab"><g:message code="vehicleinspection"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item me-4 w-auto w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="tradeIndex${trade.id}.router.navigate('workflowDataTab');"
                      data-navigation="workflowDataTab"><g:message code="workflowdata"/></span>
                <div class="slide"></div>
            </li>
        </ul>

        <div id="trade-index-section-content-${trade.id}">
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        const tradContentDiv = document.getElementById("trade-index-section-content-${trade.id}");
        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (tradContentDiv.innerHTML.trim() !== "") {
                    tradContentDiv.style.marginTop = "24px";
                    tradContentDiv.style.marginBottom = "24px";

                } else {
                    tradContentDiv.style.marginTop = "0";
                    tradContentDiv.style.marginBottom = "0";
                }
            });
        });

        observer.observe(tradContentDiv, {childList: true, subtree: true});
    });
</script>

