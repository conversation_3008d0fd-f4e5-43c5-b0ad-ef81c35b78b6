<style>
#makemodelyear:hover {
    text-decoration: underline;
    color: var(--bs-primary) !important;
}
</style>
<div class="f-16 line-height-19">
    <a href="${g.createLink(controller: 'vehicle', action: 'index', id: opportunity?.vehicle?.id)}" class="fw-bold text-black" id="makemodelyear">
        ${opportunity?.vehicle?.makeModelYear() ?: opportunity?.name}
    </a>
</div>
<div class="opportunity-info">
    <div class="d-flex align-items-center small flex-wrap">
        <div class="d-flex align-items-center">
            <g:if test="${opportunity.vehicle?.serialNumber}">
                <div class="opportunity-serial-number me-3">
                    <i class="fa-regular fa-barcode text-secondary me-2"></i>
                    ${opportunity?.vehicle?.serialNumber}
                </div>
            </g:if>
            <g:if test="${opportunity.vehicle?.modelCode}">
                <div class="opportunity-model-code me-3">
                    <i class="fa-regular fa-tally text-secondary me-2"></i>
                    ${opportunity.vehicle.modelCode}
                </div>
            </g:if>
        </div>
        <g:if test="${opportunity.vehicle?.location}">
            <div class="opportunity-location">
                <i class="fa-regular fa-location-dot text-secondary me-2"></i>
                ${opportunity.vehicle.location}
            </div>
        </g:if>
    </div>
</div>
