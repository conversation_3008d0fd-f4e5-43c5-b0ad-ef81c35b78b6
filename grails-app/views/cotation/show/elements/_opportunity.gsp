<%@ page import="traction.DateUtils; traction.cotation.Cotation; traction.opportunity.OpportunityLabel; traction.origin.Origin; traction.vehicle.enums.Odometer; traction.Media; traction.opportunity.Opportunity" %>

<asset:stylesheet src="opportunity/show.css"/>
<asset:javascript src="client/clientVehiclesDataTable.js"/>
<asset:javascript src="opportunity/show/OpportunityIndex.js"/>
<asset:javascript src="opportunity/opportunitiesDataTable.js"/>
<asset:javascript src="cotation/cotationsDataTable.js"/>
<search:vehicleInit/>


<script>
    $(function () {
        opportunityIndex${opportunity.id} = new OpportunityIndex(${opportunity.id ?: 'null'}, ${opportunity.clientId ?: 'null'}, ${opportunity.isService()});
    });
    function toggleChevronOpportunity(button) {
        // Select all chevron icons
        const allChevrons = document.querySelectorAll('.chevron-icon-opportunity');

        // Toggle the clicked chevron
        const chevronIcon = button.querySelector('.chevron-icon-opportunity');
        const isChevronLeft = chevronIcon.classList.contains('fa-chevron-left');

        // Set all chevrons to the left state
        allChevrons.forEach(chevron => {
            chevron.classList.add('fa-chevron-left');
            chevron.classList.remove('fa-chevron-right');
        });

        // If the clicked chevron was already left, set it to right
        if (isChevronLeft) {
            chevronIcon.classList.remove('fa-chevron-left');
            chevronIcon.classList.add('fa-chevron-right');
        }
    }

    // Add event listener to the document to toggle all chevrons down when clicking outside
    document.addEventListener('click', function(event) {
        const allChevrons = document.querySelectorAll('.chevron-icon-opportunity');
        const isClickInsideButton = event.target.closest('.chevron-button');

        if (!isClickInsideButton) {
            allChevrons.forEach(chevron => {
                chevron.classList.add('fa-chevron-left');
                chevron.classList.remove('fa-chevron-right');
            });
        }
    });
</script>
<style>
.opportunity-dropdown:after{
    display: none;
}
</style>

<div data-id="${opportunity.id}" class="opportunity opportunity-${opportunity.id} border rounded-1 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-xl-8 col-lg-7 p-4">
                <div class="col-12 row mx-0">
                    <div class="col-4 ps-0 pe-3">
                        <img src="${opportunity?.getVehicleFirstPictureUrl() ?: "/traction/assets/image-missing.jpg"}"
                             class="img-fluid border w-100 mb-2" alt="No image">
                        <traction:card opportunity="${cotationElement.opportunity}" mini="true" />

                        <g:if test="${opportunity?.vehicle}">
                            <div class="d-flex gap-2 mt-1 flex-wrap">
                                <span class="badge badge-outline-secondary-emphasis text-uppercase" style="height: 19px;">
                                    <g:message code="${opportunity?.vehicle?.status?.message}"/>
                                </span>
                                <g:if test="${opportunity.vehicle.getDaysInStockString()}">
                                    <span class="badge badge-outline-secondary-emphasis text-uppercase" style="height: 19px;">
                                        ${opportunity.vehicle.getDaysInStockString()}
                                    </span>
                                </g:if>
                                <g:if test="${opportunity.vehicle.reserved}">
                                    <span class="badge badge-outline-danger text-uppercase">
                                        <g:message code="vehicle.reserved"/>
                                    </span>
                                </g:if>
                                <g:if test="${vehicleOpportunityDanger || vehicleOpportunityWarning || vehicleOpportunitySecondary}">
                                    <div class="cursor d-flex align-items-center h-19 mt-2" onclick="TractionModal.show({
                                        url: '/vehicle/modalOpportunities',
                                        data: {id: ${opportunity.vehicleId}, opportunityId: ${opportunity.id}}
                                    });">
                                        <g:if test="${vehicleOpportunityDanger}">
                                            <span data-toggle="tooltip"
                                                  title="<g:message code="vehicle.opportunities.sold"/>"
                                                  class="badge badge-outline-danger ${vehicleOpportunityWarning || vehicleOpportunitySecondary ? 'me-2' : ''}">${vehicleOpportunityDanger}</span>
                                        </g:if>
                                        <g:if test="${vehicleOpportunityWarning}">
                                            <span data-toggle="tooltip"
                                                  title="<g:message code="vehicle.opportunities.active"/>"
                                                  class="badge badge-outline-warning ${vehicleOpportunitySecondary ? 'me-2' : ''}">${vehicleOpportunityWarning}</span>
                                        </g:if>
                                        <g:if test="${vehicleOpportunitySecondary}">
                                            <span data-toggle="tooltip"
                                                  title="<g:message code="vehicle.opportunities.other"/>"
                                                  class="badge badge-outline-secondary-emphasis">${vehicleOpportunitySecondary}</span>
                                        </g:if>
                                    </div>
                                </g:if>
                            </div>
                        </g:if>
                    </div>

                    <div class="col-8 pe-0 ps-2">
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2" style="height: 19px;">
                                <h6 class="text-body-secondary m-0">
                                    <g:message code="opportunity"/> #${status + 1}
                                </h6>
                            </div>
                            <a href="<g:createLink controller="opportunity" action="index" id="${opportunity?.id}"/>" class="text-uppercase fw-bold mb-3 d-block link-body-emphasis" target="_blank">${opportunity?.vehicle?.makeModelYear() ?: opportunity?.name} <i class="fa-regular fa-external-link text-primary"></i></a>

                            <div class="d-flex gap-2">
                                <span class="badge ${opportunity?.category?.badgeClass}">
                                    <i class="fa-solid ${opportunity?.category?.icon}"></i> <g:message code="${opportunity?.category?.message}"/>
                                </span>
                                <span class="badge badge-outline-primary">
                                    <g:message code="${opportunity?.status?.message}"/>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm h-39">
                            <label class="col-6 align-content-center"><g:message code="opportunity.name"/></label>

                            <div class="col-6 text-truncate">
                                <input type="text" class="form-control"
                                       placeholder="<g:message code="opportunity.name"/>"
                                       id="opportunity_name_${opportunity?.id ?: ""}"
                                       name="opportunity_name_${opportunity?.id ?: ""}"
                                       onchange="opportunityIndex${opportunity.id}.addData(this)"
                                       value="${opportunity?.name}">
                            </div>
                        </div>
                        <div class="mb-3 row lh-sm">
                            <label class="col-6 align-content-start"><g:message code="vehicle.location"/></label>

                            <div class="col-6 text-truncate">
                                <div class="input-group gap-2">
                                    <div class="d-flex align-items-center flex-grow-1">
                                        <input type="text" class="form-control rounded-1 border-end-0  h-39"
                                            style="border-bottom-right-radius: 0px!important; border-top-right-radius: 0px !important;"
                                               placeholder="<g:message code="vehicle.keyLocation"/>"
                                               id="vehicle_keyLocation_${opportunity?.id ?: ""}"
                                               name="vehicle_keyLocation_${opportunity?.id ?: ""}"
                                               onchange="opportunityIndex${opportunity.id}.addData(this)"
                                               value="${opportunity?.vehicle?.keyLocation}" ${opportunity?.vehicle ? "" : "disabled"}>
                                        <span onclick="TractionModal.show({
                                            url: '/vehicle/modalPropertyHistory',
                                            data: {
                                                vehicleId: '${opportunity?.vehicleId}',
                                                property: 'vehicle.keyLocation'
                                            }
                                        });" class="text-primary border rounded-1 border-start-0 h-39 d-flex align-items-center justify-content-center px-2" role="button" style="border-bottom-left-radius: 0px!important; border-top-left-radius: 0px!important;">
                                            <i class="fa-regular fa-clock-rotate-left"></i>
                                        </span>
                                    </div>
                                    <div class="d-flex align-items-center flex-grow-1">
                                        <input type="text" class="form-control rounded-1 border-end-0  h-39"
                                               style="border-bottom-right-radius: 0px!important; border-top-right-radius: 0px !important;"
                                               placeholder="<g:message code="vehicle.location"/>"
                                               id="vehicle_location_${opportunity?.id ?: ""}"
                                               name="vehicle_location_${opportunity?.id ?: ""}"
                                               onchange="opportunityIndex${opportunity.id}.addData(this)"
                                               value="${opportunity?.vehicle?.location}" ${opportunity?.vehicle ? "" : "disabled"}>
                                        <span onclick="TractionModal.show({
                                            url: '/vehicle/modalPropertyHistory',
                                            data: {
                                                vehicleId: '${opportunity?.vehicleId}',
                                                property: 'vehicle.location'
                                            }
                                        });" class="text-primary border rounded-1 border-start-0 h-39 d-flex align-items-center justify-content-center px-2" role="button" style="border-bottom-left-radius: 0px!important; border-top-left-radius: 0px!important;">
                                            <i class="fa-regular fa-clock-rotate-left"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label class="col-6 align-content-center"><g:message code="vehicle.stockNumber"/></label>

                            <div class="col-6">
                                <search:vehicle type="text"
                                                id="opportunity_stocknumber_${opportunity?.id ?: ""}"
                                                name="opportunity_stocknumber_${opportunity?.id ?: ""}"
                                                data-cotation-id="${cotation.id}"
                                                onchange="opportunityIndex${opportunity.id}.addData(this)"
                                                callback="opportunityIndex${opportunity.id}.vehicleSearchSelected"
                                                class="form-control form-control-sm"
                                                placeholder="${g.message(code: 'vehicle.search')}"
                                                value="${opportunity?.vehicle?.stockNumber ?: opportunity?.stocknumber}"/>
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.make"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.make ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.line"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.line ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm" >
                            <label class="col-6 h-19"><g:message code="vehicle.model"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.model ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.year"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.year ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.modelCode"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.modelCode ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.serialNumber"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.serialNumber ?: "-"}
                            </div>
                        </div>

                        <div class="mb-3 row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.odometer"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.odometer?.toString() ?: "-"}
                            </div>
                        </div>

                        <div class="row lh-sm">
                            <label class="col-6 h-19"><g:message code="vehicle.weight"/></label>

                            <div class="col-6 text-truncate h-19">
                                ${opportunity?.vehicle?.weight?.toString() ?: "-"}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-4 col-lg-5 bg-body-tertiary border-start p-4" style="border-top-right-radius: 4px;">
                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.priceSpecial"/></label>

                    <div class="col-6 align-content-center py-0 text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_promoSpecialPrice_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityPriceSpecial ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_promoSpecialPrice_${opportunity?.id ?: ""}"
                               value="${cotationElement.opportunityPriceSpecial ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.traction_price"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_msrp_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityMrsp ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_msrp_${opportunity?.id ?: ""}"
                               value="${cotationElement.opportunityMrsp ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.sellingOptions"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_optionsPrice_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityOptions ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_optionsPrice_${opportunity?.id ?: ""}"
                               value="${opportunity?.vehicle?.optionsPrice ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.priceTotal"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}">
                        <format:currency number="${opportunity?.vehicle ? cotationElement.opportunityMrsp + cotationElement.opportunityOptions : 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.freight"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_freight_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityFreight ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_freight_${opportunity?.id ?: ""}"
                               value="${cotationElement.opportunityFreight ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="col-6 align-content-center py-0"><g:message code="product.PDI"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_pdi_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityPDI ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_pdi_${opportunity?.id ?: ""}"
                               value="${cotationElement.opportunityPDI ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="${!cotation.category.isLocked() ? 'col-7' : 'col-6'} align-content-center py-0 col-form-label">
                        <g:message code="product.discount"/>
                        <sup data-toggle="tooltip" title="<g:message code='product.discount.tooltip'/>">(<g:formatNumber
                                number="${opportunity?.vehicle && (opportunity?.vehicle?.msrp ?: 0) + (opportunity?.vehicle?.optionsPrice ?: 0) != 0 ?
                                        (((opportunity?.vehicle?.promoSpecialPrice ?: 0) != 0 ?
                                                ((opportunity?.vehicle?.msrp ?: 0) + (opportunity?.vehicle?.optionsPrice ?: 0) +
                                                        (opportunity?.vehicle?.compiledData?.formulaFreightPrice ?: 0) + (opportunity?.vehicle?.compiledData?.formulaPdiPrice ?: 0) -
                                                        (opportunity?.vehicle?.promoSpecialPrice ?: 0)) : 0) +
                                                (opportunity?.discount ?: 0)) /
                                                ((opportunity?.vehicle?.msrp ?: 0) + (opportunity?.vehicle?.optionsPrice ?: 0) +
                                                        (opportunity?.vehicle?.compiledData?.formulaFreightPrice ?: 0) + (opportunity?.vehicle?.compiledData?.formulaPdiPrice ?: 0)) : 0}"
                                format="##0.00%"/>)</sup>
                    </label>

                    <g:if test="${!cotation.category.isLocked()}">
                        <div class="col-5 align-content-center py-0 text-end"  style="padding-right: 12px;">
                            <div class="input-group">
                                <input type="text" class="form-control h-39 text-end border-end-0 pe-1"
                                       id="opportunity_discount_${opportunity?.id ?: ""}"
                                       name="opportunity_discount_${opportunity?.id ?: ""}"
                                       style="padding-left: 12px;"
                                       onchange="opportunityIndex${opportunity.id}.updateSalePrice(this)"
                                       value="<format:currencyNumber number="${cotationElement.opportunityDiscount}"/>">
                                <span class="input-group-text h-39 ps-0 bg-body border-start-0 text-body-tertiary">$</span>
                            </div>
                        </div>
                    </g:if>
                    <g:else>
                        <div class="col-6 align-content-center py-0 text-end" id="opportunity_discount_${opportunity?.id ?: ""}">
                            <format:currency number="${cotationElement.opportunityDiscount}"/>
                            <input type="hidden" id="hidden_opportunity_discount_${opportunity?.id ?: ""}"
                                   value="${cotationElement.opportunityDiscount ?: 0}"/>
                        </div>
                    </g:else>
                </div>


                <div class="mb-3 row lh-sm align-content-center h-39">
                    <g:if test="${opportunity?.vehicle && !cotationElement.cotation.category.isLocked()}">
                        <label class="col-6 align-content-center py-0 cursor text-decoration-underline text-primary" onclick="TractionModal.show({
                            url: '/opportunity/modalPromo',
                            data: {
                                opportunityId: ${opportunity.id}
                            }
                        })"><g:message code="vehicle.promo"/></label>
                    </g:if>
                    <g:else>
                        <label class="col-6 align-content-center py-0"><g:message code="vehicle.promo"/></label>
                    </g:else>
                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}" id="vehicle_promo_${opportunity?.id ?: ""}">
                        <format:currency number="${cotationElement.opportunityPromo ?: 0}"/>
                        <input type="hidden" id="hidden_vehicle_promo_${opportunity?.id ?: ""}"
                               value="${cotationElement.opportunityPromo ?: 0}"/>
                    </div>
                </div>

                <div class="mb-3 row lh-sm align-content-center h-39">
                    <label class="${!cotation.category.isLocked() ? 'col-7' : 'col-6'} align-content-center py-0 col-form-label"><g:message code="opportunity.salePrice"/></label>

                    <g:if test="${!cotation.category.isLocked()}">
                        <div class="col-5 align-content-center py-0 text-end" style="padding-right: 12px;">
                            <div class="input-group">
                                <input type="text" class="form-control text-end border-end-0 h-39 pe-1"
                                       onchange="opportunityIndex${opportunity.id}.updateDiscount(this)"
                                       id="opportunity_salesprice_${opportunity?.id ?: ""}"
                                       style="padding-left: 12px;"
                                       value="<format:currencyNumber number="${0}"/>">
                                <span class="input-group-text ps-0 h-39 bg-body border-start-0 text-body-tertiary">$</span>
                            </div>
                        </div>
                    </g:if>
                    <g:else>
                        <div class="col-6 align-content-center py-0 text-end">
                            <span id="opportunity_salesprice_${opportunity?.id ?: ""}"></span>
                        </div>
                    </g:else>
                </div>

                <div class="row lh-sm align-content-center h-39">
                    <label class="col-6 py-0 align-content-center"><g:message code="opportunity.accessories"/></label>

                    <div class="col-6 py-0 align-content-center text-end ${!cotationElement.cotation.category.isLocked()? 'pe-4':''}">
                        <format:currency number="${cotationElement.opportunityAccessories ?: 0}"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="opportunity-details border-top px-4 pt-4 pb-2 f-16">
        <ul id="opportunity-navigation" class="nav nav-tabs mb-3 md-tabs nav-7 d-flex align-items-center gap-4 nav-pills flex-column flex-sm-row"
            role="tablist">
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="opportunityIndex${opportunity.id}.router.navigate('accessoriesTab');"
                      data-navigation="accessoriesTab"><g:message code="opportunity.accessoriesTab"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0" onclick="opportunityIndex${opportunity.id}.router.navigate('feesTab');"
                      data-navigation="feesTab"><g:message code="opportunity.feeTab"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0" onclick="opportunityIndex${opportunity.id}.router.navigate('infoTab');"
                      data-navigation="infoTab"><g:message code="opportunity.infoTab"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0"
                      onclick="opportunityIndex${opportunity.id}.router.navigate('descriptionTab');"
                      data-navigation="descriptionTab"><g:message code="description"/></span>

                <div class="slide"></div>
            </li>
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0" onclick="opportunityIndex${opportunity.id}.router.navigate('displayTab');"
                      data-navigation="displayTab"><g:message code="opportunity.displayTab"/></span>

                <div class="slide"></div>
            </li>
            <g:if test="${hasWorkflowDataTab}">
                <li class="nav-item w-fit-content" role="presentation">
                    <span class="cursor nav-link p-0"
                          onclick="opportunityIndex${opportunity.id}.router.navigate('workflowDataTab');"
                          data-navigation="workflowDataTab"><g:message code="opportunity.workflowDataTab"/></span>

                    <div class="slide"></div>
                </li>
            </g:if>
            <g:if test="${opportunity.vehicle}">
                <li class="nav-item w-fit-content" role="presentation">
                    <span class="cursor nav-link p-0"
                          onclick="opportunityIndex${opportunity.id}.router.navigate('inspectionsTab');"
                          data-navigation="inspectionsTab"><g:message code="vehicleinspection"/></span>

                    <div class="slide"></div>
                </li>
            </g:if>
            <li class="nav-item w-fit-content" role="presentation">
                <span class="cursor nav-link p-0" onclick="opportunityIndex${opportunity.id}.router.navigate('filesTab');"
                      data-navigation="filesTab"><g:message code="opportunity.documents"/></span>
                <div class="slide"></div>
            </li>
        </ul>
        <div id="opportunity-index-section-content-${opportunity.id}">
        </div>

        <script>
            document.addEventListener("DOMContentLoaded", function() {
                const opportunityContentDiv = document.getElementById("opportunity-index-section-content-${opportunity.id}");
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (opportunityContentDiv.innerHTML.trim() !== "") {
                            opportunityContentDiv.style.marginTop = "24px";
                            opportunityContentDiv.style.marginBottom = "24px";

                        } else {
                            opportunityContentDiv.style.marginTop = "0";
                            opportunityContentDiv.style.marginBottom = "0";
                        }
                    });
                });

                observer.observe(opportunityContentDiv, { childList: true, subtree: true });
            });
        </script>
    </div>
</div>