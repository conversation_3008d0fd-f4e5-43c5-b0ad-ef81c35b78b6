<%@ page import="grails.converters.JSON;" %>
<style>
.financing-table td,
.financing-table th,
.financing-table table td,
.financing-table table th {
    padding: 12px 24px;
}

.financing-table th:first-child {
    border-top-left-radius: 4px;
}

.financing-table th:last-child {
    border-top-right-radius: 4px;
}
</style>

<div class="modal-dialog modal-dialog-scrollable modal-xl">
    <div class="modal-content" style="height: 800px; overflow-y: hidden">
        <div class="modal-header">
            <h2 class="modal-title line-height-39">
                <g:message code="cotation.financing"/>
            </h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            </button>
        </div>

        <div class="modal-body">
            <div class="d-flex justify-content-between align-items-center h-19 mb-3">
                <h3 class="text-black fw-bold f-16 m-0"><g:message code="financing.scenario.select"/></h3>
                <button class="btn btn-link text-decoration-underline p-0" onclick="addFinancingScenario(${cotation.id})"><i
                        class="fa-solid fa-circle-plus text-primary me-2"></i><g:message
                        code="financing.scenario.create"/></button>
            </div>

            <div class="d-flex flex-wrap">
                <g:each in="${financingScenarios}" var="scenario">
                    <traction:scenarioCard scenario="${scenario}" selected="${cotation.financingScenario?.id == scenario.id}"/>
                </g:each>
            </div>
        </div>
    </div>
</div>

<script>
    function addFinancingScenario(cotationId) {
        showBigLoader();
        $.post({
            url: tractionWebRoot + "/financingScenario/save",
            data: {
                'cotationId': cotationId,
            },
            success: function (data) {
                if (data.success) {
                    $.notify(data.message, 'success');
                    TractionModal.reload();
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    }

    function selectScenario(cotationId, scenarioId) {
        showBigLoader();
        $('.scenarioCard,.scenarioTotal,.scenarioTitle').removeClass("border-primary bg-primary-subtle text-primary");
        $('#scenario' + scenarioId + ', #scenario' + scenarioId + ' .scenarioTotal, #scenario' + scenarioId + ' .scenarioTitle').addClass("border-primary text-primary");
        $('#scenario' + scenarioId + ' .scenarioTotal, #scenario' + scenarioId + ' .scenarioTitle').addClass("bg-primary-subtle");
        $.post({
            url: tractionWebRoot + "/financingScenario/linkToCotation",
            data: {
                'cotationId': cotationId,
                'scenarioId': scenarioId,
            },
            success: function (data) {
                if (data.success) {
                    $.notify(data.message, 'success');
                    TractionModal.hide();
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    }

    function updateScenario(id, input) {
        console.log('updateScenario', id, input);
        showBigLoader();
        $.post({
            url: tractionWebRoot + "/financingScenario/update",
            data: {
                'id': id,
                'property': $(input).attr('name'),
                'value': $(input).val()
            },
            success: function (data) {
                if (data.success) {
                    $.notify(data.message, 'success');
                    $('#scenario' + id + ' .scenarioTotalNumber').html(Format.currency(data.scenarioTotal));
                    $('#scenario' + id + ' .scenarioNetFinanced').html(Format.currency(data.scenarioNetFinanced));
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    }

    function deleteScenario(id) {
        showBigLoader();
        $.post({
            url: tractionWebRoot + "/financingScenario/deleteCotationScenario",
            data: {
                'id': id,
            },
            success: function (data) {
                if (data.success) {
                    $.notify(data.message, 'success');
                    TractionModal.reload();
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    }
</script>