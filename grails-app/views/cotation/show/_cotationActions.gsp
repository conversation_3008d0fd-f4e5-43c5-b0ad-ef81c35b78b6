<g:if test="${cotation}">
    <div class="d-flex flex-column">
        <div class="dropdown-item p-3" onclick="this.querySelector('button').click()" >
            <button type="button" class="btn btn-link p-0"
                    onclick="TractionModal.show({
                        url: '/pdfTemplate/modalPrintPdf',
                        data: {cotationId: '${cotation.id}'}
                    });">
                <i class="fas fa-print me-2"></i>
                <g:message code="show.printPdf"/>
            </button>
        </div>
        <hr class="my-0 mx-3" style="border-color: #B3DBFB;">
        <div class="dropdown-item p-3" onclick="this.querySelector('button').click()" >
            <button type="button" class="btn btn-link p-0"
                    onclick="cotationSummary(${cotation.id})">
                <i class="fas fa-list-radio me-2"></i>
                <g:message code="cotation.summary"/>
            </button>
        </div>
    </div>
</g:if>