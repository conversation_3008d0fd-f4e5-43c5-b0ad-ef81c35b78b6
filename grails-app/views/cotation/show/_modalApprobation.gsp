<div class="modal-dialog modal-dialog-scrollable modal-sm">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold">
                <g:message code="cotation.modal.approbation"/>
            </h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            </button>
        </div>

        <div class="modal-body">
            <div class="col-sm-12">
                <label><g:message code="cotation.expirationDate"/></label>
                <input class="form-control" name="dateExpiration"
                       value="" placeholder="YYYY-MM-dd"
                       max="9999-12-31" required>
            </div>
        </div>

        <div class="modal-footer">
            <button onClick="TractionModal.hide()"
                    type="button"
                    class="btn btn-outline-primary rounded-pill">
                <g:message code="cancel"/>
            </button>
            <button onClick="CotationIndex.updateCategory(true, $('[name=dateExpiration]').val())"
                    type="button"
                    class="btn btn-primary rounded-pill">
                <g:message code="cotation.btn.save"/>
            </button>
        </div>
    </div>
</div>
<script>
    $("input[name='dateExpiration']").daterangepicker({
        singleDatePicker: true,
        locale: DateRangePickerUtils.LOCALE
    }).on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
        $(this).attr("value", '');
    });
    $("input[name='dateExpiration']").each(function () {
        if ($(this).attr('value') == '') $(this).trigger("cancel.daterangepicker");
    });
</script>