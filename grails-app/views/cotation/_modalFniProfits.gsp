<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold">
                ${cotationElement.cotation.client.fullName}
            </h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
            <form id="fni-profits-form">
                <input type="hidden" name="cotationElementId" value="${cotationElement.id}"/>
                <div class="row">
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitReserveFinance"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitReserveFinance"
                                         value="${cotationElement.fniProfitReserveFinance}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitOthers"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitOthers"
                                         value="${cotationElement.fniProfitOthers}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitLifeInsurance"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitLifeInsurance"
                                         value="${cotationElement.fniProfitLifeInsurance}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitDisabilityInsurance"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitDisabilityInsurance"
                                         value="${cotationElement.fniProfitDisabilityInsurance}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitCriticalIllnessInsurance"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitCriticalIllnessInsurance"
                                         value="${cotationElement.fniProfitCriticalIllnessInsurance}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitReplacementInsurance"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitReplacementInsurance"
                                         value="${cotationElement.fniProfitReplacementInsurance}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitExtendedPlan"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitExtendedPlan"
                                         value="${cotationElement.fniProfitExtendedPlan}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitRentalLeaseUsury"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitRentalLeaseUsury"
                                         value="${cotationElement.fniProfitRentalLeaseUsury}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitAestheticProtection"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitAestheticProtection"
                                         value="${cotationElement.fniProfitAestheticProtection}"/>
                    </div>
                    <div class="col-6 mb-3">
                        <label><g:message code="cotationelement.fniProfitChemicals"/></label>
                        <container:inputGroup label="\$" type="text" class="form-control form-control-sm"
                                         name="fniProfitChemicals"
                                         value="${cotationElement.fniProfitChemicals}"/>
                    </div>
                </div>

            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary btn-sm" type="button" onclick="submitFniProfitsForm()">
                <g:message code="save"/>
            </button>
        </div>
    </div>

</div>


<script>
    function submitFniProfitsForm() {
        $.post({
            url: tractionWebRoot + '/cotation/saveFniProfits',
            data: $('#fni-profits-form').serialize(),
            success: function (data) {
                if (data.success) {
                    TractionModal.hide();
                    updateTable();
                }
                else {
                    $.notify(data.message, 'error');
                }
            }
        });
    }
</script>