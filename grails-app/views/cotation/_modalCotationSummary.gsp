<script>
    function handleCotationSummaryCollapse(indexName){
        const indexElement = $('#' + indexName);


        if(indexElement.hasClass('fa-chevron-up')){
            indexElement.removeClass('fa-chevron-up').addClass('fa-chevron-down')
        }
        else{
            indexElement.removeClass('fa-chevron-down').addClass('fa-chevron-up')
        }
    }
</script>
<style>
    #summaryCotation thead tr td{
        border: 1px solid white !important;
    }
    #summaryCotation tbody td{
        border: 1px solid #F4F5F7 !important;
        padding: 16px 24px;
    }
    #summaryCotation tbody tr.summary-row-toggle td{
        padding: 10px 24px;
    }
    #summaryCotation tbody td:not(.summary-label):not(:has(.fa-solid)){
        text-align: center;
    }
    #summaryCotation tbody .summary-label{
        border: 1px solid #fff !important;
        background: #F4F5F7 !important;
    }
    .summary-row-toggle{
        border-bottom: 1px solid #B3DBFB !important;
        transition: box-shadow 0.2s ease; /* Smooth transition for the shadow effect */
        /*Easter egg, plus cute sans le bleu*/
        position: relative !important;/* Ensure it can take z-index */
        z-index: 1 !important;          /* Raise above other content */

    }

    .summary-row-toggle[aria-expanded="true"]
     { /* Target elements that are open on page load */
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
    }
    .custom-thead{
        background-color: #5F7991 !important;
        color: white;
    }
    .rounded-top-right{
        border-top-right-radius: 4px;
    }
</style>

<div class="modal-dialog modal-dialog-scrollable" style="max-width: 1400px; margin-top: 5vh;">
    <div class="modal-content">
        <div class="modal-header sticky-top pb-5 bg-body">
            <h2 class="modal-title fw-bold">
                <g:message code="cotation.summary"/>
            </h2>
            <button type="button" class="btn-close opacity-100" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body overflow-auto p-0 mx-5 mb-5" style="max-height: 700px;">
            <%@ page import="java.text.DecimalFormatSymbols; java.text.DecimalFormat" %>
            <asset:stylesheet src="summaryCotation.css"/>

            <div id="summaryCotation">
                <table class="w-100 cotation-table">
                    <thead class="sticky-top custom-thead" style="border-top-left-radius: 4px; border-top-right-radius: 4px;">
                    <tr>
                        <td class="bg-emphasis border-end px-4 lh-sm" style="padding: 10px;border-top-left-radius: 4px; min-width: 400px;" data-bs-toggle="collapse" href="#cotationAllCollapse" role="button" aria-expanded="true" aria-controls="cotationAllCollapse" onclick="handleCotationSummaryCollapse('top-table')">
                            <span class="f-18 text-uppercase fw-bold text-white"><i class="fa-solid fa-chevron-up me-2 f-12" id="top-table"></i><g:message code="cotation.summary.sum.list"/></span>
                        </td>
                        <td class="bg-primary p-2 border-end text-center lh-sm" style="border-top-right-radius: 4px; min-width: 155px;">
                            <span class="fw-bold"><g:message code="cotation"/></span>
                        </td>
                        <g:each var="opp" in="${cotation.getOpportunities()}" status="i">
                            <td class="bg-secondary border-end p-2 text-center lh-sm" style="min-width: 155px;">
                                <span class="fw-bold"><g:message code="opportunity"/> #${i+1}</span>
                            </td>
                        </g:each>
                        <g:each var="tra" in="${cotation.getTrades()}" status="i">
                            <td style="min-width:155px;" class="border-end p-2 text-center lh-sm ${i == cotation.getTrades().size() - 1 ? 'rounded-top-right' : ''}">
                                <span class="fw-bold"><g:message code="trade"/> #${i+1}</span>
                            </td>
                        </g:each>
                    </tr>
                    </thead>
                    <tbody id="cotationAllCollapse" class="collapse show">
                    <tr class="p-2 summary-row-toggle show" data-bs-toggle="collapse" href="#cotationSummaryCollapse" role="button" aria-expanded="true" aria-controls="cotationSummaryCollapse" onclick="handleCotationSummaryCollapse('cotation-summary-index')">
                        <td class="fw-bold text-uppercase" colspan="${cotation.cotationElements.size() + 2}">
                            <i id="cotation-summary-index" class="fa-solid fa-chevron-up me-2 f-12"></i><g:message code="cotation.summary"/>
                        </td>
                    </tr>
                    <tr id="cotationSummaryCollapse" class="collapse show">
                        <td class="summary-label"><g:message code="client.total.cash"/></td>
                        <td class="total">
                            <span>${priceFormatter.format(cotation.cashdown)} <i class="fa-regular fa-dollar-sign"></i></span>
                        </td>
                        <g:each var="l" in="${cotation.cotationElements}">
                            <td>-</td>
                        </g:each>
                    </tr>
                    <tr id="cotationSummaryCollapse" class="collapse show">
                        <td class="summary-label"><g:message code="cotation.summary.priceAtDelivery"/></td>
                        <td class="total">
                            <span>${priceFormatter.format(total.priceAtDelivery)} <i class="fa-regular fa-dollar-sign"></i></span>
                        </td>
                        <g:each var="l" in="${cotation.cotationElements}">
                            <td>-</td>
                        </g:each>
                    </tr>
                    <tr class="p-2 summary-row-toggle" data-bs-toggle="collapse" href="#opportunityPrices" role="button" aria-expanded="true" aria-controls="opportunityPrices" onclick="handleCotationSummaryCollapse('opportunity-prices-index')">
                        <td class="fw-bold text-uppercase" colspan="${cotation.cotationElements.size() + 2}">
                            <i id="opportunity-prices-index" class="fa-solid fa-chevron-up me-2 f-12"></i><g:message code="opportunityPrices"/>
                        </td>
                    </tr>
                    <g:each var="prop" in="${["opportunityMrsp", "opportunityMinimumPrice", "opportunityOptions", "opportunityDiscount", "opportunityPromo", "opportunityAccessories", "opportunityOtherFee", "opportunityPriceSubtotal"]}">
                        <tr id="opportunityPrices" class="collapse show">
                            <td class="summary-label" data-toggle="tooltip" title="<g:message code="cotation.summary.${prop}.tooltip"/>"><g:message code="cotation.summary.${prop}"/></td>
                            <td class="total">
                                <span>${priceFormatter.format(total.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                            </td>
                            <g:each var="line" in="${cotation.cotationElements}">
                                <td>
                                    <g:if test="${line.getProperty(prop) == null}">
                                        -
                                    </g:if>
                                    <g:else>
                                        <span>${priceFormatter.format(line.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                                    </g:else>
                                </td>
                            </g:each>
                        </tr>
                    </g:each>

                    <tr class="p-2 summary-row-toggle" data-bs-toggle="collapse" href="#tradePrices" role="button" aria-expanded="true" aria-controls="tradePrices" onclick="handleCotationSummaryCollapse('trade-prices-index')">
                        <td class="fw-bold text-uppercase" colspan="${cotation.cotationElements.size() + 2}">
                            <i id="trade-prices-index" class="fa-solid fa-chevron-up me-2 f-12"></i><g:message code="tradePrices"/>
                        </td>
                    </tr>
                    <g:each var="prop" in="${["tradeGross", "tradeLink", "tradeNetTrade", "tradeRealPrice"]}">
                        <tr id="tradePrices" class="collapse show">
                            <td class="summary-label"><g:message code="cotation.summary.${prop}"/></td>
                            <td class="total">
                                <span>${priceFormatter.format(total.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                            </td>
                            <g:each var="line" in="${cotation.cotationElements}">
                                <td>
                                    <g:if test="${line.getProperty(prop) == null}">
                                        -
                                    </g:if>
                                    <g:else>
                                        <span>${priceFormatter.format(line.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                                    </g:else>
                                </td>
                            </g:each>
                        </tr>
                    </g:each>
                    <tr class="p-2 summary-row-toggle" data-bs-toggle="collapse" href="#costPrices" role="button" aria-expanded="true" aria-controls="costPrices" onclick="handleCotationSummaryCollapse('cost-prices-index')">
                        <td class="fw-bold text-uppercase" colspan="${cotation.cotationElements.size() + 2}">
                            <i id="cost-prices-index" class="fa-solid fa-chevron-up me-2 f-12"></i><g:message code="costPrices"/>
                        </td>
                    </tr>

                    <g:each var="prop" in="${["costReal", "opportunityAdditionalCost", "costTransport", "costPreparation", "opportunityOptions", "opportunityPromo", "costAccessories", "tradePriceToAbsord", "costSubtotal"]}">
                        <tr id="costPrices" class="collapse show">
                            <g:if test="${prop != "costSubtotal"}">
                                <td class="summary-label"><g:message code="cotation.summary.${prop}"/></td>
                            </g:if>
                            <g:else>
                                <td class="summary-label" data-toggle="tooltip" title="<g:message code="cotation.summary.costReal"/> + <g:message code="cotation.summary.opportunityAdditionalCost"/> + <g:message code="cotation.summary.costAccessories"/> + <g:message code="cotation.summary.tradePriceToAbsord"/>"><g:message code="cotation.summary.${prop}"/></td>
                            </g:else>
                            <td class="total">
                                <span>${priceFormatter.format(total.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                            </td>
                            <g:each var="line" in="${cotation.cotationElements}">
                                <td>
                                    <g:if test="${line.getProperty(prop) == null}">
                                        -
                                    </g:if>
                                    <g:else>
                                        <span>${priceFormatter.format(line.getProperty(prop))} <i class="fa-regular fa-dollar-sign"></i></span>
                                    </g:else>
                                </td>
                            </g:each>
                        </tr>
                    </g:each>

                    <tr class="p-2 summary-row-toggle" data-bs-toggle="collapse" href="#profitMargin" role="button" aria-expanded="true" aria-controls="profitMargin" onclick="handleCotationSummaryCollapse('profit-margin-index')">
                        <td class="fw-bold text-uppercase" colspan="${cotation.cotationElements.size() + 2}">
                            <i id="profit-margin-index" class="fa-solid fa-chevron-up me-2 f-12"></i><g:message code="profitPrices"/>
                        </td>
                    </tr>
                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label"><g:message code="cotation.summary.profitBeforeAccessories"/></td>
                        <td class="total">
                            <div class="d-flex flex-column">
                                <span>${priceFormatter.format(total.profitBeforeAccessories)} <i class="fa-regular fa-dollar-sign"></i></span>
                                <span>${String.format('%.2f', total.profitBeforeAccessoriesPercent * 100)} <i class="fa-regular fa-percent"></i></span>
                            </div>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td>
                                <g:if test="${line.profitBeforeAccessories == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <div class="d-flex flex-column">
                                        <span>${priceFormatter.format(line.profitBeforeAccessories)} <i class="fa-regular fa-dollar-sign"></i></span>
                                        <span>${String.format('%.2f', line.profitBeforeAccessoriesPercent * 100)} <i class="fa-regular fa-percent"></i></span>
                                    </div>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>
                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label"><g:message code="cotation.summary.profitAfterAccessories"/></td>
                        <td class="total">
                            <div class="d-flex flex-column">
                                <span>${priceFormatter.format(total.profitAfterAccessories)} <i class="fa-regular fa-dollar-sign"></i></span>
                                <span>${String.format('%.2f', total.profitAfterAccessoriesPercent * 100)} <i class="fa-regular fa-percent"></i></span>
                            </div>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td>
                                <g:if test="${line.profitAfterAccessories == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <div class="d-flex flex-column">
                                        <span>${priceFormatter.format(line.profitAfterAccessories)} <i class="fa-regular fa-dollar-sign"></i></span>
                                        <span>${String.format('%.2f', line.profitAfterAccessoriesPercent * 100)} <i class="fa-regular fa-percent"></i></span>
                                    </div>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>
                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label"><g:message code="cotation.summary.displayTrade"/></td>
                        <td class="total">
                            <div class="d-flex flex-column">
                                <span>${priceFormatter.format(total.displayTrade)} <i class="fa-regular fa-dollar-sign"></i></span>
                                <span>${String.format('%.2f', total.displayTradePercent * 100)} <i class="fa-regular fa-percent"></i></span>
                            </div>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td>
                                <g:if test="${line.displayTrade == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <div class="d-flex flex-column">
                                        <span>${priceFormatter.format(line.displayTrade)} <i class="fa-regular fa-dollar-sign"></i></span>
                                        <span>${String.format('%.2f', line.displayTradePercent * 100)} <i class="fa-regular fa-percent"></i></span>
                                    </div>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>
                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label"><g:message code="accessories.profit"/></td>
                        <td class="total">
                            <span>${priceFormatter.format(total.opportunityAccessoriesProfit)} <i class="fa-regular fa-dollar-sign"></i></span>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td>
                                <g:if test="${line.opportunityAccessoriesProfit == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <span>${priceFormatter.format(line.opportunityAccessoriesProfit)} <i class="fa-regular fa-dollar-sign"></i></span>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>

                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label" data-toggle="tooltip" title="<g:message code="cotation.summary.accessories.margin.tooltip"/>"><g:message code="cotation.summary.accessories.margin"/></td>
                        <td class="total">
                            <span>${String.format('%.2f', (total.opportunityAccessoriesMarginPercent?: 0.00) * 100)} <i class="fa-regular fa-percent"></i></span>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td>
                                <g:if test="${line.opportunityAccessories == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <span>${String.format('%.2f', (line?.opportunityAccessoriesMarginPercent ?: 0.00) * 100)} <i class="fa-regular fa-percent"></i></span>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>

                    <tr id="profitMargin" class="collapse show">
                        <td class="summary-label" data-toggle="tooltip" title="<g:message code="cotation.summary.accessories.margin.salesprice.tooltip"/>">
                            <g:message code="cotation.summary.accessories.margin.salesprice"/>
                        </td>
                        <td class="total">
                            <span>
                                ${String.format('%.2f', (total.opportunityAccessoriesMarginSalesPricePercent ?: 0.00) * 100)}
                                <i class="fa-regular fa-percent"></i>
                            </span>
                        </td>
                        <g:each var="line" in="${cotation.cotationElements}">
                            <td class="asdf">
                                <g:if test="${line.opportunityAccessories == null}">
                                    -
                                </g:if>
                                <g:else>
                                    <span>${String.format('%.2f', (line.opportunityAccessoriesMarginSalesPricePercent ?: 0.00) * 100)} <i class="fa-regular fa-percent"></i></span>
                                </g:else>
                            </td>
                        </g:each>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>