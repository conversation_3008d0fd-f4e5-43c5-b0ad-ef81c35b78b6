
<html>
<head>
    <title><g:message code="permission.list"/></title>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <asset:javascript src="role/index.js"/>
</head>

<body>
<div class="container p-4">
    <h2 class="mb-4"><g:message code="permission.list"/></h2>
    <table class="table table-sm table-hover w-100 border" id="permissions-table">
        <thead>
        <tr>
            <th><g:message code="id"/></th>
            <th><g:message code="permission.name"/></th>
            <th><g:message code="permission.group"/></th>
            <th><g:message code="permission.allowedLevels"/></th>
            <th><g:message code="permission.related.pages"/></th>
            <th><g:message code="user.roles"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="p" in="${permissions}">
            <tr>
                <td>
                    ${p.name()}
                </td>
                <td>
                    <g:message code="${p.name()}"/>
                </td>
                <td>
                    <g:message code="${p.group}"/>
                </td>
                <td>
                    ${p.allowedLevels.collect { g.message(code: it.message) }.join(", ")}
                </td>
                <td>
                    <ul>
                        <g:each var="menuItem" in="${p.getRelatedMenuItems(menuLevel3Items)}">
                            <li><em><g:message code="${menuItem.message}"/></em> - <a href="${menuItem.href}" target="_blank">${menuItem.href}</a></li>
                        </g:each>
                    </ul>
                </td>
                <td>
                    ${p.getRoles().collect { it.name }}
                </td>
            </tr>
        </g:each>
        </tbody>
    </table>
    <script>
        $(function () {
            TractionDataTable.init('#permissions-table', {
                id: 'permissions-table',
                dataTableOptions: {
                    serverSide: false,
                    searching: true,
                    buttons: []
                }
            });
        });
    </script>
</div>
</body>
</html>