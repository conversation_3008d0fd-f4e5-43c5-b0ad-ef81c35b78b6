<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold">
                <g:if test="${product}">
                    <g:message code="documents.trade"/>
                </g:if>
                <g:if test="${opportunity}">
                    <g:message code="documents.opportunity"/>
                </g:if>
                <g:if test="${opportunityOption}">
                    <g:message code="documents.opportunityOption"/>
                </g:if>
            </h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

            </button>
        </div>

        <div class="modal-body">
            <div id="imagesContainer"></div>
            <script>
                dataauthorcontainer({
                    formula: 'DocumentAdding',
                    type: 'FILE',
                    clientid: ${client.id},
                    <g:if test="${product}">
                    productid: ${product.id},
                    category: 'document.trade',
                    storageType: 'BlobData',
                    </g:if>
                    <g:if test="${opportunity}">
                    opporid: ${opportunity.id},
                    category: 'document.opportunity',
                    storageType: 'BlobData',
                    </g:if>
                    <g:if test="${opportunityOption}">
                    opportunityOptionId: ${opportunityOption.id},
                    category: 'document.opportunity.option',
                    storageType: 'BlobData',
                    </g:if>
                    status: 'DETECT',
                    container: 'imagesContainer',
                    features: '[dataauthorupload][get][name][edit][noresize][imagelarge]'
                });
            </script>
        </div>
    </div>
</div>