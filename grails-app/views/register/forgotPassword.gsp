<html>
    <head>
        <meta name="layout" content="mainAuth"/>
        <title><g:message code="auth.forgotpassword"/></title>
    </head>
    <body>
        <div>

            <div class="auth-box">
                <div class="header-section">
                    <div class="d-flex align-items-center justify-content-center">
                        <h5 class="fw-bold f-48 text-emphasis"><g:message code="auth.forgotpassword"/>?</h5>
                     </div>
                </div>

                <div class="mb-4">
                    <label class="text-black mb-2 fw-bold"><g:message code="user.email"/></label>
                    <div class="input-group mb-3">
                        <span class="input-group-text border-end-0 bg-body pe-1"><i class="fa-light fa-envelope text-info-emphasis f-16"></i></span>
                        <input type="email" class="form-control border-start-0" style="border-top-left-radius: 0; border-bottom-left-radius: 0;" id="email" placeholder="<g:message code="email.placeholder"/>" autocomplete="off">
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-center mb-4 gap-3" >
                        <a href="<g:createLink absolute="true" controller="login" action="auth"/>" class="btn btn-outline-primary shadow-sm fw-bold w-50"><i class="fa-solid fa-chevron-left me-2"></i><g:message code="default.button.cancel.label"/></a>
                        <button href="#!" class="btn btn-primary shadow-sm fw-bold w-50" onclick="sendMail()"><i class="fa-solid fa-send me-2"></i><g:message code="auth.sendmail"/></button>
                </div>
            </div>
        </div>
        <script>
            function sendMail() {
                $.get({
                    url: tractionWebRoot + '/Register/sendMail',
                    dataType: 'json',
                    data: {
                        email: $('#email').val(),
                    },
                    success: function (data) {
                        $.notify(data.message, data.status);

                    }
                });
            }
        </script>
    </body>
</html>