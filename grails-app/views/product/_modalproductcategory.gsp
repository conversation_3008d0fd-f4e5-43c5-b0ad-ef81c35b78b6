<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title"><g:message code="product.category.${category ? 'edit' : 'add'}"/></h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

            </button>
        </div>
        <div class="modal-body">
            <form id="editCategoryForm">

                <input type="hidden" name="id" value="${category?.id}"/>

                <div class="mb-3">
                    <label><g:message code="product.category.name"/></label>
                    <input type="text" class="form-control" name="name" value="${category?.name}" autocomplete="off">
                </div>

                <div class="mb-3">
                    <label><g:message code="product.category.kijiji"/></label>
                    <input type="text" class="form-control" name="kijiji" value="${category?.kijiji}" autocomplete="off">
                </div>

                <div class="mb-3">
                    <label><g:message code="product.category.trader"/></label>
                    <input type="text" class="form-control" name="trader" value="${category?.trader}" autocomplete="off">
                </div>

                <div class="mb-3">
                    <label><g:message code="product.category.manufacture"/></label>
                    <input type="text" class="form-control" name="manufacture" value="${category?.manufacture}" autocomplete="off">
                </div>
                <div>
                    <label><g:message code="product.category.interest"/></label>
                    <input type="text" class="form-control" name="interest" value="${category?.interest}" autocomplete="off">
                </div>

            </form>
        </div>

        <div class="modal-footer">
            <button type="submit" class="btn btn-primary rounded-pill" onclick="editProductCategory();"><g:message code="default.button.save.label"/></button>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#collapseInterests .list-group-item').on('click', function(e) {
            if (e.target == this) {
                $(this).find('input').click();
            }
        });
    });
</script>