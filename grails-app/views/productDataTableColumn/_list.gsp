<asset:javascript src="vehicle/vehicleDataTableColumn.js"/>
<script>
    $(document).ready(function() {
        window.columnsProduct = TractionDataTable.init('#columnsProduct', {
            id: 'columnsProduct',
            dataTableOptions: {
                'searching': false,
                'serverSide': false,
                'buttons': [{
                    text: I18N.m('default.button.add.label'),
                    className: 'btn text-white btn-primary-important rounded border',
                    action: function() {
                        VehicleDataTableColumn.edit(null);
                    }
                }],
                'columnDefs': [
                    {'name': 'label', 'data': 'label', 'targets': 'label'},
                    {'name': 'attribute', 'data': 'attribute', 'targets': 'attribute'},
                    {'name': 'name', 'data': 'name', 'targets': 'name'}
                ]
            }
        });
    });
</script>
<div class="m-4">
    <div class="row">
        <div class="col-12">
            <table class="table table-striped w-100" id="columnsProduct" cellspacing="0">
                <thead>
                <tr>
                    <th class="label"><g:message code="productdatatablecolumn.label"/></th>
                    <th class="attribute"><g:message code="productfilterelement.attributeCategory"/></th>
                    <th class="name"><g:message code="productfilterelement.attributeName"/></th>
                    <th><g:message code="actions"/></th>
                </tr>
                </thead>
                <tbody>
                <g:each var="f" in="${columns}">
                    <tr>
                        <td>${f.label}</td>
                        <td>${f.attributeCategory}</td>
                        <td>${f.attributeName}</td>
                        <td>
                            <div class="d-flex">
                                <button onclick="VehicleDataTableColumn.edit('${f.id}');" data-toggle="tooltip" title="<g:message code="default.button.edit.label"/>" class="fa-solid fa-pen btn text-primary me-3"></button>
                                <button onclick="VehicleDataTableColumn.delete('${f.id}');" data-toggle="tooltip" title="<g:message code="default.button.delete.label"/>" class="fa-solid fa-trash btn text-danger"></button>
                            </div>
                        </td>
                    </tr>
                </g:each>
                </tbody>
            </table>
        </div>
    </div>
</div>

