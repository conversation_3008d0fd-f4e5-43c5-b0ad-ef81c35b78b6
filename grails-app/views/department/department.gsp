<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <g:if test="${department.id}">
        <title>${department.name} | <g:message code="department"/></title>
    </g:if>
    <g:else>
        <title><g:message code="department.create"/></title>
    </g:else>
    <asset:stylesheet src="lib/codeMirror/codemirror.css"/>
    <asset:javascript src="lib/codeMirror/codemirror.js"/>
    <asset:javascript src="lib/codeMirror/mode/xml/xml.js"/>
    <asset:javascript src="lib/tagsinput.js"/>
    <asset:stylesheet src="lib/tagsinput.css"/>
    <asset:javascript src="lib/jscolor/jscolor.js"/>

    <asset:javascript src="department/department.js"/>
</head>

<body>
<style>
.formProfilesDiv input, .formProfilesDiv [data-role="remove"] {
    display: none;
}

.formProfilesDiv .bootstrap-tagsinput {
    min-height: 46px;
    cursor: default;
    background-color: #efefef;
}
</style>

<div class="m-4 d-flex">
    <g:if test="${department.id}">
        <h1><g:message code="department.edit"/> (${department.id})</h1>
    </g:if>
    <g:else>
        <h1><g:message code="department.create"/></h1>
    </g:else>
    <div class="d-flex flex-row-reverse ms-auto">
        <button onclick="saveDepartment();" class="btn btn-primary fw-bold">
            <g:message code="client.save"/>
        </button>

        <g:if test="${department.id}">
            <button class="btn btn-danger fw-bold me-2" onclick="deleteDepartment(${department.id});">
                <g:message code="default.button.delete.label"/>
            </button>
        </g:if>
    </div>
</div>

<div class="m-4">
    <form id="departmentForm">
        <input type="hidden" name="id" value="${department.id}"/>

        <div class="row mb-3">
            <div class="col-4">
                <label><g:message code="dept.name"/></label>
                <input class="form-control h-39" value="${department.name}" name="name"
                       placeholder="<g:message code="dept.name"/>"/>
            </div>

            <div class="col-2">
                <label><g:message code="associated.origin"/></label>
                <select class="form-select h-39" name="originId">
                    <option value=""><g:message code="none"/></option>
                    <g:each var="o" in="${origins}">
                        <option value="${o.id}" ${o.id == department?.originId ? 'selected' : ''}>${o.name}</option>
                    </g:each>
                </select>
            </div>

            <div class="col-2">
                <label><g:message code="department.lautopakStoreId"/></label>
                <input class="form-control h-39" value="${department.lautopakStoreId}" type="number" name="lautopakStoreId"
                       placeholder="<g:message code="department.lautopakStoreId"/>"/>
            </div>

            <div class="col-2">
                <label><g:message code="dept.dealer"/></label>
                <select class="form-select h-39" name="dmsId">
                    <option value="-1"><g:message code="none"/></option>
                    <g:each var="dms" in="${dmsList}">
                        <option value="${dms.id}" ${department.dms?.id == dms.id ? "selected" : ""}>${dms.name}</option>
                    </g:each>
                </select>
            </div>

            <div class="col-2">
                <label><g:message code="dept.dealerId"/></label>
                <input class="form-control h-39" value="${department.dmsDealerId}" name="dealerId"
                       placeholder="<g:message code="dept.dealer"/>"/>
            </div>

            <div class="col-2">
                <label><g:message code="stripeaccount"/></label>
                <select class="form-select h-39" name="stripeAccountId">
                    <option value="">----</option>
                    <g:each var="acc" in="${stripeAccounts}">
                        <option value="${acc.id}" ${acc.id == department.stripeAccountId ? 'selected' : ''}>${acc.name}</option>
                    </g:each>
                </select>
            </div>
            <div class="col align-content-end">
                <label>&nbsp;</label>
                <g:if test="${department.id}">
                    <a href="<g:createLink absolute="true" controller="workSchedule" action="edit" params="[departmentId: department.id]"/>" target="_blank" class="btn btn-primary mdi mdi-open-in-new text-white fw-bold h-39">
                        <g:message code="goto.workschedule"/>
                    </a>
                </g:if>
            </div>
        </div>

        <table class="table w-100" id="departmentInfoTable">
            <thead>
            <tr>
                <th class="min-w-150"><g:message code="dept.groups"/></th>
                <th><g:message code="icon.color"/></th>
                <th class="min-w-100"><g:message code="user.users"/></th>
                <th class="min-w-350"><g:message code="dept.goal.month"/></th>
                <th class="min-w-350"><g:message code="dept.goal.day"/></th>
                <th class="min-w-150"><g:message code="departmentgroup.defaultFromEmail"/></th>
                <th class="min-w-200">
                    <g:message code="departmentgroup.defaultUserFromEmail"/>
                    <i class="mdi mdi-information" data-toggle="tooltip" title="<g:message code="departmentgroup.defaultUserFromEmail.hint"/>"/>
                </th>
            </tr>
            </thead>
            <tbody>
            <g:each var="group" in="${department.groups}">
                <tr style="background-color: ${group.color}77;">
                    <td><strong><g:message code="${group.userGroup.message}"/></strong></td>
                    <td>
                        <input name="${group.userGroup.message}.color" onchange="updateRowColor(this)"
                               class="jscolor form-control" value="${group.color ? group.color.substring(1) : ''}">
                    </td>
                    <td>
                        <span class="badge badge-outline-secondary-emphasis">
                            ${group.getUsers().findAll { !it.deleted }.size()}
                        </span>
                    </td>
                    <td>
                        <input name="${group.userGroup.message}.monthGoal" type="number" step="1"
                               value="${group.monthGoal}"
                               class="form-control"/>
                    </td>
                    <td>
                        <input name="${group.userGroup.message}.dayGoal" type="number" step="1" value="${group.dayGoal}"
                               class="form-control"/>
                    </td>
                    <td>
                        <input name="${group.userGroup.message}.defaultFromEmail" class="form-control" value="${group.defaultFromEmail}"/>
                    </td>
                    <td>
                        <input name="${group.userGroup.message}.defaultUserFromEmail" class="form-control" value="${group.defaultUserFromEmail?.replace("%", "[username]")}"/>
                    </td>
                </tr>
            </g:each>
            </tbody>
        </table>

        <script>
            TractionDataTable.init('#departmentInfoTable', {
                id: 'departmentInfoTable',
                enableState: true,
                dataTableOptions: {
                    serverSide: false,
                    buttons: [],
                    searching: false,
                    paging: false
                }
            });
        </script>

        <div class="mb-3 col-12 row mx-0 mt-3">
            <label class="ps-0"><strong><g:message code="origin.phones"/></strong> <small>(<g:message
                    code="freepbx.list.hint"/>)</small></label>
            <input class="form-control rounded" type="text" name="phones" id="phones" value="${department.phones.join(",")}">
        </div>

        <div class="mb-3 col-12 row">
            <input type="file" name="image" accept="image/*" class="fw-bold"
                   onchange="document.getElementById('departmentImage').src = window.URL.createObjectURL(this.files[0])">
        </div>

        <div class="mb-3 col-12 row mx-0">
            <g:if test="${department.image}">
                <img id="departmentImage" class="img-fluid"
                     src="<g:createLink absolute="true" controller="web" action="getFile" params="[id: department.image.id]"/>"/>
            </g:if>
            <g:else>
                <asset:image id="departmentImage" class="img-fluid" src="department-blank.jpg"/>
            </g:else>
        </div>

        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="department.companyName"/></strong></label>
            <input class="form-control" type="text" name="CompanyName" id="CompanyName" value="${department.companyName}">
        </div>

        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.line1"/></strong></label>
            <input class="form-control" type="text" name="line1" id="line1" value="${department.address?.line1}">
        </div>
        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.line2"/></strong></label>
            <input class="form-control" type="text" name="line2" id="line2" value="${department.address?.line2}">
        </div>
        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.city"/></strong></label>
            <input class="form-control" type="text" name="city" id="city" value="${department.address?.city}">
        </div>
        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.state"/></strong></label>
            <input class="form-control" type="text" name="state" id="state" value="${department.address?.state}">
        </div>
        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.postalCode"/></strong></label>
            <input class="form-control" type="text" name="postalCode" id="postalCode" value="${department.address?.postalCode}">
        </div>
        <div class="mb-3 col-12 row mx-0">
            <label class="ps-0"><strong><g:message code="address.country"/></strong></label>
            <input class="form-control" type="text" name="country" id="country" value="${department.address?.country}">
        </div>
    </form>
</div>

<div id="userSignature">
</div>
<script>
    $(document).ready(function () {
        $('#phones').tagsinput({
            trimValue: true
        });
    });
</script>
</body>
</html>