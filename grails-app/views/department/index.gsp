<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="department"/></title>
</head>

<body>
<div class="clearfix m-4 d-flex">
        <h1 class="m-0 me-auto"><g:message code="department"/></h1>
        <a href="<g:createLink absolute="true" controller="department" action="department"/>"
           class="float-right btn btn-primary text-white fw-bold d-flex align-items-center"><i class="fa-solid fa-circle-plus me-2"></i><g:message code="create"/></a>
</div>

<div class="m-4">
    <table class="table table-hover w-100" id="departmentTable">
        <thead>
        <tr>
            <th><g:message code="dept.name"/></th>
            <th><g:message code="user.users"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="d" in="${departments}">
            <tr class="cursor"
                onclick="location.href = '<g:createLink absolute="true" controller="department" action="department" id="${d.id}"/>';">
                <td>${d.name}</td>
                <td>${d.getUsers().size()}</td>
            </tr>
        </g:each>
        </tbody>
    </table>
    <script>
        TractionDataTable.init('#departmentTable', {
            id: 'departmentTable',
            enableState: true,
            dataTableOptions: {
                serverSide: false,
                buttons: [],
                searching: false,
                paging: false
            }
        });
    </script>
</div>
</body>
</html>

