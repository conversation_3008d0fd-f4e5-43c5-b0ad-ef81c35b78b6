<g:set var="yesNoNode" value="${node.asYesNoNode()}"/>
<g:set var="optionNode" value="${node.asOptionNode()}"/>
<details ${node.opened && !optionNode?.watermark ? 'open' : ''} class="${node.opened ? optionNode?.watermark ? 'watermark-found-node watermark-node-opened' : 'watermark-node-opened' : 'watermark-node-closed'} ${optionNode ? 'watermark-option-node' : ''} ${yesNoNode ? 'watermark-yes-no-node' : ''}">
    <g:if test="${yesNoNode}">
        <summary>${node.label}</summary>
        <ul>
            <li class="watermark-yes-node ${yesNoNode.noNode.opened ? 'border-before-primary' : ''}">
                <details ${yesNoNode.yesNode.opened ? 'open' : ''} class="${yesNoNode.yesNode.opened ? 'watermark-node-opened' : 'watermark-node-closed'}">
                    <summary><g:message code="yes"/></summary>
                    <ul>
                        <li>
                            <g:render template="modalWatermarkExplainedNode" model="[node: yesNoNode.yesNode]"/>
                        </li>
                    </ul>
                </details>
            </li>
            <li class="watermark-no-node">
                <details ${yesNoNode.noNode.opened ? 'open' : ''} class="${yesNoNode.noNode.opened ? 'watermark-node-opened' : 'watermark-node-closed'}">
                    <summary><g:message code="no"/></summary>
                    <ul>
                        <li>
                            <g:render template="modalWatermarkExplainedNode" model="[node: yesNoNode.noNode]"/>
                        </li>
                    </ul>
                </details>
            </li>
        </ul>
    </g:if>
    <g:if test="${optionNode}">
        <summary class="${optionNode.watermark ? '' : ''}">${node.label} <span class="ms-2 px-3 badge rounded-pill bg-${optionNode.watermark ? 'primary' : 'secondary'}-subtle text-${optionNode.watermark ? 'primary' : 'secondary'}">${optionNode.watermark ?: g.message(code: "vehicle.no.watermark")}</span></summary>
        <g:if test="${optionNode.child}">
            <ul>
                <li>
                    <g:render template="modalWatermarkExplainedNode" model="[node: optionNode.child]"/>
                </li>
            </ul>
        </g:if>
    </g:if>
</details>
