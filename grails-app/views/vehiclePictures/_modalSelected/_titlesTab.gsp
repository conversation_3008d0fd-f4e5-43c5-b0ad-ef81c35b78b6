<div class="tab-pane fade" id="picture-meta-form-tab-titles" role="tabpanel">
    <div id="picture-meta-form-titles" class="form-mode-read">
        <div class="row my-3">
            <div class="align-self-center py-2 text-truncate" data-toggle="tooltip" title="<g:message code="filedata.metadata.byLineTitle"/>" style="height:39px; width: 180px; margin-right: 12px;">
                <label for="picture-meta-byLineTitle"><g:message code="filedata.metadata.byLineTitle"/></label>
            </div>

            <div class="mode-read align-content-center">
                ${metaData.byLineTitle ?: "-"}
            </div>

            <div class="mode-edit">
                <input class="form-control" id="picture-meta-byLineTitle" value="${metaData.byLineTitle}"/>
            </div>
        </div>

        <div class="row my-3">
            <div class="align-self-center py-2 text-truncate" data-toggle="tooltip" title="<g:message code="filedata.metadata.objectName"/>" style="height:39px; width: 180px; margin-right: 12px;">
                <label for="picture-meta-objectName"><g:message code="filedata.metadata.objectName"/></label>
            </div>

            <div class="mode-read align-content-center">
                ${metaData.objectName ?: "-"}
            </div>

            <div class="mode-edit">
                <input class="form-control" id="picture-meta-objectName" value="${metaData.objectName}"/>
            </div>
        </div>

        <div class="row my-3">
            <div class="align-self-center py-2 text-truncate" data-toggle="tooltip" title="<g:message code="filedata.metadata.headline"/>" style="height:39px; width: 180px; margin-right: 12px;">
                <label for="picture-meta-headline"><g:message code="filedata.metadata.headline"/></label>
            </div>

            <div class="mode-read align-content-center">
                ${metaData.headline ?: "-"}
            </div>

            <div class="mode-edit">
                <input class="form-control" id="picture-meta-headline" value="${metaData.headline}"/>
            </div>
        </div>

        <g:if test="${!disabled}">
            <div class="d-flex justify-content-end pt-3">
                <div class="mode-read">
                    <button type="button" class="btn btn-sm btn-link w-auto f-16"
                            onclick="FormModeUtils.edit('#picture-meta-form-titles')"><g:message
                            code="modify"/></button>
                </div>

                <div class="mode-edit">
                    <button type="button" class="btn btn-link-subtle w-auto p-0 me-3 f-16"
                            onclick="VehiclePicturesModal.updateSelected()"><g:message code="cancel"/></button>
                    <button type="button" class="btn btn-link w-auto p-0 f-16"
                            onclick="VehiclePicturesModal.savePictureMetaData()"><g:message code="save"/></button>
                </div>
            </div>
        </g:if>
    </div>
</div>