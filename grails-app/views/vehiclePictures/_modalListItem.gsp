<div class="vehicle-picture-list-item border bg-body rounded shadow-sm p-3 d-flex align-items-center mb-2"
onclick="VehiclePicturesModal.click(${picture.id})"
data-from-root="${picture.fromRoot}"
data-file-data-id="${picture.id}">
<g:if test="${!picture.fromRoot}">
    <div class="pe-3">
        <div class="mode-reorder-off">
            <i class="fa fa-regular fa-grip-dots-vertical f-28 py-3 cursor" style="color: #B3B3B3;"></i>
        </div>
        <div class="mode-reorder-on position-relative">
            <i class="fa fa-light fa-square text-primary f-28"></i>
            <span class="reorganize-order-input text-white position-absolute start-0 text-center w-100 fw-bold f-14 lh-lg"></span>
        </div>
    </div>
</g:if>
<div class="pe-3 d-flex align-items-center" style="width: 25%;">
    <img class="img-fluid rounded" src="<g:createLink controller="web" action="getFile" id="${picture.id}"/>?t=${new Date().getTime()}"/>
</div>
<div class="flex-grow-1 pe-3 text-truncate">
    <div class="text-break">${picture.name}</div>
    <div style="color: #808080;"><span class="text-truncate">${picture.date.format("yyyy-MM-dd")} | ${picture.size}</span></div>
</div>
<div class="mode-reorder-off justify-content-end" style="display: flex;width: 20%;">
<a style="display: block;text-align: center;" data-toggle="tooltip" title="<g:message code="download"/>" href="<g:createLink controller="web" action="getFile" id="${picture.id}"/>" download="${picture.name}"/>
    <i class="fa fa-light fa-arrow-down-to-bracket text-primary p-2"></i>
</a>
<g:if test="${picture.fromRoot}">
    <a style="display: block;text-align: center;" data-toggle="tooltip" title="<g:message code="vehicle.rootVehicle.pictures"/>" href="javascript:void(0);">
        <i class="fa fa-regular fa-seedling text-primary p-2"></i>
    </a>
</g:if>
<g:else>
    <a style="display: block;text-align: center;" data-toggle="tooltip" title="<g:message code="delete"/>" href="javascript:VehiclePicturesModal.delete(${picture.id});">
        <i class="fa fa-light fa-trash text-primary p-2"></i>
    </a>
</g:else>
</div>
</div>