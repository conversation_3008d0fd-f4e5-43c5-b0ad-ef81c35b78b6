<script>
    function updateClientStatus(id, status) {
        console.log(id, status);
        $.post(tractionWebRoot + '/clientProcess/updateClientStatus/' + id + '?status=' + status, function (data) {
            $.notify(data.message, data.success ? 'success' : 'error');
        });
    }
    $(document).ready(function () {
        new MessageDataTable({
            uuid: 'MessageDataTableEntryDetail${entry.id}',
            divId: 'clientcomment',
            clientId: '${entry.clientId}',
            opportunityId: '${entry.opportunityId}',
            pageLength: 6
        });
    });
</script>
<div class="row details-row">
    <div class="col-lg-4 p-3">${raw(entry.clientProcess.description)}</div>
    <div class="col-lg-4 p-3">

        <h4 class="bd-bottom-grey">
            <g:message code="${entry.opportunity ? 'opportunity' : 'client'}"/>
        </h4>
        <a href="<g:createLink absolute="true" controller="client" action="index" params="[id: entry.clientId, opporid: entry.opportunityId]"/>" target="_blank">
            <g:if test="${entry.opportunity}">
                <traction:card opportunity="${entry.opportunity}"/>
            </g:if>
            <g:else>
                <div class="card p-2 page-link cursor">
                    <p>${entry.client.toString()} <i class="mdi mdi-open-in-new"></i></p>
                    <p><g:message code="extID"/>: ${entry.client.externalIdentifiers?.join(",")}</p>
                    <p><g:message code="client.roopen"/>: ${entry.client.roopen}</p>
                    <p><g:message code="client.balance"/>: $ <g:formatNumber number="${entry.client.balance}" format="### ### ##0.00" locale="${Locale.US}"/></p>
                    <p><i class="fa fa-phone"></i> ${entry.client.phone}</p>
                    <p><i class="fa fa-mobile"></i> ${entry.client.phonecell}</p>
                </div>
            </g:else>
        </a>
        <h4 class="bd-bottom-grey">
            <g:message code="client.comments"/>
        </h4>
        <div id="clientcomment" class="ps-2 pe-2">
        </div>
    </div>
    <div class="col-lg-4 p-3">
        <h4 class="bd-bottom-grey">
            <g:message code="client"/>: <g:message code="client.status"/>
        </h4>
        <select class="form-control mb-2" onchange="updateClientStatus(${entry.clientId}, this.value)" <permission:ifHasNotPermissions permissions="[entry.client.status.permissionToChange]">disabled</permission:ifHasNotPermissions>>
            <g:each var="status" in="${clientStatusList}">
                <option value="${status.id}" ${entry.client.status == status ? 'selected' : ''}><g:message code="${status.message}"/></option>
            </g:each>
        </select>

        <button class="btn btn-sm btn-primary mt-4 w-100" onclick="TaskSidePanel.show({
                clientId: '${entry.clientId}',
                opportunityId: '${entry.opportunityId}',
                showLastTasks: true
        });">
            <i class="fa-solid fa-cicle-plus me-2"></i>
            <g:message code="client.add.task"/>
        </button>

    </div>
</div>
