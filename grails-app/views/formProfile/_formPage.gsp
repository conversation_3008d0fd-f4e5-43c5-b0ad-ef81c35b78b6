<%@ page import="traction.opportunity.Opportunity; traction.form.FormType" %>
<div id="page-${form.id}" class="fb-editor formPage">
    <form id="formPageSetting">
        <input type="hidden" name="formId" value="${form?.id}">
        <input type="hidden" name="formData" value="${form?.formData}" onchange="saveForm()">

        <div class="mb-3 col-sm-12">
            <div class="row mb-2">
                <div class="col-sm-12">
                    <div class="row">
                        <div class="col-sm-12 col-md-6 pe-2 pt-2 section noOpportunityData">
                                <label><g:message code="form.type"/></label>
                                <select class="form-control" name="type" onchange="saveForm()">
                                    <g:each var="formType" in="${FormType.getAll()}">
                                        <option value="${formType.type}" ${formType.type == form?.type ? 'selected' : ''}><g:message
                                                code="${formType.message}"/></option>
                                    </g:each>
                                </select>
                        </div>

                        <div class="col-sm-12 col-md-6 pe-2 pt-2 section">
                                <label><g:message code="form.opportunityType"/></label>
                                <select class="form-control" name="opportunityType" onchange="saveForm()">
                                    <option value="" ${!form?.opportunityType ? 'selected' : ''}>
                                        <g:message code="none"/>
                                    </option>
                                    <g:each var="opportunityType" in="${Opportunity.Type.values()}">
                                        <option value="${opportunityType.id}" ${opportunityType == form?.opportunityType ? 'selected' : ''}>
                                            <g:message code="${opportunityType.message}"/>
                                        </option>
                                    </g:each>
                                </select>
                        </div>

                        <div class="col-sm-12 col-md-6 pe-2 pt-2 section">
                                <label><g:message code="form.ordre"/></label>
                                <input type="text" class="form-control" name="ordre" value="${form?.ordre}"
                                       autocomplete="off" onchange="saveForm(updateForms); ">
                        </div>
                    </div>
                </div>

                <div class="col-sm-12 col-md-6 mt-3 section noOpportunityData">
                        <div class="form-check">
                            <input type="checkbox" onchange="saveForm()" role="switch" class="form-check-input border-primary"
                                   name="waitingNotification" ${form?.waitingNotification ? 'checked' : ''}>
                            <span><g:message code="form.awaiting"/></span>
                        </div>
                </div>

                <div class="col-sm-12 col-md-6 mt-3 section noOpportunityData">
                        <div class="form-check">
                            <input type="checkbox" name="makeTask" role="switch" class="form-check-input border-primary"
                                   onchange="saveForm()" ${form?.makeTask ? 'checked' : ''}>
                            <span><g:message code="form.maketask"/></span>
                        </div>
                </div>

                <div class="row col-12">
                    <div class="col-sm-12 col-md-6 pe-2 pt-2 section">
                            <label><g:message code="form.css"/></label>
                            <textarea name="css" id="css">${form?.css}</textarea>
                            <script>
                                var cssCodeMirror = CodeMirror.fromTextArea(document.getElementById('css'), {
                                    lineNumbers: true,
                                    smartIndent: true,
                                    mode: 'css',
                                });
                                cssCodeMirror.on('change', function () {
                                    $('#css').val(cssCodeMirror.getValue());
                                    saveForm();
                                });
                            </script>
                    </div>

                    <div class="col-sm-12 col-md-6 pe-2 pt-2 section">
                            <label><g:message code="form.javascript"/></label>
                            <textarea name="javascript" id="javascript"
                                      onchange="saveForm()">${form?.javascript}</textarea>
                            <script>
                                var javascriptCodeMirror = CodeMirror.fromTextArea(document.getElementById('javascript'), {
                                    lineNumbers: true,
                                    smartIndent: true,
                                    mode: 'javascript',
                                });
                                javascriptCodeMirror.on('change', function () {
                                    $('#javascript').val(javascriptCodeMirror.getValue());
                                    saveForm();
                                });
                            </script>
                        </div>
                </div>
            </div>
        </div>
    </form>

    <div class="formeo-editor"></div>

    <div id="preview-form-${form?.id}" class="modal fade" tabindex="-1" role="dialog"
         aria-labelledby="myLargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="formeo-render p-2"></div>
            </div>
        </div>
    </div>
    <script>
        var formeoEditor = new FormeoFormBuilder('${form?.id}', lang, '${form?.formData}');
    </script>
</div>