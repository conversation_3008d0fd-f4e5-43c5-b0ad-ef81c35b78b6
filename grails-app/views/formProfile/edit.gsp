<%@ page import="traction.I18N; grails.converters.JSON" contentType="text/html;charset=UTF-8" %>

<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message ocde="user.edit"/></title>

    <asset:javascript src="lib/jquery-ui.min.js"/>
    <asset:stylesheet src="lib/jquery-ui.css"/>
    <asset:javascript src="formProfile/edit.js"/>
    <asset:javascript src="container/attachment.js"/>
    <style>
    .CodeMirror {
        height: 150px;
    }

    .ui-state-active, .ui-widget-content .ui-state-active, .ui-widget-header .ui-state-active, a.ui-button:active, .ui-button:active, .ui-button.ui-state-active:hover {
        border: 1px solid #c5c5c5;
        background: #f6f6f6;
        font-weight: bold;
        color: #454545;
    }

    .ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
        color: #454545;
    }

    .ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor, .ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor, .ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
        cursor: pointer;
    }
    </style>
    <script>
        var lang = '${params.lang ? params.lang : request.locale.language}';
        var profileId = '${profile?.id}';
        var messageConfirm = '<g:message code="form.delete.confirm"/>';
    </script>
    <g:render template="/formProfile/formeoObj"/>
</head>

<body>
<div class="row m-4">
    <g:form name="editProfile" action="save" class="px-0">
        <div class="col-12 px-0">
            <div class="d-flex">
                <g:if test="${profile?.id}">
                    <h2 class="mb-3"><g:message code="form.profile.edit"/> ${profile?.id}</h2>
                </g:if>
                <g:else>
                    <h2 class="mb-3"><g:message code="form.profile.create"/></h2>
                </g:else>
                <div class="ms-auto">
                    <button type="submit" id="editButton" class="btn btn-primary fw-bold">
                        <g:message code="client.save"/>
                    </button>
                    <g:if test="${profile?.id}">
                        <button type="button" class="btn btn-danger fw-bold"
                                onclick="deleteProfile('<g:message code="form.profile.delete.confirm" args="${[profile.events.size(), profile.forms.size()]}" />', ${profile.id});">
                            <g:message code="default.button.delete.label"/>
                        </button>
                    </g:if>
                </div>
            </div>
            <!-- END Menu -->
            <!-- From -->
            <input type="hidden" name="id" value="${profile?.id}">

            <div class="col-sm-12 col-md-12">
                <div id="errorDailySummary" class="alert alert-danger">
                    <g:message code="form.profile.dailysummary.error"/>
                </div>
            </div>

            <div class="mb-3 col-sm-12 col-md-12">
                <div class="row mb-2">
                    <div class="col-sm-12 col-md-6 pe-2">
                        <label><g:message code="form.profile.name"/></label>
                        <input type="text" class="form-control" name="name" value="${profile?.name}">
                    </div>

                    <div class="col-sm-12 col-md-6 pe-2">
                        <label><g:message code="associated.origin"/></label>
                        <select class="form-select" name="origin">
                            <option value=""><g:message code="none"/></option>
                            <g:each var="o" in="${origins}">
                                <option value="${o.id}" ${o.id == profile?.originId ? 'selected' : ''}>${o.name}</option>
                            </g:each>
                        </select>
                    </div>

                    <div class="col-sm-12 col-md-12 pt-3">
                            <div class="form-check ">
                                <input id="checkSlider" type="checkbox" role="switch" class="form-check-input border-primary"
                                       name="naked" ${profile?.naked ? 'checked' : ''}><span><g:message
                                    code="form.profile.naked"/></span>
                            </div>
                    </div>

                    <div class="col-sm-12 col-md-12 pt-3">
                        <div class="form-check ">
                            <input id="notifyCheckSlider" type="checkbox" role="switch" class="form-check-input border-primary"
                               name="notifyUsersAssigned" ${profile?.notifyAssignedUser ? 'checked' : ''}><span><g:message
                            code="form.profile.notifyAssignedUser"/></span>
                        </div>
                    </div>

                    <div class="col-sm-6 col-md-6 pt-3">
                            <div class="form-check ">
                                <input id="redirectCheckSlider" type="checkbox" role="switch" class="form-check-input border-primary"
                                       name="redirectUsersAssigned" ${profile?.autoRedirect ? 'checked' : ''}><span><g:message
                                    code="form.profile.redirectAssignedUser"/></span>
                            </div>
                    </div>

                    <div class="col-sm-12 col-md-12 pt-3">
                        <div class="form-check ">
                            <input type="checkbox" role="switch" class="form-check-input border-primary"
                                   id="thanks" ${profile?.thanksMessage ? 'checked' : ''}><span><g:message
                                code="form.thanks.enabled"/></span>
                        </div>

                        <div class="thanksData">
                            <label><g:message code="form.thanks.data"/></label>

                            <div id="thanksMessageBody">
                                <textarea name="thanksMessage" id="thanksMessage">${profile?.thanksMessage}</textarea>
                                <script>
                                    var myCodeMirror2 = CodeMirror.fromTextArea(thanksMessage, {
                                        lineNumbers: true,
                                        smartIndent: true
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-md-12 mt-3">
                        <div class="form-check ">
                            <input type="checkbox" role="switch" class="form-check-input border-primary" id="consentOn" ${profile?.consentment ? 'checked' : ''}>
                            <span><g:message code="form.consent"/></span>
                        </div>

                        <div class="consentOnData">
                            <label><g:message code="form.consent.data"/></label>

                            <div id="consent-body">
                                <textarea name="consentment" id="consentment">${profile?.consentment}</textarea>
                                <script>
                                    var myCodeMirror = CodeMirror.fromTextArea(consentment, {
                                        lineNumbers: true,
                                        smartIndent: true
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-md-12 mt-3">
                        <div class="form-check ">
                            <input type="checkbox" name="clientEmail" role="switch" class="form-check-input border-primary"
                                   id="clientEmail" ${profile?.clientBodyEmail ? 'checked' : ''}>
                            <span><g:message code="form.email.customer"/></span>
                        </div>

                        <div class="clientEmailData">
                            <label><g:message code="form.email.data"/></label>

                            <div id="thanks-body">
                                <textarea name="clientBodyEmail"
                                          id="clientBodyEmail">${profile?.clientBodyEmail}</textarea>
                                <script>
                                    var myCodeMirror2 = CodeMirror.fromTextArea(clientBodyEmail, {
                                        lineNumbers: true,
                                        smartIndent: true
                                    });
                                </script>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-md-12 mt-3">
                        <div class="form-check ">
                            <input type="checkbox" name="clientSms" role="switch" class="form-check-input border-primary"
                                   id="clientSms" ${profile?.clientBodySms ? 'checked' : ''}>
                            <span><g:message code="action.type.smstoclient"/></span>
                        </div>

                        <div class="md-form clientSmsData">
                            <label><g:message code="twilionumber"/>:</label>

                            <select id="clientTwilioNumber" name="clientTwilioNumber">
                                <option value="">----</option>
                                <g:each var="sourceName" in="${twilioNumbers.collectMany { it.getSourceNames() }.unique()}">
                                    <optgroup label="${sourceName}">
                                    <g:each in="${twilioNumbers}" var="twilioNumber">
                                        <g:if test="${twilioNumber.isInSource(sourceName)}">
                                            <option value="${twilioNumber.getNumber()}" ${twilioNumber.getNumber() == profile?.twilionumber ? 'selected' : ''}>${twilioNumber.getNumber()} - ${twilioNumber.getUserGroup() ? g.message(code: twilioNumber.getUserGroup().message) : twilioNumber.getUser()?.getFullName()}</option>
                                        </g:if>
                                    </g:each>
                                </g:each>
                            </select><br>

                            <label><g:message code="sms.body"/></label>

                            <div id="thanks-body-sms">
                                <textarea name="clientBodySms"
                                          id="clientBodySms">${profile?.clientBodySms}</textarea>
                                <script>
                                    var myCodeMirror2 = CodeMirror.fromTextArea(clientBodySms, {
                                        lineNumbers: true,
                                        smartIndent: true
                                    });
                                </script>
                            </div>
                        </div>
                    </div>


                    <div class="col-sm-12 col-md-12 pt-3">
                        <div class="form-check ">
                            <input id="dailySummary" type="checkbox" role="switch" class="form-check-input border-primary"
                                   name="dailysummary" ${profile?.dailySummary ? 'checked' : ''}><span><g:message
                                code="form.profile.dailysummary.description"/></span>
                        </div>
                    </div>

                    <g:if test="${profile}">
                        <div class="col-sm-12 col-md-12 pt-3">
                            <div class="form-inline checkbox-slider--a checkbox-slider-md">
                                <label class="pe-2"><g:message code="form.profile.backgroundimage"/> :</label>
                                <div class="d-flex">
                                    <input id="generatedUrlAssetFile" name="backgroundImage" type="text"
                                           class="form-control rounded-end-0" value="${profile?.backgroundImage}"/>
                                    <button type="button" class="btn btn-primary fw-bold rounded-start-0"
                                            onclick="addImageModal('form-profile-background-image', '#generatedUrlAssetFile')">
                                        <g:message code="default.button.add.label"/>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 col-md-12 pt-3">
                            <div class="form-inline checkbox-slider--a checkbox-slider-md">
                                <label class="pe-2"><g:message code="form.profile.font"/> :</label>
                                <div class="d-flex">
                                    <input id="font" name="font" type="text" class="form-control rounded-end-0" value="${profile?.font}"/>
                                    <button type="button" class="btn btn-primary fw-bodl rounded-start-0"
                                            onclick="addImageModal('form-profile-font', '#font')">
                                        <g:message code="default.button.add.label"/>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 col-md-12 pt-3">
                            <div class="form-inline checkbox-slider--a checkbox-slider-md">
                                <button type="button" class="btn btn-primary fw-bold" onclick="addImageModal()">
                                    <g:message code="form.profile.image.list"/>
                                </button>
                            </div>
                        </div>
                    </g:if>

                    <div class="float-right form-inline">

                    </div>
                </div>
            </div>
        </div>
    </g:form>
    <g:if test="${profile}">
        <div id="forms" class="px-0">
            <g:render template="forms" model="['profile': profile]"/>
        </div>
    </g:if>
</div>
<script>
    function addImageModal(key, input) {
        if (key != null) {
            window[key] = function (data) {
                console.log('addImageModal callback', data, key, input)
                if (data != null && data.id != null && input != null) {
                    $(input).val(tractionWebRoot + '/web/getFile/' + data.id)
                    TractionModal.hide()
                }
            }
        }
        TractionModal.show({
            url: '/client/getAttachmentsModal',
            data: {
                formProfileId: ${profile?.id},
                addCallback: key,
                multiple: false,
                copy: true,
                options: {'addCallback': key, 'multiple': false, 'copy': true}
            }
        });
    }
</script>

<g:if test="${profile?.id}">
    <g:render template="eventsList"/>
</g:if>
</body>
</html>
