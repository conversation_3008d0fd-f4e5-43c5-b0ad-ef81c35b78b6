<script>
    $(document).ready(function() {
        window.filterProducts = TractionDataTable.init('#filterProducts', {
            id: 'filterProducts',
            dataTableOptions: {
                'searching': false,
                'serverSide': false,
                'buttons': [{
                    text: I18N.m('default.button.add.label'),
                    className: 'btn btn-primary-important text-white rounded border',
                    action: function() {
                        ProductFilter.edit(null);
                    }
                }],
                'columnDefs': [
                    {'name': 'name', 'data': 'name', 'targets': 'name'},
                    {'name': 'actions', 'data': 'actions', 'targets': 'actions', 'width': '75px'}
                ]
            }
        });
    });
</script>
<div class="m-4">
    <div class="row">
        <div class="col-12">
            <table class="table table-striped w-100" cellspacing="0" id="filterProducts" class="display">
                <thead>
                <tr>
                    <th class="name"><g:message code="product.category.name"/></th>
                    <th class="actions"><g:message code="actions"/></th>
                </tr>
                </thead>
                <tbody>
                <g:each var="f" in="${filters}">
                    <tr>
                        <td>${f.name}</td>
                        <td>
                            <div class="d-flex">
                                <i onclick="ProductFilter.edit('${f.id}');" data-toggle="tooltip" title="<g:message code="default.button.edit.label"/>" class="fa-light fa-pen cursor text-primary p-0 me-3"></i>
                                <i onclick="ProductFilter.delete('${f.id}');" data-toggle="tooltip" title="<g:message code="default.button.delete.label"/>" class="fa-light fa-trash cursor text-primary p-0"></i>
                            </div>
                        </td>
                    </tr>
                </g:each>
                </tbody>
            </table>
        </div>
    </div>
</div>

