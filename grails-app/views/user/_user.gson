import traction.security.User

model {
    User user
}

// Priorité à l'image base64, sinon fallback sur FileData
String avatarString = ""
if (user.hasImageBase64()) {
    avatarString = user.getImageBase64ForDisplay()
} else if (user.image) {
    avatarString = "/web/getFile?id=${user.image.id}"
}

json g.render(user, [expand: ['status', 'image'], excludes: ['externalIdentifiers','userGroup','password', 'passwordExpired', 'notifications', 'allManagerProductProfiles', 'allManagerProductView', 'allManagerProductFilter', 'visibleInSoldboard', 'signSmsWithName', 'allPrivateCalls', 'allPrivateCommunications', 'authCode', 'lostPassword', 'managerProductProfiles', 'workflowAssigned', 'attributes', 'templates', 'businessUnits', 'mobileConnections', 'imageBase64']]) {
    avatar avatarString
    userGroup user.userGroup.id
    externalIdentifiers tmpl."/externalIdentifier/externalIdentifiers"(externalIdentifiers: user.externalIdentifiers)
}