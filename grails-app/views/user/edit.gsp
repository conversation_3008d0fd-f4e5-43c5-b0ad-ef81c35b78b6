<%@ page contentType="text/html;charset=UTF-8" import="traction.security.User; traction.BusinessUnit; traction.security.Role; traction.security.UserRole" %>
<html>
<head>
	<meta name="layout" content="mainTraction"/>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title>${user ? user.username + " | " + g.message(code: "user.edit") : g.message(code: "user.new")}</title>
	
	<asset:javascript src="lib/password-score.js"/>
	<asset:javascript src="lib/password-score-options.js"/>
	<asset:javascript src="lib/bootstrap-strength-meter.js"/>
	<asset:javascript src="lib/jscolor/jscolor.js"/>
	<asset:javascript src="datatable/DataTableViewTable.js"/>
	
	<asset:javascript src="libTraction/inputTel.js"/>
	<asset:javascript src="user/edit.js"/>
	<asset:stylesheet src="externalIdentifier/index.css"/>
	<asset:stylesheet src="communication/CommunicationInbox.css"/>
	
	<script>
        var oldUserGroup = "${user?.userGroup?.id}";
        var newUserGroup = "${user?.userGroup?.id}";
        var interceptProcessUser = "";
	</script>

</head>

<body>
<div class="row user-edit m-4">
	<div class="col-12">
		<!-- Menu -->
		<div class="row">
			<div class="col-4">
			</div>
			
			<!-- Titre section -->
			<div class="d-flex align-items-baseline justify-content-between mt-2">
				<h2 class="mb-0">${user?.firstname} ${user?.lastname}</h2>
				<button id="deleteButton"
				        onclick="deleteProcess()"
				        class="btn btn-primary d-flex align-items-center gap-2 fw-bold px-4 py-2">
					<i class="fa fa-trash"></i>
					<g:message code="default.button.delete.label"/>
				</button>
			</div>
		
		</div>
		<!-- END Menu -->
		<!-- Navigation Menu -->
		<ul id="client-navigation" class="nav nav-tabs full-width-underline px-3 mt-5" role="tablist">
			
			<li class="nav-item mt-auto">
				<button type="button" role="tab" class="nav-link active show" data-bs-toggle="tab"
				        onclick="ClientIndex.router.navigate('Informations');" data-navigation="Informations">
					<g:message code="user.edit.informations"/>
				</button>
			</li>
			
			<li class="nav-item mt-auto">
				<a type="button" target="_blank" class="nav-link"
				   href="<g:createLink absolute='true' controller='workSchedule'
				                       action='edit'/>?userId=${user?.id}">
					<g:message code="workschedule"/>
				</a>
			
			</li>
			
			<li class="nav-item mt-auto">
				<button type="button" role="tab" class="nav-link" onclick="TractionModal.show({
                    url: '/user/modaluserpoints/${user.id}'
                });" data-navigation="Objectifs_et_performance">
					<g:message code="user.edit.goals"/>
				</button>
			</li>
		
		</ul>
		
		
		<!-- From -->
		<form id="userEditForm" method="post" enctype="multipart/form-data">
			
			<input type="hidden" name="id" id="userId" value="${user?.id}"/>
			<!-- INFORMATIONS PERSONNELLES -->
			<div class="card mt-4 p-3">
				<!-- Titre section -->
				<div class="d-flex align-items-baseline gap-3 mb-3">
					<h6 class="mb-0"><g:message code="user.edit.informations.personel"/></h6>
				</div>
				
				
				<g:set var="attrs" value="${user?.attributes?.collectEntries { [(it.name): it.value] }}"/>
				<g:set var="bStr" value="${attrs?.birthday?.toString()}"/>
				<g:set var="parts" value="${bStr ? bStr.split('-') : []}"/>
				
				<g:set var="bYear" value="${parts?.size() == 3 && parts[0]?.size() >= 4 ? parts[0] : ''}"/>
				<g:set var="bMonth" value="${parts?.size() == 3 && parts[1] ? parts[1].padLeft(2, '0') : ''}"/>
				<g:set var="bDay" value="${parts?.size() == 3 && parts[2] ? parts[2].padLeft(2, '0') : ''}"/>
				
				<!-- Variables pour hire date -->
				<g:set var="hStr" value="${attrs?.hireDate?.toString()}"/>
				<g:set var="hParts" value="${hStr ? hStr.split('-') : []}"/>
				
				<g:set var="hYear" value="${hParts?.size() == 3 && hParts[0]?.size() >= 4 ? hParts[0] : ''}"/>
				<g:set var="hMonth" value="${hParts?.size() == 3 && hParts[1] ? hParts[1].padLeft(2, '0') : ''}"/>
				<g:set var="hDay" value="${hParts?.size() == 3 && hParts[2] ? hParts[2].padLeft(2, '0') : ''}"/>
				
				
				<g:set var="months" value="${[
						[id: '01', label: 'Janvier'], [id: '02', label: 'Février'], [id: '03', label: 'Mars'],
						[id: '04', label: 'Avril'], [id: '05', label: 'Mai'], [id: '06', label: 'Juin'],
						[id: '07', label: 'Juillet'], [id: '08', label: 'Août'], [id: '09', label: 'Septembre'],
						[id: '10', label: 'Octobre'], [id: '11', label: 'Novembre'], [id: '12', label: 'Décembre']
				]}"/>
				
				<div class="row g-3">
					<!-- Colonne photo (à gauche) -->
					<div class="col-12 col-md-2">
						<div class="photo-card position-relative text-center">
							<g:if test="${user?.image}">
								<img id="profileImgPersonal" class="photo-preview"
								     src="<g:createLink absolute='true' controller='web' action='getFile'
								                        params='[id: user.image.id, v: user.dateModif?.time]'/>"/>
							</g:if>
							<g:else>
								<asset:image id="profileImgPersonal" class="photo-preview" src="avatar-blank.jpg"/>
							</g:else>

							<!-- input file pour upload photo -->
							<input type="file" id="uploadPhotoPersonal" name="file_data" accept="image/*" class="d-none"
							       onchange="uploadUserProfileImage(this)"/>

							<label for="uploadPhotoPersonal" class="upload-btn mt-2">
								<g:message code="default.button.upload.label" default="Choisir un fichier"/>
							</label>
						</div>
					</div>
					
					<!-- Colonne formulaire (grille) -->
					<div class="col-12 col-md-10">
						<div class="grid-form">
							<!-- Prénom -->
							<div class="field">
								<label><g:message code="user.firstname"/></label>
								<input type="text" class="form-control" name="firstname" value="${user?.firstname}"/>
							</div>
							
							<!-- Adresse -->
							<div class="field">
								<label><g:message code="user.address"/></label>
								<input type="text" class="form-control" name="address" value="${attrs?.address}"/>
							</div>
							
							<!-- Nom -->
							<div class="field">
								<label><g:message code="user.lastname"/></label>
								<input type="text" class="form-control" name="lastname" value="${user?.lastname}"/>
							</div>
							
							<!-- Région -->
							<div class="field">
								<label><g:message code="user.region"/></label>
								<input type="text" class="form-control" name="region" value="${attrs?.region}"/>
							</div>
							
							<!-- Anniversaire  -->
							<div class="field  il-birthday">
								<label><g:message code="user.birthday"/></label>
								
								<div class="birthday-group">
									<!-- Jour -->
									<select class="form-select" name="birthday_day" aria-label="Day">
										<g:each in="${1..31}" var="d">
											<option value="${String.format('%02d', d)}"
												${bDay == String.format('%02d', d) ? 'selected' : ''}>
												${String.format('%02d', d)}
											</option>
										</g:each>
									</select>
									
									<!-- Mois -->
									<select class="form-select" name="birthday_month" aria-label="Month">
										<g:each var="m" in="${months}">
											<option value="${m.id}" ${bMonth == m.id ? 'selected' : ''}>${m.label}</option>
										</g:each>
									</select>
									
									<!-- Année -->
									<select class="form-select" name="birthday_year" aria-label="Year">
										<g:each var="y" in="${(1950..(new Date()[Calendar.YEAR])).toList().reverse()}">
											<option value="${y}" ${bYear == y.toString() ? 'selected' : ''}>${y}</option>
										</g:each>
									</select>
								</div>
								
								<!-- Valeur ISO pour le backend -->
								<input type="hidden" id="birthday" name="birthday" value="${attrs?.birthday}"/>
							</div>
							
							<!-- Province -->
							<div class="field">
								<label><g:message code="user.province"/></label>
								<input type="text" class="form-control" name="province" value="${attrs?.province}"/>
							</div>
							
							<!-- Genre -->
							<div class="field">
								<label><g:message code="user.gender"/></label>
								<select class="form-select" name="gender">
									<g:set var="gval" value="${attrs?.gender}"/>
									<g:each var="s" in="${genders}">
										<option value="${s.message}" ${gval && gval.equals(s.message) ? 'selected' : ''}>
											<g:message code="${s.message}"/>
										</option>
									</g:each>
								</select>
							</div>
							
							<!-- Pays -->
							<div class="field">
								<label><g:message code="user.country"/></label>
								<input type="text" class="form-control" name="country" value="${attrs?.country}"/>
							</div>
							
							<!-- Langue -->
							<div class="field">
								<label><g:message code="user.language"/></label>
								<g:select from="${['FR', 'EN', 'ES']}" value="${attrs?.language}" class="form-select"
								          name="language"/>
							</div>
							
							<!-- Code postal -->
							<div class="field">
								<label><g:message code="user.zipcode"/></label>
								<input type="text" class="form-control" name="zipCode" value="${attrs?.zipCode}"/>
							</div>
							
							<!-- Email -->
							<div class="field ">
								<label><g:message code="user.email"/></label>
								<input type="email" class="form-control" name="email" value="${user?.email}"/>
							</div>
							
							<!-- Téléphone -->
							<div class="field">
								<label><g:message code="client.phone"/></label>
								
								<div class="input-group">
									<select class="form-select flex-grow-0" name="phone_cc" style="max-width:90px;">
										<option value="+1" ${attrs?.country == 'Canada' ? 'selected' : ''}>+1</option>
										<option value="+33" ${attrs?.country == 'France' ? 'selected' : ''}>+33</option>
									</select>
									<input type="tel" class="form-control" name="phone" value="${attrs?.phone}"
									       placeholder="(XXX) XXX-XXXX"/>
								</div>
							</div>
							
							<!-- Notifications -->
							<div class="field">
								<label class="me-2"><g:message code="notifications"/></label>
								
								<div class="form-check form-switch">
									<input name="notifications" type="checkbox" class="form-check-input"
									       id="notifSwitch"
										${user?.notifications ? 'checked' : ''}/>
								</div>
							</div>
							
							<!-- Mobile -->
							<div class="field">
								<label><g:message code="client.phonecell"/></label>
								
								<div class="input-group">
									<select class="form-select flex-grow-0" name="cell_cc" style="max-width:90px;">
										<option value="+1" ${attrs?.country == 'Canada' ? 'selected' : ''}>+1</option>
										<option value="+33" ${attrs?.country == 'France' ? 'selected' : ''}>+33</option>
									</select>
									<input type="tel" class="form-control" name="cell" value="${attrs?.cell}"
									       placeholder="(XXX) XXX-XXXX"/>
								</div>
							</div>
							
							<!-- Actions (plein largeur) -->
							<div class="actions full d-flex justify-content-end">
								<g:link class="btn btn-sm btn-link-subtle w-auto f-16 py-0 me-2" action="index">
									<g:message code="cancel"/>
								</g:link>
								<button type="submit" class="btn btn-sm btn-link w-auto pe-0 f-16 py-0"
								        onclick="editProcess()">
									<g:message code="save"/>
								</button>
							</div></div>
					</div>
				</div>
				
				<!-- Compose birthday (YYYY-MM-DD) avant submit -->
				<script>
                    (function () {
                        const form = document.getElementById('userEditForm');
                        if (!form) return;
                        form.addEventListener('submit', function () {
                            // Compose birthday
                            const d = document.querySelector('[name="birthday_day"]')?.value;
                            const m = document.querySelector('[name="birthday_month"]')?.value;
                            const y = document.querySelector('[name="birthday_year"]')?.value;
                            if (d && m && y) document.getElementById('birthday').value = `${y}-${m}-${d}`;

                            // Compose hire date
                            const hd = document.querySelector('[name="hireDate_day"]')?.value;
                            const hm = document.querySelector('[name="hireDate_month"]')?.value;
                            const hy = document.querySelector('[name="hireDate_year"]')?.value;
                            if (hd && hm && hy) document.getElementById('hireDate').value = `${hy}-${hm}-${hd}`;

                            // Synchroniser les photos : s'assurer qu'un seul input file est utilisé
                            const personalFile = document.getElementById('uploadPhotoPersonal').files[0];
                            const workFile = document.getElementById('uploadPhotoWork').files[0];

                            // Si une photo est sélectionnée dans la section travail, la copier vers la section personnelle
                            if (workFile && !personalFile) {
                                // Créer un nouveau FileList avec le fichier de la section travail
                                const dt = new DataTransfer();
                                dt.items.add(workFile);
                                document.getElementById('uploadPhotoPersonal').files = dt.files;
                            }

                        }, {capture: true});
                    })();

                    // Fonction pour prévisualiser l'image de profil utilisateur
                    function uploadUserProfileImage(input) {
                        const file = input.files[0];
                        if (!file) return;

                        // Prévisualisation immédiate
                        const url = window.URL.createObjectURL(file);
                        document.getElementById('profileImgPersonal').src = url;
                        document.getElementById('profileImgWork').src = url;

                        // Synchroniser les deux inputs
                        if (input.id === 'uploadPhotoPersonal') {
                            document.getElementById('uploadPhotoWork').files = input.files;
                        } else {
                            document.getElementById('uploadPhotoPersonal').files = input.files;
                        }

                        // L'image sera uploadée lors du save du formulaire
                        $.notify('Image sélectionnée. Cliquez sur "Save" pour sauvegarder.', 'info');
                    }
				</script>
			</div>
			<!-- INFORMATIONS DE TRAVAIL -->
			<div class="card mt-4 p-3">
				<!-- Titre section -->
				<div class="d-flex align-items-baseline gap-3 mb-3">
					<h6 class="mb-0"><g:message code="user.edit.informations.work"/></h6>
				</div>
				
				<div class="row g-3 align-items-start">
					
					<!-- PHOTO À GAUCHE -->
					<div class="col-12 col-md-2">
						<div class="photo-card-pro position-relative text-center">
							<g:if test="${user?.image}">
								<img id="profileImgWork" class="photo-preview-pro"
								     src="<g:createLink absolute='true' controller='web' action='getFile'
								                        params='[id: user.image.id, v: user.dateModif?.time]'/>"/>
							</g:if>
							<g:else>
								<asset:image id="profileImgWork" class="photo-preview-pro" src="avatar-blank.jpg"/>
							</g:else>

						<!-- input file caché -->
							<input type="file" id="uploadPhotoWork" name="file_data" accept="image/*" class="d-none"
							       onchange="uploadUserProfileImage(this)"/>

							<!-- bouton flottant caméra -->
							<label for="uploadPhotoWork" class="upload-fab-pro" aria-label="Choisir une photo">
								<i class="fa fa-camera"></i>
							</label>
						</div>
					</div>
					
					<!-- GRILLE À DROITE -->
					<div class="col-12 col-md-10">
						<div class="grid-form">
							
							<!-- Username -->
							<div class="field">
								<label><g:message code="user.username"/></label>
								<input type="text" class="form-control" name="username" value="${user?.username}">
							</div>
							
							<!-- Hire date -->
							<div class="field  il-birthday">
								<label><g:message code="user.hireDate"/></label>
								
								<div class="birthday-group">
									<!-- Jour -->
									<select class="form-select" name="hireDate_day" aria-label="Day">
										<g:each in="${1..31}" var="d">
											<option value="${String.format('%02d', d)}"
												${hDay == String.format('%02d', d) ? 'selected' : ''}>
												${String.format('%02d', d)}
											</option>
										</g:each>
									</select>
									
									<!-- Mois -->
									<select class="form-select" name="hireDate_month" aria-label="Month">
										<g:each var="m" in="${months}">
											<option value="${m.id}" ${hMonth == m.id ? 'selected' : ''}>${m.label}</option>
										</g:each>
									</select>
									
									<!-- Année -->
									<select class="form-select" name="hireDate_year" aria-label="Year">
										<g:each var="y" in="${(1980..(new Date()[Calendar.YEAR])).toList().reverse()}">
											<option value="${y}" ${hYear == y.toString() ? 'selected' : ''}>${y}</option>
										</g:each>
									</select>
								</div>
								
								<!-- Valeur ISO pour le backend -->
								<input type="hidden" id="hireDate" name="hireDate" value="${attrs?.hireDate}"/>
							</div>
							
							<!-- Password -->
							<div class="field">
								<label><g:message code="user.password"/></label>
								<input type="password" id="password" class="form-control" name="motdepasse"
								       value="${user?.password}">
							</div>
							
							<!-- Succursale -->
							<div class="field">
								<label><g:message code="user.department"/></label>
								<select name="department" id="department-multiselect" class="form-select"
									${allowMultipleDepartment ? 'multiple="multiple"' : ''}>
									<g:each var="dept" in="${departments}">
										<option value="${dept.id}" ${user?.departments?.contains(dept) ? "selected" : ""}>${dept.name}</option>
									</g:each>
								</select>
							</div>
							
							<!-- Auth code -->
							<div class="field">
								<label><g:message code="user.authCode"/></label>
								<input type="password" id="authCode" class="form-control" name="authCode"
								       value="${user?.authCode}">
							</div>
							
							<!-- Department -->
							<div class="field">
								<label><g:message code="user.userGroup"/></label>
								<select name="department" id="department-multiselect" class="form-select"
									${allowMultipleDepartment ? 'multiple="multiple"' : ''}>
									<g:each var="dept" in="${departments}">
										<option value="${dept.id}" ${user?.departments?.contains(dept) ? "selected" : ""}>${dept.name}</option>
									</g:each>
								</select>
							</div>
							
							<!-- Password strength -->
							<div class="field">
								<label><g:message code="user.password.strength"/></label>
								
								<div id="passwordstrength"></div>
							</div>
							
							
							<!-- Title -->
							<div class="field">
								<label><g:message code="user.title"/></label>
								<input type="text" class="form-control" name="title"
								       value="${user?.attributes.find { it.name == 'title' }?.value}">
							</div>
						
						<!-- Identifiants externes (plein largeur) -->
							<g:if test="${user}">
								<div class="field ">
									<label><g:message code="extID"/> :</label>
									
									<div class="chips">
										<g:each var="extId" in="${user.externalIdentifiers}">
											<span class="chip">
												<span class="chip-text">
													<g:message code="${extId.dms.name}"/> : ${extId.extId}
												</span>
												<button type="button"
												        class="chip-btn mdi mdi-trash-can"
												        title="<g:message code='default.button.delete.label'/>"
												        onclick="ExternalIdentifier.deleteExtId(${extId.id}, event)"></button>
											</span>
										</g:each>
										
										<button type="button"
										        class="add-chip mdi mdi-plus"
										        data-toggle="tooltip"
										        title="<g:message code='extID.add'/>"
										        onclick="TractionModal.show({
                                                    url: '/externalIdentifier/modalAddExtId',
                                                    data: {userId: ${user.id}}
                                                });"></button>
										<asset:javascript src="externalIdentifier/index.js"/>
									</div>
								</div>
							</g:if>
						
						<!-- Business Unit -->
							<div class="field">
								<label><g:message code="user.edit.businessUnit"/></label>
								<select name="businessUnit" id="boot-multiselect" class="form-select"
								        multiple="multiple">
									<g:each var="bu" in="${BusinessUnit.getAll()}">
										<option value="${bu.id}" ${user?.businessUnits?.contains(bu) ? "selected" : ""}>
											<g:message code="${bu.name}"/>
										</option>
									</g:each>
								</select>
							</div>
							
							<!-- Default From Email -->
							<div class="field">
								<label><g:message code="user.defaultFromEmail"/></label>
								<input class="form-control" name="defaultFromEmail" value="${user?.defaultFromEmail}">
							</div>
							
							<!-- Color -->
							<div class="field">
								<label><g:message code="user.color"/></label>
								<input name="color" class="jscolor form-control"
								       value="${user?.color ? user?.color.substring(1) : randomColor.substring(1)}">
							</div>
							
							<!-- Notifications -->
							<div class="field">
								<label class="me-2"><g:message code="notifications"/></label>
								
								<div class="form-check form-switch">
									<input name="notifications" type="checkbox" class="form-check-input"
									       id="notifSwitch"
										${user?.notifications ? 'checked' : ''}/>
								</div>
							</div>
							
							<!-- Actif -->
							<div class="field">
								<label class="me-2">Actif -</label>
								
								<div class="form-check form-switch">
									<input name="notifications" type="checkbox" class="form-check-input"
									       id="notifSwitch"
										${user?.notifications ? 'checked' : ''}/>
								</div>
							</div>
							
							<!-- Actions (plein largeur) -->
							<div class="actions full d-flex justify-content-end">
								<g:link class="btn btn-sm btn-link-subtle w-auto f-16 py-0 me-2" action="index">
									<g:message code="cancel"/>
								</g:link>
								<button type="submit" class="btn btn-sm btn-link w-auto pe-0 f-16 py-0"
								        onclick="editUser()">
									<g:message code="save"/>
								</button>
							</div>
						
						</div>
					</div>
				</div>
			</div>
			<!-- SIGNATURE -->
			<div class="card mt-4 p-3">
				<!-- Titre section + Label en flex -->
				<div class="d-flex align-items-center gap-3 mb-3">
					<h6 class="mb-0"><g:message code="user.edit.signature"/></h6>
					<label class="mb-0"><g:message code="user.signature"/></label>
				</div>
				
				<div class="row">
					<div class="col-sm-12">
						<div class="border" id="userSignaturePreview"></div>
					</div>
				</div>
			</div>
			
			<!-- RÔLE ET AUTORISATION -->
			<div class="card mt-4 p-3">
				<div class="d-flex align-items-baseline gap-3 mb-3">
					<h6 class="mb-0"><g:message code="user.edit.informations.autorisations"/></h6>
				</div>
				
				<div class="mb-3 full">
					<label><g:message code="user.permissionLevel"/></label>
					<select class="form-select mb-2" name="permissionLevel">
						<g:each var="level" in="${traction.permissions.PermissionLevel.values()}">
							<option value="${level.name()}" ${user?.permissionLevel == level ? "selected" : ""}>
								<g:message code="${level.message}"/>
							</option>
						</g:each>
					</select>
				
				</div>
				
				<div class="row">
					<!-- Bloc Roles (colonne gauche) -->
					<div class="mb-3 col-sm-12 col-md-6">
						<label><g:message code="user.roles"/></label>
						<ul class="list-group">
							<g:each var="role" in="${Role.findAll()}">
								<g:if test="${!role.isHidden()}">
									<li class="list-group-item d-flex justify-content-between"
									    data-original-role="${user && UserRole.exists(user.id, role.id) ? 'true' : 'false'}"
									    data-role="${role.authority}">
										${role.name}
										<input type="checkbox" role="switch" class="form-check-input border-primary"
										       name="${role.authority}" ${user && UserRole.exists(user.id, role.id) ? 'checked' : ''}>
									</li>
								</g:if>
							</g:each>
						</ul>
					</div>
					
					<!-- Colonne droite : Group + Account -->
					<div class="col-sm-12 col-md-6">
						<!-- Bloc Group Assignation -->
						<div class="mb-3">
							<label><g:message code="user.groupassignation"/></label>
							<ul id="groups" class="list-group">
								<g:each var="group" in="${groups}">
									<li class="list-group-item d-flex justify-content-between">
										<g:message code="${group.message}"/>
										<input name="${group.message}"
										       id="group-${group.id}" role="switch"
										       class="form-check-input border-primary"
										       onclick="newUserGroup = ${group.id};
                                               oldUserGroup = ${user?.userGroup?.id}"
										       type="checkbox" ${user?.userGroup == group ? 'checked' : ''}/>
									</li>
								</g:each>
							</ul>
						</div>
						
						<!-- Bloc Account -->
						<div class="mb-3">
							<label><g:message code="user.account"/></label>
							<ul class="list-group">
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.enabled"/>
									<input name="enabled" type="checkbox" role="switch"
									       class="form-check-input border-primary" ${user?.enabled ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.visibleInSoldboard"/>
									<input name="visibleInSoldboard" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.visibleInSoldboard ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.signSmsWithName"/>
									<input name="signSmsWithName" role="switch" class="form-check-input border-primary"
									       type="checkbox" ${!user || user?.signSmsWithName ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.allPrivateCalls"/>
									<input name="allPrivateCalls" role="switch" class="form-check-input border-primary"
									       type="checkbox" ${user?.allPrivateCalls ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.allPrivateCommunications"/>
									<input name="allPrivateCommunications" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.allPrivateCommunications ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.allManagerProductProfiles"/>
									<input name="allManagerProductProfiles" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.allManagerProductProfiles ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.allManagerProductView"/>
									<input name="allManagerProductView" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.allManagerProductView ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.allManagerProductFilter"/>
									<input name="allManagerProductFilter" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.allManagerProductFilter ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.notificationService"/>
									<input name="notificationService" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.notificationService ? 'checked' : ''}>
								</li>
								<li class="list-group-item d-flex justify-content-between">
									<g:message code="user.notificationDepartmentService"/>
									<input name="notificationDepartmentService" role="switch"
									       class="form-check-input border-primary"
									       type="checkbox" ${user?.notificationDepartmentService ? 'checked' : ''}>
								</li>
							</ul>
						</div>
					</div>
				</div>
			
			</div>
			<g:if test="${user}">
				<!-- Modal -->
				<div class="modal fade" id="userGroupChange" tabindex="-1" role="dialog"
				     aria-labelledby="exampleModalLabel"
				     aria-hidden="true">
					<div class="modal-dialog modal-dialog-scrollable" role="document">
						<div class="modal-content">
							<div class="modal-header">
								<h2 class="modal-title fw-bold" id="exampleModalLabel"><g:message
										code="user.edit.usergroupchange.title"/></h2>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
								
								</button>
							</div>
							
							<div class="modal-body">
								<div class="mb-3">
									<label><g:message code="user.edit.usergroupchange.user"/></label>
									<select id="userToReplace" name="userToReplace" class="form-control">
										<option value=""><g:message code="none"/></option>
										<g:each var="u"
										        in="${traction.security.User.findAllByUserGroup(user.userGroup)}">
											<g:if test="${u.id != user.id}">
												<option value="${u.id}">${u.getFullName()}</option>
											</g:if>
										</g:each>
									</select>
								</div>
								<g:message code="user.edit.usergroupchange.warning"/>
								<div class="text-danger fw-bold"><g:message
										code="default.button.delete.confirm.message"/></div>
							</div>
							
							<div class="modal-footer">
								<button type="button" class="btn btn-outline-primary fw-bold shadow-sm me-2"
								        data-bs-dismiss="modal">
									<g:message code="cancel"/>
								</button>
								<button id="editConfirmButton" type="button"
								        class="btn btn-primary fw-bold shadow-sm me-2"
								        style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"
								        onclick="continueProcessUser()">
									<g:message code="default.button.edit.label"/>
								</button>
								<button id="deleteConfirmButton" type="button" class="btn btn-danger fw-bold shadow-sm"
								        style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"
								        onclick="continueProcessUser()">
									<g:message code="default.button.delete.label"/>
								</button>
							</div>
						</div>
					</div>
				</div>
			</g:if>
		</form>
		<!-- COOKIES AUTORISES -->
		<div class="card mt-4 p-3">
			<h6 class="mb-0"><g:message code="user.edit.informations.cookies"/></h6>
			
			<div class="card border-0" id="user-datatableview-table">
			</div>
		</div>
	
	</div>
</div>

<!-- END From -->
<script>

    $(document).ready(function () {
        window.userDataTableViews = new DataTableViewTable('datatableviews', 'user-datatableview-table', true, ${user?.id ?: 0});
    });

    $(document).ready(function () {
        setAllManagerProfileViews();
        $('input.manager-product-profile').on('change', setAllManagerProfileViews);
        $('#password').strengthMeter('progressBar', {
            container: $('#passwordstrength'),
            hierarchy: {
                '0': 'bg-danger',
                '25': 'bg-warning',
                '50': 'bg-info',
                '75': 'bg-success'
            }
        });
        $('#boot-multiselect').multiselect({
            includeSelectAllOption: true,
            buttonWidth: "100%"
        });

        $('#department-multiselect').multiselect({
            includeSelectAllOption: true,
            buttonWidth: "100%"
        });

        <g:if test="${user}">
        updateUserSignature();
        </g:if>

        var $groups = $('#groups li input');
        $groups.on('change', function () {
            var checked = $(this).prop('checked');
            $groups.each(function (i) {
                $groups[i].checked = false;
            });
            $(this).prop('checked', checked);
        });
    });

    function setAllManagerProfileViews() {
        $('.manager-product-profile').each(function () {
            if ($(this).prop('checked')) {
                $('#' + $(this).attr("name")).show();
            } else {
                $('#' + $(this).attr("name")).hide();
                $('#' + $(this).attr("name")).find('input').prop('checked', false);
            }
        });
    }

    function updateUserSignature() {
        var userId = $('#userId').val();
        $.get(tractionWebRoot + '/mailTemplate/getPreviewUserSignature?userId=' + userId, function (html) {
            $('#userSignaturePreview').html(html);
        })
    }

    function editProcess() {
        let user = "${user?.id}"
        let noUserGroup = true;
        console.log('oldUserGroup', oldUserGroup);
        if ($('#groups input:checked').length) {
            noUserGroup = false;
        }

        if (oldUserGroup !== "" && noUserGroup && user != "") {
            interceptProcessUser = "edit";
            $('#deleteConfirmButton').hide();
            $('#editConfirmButton').show();
            interceptorProcessUser();
        } else if (oldUserGroup !== newUserGroup) {
            maybeShowUsergroupChangeModal(user, oldUserGroup, newUserGroup)
        } else {
            editUser();
        }
    }

    function deleteProcess() {
        interceptProcessUser = "delete";
        $('#deleteConfirmButton').show();
        $('#editConfirmButton').hide();
        interceptorProcessUser();
    }

    function interceptorProcessUser() {
        if (interceptProcessUser === "edit" || interceptProcessUser === "delete") {
            if (interceptProcessUser === "delete") {
                $('#suppressWarning').show();
            } else {
                $('#suppressWarning').hide();
            }
            hideBigLoader();
            $('#userGroupChange').modal('show');
        } else {
            editUser()
        }
    }

    function continueProcessUser() {
        if (interceptProcessUser === "delete") {
            deleteUser();
        }

        if (interceptProcessUser === "edit") {
            editUser();
        }
    }

    function editUser() {
        if ($('#passwordstrength .bg-danger, #passwordstrength .bg-warning').length) {
            $.notify('<g:message code="user.password.tooweak" />', 'error');
        } else {
            showBigLoader();
            $.post({
                url: tractionWebRoot + '/User/save',
                dataType: 'json',
                cache: false,
                contentType: false,
                processData: false,
                data: new FormData(document.getElementById('userEditForm')),
                success: function (data) {
                    if (data.success == 'error') {
                        $.notify(data.message, 'error');
                        hideBigLoader();
                    } else {
                        document.location.href = tractionWebRoot + '/user/index?message=' + data.message + '&success=' + data.success;
                    }
                },
                error: function () {
                    $.notify('Error.', 'error');
                    hideBigLoader();
                }
            });
        }
    }

    function deleteUser() {
        $.post({
            url: tractionWebRoot + '/User/delete?id=${user?.id}&userToReplace=' + $('#userToReplace').val(),
            success: function (data) {
                if (data.success) {
                    document.location.href = tractionWebRoot + '/user/index';
                } else {
                    $.notify(data.message, 'error');
                }
            },
            error: function () {
                $.notify('Error.', 'error');
            }
        });
    }

    function maybeShowUsergroupChangeModal(userID, oldUserGroup, newUserGroup) {
        if (oldUserGroup != null && newUserGroup != null && oldUserGroup != newUserGroup) {
            TractionModal.show({
                url: '/user/userGroupModal',
                bigLoader: true,
                data: {
                    userId: userID,
                    oldUserGroup: oldUserGroup,
                    newUserGroup: newUserGroup
                },
            });
        }
    }
</script>
</body>
</html>