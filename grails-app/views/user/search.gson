import traction.I18N
import traction.security.User

model {
    List<User> users = []
}

json {
    status true
    error null
    data {
        users users.collect {
            [
                    id: it.id,
                    username: it.username,
                    fullName: it.fullName,
                    firstname: it.firstname,
                    lastname: it.lastname,
                    picture: it.imageId,
                    color: it.color,
                    initial: it.getInitial(),
                    department: it.currentDepartment ? [id: it.currentDepartment.id, name: it.currentDepartment.name] : null,
                    usergroup: I18N.m(it.userGroup?.message),
                    roles : it?.attributes?.find { it.name == 'title' }?.value
            ]
        }
    }
}