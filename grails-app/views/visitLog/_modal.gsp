<div class="modal-dialog">
    <div class="modal-content">

        <div class="modal-header">
            <h2 class="modal-title"><g:message code="${visitLog ? 'visitlog.edit' : 'visitlog.add'}"/></h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
            <div class="mb-3">
                <label><g:message code="user"/></label>
                <container:userSelect2 onchange="updateVisitLogDepartmentGroupVisibility()" addNoneOption="true" singleSelect="true" id="visitLog-user" selectedUsers="${[visitLog ? visitLog.user : user]}"/>
            </div>
            <div id="visitLog-departmentGroup-div" class="mb-3 ${user ? 'd-none' : ''}">
                <label><g:message code="departmentGroup"/></label>
                <container:departmentGroupSelect2 addNoneOption="true" singleSelect="true" id="visitLog-departmentGroup" selectedDepartmentGroups="${[visitLog ? visitLog.departmentGroup : departmentGroup]}"/>
            </div>
            <div class="mb-3">
                <label>
                    <g:message code="client"/>
                    <small>(<g:message code="visitlog.client.hint"/>)</small>
                </label>
                <search:clientInit />
                <search:clientSearch type="text" id="visitLog-client" value="${visitLog?.clientId ?: visitLog?.noteOnClient}" class="form-control" callback="saveVisitLog" placeholder="${g.message(code: "client.search")}" />
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" onclick="saveVisitLog();" class="btn btn-primary shadow-sm fw-bold"><g:message code="save"/></button>
        </div>
    </div>
</div>
<script>
    $(function () {
        updateVisitLogDepartmentGroupVisibility();
    });
    function updateVisitLogDepartmentGroupVisibility() {
        $('#visitLog-user').val() == '' ? $('#visitLog-departmentGroup-div').removeClass('d-none') : $('#visitLog-departmentGroup-div').addClass('d-none');
    }

    function saveVisitLog() {
        var user = $('#visitLog-user').val();
        var departmentGroup = $('#visitLog-departmentGroup-div').hasClass('d-none') ? "" : $('#visitLog-departmentGroup').val();
        if ($('#visitLog-client').val() == '') {
            $('#visitLog-client').focus();
            $.notify(I18N.m('visitlog.save.error'), 'error');
        } else if (user == '' && departmentGroup == '') {
            $('#visitLog-user').focus();
            $.notify(I18N.m('visitlog.save.error'), 'error');
        } else {
            $.post({
                url: tractionWebRoot + '/visitLog/save',
                data: {
                    id: '${visitLog?.id}',
                    user: user,
                    departmentGroup: departmentGroup,
                    client: $('#visitLog-client').val()
                },
                success: function (data) {
                    if (data.success) {
                        $.notify(data.message, 'success');
                        if (window.filterMenu_userStats) {
                            filterMenuChange(window.filterMenu_userStats);
                        }
                        TractionModal.hide();
                        if (window.VisitLog) {
                            window.VisitLog.update();
                        }
                    } else {
                        $.notify(data.message, 'error');
                    }
                }
            });
        }
    }
</script>
