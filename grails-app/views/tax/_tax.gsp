    <asset:javascript src="tax/TaxTable.js"/>
<div class="h-100 overflow-scroll  border rounded-2 p-4">
    <h5 class="text-emphasis fw-bold f-18 text-uppercase mb-3"><g:message code="tax.config.title"></g:message></h5>

    <table class="table  w-100" id="taxTable">
        <thead>
        <tr>
            <th class="id">
                <g:message code="id"/>
            </th>
            <th class="name">
                <g:message code="tax.name"/>
            </th>
            <th class="abreviation">
                <g:message code="tax.abreviation"/>
            </th>
            <th class="amount">
                <g:message code="tax.amount"/>
            </th>
            <th class="action" style="width: 10%;">
                <g:message code="actions"/>
            </th>
        </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</div>