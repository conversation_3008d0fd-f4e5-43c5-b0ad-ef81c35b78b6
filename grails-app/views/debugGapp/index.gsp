<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title>DebugGappController</title>
</head>
<body>
    <div class=" border p-4 m-4">
        <h1 class="w-100 bd-bottom-grey">
            DebugGappController
        </h1>
        <div class="mb-3">
            <a class="btn btn-primary mt-2" href="<g:createLink absolute="true" action="clearCredential" />">clearCredential</a>
        </div>
        <div class="mb-3">
            <form action="<g:createLink absolute="true" action="getCredential" method="post" />">
                <input size="40" name="email" value="${emailConfig}"/>
                <button type="submit" class="btn btn-primary">getCredential</button>
            </form>
        </div>
        <div class="mb-3">
            <form action="<g:createLink absolute="true" action="getMessageBody" />">
                <input size="40" name="email" value="${emailConfig}"/>
                <input type="text" name="id" value="164af96d9c11ce57"/>
                <button type="submit" class="btn btn-primary" >getMessageBody</button>
            </form>
        </div>
        <form class="mb-3" action="<g:createLink absolute="true" action="syncMail" method="post" />">
            <input size="40" name="email" value="${emailConfig}"/>
            <input name="numMessage" value="10"/>
            <button type="submit" class="btn btn-primary">Sync Emails</button>
        </form>

        <div class="mb-3">
            <form action="<g:createLink absolute="true" action="syncOneMail" />">
                <input size="40" name="email" value="${emailConfig}"/>
                <input type="text" name="id" value="168dddf26d2f6c53"/>
                <button type="submit" class="btn btn-primary">syncOneMail</button>
            </form>
        </div>

        <div class="mb-3">
            <form action="<g:createLink absolute="true" action="syncManyMail" />" method="post">
                <input size="40" name="email" value="${emailConfig}"/>
                <textarea name="ids" rows="4" cols="50"></textarea>
                <button type="submit" class="btn btn-primary" >syncManyMail</button>
                <p class="f-10">This will sync many email, enter one extid per line.</p>
            </form>
        </div>
        <form class="mb-3" action="<g:createLink absolute="true" action="syncEmailHistory" method="post" />">
            <input size="40" name="email" value="${emailConfig}"/>
            HistID:<input name="histID" value=""/>
            <button type="submit" class="btn btn-primary" >Sync Emails</button>
        </form>
        <form class="mb-3" action="<g:createLink absolute="true" action="watch" method="post" />">
            <input size="40" name="email" value="${emailConfig}"/>
            <button type="submit" class="btn btn-primary" >Watch Emails</button>
        </form>
        <form class="mb-3" action="<g:createLink absolute="true" action="watchJob" method="post" />">
            <button type="submit" class="btn btn-primary">Launch Watch Emails Job</button>
        </form>
        <div class="mb-3">
            Debug data:
            <textarea class="form-control" rows="10">${debugData.encodeAsRaw()}</textarea>
        </div>
    </div>
</body>
</html>
