<g:set var="board" value="${traction.workflow.WorkflowBoard.get(board.id)}"/>
<g:set var="min" value="${282 * board.width}"/>
<g:set var="max" value="${282 * board.width}"/>
<g:set var="height" value="${190 * board.height}"/>
<div id="div-board-${board.id}">
    <div class="cardBoard border-left border-white" id="minBoard-${board.id}"
         style="width: 70px;display: ${isMinimized ? 'block' : 'none'};">
        <div class="w-100 d-md-flex bg-${actions.any { it.type.involveClient } ? 'warning' : 'grey'}">
            <a class="material-icons" id="max-${board.id}" href="#!" data-toggle="tooltip"
               title="<g:message code="maximize"/>" style=""
               onClick="Workflow.maximize(${board.id});">zoom_in</a>
            <span>(${workflowDatasCount})</span>
        </div>

        <div class="container overflow-hidden">
            <div class="rotated-text"><span class="rotated-text__inner mt-3"><h4>${board.name}</h4></span></div>
        </div>
    </div>

    <div class="cardBoard" id="board-${board.id}"
         style="min-width: ${min}px; max-width: ${max}px;min-height: ${height}px;display: ${isMinimized ? 'none' : 'block'};">
        <!--<gif test="{!isMinimized}">-->
        <div>
            <div style="border-left: 4px solid #212529 !important; top: 157px; z-index: 1;"
                 class="w-100 d-md-flex bg-${actions.any { it.type.involveClient } ? 'warning' : 'body-secondary'} position-sticky border">
                <a class="material-icons align-content-center" id="min-${board.id}" href="#!" data-toggle="tooltip"
                   title="<g:message code="minimize"/>"
                   onclick="Workflow.minimize(${board.id});">zoom_out</a>

                <div class="dropdown align-content-center">
                    <g:set var="subscription" value="${board.subscriptions.find { it.user == user }}"/>
                    <span data-bs-toggle="dropdown">
                        <i class="${subscription ? subscription.type.icon : 'mdi mdi-email'} me-1"
                           data-toggle="tooltip"
                           title="<g:message
                                   code="${subscription ? subscription.type.message : 'workflowboardsubscription.type.none'}"/>"></i>
                    </span>

                    <div class="dropdown-menu">
                        <span class="dropdown-item cursor" onclick="Workflow.setSubscription(${board.id}, null, this);">
                            <i class="mdi mdi-email me-1"></i>
                            <g:message code="workflowboardsubscription.type.none"/>
                        </span>
                        <g:each var="t" in="${subscriptionTypes}">
                            <span class="dropdown-item cursor"
                                  onclick="Workflow.setSubscription(${board.id}, ${t.id}, this);">
                                <i class="${t.icon} me-1"></i>
                                <g:message code="${t.message}"/>
                            </span>
                        </g:each>
                    </div>
                </div>

                <g:if test="${actions}">
                    <span class="mdi mdi-cogs ms-2 d-flex align-items-center" data-toggle="tooltip" title="<g:message
                            code="actions"/>: ${actions.collect { it.name }.join(", ")}">(${actions.size()})</span>
                </g:if>
                <div class="m-auto d-flex">
                    <g:if test="${board?.description}">
                        <i class="mdi mdi-information cursor" data-toggle="tooltip"
                           title="<g:message code="workflowboard.description.click"/>" onclick="TractionModal.show({
                            url: '/workflow/descriptionModal',
                            data: {workflowBoardId: ${board?.id}}
                        });"></i>
                    </g:if>
                    <permission:ifHasNotPermissions permissions="[traction.permissions.Permission.PERM_WORKFLOW_WRITE]">
                        ${board?.name} (${workflowDatasCount})
                    </permission:ifHasNotPermissions>
                    <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_WORKFLOW_WRITE]">
                        <a href="#!" class="d-flex ${board?.description ? 'ms-2' : ''}"
                           onclick="showModal('editBoard', null, ${board.ordre}, ${board.id}, ${board.workflowMaster.id});">
                            <span class="text-truncate" data-toggle="tooltip" title="${board?.name}" style="${max < 283 ? 'max-width: 100px' : ''};">${board?.name}</span> (${workflowDatasCount})
                        </a>
                    </permission:ifHasPermissions>
                </div>
                <a href="#!" onclick="Workflow.setSelectedBoardCards(${board.id}, false)" data-toggle="tooltip"
                   title="<g:message code="datatable.selectNone"/>"
                   class="mdi mdi-checkbox-blank-outline float-right"></a>
                <a href="#!" onclick="Workflow.setSelectedBoardCards(${board.id}, true)" data-toggle="tooltip"
                   title="<g:message code="datatable.selectAll"/>"
                   class="mdi mdi-checkbox-marked-outline float-right"></a>
            </div>

            <div class="d-md-flex flex-wrap" data-board-id="${board.id}">
                <g:each var="data" in="${workflowDatas}" status="i">
                    <span class="span-card"
                          onclick="Workflow.showEdit(${data.opportunityId}, ${board.id}, ${data.clientId}, ${i + 1})">
                        <div class="span-card-hover"></div>
                        ${raw(data.html)}
                    </span>
                </g:each>
                <span class="span-card" onclick="Workflow.showEdit(null, ${board.id}, null, 'last', true)">
                    <div class="span-card-hover"></div>

                    <div class="card-workflow d-block" style="border: none;"></div>
                </span>
            </div>
        </div>

        <div style="min-width: ${min}px;" class="d-md-flex flex-wrap">
            <g:each var="b" in="${childBoards}">
                <g:render template="board"
                          model="${b}"/>
            </g:each>
        </div>
        <!--</gif>-->
    </div>
</div>