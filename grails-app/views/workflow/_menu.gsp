<search:userInit/>

<div id="workflow-menu" class="bg-body px-4 pt-4">
    <div class="d-flex flex-column gap-2">
        <div class="d-flex flex-row">
            <div class="dropend me-4">
                <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton1"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa-solid fa-calendar-day text-warning"></i> ${masterSelect?.name}
                </button>

                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
                    <g:each var="m" in="${masters}">
                        <a class="dropdown-item"
                           href="<g:createLink absolute="true" action="index" id="${m.id}"/>">${m.name}</a>
                    </g:each>
                </div>
            </div>
            <div class="card bg-body-tertiary d-flex flex-row p-2 pb-0 gap-2" data-toggle="tooltip" title="<g:message code="cards.matching.filter"/>">
                <i class="fa-regular fa-credit-card-front text-primary pt-1"></i>
                <span id="result-count">---</span>
                <span><g:message code="cards.cards" /></span>

            </div>
            <div class="ms-auto d-flex flex-row gap-2">
                <a class="btn btn-outline-primary" href="<g:createLink absolute="true" controller="workflowCalendar" action="index"
                                       id="${masterSelect?.id}"/>"
                   data-toggle="tooltip" title="<g:message code="workflow.menu.calendar"/>">
                    <i class="fa-solid fa-calendar-range text-primary"></i>
                </a>

                <a class="btn btn-outline-primary" href="<g:createLink absolute="true" controller="workflowData" action="list"
                                       id="${masterSelect?.id}"/>"
                   data-toggle="tooltip" title="<g:message code="workflowdata.list"/>">
                    <i class="fa-solid fa-list text-primary"></i>
                </a>
                <div>
                    <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_WORKFLOW_WRITE]">
                        <g:set var="disabled" value=""/>
                        <g:set var="modal" value="modal"/>
                        <g:if test="${!masterSelect}">
                            <g:set var="disabled" value="disabled"/>
                            <g:set var="modal" value=""/>
                        </g:if>
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" id="dropdownMenuButton" data-bs-toggle="dropdown"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                <g:message code="actions" />
                            </button>

                            <div class="dropdown-menu dropdown-menu-end">
                                <a href="#!" class="dropdown-item ${disabled}"
                                   onclick="showModal('editBoard', null, null, null, ${masterSelect?.id ? masterSelect.id : "0"});">
                                    <g:message code="workflow.addBoard"/>
                                </a>

                                <div class="dropdown-divider"></div>
                                <a href="#!" class="dropdown-item"
                                   onclick="showModal('addMaster', null, null, null, null);">
                                    <g:message code="workflow.addWorkflow"/>
                                </a>
                                <a href="#!" class="dropdown-item ${disabled}"
                                   onclick="showModal('editWorkflow', null, null, null, ${masterSelect?.id ? masterSelect.id : "0"});">
                                    <g:message code="workflow.editWorkflow"/>
                                </a>
                                <a href="#!" class="dropdown-item ${disabled}"
                                   onclick="showModal('archiveWorkflow', null, null, null, ${masterSelect?.id ? masterSelect.id : "0"});">
                                    <g:message code="workflow.archiveWorkflow"/>
                                </a>
                                <a href="#!" class="dropdown-item ${disabled}"
                                   onclick="showModal('duplicateWorkflow', null, null, null, ${masterSelect?.id ? masterSelect.id : "0"});">
                                    <g:message code="workflow.duplicateWorkflow"/>
                                </a>
                                <a href="#!" class="dropdown-item ${disabled}"
                                   onclick="showModal('deleteWorkflow', null, null, null, ${masterSelect?.id ? masterSelect.id : "0"});">
                                    <g:message code="workflow.deleteWorkflow"/>
                                </a>
                            </div>
                        </div>
                    </permission:ifHasPermissions>
                </div>
            </div>
        </div>

        <div class="d-flex flex-row">
            <div class="d-flex flex-row gap-2 col-4">
                <button id="openFilterBtn"
                        onclick="Workflow.openFilter()"
                        class="btn btn-outline-primary"
                        data-toggle="tooltip"
                        title="<g:message code="filtermenu.expand"/>">
                    <i class="fa-regular fa-filter-list"></i>
                </button>
                <button onclick="Workflow.applyFilter()"
                        class="btn btn-sm btn-primary d-inline-flex align-items-center"
                        data-toggle="tooltip"
                        title="<g:message code="filtermenu.apply"/>">
                    <i class="fa-solid fa-filter me-2"></i>
                    <g:message code="apply"/>
                </button>
                <button onclick="Workflow.resetFilters()" style="position: relative;" class="btn btn-sm btn-outline-primary d-inline-flex align-items-center"
                        data-toggle="tooltip" title="<g:message code="filtermenu.reset"/>">
                    <span id="filter-count" class="badge bg-primary"
                          style="position: absolute;top: -8px;right: -9px;pointer-events: none;"></span>
                    <i class="fa-solid fa-arrows-rotate me-2" data-toggle="tooltip" title="<g:message code="datatable.resetState"/>"></i>
                    <g:message code="datatable.resetState"/>
                </button>
                <div class="typeahead__container" id="clientDiv">
                    <div class="input-group typeahead__field">
                        <span class="typeahead__query">
                            <input type="search" class="form-control js-typeahead" id="searchCard" oninput="toggleMagnifyingGlass(this)" autocomplete="off" placeholder="<g:message code="workflow.menu.find.opportunity"/>">
                            <i id="magnifyingGlass" class="fa-solid fa-magnifying-glass text-primary position-absolute" style="right: 1rem; top: 50%; transform: translateY(-50%);"></i>
                        </span>
                    </div>
                </div>
            </div>
            <div class="d-flex flex-row ms-auto gap-2">
                <label class="pt-1"><g:message code="sort.card.by"/>:</label>

                <div class="float-right">
                    <select class="form-select float-right" id="board-sort">
                        <option value="ordre"><g:message code="workflowdata.order"/></option>
                        <option value="dateParts"><g:message code="workflowdata.dateParts"/></option>
                        <option value="dateStart"><g:message code="workflowdata.dateStart"/></option>
                        <option value="datePreparation"><g:message code="workflowdata.datePreparation"/></option>
                        <option value="dateEnd"><g:message code="workflowdata.dateEnd"/></option>
                    </select>
                </div>

                <div class="float-right">
                    <select class="form-select float-right" id="board-order">
                        <option value="asc"><g:message code="order.asc"/></option>
                        <option value="desc"><g:message code="order.desc"/></option>
                    </select>
                </div>

                <button id="toggle-scale" onclick="Workflow.toggleScale()"
                        class="btn btn-outline-primary mdi mdi-magnify-minus me-1" data-toggle="tooltip"
                        title="<g:message code="workflow.zoom"/>">
                </button>
            </div>

        </div>
    </div>

    <div class="d-flex mt-1 justify-content-between">

        <div class="float-right w-50 text-end">



        </div>
    </div>

    <div class="row open-multi-select" id="selects-div" style="display: none;">
        <div class="w-100 bd-bottom-grey m-3"></div>

        <div class="col-3">
            <h5 class="bd-bottom-grey"><g:message code="user"/></h5>
            <container:userSelect2 id="select-users"/>

            <h5 class="bd-bottom-grey"><g:message code="workflowdata.typeOfCard"/></h5>
            <select id="select-typeOfCard" multiple="multiple">
                <g:each var="c" in="${cardTypes}">
                    <option data-color="${c.color}" value="${c.id}">${c.title}</option>
                </g:each>
            </select>
            <h5 class="bd-bottom-grey"><g:message code="opportunity.labels"/></h5>
            <select id="select-labels" multiple="multiple">
                <g:each var="l" in="${labels}">
                    <option data-color="${l.color}" value="${l.id}">${l.name}</option>
                </g:each>
            </select>
            <g:each var="i" in="${icons}">
                <h5 class="bd-bottom-grey"><g:message code="workflowdata.${i.property}"/></h5>
                <select class="select-class-icon" id="select-${i.property}" multiple="multiple">
                    <g:each var="c" in="${i.icons}">
                        <option data-html="${c.html()}" value="${c.id}">${c.title}</option>
                    </g:each>
                </select>
            </g:each>
        </div>

        <div class="col-3">
            <h5 class="bd-bottom-grey"><g:message code="workflowdata"/> <i class="mdi mdi-calendar"></i></h5>
            <div>
                <g:message code="workflowdata.date"/>: <i class="mdi mdi-calendar"></i>
            </div>
            <input id="dataDate" class="date-filter form-control w-100">
            <div>
                <g:message code="workflowdata.dateParts"/>: <i class="mdi mdi-calendar text-primary"></i>
            </div>
            <input id="dateParts" class="date-filter form-control w-100">
            <div>
                <g:message code="workflowdata.dateStart"/>: <i class="mdi mdi-calendar text-success"></i>
            </div>
            <input id="dateStart" class="date-filter form-control w-100">
            <div>
                <g:message code="workflowdata.datePreparation"/>: <i class="mdi mdi-calendar text-secondary"></i>
            </div>
            <input id="datePreparation" class="date-filter form-control w-100">
            <div>
                <g:message code="workflowdata.dateEnd"/>: <i class="mdi mdi-calendar text-danger"></i>
            </div>
            <input id="dateEnd" class="date-filter form-control w-100">
        </div>

        <div class="col-3">
            <h5 class="bd-bottom-grey"><g:message code="opportunity"/> <i class="mdi mdi-calendar"></i></h5>
            <div>
                <g:message code="opportunity.date"/>: <i class="mdi mdi-calendar"></i>
            </div>
            <input id="date" class="date-filter form-control w-100">
            <div>
                <g:message code="opportunity.dateMOD"/>: <i class="mdi mdi-calendar"></i>
            </div>
            <input id="dateMOD" class="date-filter form-control w-100">
            <div>
                <g:message code="opportunity.dateSOLD"/>: <i class="mdi mdi-calendar"></i>
            </div>
            <input id="dateSOLD" class="date-filter form-control w-100">
            <div>
                <g:message code="opportunity.actDate"/>: <i class="mdi mdi-calendar"></i>
            </div>
            <input id="actDate" class="date-filter form-control w-100">
        </div>

        <div class="col-3">
            <h5 class="bd-bottom-grey"><g:message code="search"/></h5>
            <input id="search" placeholder="<g:message code="search"/>" class="form-control"/>

            <h5 class="bd-bottom-grey"><g:message code="show.filtered.opportunities"/></h5>
            <select id="select-display">
                <option value="opacity"><g:message code="yes"/></option>
                <option value="display"><g:message code="no"/></option>
            </select>

            <h5 class="bd-bottom-grey"><g:message code="workflowcard.elements.to.hide"/></h5>

            <div id="select-hide-div">
                <select id="select-hide" multiple="true">
                    <option value=".opp-label"><g:message code="opportunity.labels"/></option>
                    <option value=".sales2"><g:message code="opportunity.userSales2"/></option>
                    <option value=".o-n"><g:message code="opportunity.name"/></option>
                    <option value=".o-vi"><g:message code="opportunity.vehiculeIdentification"/></option>
                    <option value=".c-n"><g:message code="form.client.name"/></option>
                    <option value=".c-icons"><g:message code="card.client.icons"/></option>
                    <option value=".c-w"><g:message code="client.workOrders"/></option>
                    <option value=".c-i"><g:message code="card.skuline"/></option>
                    <option value=".o-i"><g:message code="opportunity.otherInfo"/></option>
                    <option value=".d.start"><g:message code="workflowdata.dateStart"/></option>
                    <option value=".d.end"><g:message code="workflowdata.dateEnd"/></option>
                    <option value=".d.parts"><g:message code="workflowdata.dateParts"/></option>
                    <option value=".d.task"><g:message code="next.task.todo"/></option>
                    <option value=".w-a"><g:message code="workflowdata.assigned"/></option>
                    <option value=".c-b"><g:message code="client.balance"/></option>
                    <option value=".w-service"><g:message code="workflowdata.classOfService"/></option>
                    <option value=".w-sale"><g:message code="workflowdata.classOfSale"/></option>
                    <option value=".w-parts"><g:message code="workflowdata.classOfParts"/></option>
                    <option value=".w-shipping"><g:message code="workflowdata.classOfShipping"/></option>
                    <option value=".u-s"><g:message code="opportunity.userSales"/></option>
                    <option value=".o-a"><g:message code="card.lastactivityicon"/></option>
                    <option value=".w-p"><g:message code="workflowdata.priority"/></option>
                    <option value=".w-t"><g:message code="workflowdata.timers"/></option>
                    <option value=".o-s"><g:message code="opportunity.status.icon"/></option>
                    <option value=".o-o"><g:message code="opportunity.origin"/></option>
                    <option value=".u-fni"><g:message code="opportunity.userFni"/></option>
                    <option value=".u-ser"><g:message code="opportunity.userService"/></option>
                    <option value=".u-par"><g:message code="opportunity.userParts"/></option>
                    <option value=".u-shi"><g:message code="opportunity.userShipping"/></option>
                    <option value=".u-sho"><g:message code="opportunity.userShop"/></option>
                    <option value=".u-tec"><g:message code="opportunity.userTech"/></option>
                </select>
            </div>

            <h5 class="bd-bottom-grey"><g:message code="focus.enable"/></h5>
            <select id="select-focus">
                <option value="yes"><g:message code="yes"/></option>
                <option value="no"><g:message code="no"/></option>
            </select>
        </div>
    </div>
</div>

<script>
    $(document).ready(function (){

        window.addEventListener('scroll', () => {
            const scrollLeft = window.scrollX;
            const scrollDown = window.scrollY;

            const workflowMenu = $("#workflow-menu");

            workflowMenu.css('left', scrollLeft +  "px");
            workflowMenu.css('top', scrollDown +  "px");
        })
    });

    function toggleMagnifyingGlass(input) {
        const magnifyingGlass = document.getElementById('magnifyingGlass');
        if (input.value.length > 0) {
            magnifyingGlass.style.display = 'none';
        } else {
            magnifyingGlass.style.display = 'block';
        }
    }
</script>
