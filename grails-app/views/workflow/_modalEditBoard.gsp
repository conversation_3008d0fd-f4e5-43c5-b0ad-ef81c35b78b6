<form action="<g:createLink absolute="true" action="saveBoard"/>" method="get">
    <div class="row">
        <input type="hidden" class="form-control" name="id" value="${board?.id}">
        <input type="hidden" class="form-control" name="masterid" id="masterid" value="${board ? board.workflowMasterId : masterid}">
        <input type="hidden" class="form-control" id="parent" value="${board ? board.parent : 0}">

        <div class="col">
            <div class="mb-3">
                <label for="name"><g:message code="workflow.modal.board.name"/></label>
                <input type="text" class="form-control" id="name" name="name" value="${board?.name}">
            </div>

            <div class="row">
                <div class="mb-3 col-6">
                    <label for="width"><g:message code="workflow.modal.board.width"/></label>
                    <input type="number" class="form-control" id="width" name="width" value="${board?.width}">
                </div>

                <div class="mb-3 col-6">
                    <label for="height"><g:message code="workflow.modal.board.height"/></label>
                    <input type="number" class="form-control" id="height" name="height" value="${board?.height}">
                </div>
            </div>

            <div class="mb-3">
                <label for="modalBoardOrder"><g:message code="workflow.modal.board.order"/></label>
                <select class="form-control" id="modalBoardOrder" name="ordre">
                    <option><g:message code="none"/></option>
                </select>
            </div>
        </div>

        <div class="col">
            <div class="mb-3">
                <label for="modalBoardParent"><g:message code="workflow.modal.board.parent"/></label>
                <select class="form-control" id="modalBoardParent" name="parent" onchange="onChangeParent();">
                    <option><g:message code="none"/></option>
                </select>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <label>
            <g:message code="workflowboard.description"/>
        </label>
        <container:summernote name="description" value="${board?.description}"/>
    </div>
    <g:if test="${board}">
        <div class="mb-3">
            <script>
                dataauthorcontainer({
                    formula: 'DOCUMENTBYWORKFLOWBOARD',
                    type: 'FILE',
                    workflowBoardId: ${board.id},
                    category: 'category.files.workflowboard',
                    status: 'DETECT',
                    container: 'editworkflowboardfiles',
                    features: '[dataauthorupload][get][name][description][edit][image]'
                });
            </script>

            <div id="editworkflowboardfiles"></div>
        </div>
    </g:if>

    <div class="d-flex justify-content-between">
        <g:if test="${board}">
            <button type="button" class="btn btn-danger"
                    onclick="location.href = '<g:createLink absolute="true" action="deleteBoard" params="[masterid: board.workflowMasterId, id: board.id]"/>'">
                <g:message code="workflow.modal.board.delete"/>
            </button>
        </g:if>
        <button type="submit" class="btn btn-primary float-right">
            <g:message code="workflow.modal.board.save"/>
        </button>
    </div>
</form>
