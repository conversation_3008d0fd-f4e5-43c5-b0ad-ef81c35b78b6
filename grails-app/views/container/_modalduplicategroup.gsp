<div class="modal-header">
    <h5 class="modal-title" id="title"><g:message code="${containermodal?.titre}"/></h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" id="modaldismiss" aria-label="Close">

    </button>
</div>

<g:form url="[action:'datatableduplicategroup',controller:'container']">
    <input type="hidden" value="${containermodal.method}" name="method">
    <input type="hidden" value="${containermodal.idlist}" name="idlist">
    <input type="hidden" value="${containermodal.varlist}" name="varlist">
    <input type="hidden" value="${containermodal?.profile?.id}" name="profileid">
    <input type="hidden" value="${containermodal?.view?.id}" name="viewid">
    <input type="hidden" value="${containermodal?.multilocalid}" name="multilocalid">

    ${raw(containermodal?.finalrender)}

    <div class="d-flex flex-row-reverse">
        <div class="p-2">
            <button type="button" class="btn btn-default pull-right" data-bs-dismiss="modal"><g:message code="product.affichage.no"/></button>
        </div>
        <g:if test="${containermodal?.idlist.equalsIgnoreCase("")==false}">
            <div class="p-2">
                <button type="submit" class="btn btn-danger pull-right" onclick="showBigLoader();"><g:message code="client.duplicategroup"/></button>
            </div>
        </g:if>
    </div>
</g:form>