<asset:stylesheet src="lib/fileinput/fileinput.css"/>
<asset:javascript src="lib/fileinput/fileinput.js"/>

<script>
var uploadcontent_${containerupload?.container} = {
${containerupload.client ? "clientid: " + containerupload.client.id + "," : ""}
${containerupload.user ? "userid: " + containerupload.user.id + "," : ""}
${containerupload.oppor ? "opporid: " + containerupload.oppor.id + "," : ""}
${containerupload.doc ? "trainingDocid: " + containerupload.doc.id + "," : ""}
    formula1:"${containerupload?.formula1}",
    type1:"${containerupload?.type1}",
    status:'${containerupload?.status}',
    category:'${containerupload?.category}',
    container:'${containerupload?.container}',
    refresh:'${containerupload?.refresh}',
    features:'${containerupload?.features}',
    filter:'${containerupload?.filter}'
};
uploadInit(uploadcontent_${containerupload?.container},'<g:message code='document.addFile'/>');
</script>

<input name="uploadid_${containerupload?.container}" id="uploadid_${containerupload?.container}" type="file" multiple="true" class="file" >                
