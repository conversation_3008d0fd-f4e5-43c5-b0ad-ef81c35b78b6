<div class="modal-header">
    <h5 class="modal-title" id="title"><g:message code="${containermodal?.titre}"/></h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" id="modaldismiss" aria-label="Close">

    </button>
</div>

    ${raw(containermodal?.finalrender)}
    <div style="display:block;text-align:center;">
        <i class="fa fa-times fa-4x animated rotateIn"></i>
    </div>

    <div class="d-flex flex-row-reverse">
        <div class="p-2">
            <button type="button" class="btn btn-default pull-right" data-bs-dismiss="modal"><g:message code="product.affichage.no"/></button>
        </div>
        <g:if test="${containermodal?.idlist.equalsIgnoreCase("")==false}">
            <div class="p-2">
                <button type="submit" class="btn btn-danger pull-right" onclick="addlinksprdmain('${containermodal.method}','${containermodal.idlist}','${containermodal.varlist}','${containermodal?.profile?.id}','${containermodal?.view?.id}','${containermodal?.prdconfiguratorfilterdata?.id}');showBigLoader();" data-bs-dismiss="modal"><g:message code="productmanager.addtolinks_prdmain"/></button>
            </div>
        </g:if>
    </div>
