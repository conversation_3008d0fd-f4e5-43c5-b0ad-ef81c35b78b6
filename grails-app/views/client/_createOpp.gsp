<asset:javascript src="lib/viewBox/jquery.viewbox.min.js"/>
<script>
    $(document).ready(function () {
        new TractionVehicleDataTable('createopptable', 'create-opp-div', 'createOpp');
    });

    function createOpp(row) {
        console.log('create opp with product', row);

        var productId;
        if (row) {
            productId = row.id;
        }
        let oppName;
        if (!productId) {
            oppName = $('#searchcreateopptable').val();
        }
        console.log(productId);
        if (productId || oppName) {
            showBigLoader();
            $.post({
                url: tractionWebRoot + "/opportunity/create",
                data: {
                    'clientId': ${client.id},
                    'vehicleId': productId,
                    'opportunity_name': oppName,
                    //'potentialOpportunityId': potentialOpportunityId
                },
                success: function (data) {
                    console.log(data);
                    if (data.success) {
                        $.notify(data.message, 'success');
                        window.location.href = data.url;
                    } else {
                        $.notify(data.message, 'error');
                    }
                },
                complete: hideBigLoader
            });
        } else {
            $.notify("<g:message code="opportunity.error.name"/>", "error")
        }
    }
</script>

<div id="create-opp-div"></div>