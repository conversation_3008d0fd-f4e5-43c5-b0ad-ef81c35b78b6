<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold">
                <g:message code="client.image.modify"/>
            </h2>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
            <div class="mb-3">
                <div>
                    <g:message code="client.image.current"/>:
                </div>
                <communication:avatar client="${client}" class="communication-avatar-lg"/>
            </div>
            <div class="mb-3">
                <div>
                    <g:message code="client.image.upload"/>:
                </div>
                <input class="form-control" type="file" id="client-img-input">
            </div>

            <g:if test="${client.image}">
                <div class="mb-3">
                    <div>
                        <g:message code="client.image.delete"/>
                        (<communication:avatar client="${client}" forceInitials="true" class="communication-avatar-sm d-inline-block"/>):
                    </div>
                    <button class="btn btn-danger" onclick="deleteClientImage()"><g:message code="default.button.delete.label"/></button>
                </div>
            </g:if>
        </div>
    </div>
</div>
<script>
    function deleteClientImage() {
        $.post({
            url: tractionWebRoot + '/client/uploadImage/${client.id}',
            success: function(data) {
                if (data.success) {
                    location.reload();
                }
                else {
                    $.notify(data.message, 'error');
                }
            }
        });
    }
    $(document).ready(function () {
        var $input = $('#client-img-input');
        $input.fileinput({
            uploadUrl: tractionWebRoot + '/client/uploadImage/${client.id}',
            uploadAsync: true,
            showUpload: false,
            showRemove: false,
            showPreview: false,
            showCancel: false,
            showCaption: false,
            allowedFileTypes: ['image'],
            maxFileCount: 1
        }).on("filebatchselected", function () {
            $input.fileinput("upload");
        }).on('fileuploaded', function(event, data) {
            if (data.response.success) {
                location.reload();
            }
            else {
                $.notify(data.response.message, 'error');
            }
        });
    });
</script>
