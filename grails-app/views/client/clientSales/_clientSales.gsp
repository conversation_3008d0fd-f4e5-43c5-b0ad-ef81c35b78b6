<asset:javascript src="opportunity/opportunitiesDataTable.js" />
<asset:javascript src="cotation/cotationsDataTable.js" />
<asset:javascript src="trade/tradesDataTable.js" />
<asset:javascript src="client/clientVehiclesDataTable.js" />
<asset:javascript src="vehicle/TractionVehicleDataTable.js"/>

<style>
.slide {
    background-color: var(--bs-primary-bg-subtle) !important;
    height: 1px !important;
}

.nav-item .nav-link.active + .slide {
    background-color: var(--bs-primary) !important;
}
#ClientOpportunitiesDataTableTab_wrapper .tractionDataTableBtnContainer, #ClientCotationsDataTableTab_wrapper .tractionDataTableBtnContainer {
    margin-bottom: 16px !important;
    padding-left : 24px !important;
    padding-right : 24px !important;
}

#ClientOpportunitiesDataTableTab_wrapper div.dt-scroll-head, #ClientCotationsDataTableTab_wrapper div.dt-scroll-head,
#ClientOpportunitiesDataTableTab_wrapper div.dt-scroll, #ClientCotationsDataTableTab_wrapper div.dt-scroll {
    border-radius: 0 !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
}
#ClientOpportunitiesDataTableTab_wrapper .clearfix:has(.dt-paging.paging_full_numbers),
#ClientCotationsDataTableTab_wrapper .clearfix:has(.dt-paging.paging_full_numbers) {
    border:none !important;
}

#accordionOpportunity .accordion-button::after, #accordionCotation .accordion-button::after {
    display: none;
}
</style>

<div class="d-flex flex-row mt-1 float-left">
    <h4 class="text-uppercase mb-5"><g:message code="client.sales"/></h4>
</div>

<div id="salesTable"></div>

<script>
    function updateToggleIcon(buttonId, iconId) {
        const collapseButton = document.getElementById(buttonId);
        const toggleIcon = document.getElementById(iconId);

        if (collapseButton.classList.contains('collapsed')) {
            toggleIcon.classList.remove('fa-minus');
            toggleIcon.classList.add('fa-plus');
        } else {
            toggleIcon.classList.remove('fa-plus');
            toggleIcon.classList.add('fa-minus');
        }
    }
    $(document).ready(function () {
        window.clientOpportunityDataTable = new OpportunitiesDataTable({
            uuid: 'ClientSalesDataTableTab',
            divId: 'salesTable',
            clientId: ${clientId},
            buttons: true,
        });

        %{--window.clientCotationsDataTable = new CotationsDataTable({--}%
        %{--    uuid: 'ClientCotationsDataTableTab',--}%
        %{--    divId: 'cotations_table',--}%
        %{--    clientId: ${clientId}--}%

        %{--});--}%
    });
</script>