<div id="client-products-container" class="mb-5">
    <div class="row">
        <div class="col-auto">
            <h4 class="mb-5 text-uppercase"><g:message code="client.units"/></h4>
        </div>

        <div class="col text-end">
            <a class="btn btn-primary fs-6 ms-auto fw-bold px-3" role="button"
               href="${createLink(controller: 'vehicle', action: 'create', params: [source: 'clientproduct', client: client.id])}">
                <i class="bi fa-solid fa-circle-plus me-2"></i>
                <g:message code="client.units.add"/>
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="d-flex flex-row bd-highlight mb-3">
                <div class="bd-highlight">
                    <input id="client-vehicles-search-input" type="text" autocomplete="off"
                           class="form-control rounded-0 rounded-start w-100 tractagSearch pe-4"
                           placeholder="<g:message code="search.client.vehicle"/>" onchange="clientVehicleSearch()" oninput="toggleMagnifyingGlassSearchVehicle()">
                </div>
                <i class="fa-solid fa-magnifying-glass text-primary pe-2" id="magnifyingGlassSearchVehicle"  onclick="onClearSearchVehicle()" style="padding-top: 0.7rem; transform: translateX(-100%);"></i>
            </div>
        </div>
    </div>

    <div id="client-vehicles-data-container">
        <g:if test="${vehicles.size() == 0}"><p><g:message code="client.vehicle.empty"/></p></g:if>
        <g:else>
            <g:each var="vehicle" in="${vehicles}">
                <g:include controller="vehicle" action="renderCard" params="[id: vehicle.id]"/>
            </g:each>
        </g:else>
    </div>

    <script>

        function toggleMagnifyingGlassSearchVehicle() {
            const magnifyingGlass = $('#magnifyingGlassSearchVehicle');
            const input = $('#client-vehicles-search-input');
            if (input.val().length > 0) {
                magnifyingGlass.addClass("fa-filter-circle-xmark")
                magnifyingGlass.removeClass("fa-magnifying-glass")
            } else {
                magnifyingGlass.addClass("fa-magnifying-glass")
                magnifyingGlass.removeClass("fa-filter-circle-xmark")
            }
        }

        function onClearSearchVehicle() {
            const input = $('#client-vehicles-search-input');
            input.val('');
            toggleMagnifyingGlassSearchVehicle()
            clientVehicleSearch()
        }

        function clientVehicleSearch() {
            showLogoLoader()
            $.get({
                url: tractionWebRoot + '/client/getClientVehiclesData',
                data: {
                    "clientId": ClientIndex.id,
                    "search": $('#client-vehicles-search-input').val()
                },
                complete: hideLogoLoader,
                success: function (html) {
                    if (html) {
                        $('#client-vehicles-data-container').html(html)
                    }
                }
            });
        }
    </script>
</div>
