<%@ page import="traction.client.Client" %>
<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title>${client ? client.toString() : g.message(code: 'dashboard.newclient')} | <g:message code="client.view"/></title>
    <asset:stylesheet src="/client/index.css"/>
    <asset:javascript src="communication/CommunicationInbox.js"/>
    <asset:stylesheet src="communication/CommunicationInbox.css"/>
    <asset:javascript src="communication/CommunicationBox.js"/>
    <asset:stylesheet src="communication/CommunicationBox.css"/>
    <asset:javascript src="attachments/attachments.js"/>
    <asset:javascript src="/cotation/show/CotationIndex.js"/>
    <script>
        $(function () {
            ClientIndex.init(${client?.id ?: 'null'}, '${isEnterprise}');
        });
    </script>
</head>

<body>

<div class="container-fluid p-0 m-0 pt-4">
    <div class="row pb-0 align-items-center g-3 mx-0">
        <div class="d-flex justify-content-between">

        </div>
        <div class="col-auto ps-4">
            <g:if test="${client}">
                <div class="visible-hover-container" id="avatar-edit"
                     onclick="TractionModal.show({url: '/client/modalImage/${client.id}'});">
                    <communication:avatar client="${client}" class="communication-avatar-lg"/>
                    <i class="fa fa-pen visible-hover"></i>
                </div>
            </g:if>
        </div>
        <div class="col-auto align-middle d-flex flex-column flex-grow-1 pe-4">
            <div class="d-flex justify-content-between">
                <h2 class="mb-0">${client?.getFullName()}</h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-outline-primary">
                        <i class="fa fa-scale-balanced me-2"></i>
                        <format:currency number="${client?.balance}"/>
                    </button>
                    <button class="btn btn-primary" onclick="ClientIndex.router.navigate('communications');" data-navigation="communications">
                        <i class="fa fa-comments me-2"></i>
                        <g:message code="communication"/>
                    </button>
                    <button class="btn btn-accent" onclick="ClientIndex.router.navigate('tasks')">
                        <i class="fa fa-list-check me-2"></i>
                        <g:message code="all.task"/>
                    </button>
                </div>
            </div>
            <div class="d-flex flex-wrap lh-1 mt-1 gap-2 gap-lg-3">
                <div>
                    <i class="fa-light fa-hashtag text-body-secondary"></i>
                    <span>
                        &nbsp;${client?.id}
                    </span>
                </div>
                <g:if test="${client?.phone}">
                    <div class="vr"></div>
                    <div>
                        <i class="fa-light fa-phone text-body-secondary"></i>
                        <span class="d-none d-lg-inline">
                            &nbsp;${client?.getFormattedPhone()}
                        </span>
                    </div>
                </g:if>
                <g:elseif test="${client?.asIndividualClient()?.phonecell}">
                    <div class="vr"></div>
                    <div>
                        <i class="fa-light fa-phone text-body-secondary"></i>
                        <span class="d-none d-lg-inline">
                            &nbsp;${client?.asIndividualClient()?.getFormattedPhonecell()}
                        </span>
                    </div>
                </g:elseif>
                <div class="vr"></div>
                <div>
                    <i class="fa-light fa-envelope text-body-secondary"></i>
                    <span class="d-none d-lg-inline">
                        &nbsp;${client?.email}
                    </span>
                </div>
                <g:if test="${client?.address && client?.address != '' && client?.address.toString().size() > 0}">
                    <div class="vr"></div>
                    <div>
                        <a class="link-body-emphasis" target="_blank" href="https://www.google.com/maps/search/${client?.address}">
                            <i class="fa-light fa-location-dot text-body-secondary"></i>
                            <span class="d-none d-lg-inline">
                                &nbsp;${client?.address?.toString()}
                            </span>
                        </a>
                    </div>
                </g:if>
                <g:if test="${client && client.status != Client.Status.NEW}">
                    <div class="vr"></div>
                    <div>
                        <span class="badge rounded-pill bg-${client?.status?.bsClass}-subtle text-${client?.status?.bsClass} d-inline text-uppercase">
                            <g:message code="${client?.status?.message}"/>
                        </span>
                    </div>
                </g:if>
                <g:if test="${client.isEnterprise()}">
                    <div class="col-auto align-middle">
                        <span class="badge bg-warning text-dark f-12 text-body-secondary">
                            <g:message code="enterprise"/>
                        </span>
                    </div>
                </g:if>
            </div>
        </div>


        <!-- dummy column with visibility hidden just make the above column centered -->

        <ul id="client-navigation" class="nav nav-tabs full-width-underline px-3 mt-5" role="tablist">
        <g:if test="${client}">
            <li class="nav-item mt-auto">
                <button type="button" role="tab" class="nav-link active show" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('summary');" data-navigation="summary">
                    <g:message code="client.contact.sumarry"/>
                </button>
            </li>
        </g:if>
            <li class="nav-item mt-auto">
                <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('contactDetails');" data-navigation="contactDetails">
                    <g:message code="client.contact.details"/>
                </button>
            </li>
            <g:if test="${client}">
                <li class="nav-item mt-auto">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('clientVehicles');" data-navigation="clientVehicles">
                        <g:message code="client.units"/>
                    </button>
                </li>
                <li class="nav-item mt-auto">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('sales');" data-navigation="sales">
                        <g:message code="client.sales"/>
                    </button>
                </li>
                <li class="nav-item mt-auto d-none d-lg-block">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('service');" data-navigation="service">
                        <g:message code="client.service"/>
                    </button>
                </li>
                <li class="nav-item mt-auto d-none d-lg-block">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('parts');" data-navigation="parts">
                        <g:message code="client.parts"/>
                    </button>
                </li>
                <li class="nav-item mt-auto d-none">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="#">
                        <g:message code="client.delivery"/> %{--(TODO)--}%
                    </button>
                </li>
                <li class="nav-item mt-auto d-none">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="#">
                        <g:message code="client.finances"/> %{--(TODO)--}%
                    </button>
                </li>
                <li class="nav-item mt-auto d-xl-xl-block">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('comments');" data-navigation="comments">
                        <g:message code="comments"/>
                    </button>
                </li>
                <li class="nav-item mt-auto d-xl-xl-block">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="ClientIndex.router.navigate('files');" data-navigation="files">
                        <g:message code="client.documents"/>
                    </button>
                </li>
                <li class="nav-item mt-auto d-none">
                    <button type="button" role="tab" class="nav-link" data-bs-toggle="tab" onclick="#">
                        <g:message code="client.admin"/> %{--(TODO)--}%
                    </button>
                </li>
                <li class="nav-item dropdown mt-auto">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <g:message code="others"/>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('payments');"><g:message code="stripepaymentintent.list"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('callNotes');"><g:message code="main.callnotes"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('calls');"><g:message code="calls.title"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('workOrders');"><g:message code="workorder"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('formDatas');"><g:message code="form.menu.title"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('histories');"><g:message code="histories"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('actionLogs');"><g:message code="actionlog"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('communications');"><g:message code="communications"/></li>
                        <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('tasks');"><g:message code="tasks"/></li>
                        <li class="dropdown-item cursor" onclick="TractionModal.show({
                            url: '/serviceSchedule/modalGoTo',
                            bigLoader: true,
                            data: {
                                clientId: ${client?.id}
                            }
                        });"><g:message code="servicecalendar"/></li>
                        <g:if test="${client.isIndividualClient()}">
                            <li class="dropdown-item cursor" onclick="ClientIndex.router.navigate('merge');"><g:message code="client.merge"/></li>
                        </g:if>
                    </ul>
                </li>
            </g:if>
        </ul>
    </div>
</div>

<div class="container-fluid px-0">
    <div id="client-index-section-content" class="px-4 py-5">
    </div>
</div>
</body>
</html>