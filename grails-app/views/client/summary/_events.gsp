<%@ page import="traction.I18N; grails.converters.JSON" %>
<asset:stylesheet src="lib/fullcalendar/fullcalendar.css"/>
<asset:javascript src="lib/fullcalendar/fullcalendar.js"/>
<asset:javascript src="lib/moment.min.js"/>

<g:if test="${events.size() == 0}">
    <div class="d-flex justify-content-center align-items-center h-100">
        <span class="text-body-tertiary f-14"><g:message code="client.event.empty"></g:message></span>
    </div>
</g:if>
<g:each in="${events}" var="event">
    <div class="px-4 py-3 event-item"
         data-department="${event.assigned ? event.assigned.collect { it.currentDepartmentGroup }.unique().join(', ') : I18N.m('no.department.assigned')}"
         data-date="${event.date.format('yyyy-MM-dd')}">
        <div class="d-flex align-items-center justify-content-between mb-2 line-height-19">
            <div class="d-flex flex-column">
                <div class="text-emphasis text-uppercase f-16"><i class="${event.icon}"></i>
                    ${event.type}</div>

                <div class="small">${event.subType}</div>
            </div>

            <div class="d-flex align-items-center ">
                <g:if test="${event.assigned}">
                    <g:set var="uniqueUserGroups" value="${event.assigned.collect { it.userGroup }.unique()}"/>
                    <g:each in="${uniqueUserGroups}" var="userGroup">
                        <span class="border bg-body text-uppercase p-2 pb-0 pt-0 fw-bold f-12 rounded-pill line-height-19 me-2"
                              data-toggle="tooltip" data-placement="top" title="${I18N.m(userGroup)}"
                              style="color: ${userGroup.color}; border: 1px solid ${userGroup.color} !important;">
                            <i class="${userGroup.icon}"></i>
                        </span>
                    </g:each>
                </g:if>
                <a href="${event.link}"
                   onclick="${event.onclick}" ${event.link == 'javascript:void(0)' ? '' : 'target="_blank"'}>
                    <i class="fa-regular fa-arrow-up-right-from-square"></i>
                </a>
            </div>
        </div>
        <span class="text-black fw-bold f-16 mb-1 line-height-19">${event.date.format("yyyy-MM-dd")}${event.duration ? ', ' + event.duration : ''}</span>

        <div>
            <g:if test="${event.assigned}">
                <g:each in="${event.assigned}" var="assignee">
                    <div class="d-flex align-items-center flex-wrap mb-1 line-height-19">
                        <span class="text-black f-14">
                            <i class="${assignee.userGroup.icon} text-black f-14 me-2"
                               style="width: 18px; text-align: center;"></i>
                            ${assignee.currentDepartmentGroup}
                        </span>
                        <span class="text-black f-14 flex-wrap ms-1">- ${assignee.fullName} ${assignee.id}</span>
                    </div>
                </g:each>
            </g:if>
        </div>
        <span class="text-body-tertiary f-14 line-height-19 d-block text-truncate"
              id="event-description-${event.id}">
            ${event.description}
        </span>
    </div>
    <hr class="mx-3 my-0 p-0" style="border-color: #F4F5F7"/>
</g:each>

<script>
    $(document).ready(function () {
        $('.text-truncate').each(function () {
            var $element = $(this);
            if ($element[0].scrollWidth > $element[0].clientWidth) {
                var $link = $('<a>', {
                    href: 'javascript:void(0);',
                    class: 'text-blue',
                    html: '<i class="fa-solid fa-plus-circle"></i>',
                    click: function () {
                        toggleDescription($element.attr('id').replace('event-description-', ''));
                    }
                });
                $element.append($link);
            }
        });
    });

    function toggleDescription(eventId) {
        var $descriptionElement = $('#event-description-' + eventId);
        if ($descriptionElement.length === 0) {
            return;
        }

        var $linkElement = $descriptionElement.find('a');
        if ($linkElement.length === 0) {
            return;
        }

        if ($descriptionElement.hasClass('text-truncate')) {
            $descriptionElement.removeClass('text-truncate');
            $linkElement.html('<i class="fa-solid fa-minus-circle"></i>');
        } else {
            $descriptionElement.addClass('text-truncate');
            $linkElement.html('<i class="fa-solid fa-plus-circle"></i>');
        }
    }

    var selectedDepartment = 'all';

    $(document).ready(function () {
        document.querySelectorAll('.overflow-ellipsis-clic-wrap').forEach(function (element) {
            element.addEventListener('click', function () {
                element.classList.toggle('expanded');
            });
        });
        <g:each in="${departments}" var="department">
        $('#clientEvents-container #department-select').append('<option value="${department}">${department}</option>');
        </g:each>

        selectedDepartment = getCookie('selectedDepartment') || 'all';
        $('#department-select').val(selectedDepartment);

        setTimeout(function () {
            initializeCalendar();
        }, 1000);

        $('#department-select').change(function () {
            selectedDepartment = $(this).val();
            setCookie('selectedDepartment', selectedDepartment, 30);
            initializeCalendar();
        });
    });

    var lastClickedDate = null;

    function initializeCalendar() {
        var calendarEl = $('#calendar');
        <g:applyCodec encodeAs="none">
        var events = JSON.parse('${events.collect { event ->
                event.assigned.collect { assignee ->
                    [title: assignee.currentDepartmentGroup.toString(), start: event.date.format('yyyy-MM-dd'), className: assignee.userGroup.userProperty, department: assignee.currentDepartmentGroup.toString()]
                }.unique()
            }.flatten().groupBy { it.start + it.className }.collect { it.value[0] } as JSON}');
        </g:applyCodec>


        var filteredEvents = events.filter(function (event) {
            return selectedDepartment === 'all' || event.department === selectedDepartment;
        });

        calendarEl.fullCalendar('destroy');
        calendarEl.fullCalendar({
            header: {
                center: 'prev,title,next',
                left: '',
                right: ''
            },
            defaultDate: '2025-03-18',
            editable: false,
            eventLimit: 4,
            selectable: true,
            select: function (start, end, jsEvent, view) {
                $('.fc-highlight').removeClass('fc-highlight');
                $(jsEvent.target).closest('.fc-day').addClass('fc-highlight');
            },
            events: filteredEvents,
            dayClick: function (date, jsEvent) {
                if (lastClickedDate && lastClickedDate.isSame(date, 'day')) {
                    resetDateFilter();
                    lastClickedDate = null;
                    $(jsEvent.target).closest('.fc-day-top').removeClass('highlighted-event');
                } else {
                    lastClickedDate = date;
                    $('.fc-day-top').removeClass('highlighted-event');
                    $(jsEvent.target).closest('.fc-day-top').addClass('highlighted-event');
                    filterEvents();
                }
            },
            eventRender: function (event, element) {
                setTooltip(element);
            },
            eventAfterAllRender: function (view) {
                $('.fc-day').each(function () {
                    var dayEvents = $(this).find('.fc-event');
                    if (dayEvents.length > 4) {
                        dayEvents.slice(4).hide();
                        $(this).append('<div class="fc-more">+ ' + (dayEvents.length - 4) + '</div>');
                    }
                });
            }
        });
    }

    function setTooltip(element) {
        var classes = element.attr('class').split(' ');
        var lastClass = classes[classes.length - 1];
        element.attr('title', lastClass);
    }

    function toggleCalendar() {
        $('#calendar').toggle();
        $('#hr').toggle();
        var toggleText = $('#toggle-text');
        if ($('#calendar').is(':visible')) {
            toggleText.html('<g:message code="hide"></g:message>');
        } else {
            toggleText.html('<g:message code="Show"></g:message>');
        }
    }

    function filterEvents() {
        var events = document.querySelectorAll('.event-item');
        var selectedDate = lastClickedDate ? lastClickedDate.format() : null;
        selectedDepartment = $('#department-select').val();

        events.forEach(function (event) {
            var eventDate = event.getAttribute('data-date');
            var eventDepartments = event.getAttribute('data-department').split(', ');
            if ((selectedDepartment === 'all' || eventDepartments.includes(selectedDepartment)) &&
                (!selectedDate || eventDate === selectedDate)) {
                event.style.display = 'block';
            } else {
                event.style.display = 'none';
            }
        });
    }

    function resetDateFilter() {
        lastClickedDate = null;
        filterEvents();
    }

    // Function to set a cookie
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
</script>
<style>
td.fc-day-top.fc-mon.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-mon.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-tue.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-tue.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-wed.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-wed.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-thu.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-thu.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-fri.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-fri.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-sat.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-sat.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-sun.fc-future.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}

td.fc-day-top.fc-sun.fc-past.highlighted-event {
    background-color: aliceblue; /* Highlight color */
}


.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end:hover::after {
    content: attr(title);
    position: absolute;
    background-color: #333;
    color: #fff;
    padding: 5px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 1000;
    top: -27px;
    left: -27px;
    transform: translateX(-50%);
    margin-top: 5px;
    font-size: 12px;
}

.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end {
    position: relative;
}

.fc-event {
    position: revert;
}

.fc-more-cell a {
    width: 100%;
}

#calendar {
    width: 100%;
    padding: 16px;
    margin: 0 !important;
    font-size: 10px;
}

.fc-scroller fc-day-grid-container {
    height: 75%;
}

.fc-scroller.fc-day-grid-container {
    overflow: visible !important;
}

.fc-center {
    display: flex !important;
    justify-content: center;
    align-items: center;
}

.fc-center div {
    display: flex;
    gap: 60px;
}

.fc-button {
    background-color: transparent;
    background-image: none;
    border: none;
    box-shadow: none;
}

.fc-button span {
    color: var(--bs-primary);
    font-size: 16px;
}

.fc-toolbar h2 {
    font-size: 16px !important;
    white-space: normal !important;
    width: 120px;
    font-weight: bold;
}

td.fc-day-top {
    text-align: center;
    cursor: pointer;
}

.fc-ltr .fc-basic-view .fc-day-top .fc-day-number {
    float: none;
}

th.fc-day-header {
    color: #5F7991;
    font-size: 16px;
    font-weight: bold;
}

.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row, .fc-unthemed .fc-content, .fc-unthemed .fc-popover, .fc-unthemed .fc-list-view, .fc-unthemed .fc-list-heading td {
    border-color: transparent !important;
}

.fc-toolbar {
    font-size: .9em;
}

.fc-toolbar h2 {
    font-size: 12px;
    white-space: normal !important;
}

.fc-more-cell a {
    width: 40%;
}


.fc-more-popover {
    width: 100px;
}

.fc-view-month .fc-event, .fc-view-agendaWeek .fc-event, .fc-content {
    font-size: 0;
    overflow: hidden;
    height: 2px;
}

.fc-view-agendaWeek .fc-event-vert {
    font-size: 0;
    overflow: hidden;
    width: 2px !important;
}

.fc-agenda-axis {
    width: 20px !important;
    font-size: .7em;
}

.fc-button-content {
    padding: 0;
}

.fc-event.userSales {
    background-color: #2EAE1A; /* Green for tasks */
    border: 1px solid #2EAE1A;
}

.fc-event.userFni {
    background-color: #A532FF; /* Orange for meetings */
    border: 1px solid #A532FF;
}

.fc-event.userParts {
    background-color: #FF7A00;
    border: 1px solid #FF7A00;
}

.fc-event.userService {
    background-color: var(--bs-primary);
    border: 1px solid var(--bs-primary);
}

.fc-event.userShipping {
    background-color: #EE062E;
    border: 1px solid #EE062E;
}

.fc-event.userShop {
    background-color: #FF61B6;
    border: 1px solid #FF61B6;
}

.fc-event.userTech {
    background-color: #5F7991;
    border: 1px solid #5F7991;
}

.fc-event.userBdc {
    background-color: #00C2C2;
    border: 1px solid #00C2C2;
}

.text-blue {
    position: absolute;
    right: 5px;
}
</style>