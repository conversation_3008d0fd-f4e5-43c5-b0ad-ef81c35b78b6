<%@ page import="traction.I18N" %>
<g:if test="${allTasks.size() == 0}">
    <div class="d-flex justify-content-center align-items-center h-100">
        <span class="text-body-tertiary f-14"><g:message code="client.tasktodo.empty"></g:message></span>
    </div>
</g:if>
<g:each in="${allTasks}" var="task">
    <a href="javascript:void(0);" onclick="ClientIndex.router.navigate('tasks', {taskId: ${task.id}});"
       class="d-flex align-items-center justify-content-between mb-3 line-height-19 text-decoration-none">
        <div class="d-flex align-items-center gap-2">
            <i class="${task.isLate() ? 'text-danger' : 'text-primary'} ${task.getIcon()}"></i>
            <span class="f-16">
                <g:message code="${task.userGroup ? task.userGroup : traction.I18N.m('no.group')}"/>
            </span>
        </div>
        <div class="d-flex">
            <g:each in="${task.getAssignedUsers()}" var="assignee">
            <communication:avatar user="${assignee}" class="communication-avatar-xs"/>
        </g:each>
        </div>

    </a>
</g:each>
