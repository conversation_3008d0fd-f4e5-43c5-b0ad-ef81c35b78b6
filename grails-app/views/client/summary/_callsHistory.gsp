<g:if test="${clientCallData.size() == 0}">
    <div class="d-flex justify-content-center align-items-center h-100">
        <span class="text-body-tertiary f-14"><g:message code="client.call.history.empty"></g:message></span>
    </div>
</g:if>
<g:each in="${clientCallData}" var="call">
    <div class="d-flex flex-column  p-3">
        <div class="d-flex  justify-content-between align-items-center mb-2">
            <div class="d-flex align-items-center">
                <div class="d-flex align-items-center justify-content-center text-white ${call.category.outbound ? 'bg-danger' : 'bg-succes'}  me-2"
                     style="padding: 2px 4px; border-top-left-radius: 4px; border-bottom-left-radius: 4px;">
                    <i class="${call.category.icon} f-8"></i>
                </div>
                <i class="${call.getIcon()} f-16" style="color: ${call.iconColor};"></i>
            </div>

            <div>
                <span class="text-body-tertiary">${call.date.format('dd-MM-yyyy, HH\'h\'mm')}</span>
            </div>
        </div>
        <g:set var="callUser" value="${call.getCallNoteUser()}"/>
        <g:set var="extensions" value="${callUser?.getExtensions() ?: []}"/>

        <g:if test="${callUser}">
            <div class="d-flex align-items-center justify-content-between text-uppercase mb-1">
                <span class="badge badge-outline-secondary-emphasis me-2 text-uppercase">
                    <i class="fa-solid fa-user-tie me-1"></i>${callUser?.fullName}
                <g:if test="${extensions.size() > 0}">
                    #${extensions.first().ext}
                </g:if><g:else>
                    #XXX
                </g:else>
                </span>
                <div id="cutnn" class="badge badge-outline-primary">
                    <i class="fa-solid fa-pen-field"></i>
                    <span class="ms-1">${curUserTNN}</span>
                </div>
            </div>
        </g:if>
        <container:playbox callData="${call}"/>
    </div>
    <hr class="mx-3 my-0 p-0" style="border-color: #F4F5F7 ">
</g:each>

<script>
    $('#callsTodayCount').text(${todayCallDatas});
</script>
