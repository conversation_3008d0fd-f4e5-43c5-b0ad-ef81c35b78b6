<g:if test="${callNotes.size() == 0}">
    <div class="d-flex justify-content-center align-items-center h-100">
        <span class="text-body-tertiary f-14"><g:message code="client.no.call.notes"></g:message></span>
    </div>
</g:if>
<g:each in="${callNotes}" var="note">
    <a href="javascript:void(0);" onclick="ClientIndex.router.navigate('callNotes', {callNotesIds: ${note.id}});"
       class="d-flex align-items-center justify-content-between flex-wrap mb-3 line-height-19 text-decoration-none">
        <g:if test="${note.departmentGroup}">
            <span>
                <g:message code="${note.departmentGroup}"></g:message>
            </span>
        </g:if>
        <g:else>
            <span>
                <g:message code="no.group"></g:message>
            </span>
        </g:else>

        <div class="d-flex align-items-center justify-content-between">
            <g:if test="${note.user}">
                <communication:avatar user="${note.user}" class="communication-avatar-xs"/>
            </g:if>
            <span class="border border-danger bg-body text-uppercase text-danger p-2 pb-0 pt-0 fw-bold f-10 rounded-pill h-19">
                ${traction.DateUtils.getReallyShortDurationString(note.date)}
            </span>
        </div>
    </a>
</g:each>
