<asset:javascript src="communication/CommunicationBox.js"/>
<asset:stylesheet src="communication/CommunicationBox.css"/>

<div class="modal-dialog modal-dialog-scrollable modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="title"><g:message code="filtermenu.communication"/></h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
            </button>
        </div>
        <div id="communication-bulk-sender" class="modal-body">
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        CommunicationBox.initBulk('communication-bulk-sender', ${oppIds}, ${clientIds}, function() {
            TractionModal.hide();
        })
    });
</script>


