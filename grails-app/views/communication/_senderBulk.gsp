<%@ page import="traction.I18N" %>

<div class="border rounded-top p-4">
    <div class="w-100 clearfix f-16">

        <div class="row mb-2 align-items-center">
            <div class="col-2">
                <label><g:message code="use.mail.template"/></label>
            </div>

            <div class="col-10">
                <g:select name="template" id="template"
                          from="${currentUser.getActiveTemplates().sort { it.title.toLowerCase() }}"
                          optionValue="title" optionKey="id"
                          noSelection="${["0": g.message(code: 'communication.sender.template.placeholder')]}"
                          onchange="CommunicationBox.setTemplate(this.value, ${method == "MailMessage"});"
                          value="0" class="form-select bg-body"/>
            </div>
        </div>

        <g:if test="${method == "SmsMessage"}">
            <div class="row mb-2 align-items-center">
                <div class="col-2">
                    <label><g:message code="twilionumber"/></label>
                </div>

                <div class="col-10">
                    <select id="twilioNumber" class="form-select bg-body"
                            onchange="CommunicationBox.saveInputs()" ${twilioNumbers.size() == 1 ? 'disabled' : ''}>
                        <g:each var="num" in="${twilioNumbers}">
                            <option value="${num.number}">${num.number.replaceFirst("(\\d{1})(\\d{3})(\\d{3})(\\d+)", "\$1 (\$2) \$3-\$4")} - ${num.getDescription()}</option>
                        </g:each>
                    </select>
                </div>
            </div>
        </g:if>
        <g:elseif test="${method == "MailMessage"}">
            <div class="row mb-2 align-items-center">
                <div class="col-2">
                    <label><g:message code="client.email.from"/></label>
                </div>

                <div class="col-10">
                    <select name="from" id="from" class="form-select bg-body"
                            onchange="CommunicationBox.saveInputs()" ${fromList.size() == 1 ? 'disabled' : ''}>
                        <g:each var="f" in="${fromList}">
                            <option value="${f}" ${f == from ? 'selected' : ''}>${f}</option>
                        </g:each>
                    </select>
                </div>
            </div>
        </g:elseif>

        <div class="row mb-2 align-items-center">
            <div class="col-2">
                <label><g:message code="product.search"/></label>
            </div>

            <div class="col-10">
                <search:vehicleInit/>
                <search:vehicle id="communication-box-product-search" type="text" class="w-100"
                                callback="CommunicationBox.addProductDisplayUrl"
                                placeholder="${traction.I18N.m('product.search.txt')}"/>
            </div>
        </div>

        <g:if test="${method == "MailMessage"}">

            <div class="row mb-2 align-items-center">
                <div class="col-2">
                    <label><g:message code="mail.subject"/></label>
                </div>

                <div class="col-10">
                    <input type="text" autocomplete="off" id="subject" class="form-control"
                           placeholder="<g:message code="mail.subject"/>" value="${subject}"
                           onchange="CommunicationBox.saveInputs()"/>
                </div>
            </div>
        </g:if>


        <script>
            $(document).ready(function () {
                CommunicationBox.updateAttachedFiles();
            });
        </script>
    </div>

    <div class="mb-3">
        <g:if test="${method == "MailMessage"}">
            <div class="form-check  text-center w-100 cursor d-flex align-items-center my-2"
                 onclick="resizeIframe(document.querySelector('#signature-preview iframe'))"
                 data-bs-toggle="collapse" data-bs-target="#signature-preview">
                <input class="form-check-input me-2 border-primary" type="checkbox" id="toggle-signature">
                <label class="form-check-label text-secondary cursor" for="toggle-signature">
                    <g:message code="preview.user.signature"/>
                </label>
            </div>

            <script>
                $(function () {
                    const checkbox = document.getElementById('toggle-signature');
                    const label = document.querySelector('label[for="toggle-signature"]');

                    function updateLabelColor() {
                        if (checkbox.checked) {
                            label.classList.remove('text-secondary');
                            label.classList.add('text-primary');
                        } else {
                            label.classList.remove('text-primary');
                            label.classList.add('text-secondary');
                        }
                    }

                    checkbox.addEventListener('change', updateLabelColor);

                    updateLabelColor();
                });
            </script>
        </g:if>
        <div id="signature-preview" class="collapse">
            <iframe style="width: 100%; height: 0px;" frameborder="0" scrolling="no"
                    srcdoc="${signature}"></iframe>
        </div>
    </div>

    <div id="communication-files"></div>
</div>

<div class="d-flex align-items-end p-3 gap-4 shadow-sm position-relative bg-emphasis" id="communication-sender-container">
    <div class="h-39 d-flex align-items-center"><g:message code="communication.sender.mode"/></div>

    <div>
        <select id="communication-method" class="form-select"
                onchange="CommunicationBox.setSenderMethod($(this).val(), true)">
            <g:each var="m" in="${possibleMethods}">
                <option value="${m}" ${method == m ? "selected" : ""}><g:message
                        code="traction.communication.${m}"/></option>
            </g:each>
        </select>
    </div>

    <div style="border-left:1px solid var(--bs-secondary);height:35px;"></div>

    <div class="">
        <div class="dropdown h-39 d-flex align-items-center">
            <button class="far fa-paperclip-vertical cursor btn btn-link fs-4 p-0  icon-file" type="button"
                    data-bs-toggle="dropdown"
                    data-toggle="tooltip" title="<g:message code="communication.sender.join.files"/>"
                    aria-haspopup="true" aria-expanded="false"></button>

            <div id="attachmentsChoice" class="dropdown-menu">
                <a href="javascript:void(0);" class="dropdown-item"
                   onclick="$('#communication-files-input').click()"><g:message
                        code="client.attachments.device"/></a>
                <a href="javascript:void(0);" class="dropdown-item" onclick="TractionModal.show({
                    url: '/communication/getClientAttachmentsModal',
                    data: {clientId: ${client?.id}}
                });"><g:message code="client.attachments.attachment"/></a>
            </div>
        </div>
        <input class="d-none" id="communication-files-input" type="file" multiple="true"
               onchange="CommunicationBox.uploadFiles()">
    </div>


    <div class="flex-grow-1 position-relative" id="sender-div">
        <div class="d-flex">
            <container:summernote id="communication-text" onInit="CommunicationBox.updateSelectedFacebookPage()"
                                  placeholder="${I18N.m('comment.text.placeholder')}"
                                  onchange="CommunicationBox.saveInputs()"
                                  noHtml="${method != "MailMessage"}"/>
            <i class="fas fa-circle-plus text-primary btn btn-link fs-5 p-0 cursor position-absolute"
               style="right:8px; bottom:9px;"
               data-toggle="tooltip"
               id="expand-icon"
               title="<g:message code="filtermenu.expand"/>"
               onclick="CommunicationBox.toggleLargeEditor();"></i>
        </div>
    </div>
    <button class="btn btn-link-primary outline border-0 d-flex align-items-center communication-send-btn fs-4 text-light cursor"
            disabled
            onclick="CommunicationBox.send('bulk')">
        <i class="fa fa-send" data-toggle="tooltip" title="<g:message code="send"/>"></i>
    </button>
</div>
