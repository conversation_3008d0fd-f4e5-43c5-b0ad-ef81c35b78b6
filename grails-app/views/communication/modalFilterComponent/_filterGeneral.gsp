<%@ page import="traction.I18N" %>
<div id="generalFilterTemplate" class="w-100">
    <div class="d-flex flex-row w-100 align-items-center mb-3 " role="button" onclick="handleGeneralCollapse()">
        <i id="headingGeneral" class="fa-regular fa-minus text-emphasis me-2"></i>
        <h5 class="text-uppercase">
            <g:message code="communication.modal.conversation.filter.general"/>
        </h5>
    </div>

    <div id="generalCollapse" class="collapse show" data-parent="#generalFilterTemplate">
        <div class="d-flex flex-row">
            <label class=" text-info-emphasis mb-2 fw-bold">
                <g:message code="communication.modal.search.key.word"/>
            </label>
            <label class="ms-auto text-primary text-decoration-underline" id="button-key-removeAll" role="button"
                   onclick="removeAllBadges()">
                <g:message code="communication.modal.remove.all"/>
            </label>
        </div>

        <div class="d-flex flex-row mb-2">
            <input id="conversation-filter-keywords" class="form-control form-control-md w-100" type="text"
                   placeholder="${I18N.m('communication.modal.search.key.word')}"/>
            <button class="btn border-0" onclick="addKeyWordAndRefresh()">
                <i class="fa-solid fa-plus text-primary"></i>
            </button>
        </div>

        <div id="key-words-container" class="d-flex flex-row mb-3 d-none">

        </div>

        <div class="d-flex flex-column  mb-4">
            <label class="text-info-emphasis mb-2 fw-bold">
                <g:message code="communication.modal.select.time.period"/>
            </label>
            <select id="conversation-filter-timePeriod" class="form-select  bg-body">
                <option value="all"><g:message
                        code="communication.modal.select.time.period.from.the.begining"/></option>
                <option value="7days"><g:message code="communication.modal.select.time.period.7days"/></option>
                <option value="30days"><g:message code="communication.modal.select.time.period.30days"/></option>
                <option value="90days"><g:message code="communication.modal.select.time.period.90days"/></option>

            </select>
        </div>


        <div class="d-flex flex-column mb-4">
            <label class=" text-info-emphasis mb-2 fw-bold">
                <g:message code="communication.modal.select.visible.message.state"/>
            </label>

            <div class="d-flex flex-row">
                <span
                        class="badge rounded-pill border border-primary text-primary me-2 p-2 w-25 msg-state-btn f-16 shadow-sm"
                        data-value="all"
                        role="button"
                        onclick="handleMessageStateBtn(this, true)"
                        style="background-color: #F2F9FE">
                    <g:message code="communication.modal.conversation.messages.status.all"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-state-btn f-16 shadow-sm"
                        data-value="important"
                        role="button"
                        onclick="handleMessageStateBtn(this, false)">
                    <i class="far fa-star text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.status.important"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-state-btn f-16 shadow-sm"
                        data-value="private"
                        role="button"
                        onclick="handleMessageStateBtn(this, false)">
                    <i class="far fa-lock text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.status.private"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-state-btn f-16 shadow-sm"
                        data-value="notified"
                        role="button"
                        onclick="handleMessageStateBtn(this, false)">
                    <i class="far fa-bell text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.status.notified"/>
                </span>
            </div>
        </div>

        <div class="d-flex flex-column mb-4">
            <label class="text-info-emphasis mb-2 fw-bold">
                <g:message code="communication.modal.select.visible.message.direction"/>
            </label>

            <div class="d-flex flex-row">
                <span
                        class="badge rounded-pill border border-primary text-primary me-2 p-2 w-25 msg-direction-btn f-16 shadow-sm"
                        data-value="all"
                        role="button"
                        onclick="handleMessageDirectionBtn(this)"
                        style="background-color: #F2F9FE">
                    <g:message code="communication.modal.conversation.messages.direction.all"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-direction-btn f-16 shadow-sm"
                        data-value="incoming"
                        role="button"
                        onclick="handleMessageDirectionBtn(this)">
                    <i class="far fa-arrow-down-left text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.direction.incoming"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-direction-btn f-16 shadow-sm"
                        data-value="outgoing"
                        role="button"
                        onclick="handleMessageDirectionBtn(this)">
                    <i class="far fa-arrow-up-right text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.direction.outgoing"/>
                </span>
            </div>
        </div>

        <div class="d-flex flex-column mb-4">
            <label class="text-info-emphasis mb-2 fw-bold">
                <g:message code="communication.modal.select.visible.message.readability"/>
            </label>

            <div class="d-flex flex-row">
                <span
                        class="badge rounded-pill border border-primary text-primary me-2 p-2 w-25 msg-readability-btn f-16 shadow-sm"
                        data-value="all"
                        role="button"
                        onclick="handleMessageReadabilityBtn(this)"
                        style="background-color: #F2F9FE">
                    <g:message code="communication.modal.select.visible.message.readability.all"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-readability-btn f-16 shadow-sm"
                        data-value="read"
                        role="button"
                        onclick="handleMessageReadabilityBtn(this)">
                    <i class="fa-solid fa-envelope-open-text text-secondary me-2"></i>
                    <g:message code="communication.modal.select.visible.message.readability.read"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-readability-btn f-16 shadow-sm"
                        data-value="unread"
                        role="button"
                        onclick="handleMessageReadabilityBtn(this)">
                    <i class="fa-solid fa-envelope-dot text-secondary me-2"></i>
                    <g:message code="communication.modal.select.visible.message.readability.unread"/>
                </span>
            </div>
        </div>

        <div class="d-flex flex-column mb-5">
            <label class="text-info-emphasis fw-bold mb-2">
                <g:message code="communication.modal.client.important"/>
            </label>

            <div class="d-flex flex-row">
                <span
                        class="badge rounded-pill border border-primary text-primary me-2 p-2 w-25 msg-client-important-btn f-16 shadow-sm"
                        value="false"
                        role="button"
                        onclick="handleClientImportantBtn(this)"
                        style="background-color: #F2F9FE">
                    <g:message code="communication.modal.conversation.messages.status.all"/>
                </span>
                <span
                        class="badge rounded-pill border border-secondary text-secondary me-2 p-2 w-25 msg-client-important-btn f-16 shadow-sm"
                        value="true"
                        role="button"
                        onclick="handleClientImportantBtn(this)">
                    <i class="far fa-star text-secondary"></i>
                    <g:message code="communication.modal.conversation.messages.status.important"/>
                </span>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        refreshBadges();
        handleMessageTimePeriod();
        handleClientImportantBtn();
        handleMessageStateBtn();
        handleMessageDirectionBtn();
        handleMessageReadabilityBtn();

        $("#conversation-filter-timePeriod").on('change', function () {
            handleMessageTimePeriod($(this).val());
        });

        $("#conversation-filter-keywords").on('keydown', function (e) {
            if (e.keyCode === 13) {
                addKeyWordAndRefresh();
            }
        });
    });

    function addKeyWordAndRefresh() {
        const keyWord = $("#conversation-filter-keywords").val();
        $("#conversation-filter-keywords").val("");
        CommunicationInbox.filter.setKeyWords(keyWord);
        refreshBadges();
    }

    function removeBadge(btn) {
        const keyWord = $(btn).prev('span').text();
        CommunicationInbox.filter.setKeyWords(keyWord);
        refreshBadges();
    }

    function refreshBadges() {
        const keyWords = CommunicationInbox.filter.getKeyWordsBadges();
        $('#key-words-container').html(keyWords);

        if (keyWords.length > 0) {
            $('#key-words-container').removeClass('d-none');
            $('#button-key-removeAll').removeClass('d-none').addClass('d-flex');
        } else {
            $('#key-words-container').addClass('d-none');
            $('#button-key-removeAll').addClass('d-none').removeClass('d-flex');
        }
    }

    function removeAllBadges() {
        CommunicationInbox.filter.removeAllKeyWords();
        refreshBadges();
    }

    function handleClientImportantBtn(btn = null) {
        if (btn) {
            CommunicationInbox.filter.setClientImportant($(btn).attr('value') === 'true');
        }

        $('.msg-client-important-btn').each(function () {
            const isSelected = ($(this).attr('value') === "true") === CommunicationInbox.filter.clientImportant;

            $(this)
                .toggleClass('border-primary text-primary', isSelected)
                .toggleClass('border-secondary text-secondary', !isSelected)
                .css('background-color', isSelected ? '#F2F9FE' : 'white')
                .children('i')
                .css('font-weight', isSelected ? 'bold' : 'normal');

            $(this)
                .find('i')
                .toggleClass('text-primary', isSelected)
                .toggleClass('text-secondary', !isSelected);
        });
    }

    function handleMessageStateBtn(btn = null) {
        if (btn) {
            CommunicationInbox.filter.setMessageState($(btn).data('value'));
        }

        let currentFilterMsgState = null;

        if (CommunicationInbox.filter.msgImportant)
            currentFilterMsgState = "important";
        else if (CommunicationInbox.filter.msgPrivate)
            currentFilterMsgState = "private";
        else if (CommunicationInbox.filter.msgNotified)
            currentFilterMsgState = "notified";
        else
            currentFilterMsgState = "all";

        $('.msg-state-btn').each(function () {
            const isSelected = $(this).data('value') === currentFilterMsgState;
            const $icon = $(this).children('i');

            $(this)
                .toggleClass('border-primary text-primary', isSelected)
                .toggleClass('border-secondary text-secondary', !isSelected)
                .css('background-color', isSelected ? '#F2F9FE' : 'white');

            $icon
                .css('font-weight', isSelected ? 'bold' : 'normal')
                .toggleClass('text-primary', isSelected)
                .toggleClass('text-secondary', !isSelected);
        });
    }

    function handleMessageDirectionBtn(btn = null) {
        if (btn) {
            CommunicationInbox.filter.setMessageDirection($(btn).data('value'));
        }

        let currentFilterMsgDirection = null;

        if (CommunicationInbox.filter.msgIncoming)
            currentFilterMsgDirection = "incoming";
        else if (CommunicationInbox.filter.msgOutgoing)
            currentFilterMsgDirection = "outgoing";
        else
            currentFilterMsgDirection = "all";

        $('.msg-direction-btn').each(function () {
            const isSelected = $(this).data('value') === currentFilterMsgDirection;

            $(this)
                .toggleClass('border-primary text-primary', isSelected)
                .toggleClass('border-secondary text-secondary', !isSelected)
                .css('background-color', isSelected ? '#F2F9FE' : 'white')
                .children('i')
                .css('font-weight', isSelected ? 'bold' : 'normal');

            $(this)
                .find('i')
                .toggleClass('text-primary', isSelected)
                .toggleClass('text-secondary', !isSelected);
        });
    }

    function handleMessageReadabilityBtn(btn = null) {
        if (btn) {
            CommunicationInbox.filter.setMessageReadability($(btn).data('value'));
        }

        let currentFilterMsgReadabilty = null;

        if (CommunicationInbox.filter.msgRead)
            currentFilterMsgReadabilty = "read";
        else if (CommunicationInbox.filter.msgUnread)
            currentFilterMsgReadabilty = "unread";
        else
            currentFilterMsgReadabilty = "all";

        $('.msg-readability-btn').each(function () {
            const isSelected = $(this).data('value') === currentFilterMsgReadabilty;

            $(this)
                .toggleClass('border-primary text-primary', isSelected)
                .toggleClass('border-secondary text-secondary', !isSelected)
                .css('background-color', isSelected ? '#F2F9FE' : 'white')
                .children('i')
                .css('font-weight', isSelected ? 'bold' : 'normal');

            $(this)
                .find('i')
                .toggleClass('text-primary', isSelected)
                .toggleClass('text-secondary', !isSelected);
        });
    }

    function handleMessageTimePeriod(timePeriod) {
        if (timePeriod) {
            CommunicationInbox.filter.setTimePeriod(timePeriod);
        } else {
            $('#conversation-filter-timePeriod').val(CommunicationInbox.filter.timePeriod).change();
        }
    }

    function handleGeneralCollapse() {
        const iconGeneral = $('#headingGeneral');
        const generalDiv = $('#generalCollapse');

        if (!generalDiv.hasClass('show')) {
            iconGeneral.removeClass('fa-plus').addClass('fa-minus');
            generalDiv.addClass('show');
        } else {
            iconGeneral.removeClass('fa-minus').addClass('fa-plus');
            generalDiv.removeClass('show');
        }
    }
</script>