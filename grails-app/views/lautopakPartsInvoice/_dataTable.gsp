<div>
    <h4 class="text-uppercase mb-5">
        <g:message code="lautopakpartsinvoice.list"/>
        <small>(<g:message code="${queryBuilder ? 'advanced.filters' : 'basic.filters'}"/>)</small>
    </h4>

    <div class="row" id="selects-div${id}" style="display: none;">
        <g:if test="${queryBuilder}">
            <div class="col-12">
                <div class="w-100 clearfix">
                    <select class="float-right w-50 form-control" id="builder-select"
                            onchange="PartsInvoiceList.setQueryBuilderFilter()">
                        <option value="0"><g:message code="filter.select.to.load"/></option>
                        <g:each var="f" in="${filters}">
                            <option value="${f.id}">${f.name}</option>
                        </g:each>
                    </select>
                </div>

                <div class="w-100">
                    <container:queryBuilder id="builder" type="${traction.Filter.Type.LAUTOPAK_PARTS_INVOICE}"
                                            getInitialRulesFunction="getCookieQueryBuilderRules"/>
                </div>
            </div>
        </g:if>
    </div>
    <table class="table border" id="${id}">
        <thead>
        <tr>
            <th class="check"></th>
            <th class="roID min-w-200"><g:message code="lautopakpartsinvoice.roID"/></th>
            <th class="type min-w-100"><g:message code="lautopakpartsinvoice.type"/></th>
            <th class="department min-w-100">
                <i class="mdi mdi-store" data-toggle="tooltip"
                   title="<g:message code="lautopakpartsinvoice.department"/>"></i>
                <span class="d-none"><g:message code="lautopakpartsinvoice.department"/></span>
            </th>
            <th class="client"><g:message code="client"/></th>
            <th class="serialnumber min-w-150"><g:message code="product.serialnumber"/></th>
            <th class="partsTotal min-w-150"><g:message code="lautopakpartsinvoice.partsTotal"/></th>
            <th class="openingDate min-w-150"><g:message code="lautopakpartsinvoice.openingDate"/></th>
            <th class="closingDate min-w-100"><g:message code="lautopakpartsinvoice.closingDate"/></th>
            <th class="employeeExtId min-w-100"><g:message code="lautopakpartsinvoice.employeeExtId"/></th>
            <th class="clerkExtId"><g:message code="lautopakpartsinvoice.clerkExtId"/></th>
            <th class="lautopakPartReservationStatus min-w-100">
                <i class="mdi mdi-dropbox" data-toggle="tooltip"
                   title="<g:message code="lautopakpartsinvoice.lautopakPartReservationStatus"/>"></i>
                <span class="d-none"><g:message code="lautopakpartsinvoice.lautopakPartReservationStatus"/></span>
            </th>
            <th class="status min-w-150"><g:message code="lautopakpartsinvoice.status"/></th>
            <th class="lastAction"><g:message code="actionlog.last"/></th>
            <th class="statusLastChange">
                <i class="mdi mdi-calendar" data-toggle="tooltip"
                   title="<g:message code="lautopakpartsinvoice.lautopakPartReservationStatusLastChange"/>"></i>
                <span class="d-none"><g:message
                        code="lautopakpartsinvoice.lautopakPartReservationStatusLastChange"/></span>
            </th></tr>
        </thead>
        <tbody></tbody>
    </table>
</div>