<html>
<head>
    <g:if test="${profile?.naked}">
        <meta name="layout" content="publicLayoutNoMenu"/>
    </g:if>
    <g:else>
        <meta name="layout" content="publicLayout"/>
    </g:else>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>${event?.name} - ${form?.name}</title>

    <script>
        var errorSending = '<g:message code="error.occurred"/>';
        var errorField = '<g:message code="form.error.field"/>';
    </script>

    <link rel="stylesheet" href="${resource(dir: 'assets/form', file: 'form.css', absolute: true)}"/>

    <link rel="stylesheet" href="${resource(dir: 'assets/lib', file: 'jquery-ui.css', absolute: true)}"/>
    <link rel="stylesheet" href="${resource(dir: 'assets/lib', file: 'jquery.signature.css', absolute: true)}"/>
    <script src="${resource(dir: 'assets/lib', file: 'jquery-ui.min.js', absolute: true)}"></script>

    <script src="${resource(dir: 'assets/lib', file: 'jquery.signature.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib', file: 'jquery.ui.touch-punch.min.js', absolute: true)}"></script>

    <script src="${resource(dir: 'assets/lib', file: 'jquery.barrating.min.js', absolute: true)}"></script>
    <link rel="stylesheet" href="${resource(dir: 'assets/lib', file: 'font-awesome.min.css', absolute: true)}"/>
    <link rel="stylesheet" href="${resource(dir: 'assets/lib', file: 'fontawesome-stars.css', absolute: true)}"/>



    <link rel="stylesheet" href="${resource(dir: 'assets/lib/select2', file: 'select2.min.css', absolute: true)}"/>
    <link rel="stylesheet"
          href="${resource(dir: 'assets/lib/select2', file: 'select2-bootstrap-5-theme.min.css', absolute: true)}"/>
    <script src="${resource(dir: 'assets/lib/select2', file: 'select2.full.min.js', absolute: true)}"></script>

    <script src="${resource(dir: 'assets/form', file: 'index.js', absolute: true)}"></script>

    <script>${raw(form?.javascript)}</script>
    <style>${raw(form?.css)}</style>

    <script>
        var eventId = '${event.id}';
        var eventName = '${event.name}';
        $(document).ready(function () {
            initParams();
        });

        <g:if test="${formClientData}">

        function getDatas(onSuccess) {
            $.get(tractionWebRoot + '/form/getFormClientDatas/${formClientData.id}', function (data) {
                if (typeof onSuccess === 'function') {
                    onSuccess(data);
                }
            });
        }

        </g:if>

        function initParams() {
            let urlParams = new URLSearchParams(window.location.search)
            for (const key of urlParams.keys()) {
                var element = $('[id="' + key + '"]');
                if (element.length === 0) {
                    element = $('[name="' + key + '"]');
                }
                if (element.length !== 0 && element.attr('href') !== undefined) {
                    element.attr('href', urlParams.get(key));
                } else if (element.length !== 0) {
                    element.val(urlParams.get(key)).change();
                }
            }
        }

        function isIE() {
            // IE 10 and IE 11
            return /Trident\/|MSIE/.test(window.navigator.userAgent);
        }

        let showBrowserAlert = (function () {
            if (document.querySelector('.unsupported-browser')) {
                let d = document.getElementsByClassName('unsupported-browser');

                is_IE11 = isIE();

                if (is_IE11) {
                    d[0].innerHTML = "<p class='alert alert-warning text-center'><g:message code="error.unsupportedBrowser"/></p>";
                    d[0].style.display = 'block';
                }
            }
        });

        document.addEventListener('DOMContentLoaded', showBrowserAlert);
    </script>
    <g:if test="${profile.font}">
        <style>
        @font-face {
            font-family: customFont;
            src: url('${profile.font}');
        }

        * {
            font-family: customFont, Helvetica, sans-serif !important;
        }
        </style>
    </g:if>
    <g:if test="${profile.backgroundImage}">
        <style>
        body, html {
            min-height: 100%;
        }

        body {
            background-repeat: no-repeat !important;
            background-size: cover !important;
            background-attachment: fixed !important;
            background-position: center !important;
            background-image: url('${profile.backgroundImage}') !important;
        }
        </style>
    </g:if>
    <g:render template="/formProfile/formeoObj"/>
</head>

<body>
<div class="unsupported-browser"></div>
<!--[if IE]>
    <p class="alert alert-warning"><g:message code="error.unsupportedBrowser"/></p>
<![endif]-->
<div id="bigLoader" style="display: none;"></div>

<div id="formWrapper" class="w-100 p-4">
    <form id="form">
        <input type="hidden" value="${form?.id}" name="formid">
        <input type="hidden" value="${event?.id}" name="eventid">
        <input type="hidden" value="${profile?.naked}" name="noMenu">
        <input type="hidden" id="timezone" name="timezone">
        <input type="hidden" value="${params.formClientDataId}" name="formClientDataId">
        <input id="consent" type="hidden" value="" name="consent">

        <div id="errorMessages" class="alert alert-danger w-100 mt-2" style="display:none;"></div>

        <div id="page-${form.id}" class="row rowClass">
            <div class="formeo-render w-100"></div>
            <script>
                var formeoRenderer = new FormeoFormBuilder('${form.id}', 'en', '${form.formData}', initParams);
            </script>
        </div>
        <g:if test="${form.profile.consentment}">
            <div id="modalConsentment" class="modal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-dialog-scrollable" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 class="modal-title fw-bold"><g:message code="form.consentmentTitle"/></h2>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">

                            </button>
                        </div>

                        <div class="modal-body">
                            <p>${raw(form?.profile.consentment)}</p>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary"
                                    onclick="Form.consentForm('true');">
                                <g:message code="form.consent.accept"/></button>
                            <button type="button" class="btn btn-secondary"
                                    onclick="Form.consentForm('false');">
                                <g:message code="form.consent.refuse"/>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </g:if>
    </form>
</div>
</body>
</html>
