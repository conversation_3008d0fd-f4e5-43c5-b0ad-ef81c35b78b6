<ul class="typeahead__list position-static m-0 p-0 no-scrollbar rounded-1">
    <g:set var="sec" bean="springSecurityService"/>
    <g:set var="user" value="${(traction.security.User) sec.currentUser}"/>
    <g:if test="${navigationHistories}">
        <g:each var="navigationHistory" in="${navigationHistories}">
            <li class="typeahead__item">
                <g:if test="${navigationHistory.client}">
                    <a href="<g:createLink controller='client' action='index'
                                           params='[id: navigationHistory.clientId, opporid: navigationHistory.opportunityId, cotationId: navigationHistory.cotationId]'/>">
                        <div class="d-flex flex-column p-3 search-avatar" style="width:360px; min-height: 135px;">

                            <div class="d-flex">
                                <div class="d-flex flex-column align-items-center">
                                    <communication:avatar client="${navigationHistory.client}"
                                                          style="width: 59px; height: 59px;"/>
                                    <div class="mx-2 mt-2 f-16" style="line-height: 16px; color:#B3B3B3;">
                                        ${traction.DateUtils.getReallyShortDurationString(navigationHistory.date)}
                                    </div>
                                </div>

                                <div class="d-flex flex-column w-100 ms-3">
                                    <div class="d-flex align-items-center mb-1 f-16 h-19">
                                        <div class="w-100-overflow-ellipsis">${navigationHistory.client.getFullName()}</div>
                                    </div>
                                    <g:if test="${navigationHistory.client.id}">
                                        <div class="d-flex align-items-center mb-1 f-16 h-19">
                                            <div class="w-100-overflow-ellipsis">#${navigationHistory.client.id}</div>
                                        </div>
                                    </g:if>

                                    <g:if test="${navigationHistory.client.email && navigationHistory.client.email !== navigationHistory.client.getFullName()}">
                                        <div class="d-flex align-items-center mb-1 f-16 h-19">
                                            <div class="w-100-overflow-ellipsis">${navigationHistory.client.email}</div>
                                        </div>
                                    </g:if>

                                    <g:if test="${navigationHistory.client.getFormattedPhone()}">
                                        <div class="d-flex align-items-center f- h-19">
                                            <div class="w-100-overflow-ellipsis">${navigationHistory.client.getFormattedPhone()}</div>
                                        </div>
                                    </g:if>
                                </div>
                            </div>

                            <!-- Second Row: Badge and Links -->
                            <div class="d-flex justify-content-between align-items-center mt-3 h-19">
                                <g:if test="${navigationHistory.client.isEnterprise()}">
                                    <div class="w-100-overflow-ellipsis">
                                        <span class="badge badge-outline-primary text-uppercase">
                                            <g:message code="enterprise"/>
                                        </span>
                                    </div>
                                </g:if>
                                <g:else>
                                    <div class="w-100-overflow-ellipsis">
                                        <span class="badge badge-outline-primary text-uppercase">
                                            <g:message code="client"/>
                                        </span>
                                    </div>
                                </g:else>

                                <div class="d-flex justify-content-end align-items-center gap-3">
                                    <i class="fa-solid fa-messages scale-hover-2 f-14 text-primary"
                                       data-toggle="tooltip" title="<g:message code='communications'/>"
                                       onclick="event.preventDefault();
                                       event.stopPropagation();
                                       TractionModal.show({
                                           url: '/communication/modal',
                                           bigLoader: true,
                                           data: {
                                               clientId: ${navigationHistory.clientId},
                                               user: ${user.id}
                                           }
                                       });"></i>
                                    <i class="fa-solid fa-calendar-day scale-hover-2 f-14 text-primary"
                                       data-toggle="tooltip" title="<g:message code='serviceschedule'/>"
                                       onclick="event.preventDefault();
                                       TractionModal.show({
                                           url: '/serviceSchedule/modalGoTo',
                                           bigLoader: true,
                                           data: {
                                               clientId: ${navigationHistory.clientId}
                                           }
                                       });"></i>
                                    <i class="fa-solid fa-clipboard-list-check scale-hover-2 f-14 text-primary"
                                       data-toggle="tooltip" title="<g:message code='main.workflow'/>"
                                       onclick="event.preventDefault();
                                       TractionModal.show({
                                           url: '/workflowData/modalGoTo',
                                           bigLoader: true,
                                           data: {
                                               clientId: ${navigationHistory.clientId}
                                           }
                                       });"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </g:if>
                <g:elseif test="${navigationHistory.vehicle}">
                    <a href="<g:createLink controller='vehicle' action='index'
                                           params='[id: navigationHistory.vehicleId]'/>">
                        <div class="d-flex flex-column p-3 search-avatar" style="width:360px; min-height: 135px;">

                            <div class="d-flex">
                                <div class="d-flex flex-column align-items-center">
                                    <g:set var="pictures"
                                           value="${navigationHistory.vehicle?.asInheritedVehicle()?.getInheritedPictures()}"/>
                                    <g:set var="firstImage" value="${pictures?.getFirstWatermarkedPicture()}"/>
                                    <g:if test="${firstImage}">
                                        <img src="<g:createLink absolute="true" controller="web" action="getFile"
                                                                id="${firstImage.id}"/>" class="img-fluid border rounded-circle" alt=""
                                             style="width: 56px; height: 56px;">
                                    </g:if>
                                    <g:else>
                                        <asset:image src="image-missing.jpg" class="img-fluid border rounded-circle" style="width: 56px; height: 56px;" alt=""/>
                                    </g:else>
                                    <div class="mx-2 mt-2 f-16" style="line-height: 16px; color:#B3B3B3;">
                                        ${traction.DateUtils.getReallyShortDurationString(navigationHistory.date)}
                                    </div>
                                </div>

                                <div class="d-flex flex-column ms-3">
                                    <div class="d-flex align-items-center mb-1 f-16 h-19">
                                        <div class="w-100-overflow-ellipsis">${navigationHistory.vehicle.makeModelYear()}</div>
                                    </div>
                                    <g:if test="${navigationHistory.vehicle.id}">
                                        <div class="d-flex align-items-center mb-1 f-16 h-19">
                                            <div class="w-100-overflow-ellipsis">#${navigationHistory.vehicle.id}</div>
                                        </div>
                                    </g:if>

                                    <g:if test="${navigationHistory.vehicle.asInheritedVehicle().stockNumber}">
                                        <div class="d-flex align-items-center mb-1 f-16 h-19">
                                            <div class="w-100-overflow-ellipsis">${navigationHistory.vehicle.asInheritedVehicle().stockNumber}</div>
                                        </div>
                                    </g:if>
                                </div>
                            </div>

                            <!-- Second Row: Badge and Links -->
                            <div class="d-flex justify-content-between align-items-center mt-3 h-19">
                                <div class="w-100-overflow-ellipsis">
                                    <span class="badge badge-outline-primary text-uppercase">
                                        <g:message code="vehicle"/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </a>
                </g:elseif>
            </li>
        </g:each>
    </g:if>
    <g:else>
        <li class="typeahead__item">
            <a href="javascript:void(0);">
                <g:message code="navigationhistory.empty"/>
            </a>
        </li>
    </g:else>
</ul>