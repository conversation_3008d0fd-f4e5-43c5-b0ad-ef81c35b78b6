import traction.ConfigService
import traction.history.History

model {
    History history
    ConfigService configService
}

json {
    id history.id
    value history.value1
    points history.points
    status history.status?.id
    date history.date
    category history.category?.id

    if (history.target) {
        target g.render(template: "/v1_0_6/apiUser/show", model: [user: history.target, expand: []])
    } else {
        target null
    }

    if (history.user) {
        user g.render(template: "/v1_0_6/apiUser/show", model: [user: history.user, expand: []])
    } else {
        user null
    }

    if (history.client) {
        client g.render(template: "/v1_0_6/apiClient/show", model: [client: history.client, expand: []])
    } else {
        client null
    }

    if (history.opportunity) {
        opportunity g.render(
                template: "/v1_0_6/apiOpportunity/show",
                model: [opportunity: history.opportunity, configService: configService, expand: ['product', 'status']]
        )
    } else {
        opportunity null
    }
}