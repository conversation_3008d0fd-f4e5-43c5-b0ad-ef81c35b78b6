import traction.service.WorkOrder

model {
    WorkOrder workOrder
}
//System.out.println("WHAT workOrder binding : $binding.variables")
json {
    id workOrder.id
    roID workOrder.roID
    message workOrder.toString()
    title workOrder.title
    client g.render(template: "/v1_0_6/apiClient/show", model: [client:workOrder.client, expand:[]])
    promisedDate workOrder.promisedDate
    if(workOrder.assignmentStatus != null){
        assignmentStatus {
            id workOrder.assignmentStatus.id
            title workOrder.assignmentStatus.message
            icon workOrder.assignmentStatus.icon
            color workOrder.assignmentStatus.iconColor
            borderColor workOrder.assignmentStatus.borderColor

        }
    }
    if(workOrder.lautopakPartReservationStatus != null){
        lautopakPartReservationStatus {
            id workOrder.lautopakPartReservationStatus.id
            title workOrder.lautopakPartReservationStatus.message
            icon workOrder.lautopakPartReservationStatus.icon
            color workOrder.lautopakPartReservationStatus.iconColor
        }
    }
    serialnumber workOrder.serialnumber
    if (workOrder.vehicle != null) {
        //product g.render(template: "/v1_0_6/apiProduct/show", model: [product: workOrder.vehicle])
    }

    if(workOrder.workflowData != null) {
        workflowData g.render(template: "/v1_0_6/apiWorkflow/workflowData", model: [data: workOrder.workflowData, expand: []])
    }

    // not necessary for now
    //date workOrder.date
    //syncPromisedDate workOrder.syncPromisedDate
    //appointmentDate workOrder.appointmentDate
    //status workOrder.status
    //type workOrder.type
    //unread workOrder.unread
    //closed workOrder.closed
    //clientComment workOrder.clientComment
    //recommendations workOrder.recommendations
    //openingAdvisor g.render(template: "/v1_0_6/apiUser/show", model: [user: workOrder.openingAdvisor, expand: []])
    //closingAdvisor g.render(template: "/v1_0_6/apiUser/show", model: [user: workOrder.closingAdvisor, expand: []])
    //icon workOrder.icon
    //priority workOrder.priority
    //estimatedTime workOrder.estimatedTime
    //timeWorked workOrder.timeWorked
    //timeLeft workOrder.timeLeft
    //timeLate workOrder.timeLate
    //exceeding workOrder.exceeding
    //percentCompleted workOrder.percentCompleted
}
