import traction.ConfigService
import traction.opportunity.OpportunityService
import traction.product.Trade

model {
    Trade trade
    ConfigService configService
    OpportunityService opportunityService
    List<String> expanded
}

json {
    id trade.id
    priceAsked trade.priceAsked
    priceAccepted trade.priceAccepted
    priceLink trade.priceLink
    if (expanded?.contains("product") && trade.product != null) {
        product g.render(template: "/v1_0_6/apiProduct/show",
                model: [
                        product           : trade.product,
                        expanded          : ["opportunities"],
                        configService     : configService,
                        opportunityService: opportunityService,
                ])
    }
    if (expanded?.contains("cotationElement") && trade.cotationElement != null) {
        cotationElement g.render(template: "/v1_0_6/apiCotation/cotationElement",
                model: [
                        cotationElement   : trade.cotationElement,
                        expand            : [],
                        configService     : configService,
                        opportunityService: opportunityService,
                ])
    }
}