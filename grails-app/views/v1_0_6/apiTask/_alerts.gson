import traction.VisitLog
import traction.task.Task

model {
    List<Task> soonTaskList
    List<Task> todoTaskList
    List<Task> providerTaskList
    List<Task> deliveryTaskList
    List<VisitLog> visitLogList
    boolean isSoon
    boolean isTodo
    boolean isProvider
    boolean isDelivery
}

json {
    soonTasks g.render(template: "/v1_0_6/apiTask/show", var: "task", collection: soonTaskList ?: [])
    todoTasks g.render(template: "/v1_0_6/apiTask/show", var: "task", collection: todoTaskList ?: [])
    providerTasks g.render(template: "/v1_0_6/apiTask/show", var: "task", collection: providerTaskList ?: [])
    deliveryTasks g.render(template: "/v1_0_6/apiTask/show", var: "task", collection: deliveryTaskList ?: [])
    visitLogs g.render(template: "/v1_0_6/apiTask/showVisit", var: "visitLog", collection: visitLogList ?: [])
    soon isSoon
    todo isTodo
    provider isProvider
    delivery isDelivery
}