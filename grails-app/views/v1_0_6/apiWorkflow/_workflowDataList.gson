import traction.ConfigService
import traction.opportunity.OpportunityService
import traction.workflow.WorkflowData

model {
    List<WorkflowData> workflowDataList
    List<String> expand
    ConfigService configService
    OpportunityService opportunityService
}

json g.render(
        template: "/v1_0_6/apiWorkflow/workflowData",
        collection: workflowDataList ?: [],
        var: "data",
        model: [configService: configService, opportunityService: opportunityService, expand: expand ?: []]
)
