import traction.client.Client
import traction.opportunity.Opportunity
import traction.stripe.StripeAccount

model {
    Opportunity _opportunity
    Client _client
    List<StripeAccount> _accountList
}

json {
    if (_opportunity) {
        opportunity g.render(template: "/v1_0_6/apiOpportunity/show", model: [opportunity: _opportunity, expand: []])
    } else {
        opportunity null
    }
    if (_client) {
        client g.render(template: "/v1_0_6/apiClient/show", model: [client: _client, expand: []])
    } else {
        client = _client
    }
    if (_accountList) {
        accountList _accountList.collect { account ->
            [value  :account.id, name: account.name]
        }
    }
}