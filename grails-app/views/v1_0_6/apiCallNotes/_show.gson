import traction.communication.CallNotes

model {
    CallNotes callNotes
}

json {
    id callNotes.id
    date callNotes.date
    completed callNotes.completed
    phone callNotes.phone
    text callNotes.text
    comment callNotes.comment
    if(callNotes.departmentGroup) {
        departmentGroup {
            id callNotes.departmentGroup.id
            userGroup g.message(code: callNotes.departmentGroup.userGroup?.message)
            department {
                id callNotes.departmentGroup.department.id
                name callNotes.departmentGroup.department.name
                date callNotes.departmentGroup.department.date
            }
            //users
        }
    }
    if(callNotes.departmentGroup) {
        department {
            id callNotes.department.id
            name callNotes.department.name
            date callNotes.department.date
            //users
        }
    }
    if (callNotes.client != null) {
        client g.render(template: "/v1_0_6/apiClient/show", model: [client: callNotes.client, expand: []])
    }
    if (callNotes.user != null) {
        user g.render(template: "/v1_0_6/apiUser/show", model: [user: callNotes.user, expand: []])
    }
    if(callNotes.voicemailFileDataId != null) {
        voiceMailId callNotes.voicemailFileDataId
        voiceMailUrl g.link(absolute:true, controller:"web", action: "getFile", id: callNotes.voicemailFileDataId)
    }
    if( callNotes.callData != null) {
        callData {
            id callNotes.callData.id
            duration callNotes.callData.duration
            url g.link(absolute:true, controller:"web", action: "getAudiopbx", id: callNotes.callData.id)
        }
    }
}