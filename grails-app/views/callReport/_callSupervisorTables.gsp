<g:each var="dept" in="${departmentGroups}">
    <script>

        $(document).ready(function () {
            var url = tractionWebRoot + '/callReport/callSupervisorTableJSON?isAllDepartments=${isAllDepartments}&departmentGroupId=${dept.id}&dateFrom=${dateFrom}&dateTo=${dateTo}';

            if ($.fn.DataTable.isDataTable('#callSupervisorTable-${dept.id}')) {
                $('#callSupervisorTable-${dept.id}').dataTable().api().ajax.url(url).load();
            }
            else {
                TractionDataTable.init('#callSupervisorTable-${dept.id}', {
                    id: 'callSupervisorTable-${dept.id}',
                    enableExport: true,
                    dataTableOptions: {
                        serverSide: false,
                        paging: false,
                        searching: false,
                        ajax: {
                            url: url,
                            type: 'POST'
                        },
                        columnDefs: [
                            {
                                name: 'name',
                                targets: 'name',
                                data: 'name',
                                render: function (data, type, row, meta) {
                                    if (row.isDepartment) {
                                        return "<span style=\"display: none;\">_</span><b>" + data + "</b>";
                                    }
                                    return data;
                                }
                            },
                            {name: 'total', data: 'total', targets: 'total'},
                            {name: 'out', data: 'out', targets: 'out'},
                            {name: 'in', data: 'in', targets: 'in'},
                            {name: 'queues', data: 'queues', targets: 'queues'},
                            {name: 'queueAnswerSpd', data: 'queueAnswerSpd', targets: 'queueAnswerSpd'},
                            {name: 'transferAnswerSpd', data: 'transferAnswerSpd', targets: 'transferAnswerSpd'},
                            {name: 'transfers', data: 'transfers', targets: 'transfers'},
                            {name: 'queueReturnCalls', data: 'queueReturnCalls', targets: 'queueReturnCalls'},
                            {name: 'transferReturnCalls', data: 'transferReturnCalls', targets: 'transferReturnCalls'},
                            {name: 'avgDuration', data: 'avgDuration', targets: 'avgDuration'},
                            {
                                name: 'avgRating',
                                data: 'avgRating',
                                targets: 'avgRating',
                                render: function (data, type, row, meta) {
                                    return data + ' <i class="mdi mdi-star"></i>';
                                }
                            }
                        ],
                        buttons: [
                            'modal'
                        ]
                    }
                });
            }
        });
    </script>
    <div class=" border m-4 p-4 rounded">

    <h2><g:message code="${dept.userGroup}"/> ${dept.department.name}</h2>
    <table class="table table-striped w-100" cellspacing="0" id="callSupervisorTable-${dept.id}" style="overflow: scroll">
        <thead>
        <tr>
            <th class="name min-w-150"><g:message code="report.name"/></th>
            <th class="total min-w-150">
                <span data-toggle="tooltip" title="<g:message code="report.calls.tooltip"/>">
                    <g:message code="report.calls"/>
                </span>
            </th>
            <th class="out min-w-150">
                <span data-toggle="tooltip" title="<g:message code="report.outbound.tooltip"/>">
                <g:message code="callmanager.outbound"/>
                </span>
            </th>
            <th class="in min-w-150">
                <span data-toggle="tooltip" title="<g:message code="report.inbound.tooltip"/>">
                    <g:message code="callmanager.inbound"/>
                </span>
            </th>
            <th class="transfers min-w-200">
                <span data-toggle="tooltip" title="<g:message code="report.transfers.tooltip"/>">
                    <g:message code="report.transfers"/>
                </span>
            </th>
            <th class="transferReturnCalls min-w-350">
                <span data-toggle="tooltip" title="<g:message code="report.transferReturnCalls.tooltip"/>">
                    <g:message code="report.transferReturnCalls"/>
                </span>
            </th>
            <th class="avgRating min-w-200">
                <span data-toggle="tooltip" title="<g:message code="report.avgRating.tooltip"/>">
                    <g:message code="report.avgRating"/>
                </span>
            </th>
            <th class="avgDuration min-w-200">
                <span data-toggle="tooltip" title="<g:message code="report.avgDuration.tooltip"/>">
                    <g:message code="report.avgDuration"/>
                </span>
            </th>
            <th class="transferAnswerSpd min-w-300">
                <span data-toggle="tooltip" title="<g:message code="report.transferAnswerSpd.tooltip"/>">
                    <g:message code="report.transferAnswerSpd"/>
                </span>
            </th>
            <th class="queues min-w-300">
                <span data-toggle="tooltip" title="<g:message code="report.queues.tooltip"/>">
                    <g:message code="report.queues"/>
                </span>
            </th>
            <th class="queueReturnCalls min-w-400">
                <span data-toggle="tooltip" title="<g:message code="report.queueReturnCalls.tooltip"/>">
                    <g:message code="report.queueReturnCalls"/>
                </span>
            </th>
            <th class="queueAnswerSpd min-w-350">
                <span data-toggle="tooltip" title="<g:message code="report.queueAnswerSpd.tooltip"/>">
                    <g:message code="report.queueAnswerSpd"/>
                </span>
            </th>
        </tr>
        </thead>
        <tbody></tbody>
    </table>
    </div>
</g:each>