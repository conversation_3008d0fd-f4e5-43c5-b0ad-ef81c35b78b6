<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message code="callmanager.stats"/> - <g:message code="main.callmanager"/></title>
    <asset:javascript src="libTraction/chartjs-simple-tooltip.js"/>
    <asset:stylesheet src="callManager/index.css"/>
    <asset:javascript src="callReport/index.js"/>
    <asset:stylesheet src="callReport/index.css"/>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/vfs_fonts.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0"></script>
</head>

<body>

<div class="m-4">
    <h1>
        <g:message code="callmanager.stats"/> - <g:message code="main.callmanager"/>
        <g:if test="${isAllDepartments}">
            <small>(<g:message code="callmanager.alldepts"/>)</small>
            <a href="<g:createLink absolute="true" controller="CallReport" action="index"
                                   params="[departments: 'useronly']"/>"
               class="float-right btn btn-primary"><g:message code="callmanager.onlymydepts"/></a>
        </g:if>
        <g:else>
            <small>(<g:message code="callmanager.onlymydepts"/>)</small>
            <a href="<g:createLink absolute="true" controller="CallReport" action="index"
                                   params="[departments: 'all']"/>"
               class="float-right btn btn-primary"><g:message code="callmanager.alldepts"/></a>
        </g:else>
    </h1>
</div>

<div class="row mx-2 my-4 divLoader justify-content-between">
    <div class="col-md-3 d-flex">
        <container:livestat livestat="${countCall}"/>
    </div>

    <div class="col-md-3 d-flex ">
        <container:livestat livestat="${avgDuration}"/>
    </div>

    <div class="col-md-3 d-flex">
        <container:livestat livestat="${lostCall}"/>
    </div>

    <div class="col-md-3 d-flex">
        <container:livestat livestat="${avgDelai}"/>
    </div>
</div>

<container:callStatsToday departmentGroups="${departmentGroups}" userDepartments="${userDepartments}"
                          isAllDepartments="${isAllDepartments}"/>

<div class="row  border rounded me-4 ms-4 mt-4">
        <div class="float-right">
            <div class="row">
                    <div class="d-flex flex-row-reverse p-3">
                        <container:filterMenu filterMenu="${filterMenu}"/>
                    </div>
            </div>
        </div>
</div>

<div id="charts"></div>

<div id="callSupervisorTablesDiv"></div>

</body>
</html>