<%@ page import="grails.converters.JSON" %>
<script>

    $(document).ready(function () {
        if (window.charts['chart-bar-request']) {
            window.charts['chart-bar-request'].abort();
        }
        window.charts['chart-bar-request'] = $.get(tractionWebRoot + '/callReport/getBarChart?isAllDepartments=${isAllDepartments}&departmentGroupIds=${departmentGroups.id.join(',')}&from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}', function (data) {
            var chartBar = document.getElementById('chart-bar');
            new Chart(chartBar.getContext('2d'), {
                type: 'horizontalBar',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    scales: {
                        yAxes: [{
                            barPercentage: 1.0,
                            categoryPercentage: 0.6
                        }]
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'top',
                        onHover: function (event, legendItem) {
                            chartBar.style.cursor = 'pointer';
                        },
                        onLeave: function (event, legendItem) {
                            chartBar.style.cursor = '';
                        }
                    }
                }
            });

            var chartPie = document.getElementById('chart-pie');
            console.log("report.calls", data.datasets.find(function (element) {
                return element.message == 'report.calls';
            }));
            var pieDataSet = data.datasets.find(function (element) {
                return element.message == 'report.calls';
            });
            pieDataSet.backgroundColor = pieDataSet.backgroundColorList;
            pieDataSet.borderColor = pieDataSet.borderColorList;
            var sum = pieDataSet.data.reduce((a, b) => a + b, 0);
            for (var i in data.labels) {
                var currentValue = pieDataSet.data[i];
                var percentage = 0;
                if (sum) {
                    percentage = Math.floor(((currentValue / sum) * 100) + 0.5);
                }
                data.labels[i] = data.labels[i] + ": " + currentValue + " (" + percentage + "%)";
            }

            new Chart(chartPie.getContext('2d'), {
                type: 'pie',
                data: {
                    labels: data.labels,
                    datasets: [
                        pieDataSet
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'left',
                        onHover: function (event, legendItem) {
                            chartPie.style.cursor = 'pointer';
                        },
                        onLeave: function (event, legendItem) {
                            chartPie.style.cursor = '';
                        }
                    },
                    hover: {
                        mode: 'nearest',
                        intersect: false
                    },
                    tooltips: {
                        mode: 'point',
                        intersect: true,
                        callbacks: {
                            label: function (tooltipItem, data) {
                                var label = data.labels[tooltipItem.index];
                                return label;
                            }
                        }
                    }
                }
            });

            $(chartPie).closest('.divLoader').removeClass('divLoader');
            $(chartBar).closest('.divLoader').removeClass('divLoader');
        });

        ajaxCallChart('received', '${isAllDepartments}', '${departmentGroups.id.join(',')}', '${from.format("yyyy-MM-dd")}', '${to.format("yyyy-MM-dd")}');
        ajaxCallChart('receptionreturn', '${isAllDepartments}', '${departmentGroups.id.join(',')}', '${from.format("yyyy-MM-dd")}', '${to.format("yyyy-MM-dd")}');
        ajaxCallChart('answered', '${isAllDepartments}', '${departmentGroups.id.join(',')}', '${from.format("yyyy-MM-dd")}', '${to.format("yyyy-MM-dd")}');
        ajaxCallChart('delai', '${isAllDepartments}', '${departmentGroups.id.join(',')}', '${from.format("yyyy-MM-dd")}', '${to.format("yyyy-MM-dd")}');
        ajaxCallChart('sources', '${isAllDepartments}', '${departmentGroups.id.join(',')}', '${from.format("yyyy-MM-dd")}', '${to.format("yyyy-MM-dd")}');

        if (window.charts['lostcall-request']) {
            window.charts['lostcall-request'].abort();
        }
        window.charts['lostcall-request'] = $.get(tractionWebRoot + '/callReport/getLostChartData?from=${from.format("yyyy-MM-dd")}&to=${to.format("yyyy-MM-dd")}', function (data) {
            var chartElement = document.getElementById("chart-lostcall");
            console.log('chartElement', chartElement, chartElement.getContext("2d"), data);
            var legend = {
                labels: {
                    fontSize: 16
                },
                position: 'bottom',
                onHover: function (event, legendItem) {
                    chartElement.style.cursor = 'pointer';
                },
                onLeave: function (event, legendItem) {
                    chartElement.style.cursor = '';
                }
            };
            new Chart(chartElement.getContext("2d"), {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    tooltips: {
                        mode: 'x',
                        intersect: true,
                        enabled: false,
                        footerFontStyle: 'normal',
                        custom: chartJSSimpleTooltip,
                        itemSort: (a, b) => b.yLabel - a.yLabel
                    },
                    scales: {
                        yAxes: [
                            {
                                type: 'linear', // only linear but allow scale type registration. This allows extensions to exist solely for log scale for instance
                                display: true,
                                stacked: true,
                                position: 'left',
                                id: 'y-axis',
                                ticks: {
                                    suggestedMin: 0
                                }
                            }
                        ],
                    },
                    legend: legend,
                    animation: {
                        duration: 1500
                    }
                }
            });
            $(chartElement).closest('.divLoader').removeClass('divLoader');
        });
    });
</script>


<div class="row rounded  border rounded m-4 p-4">
    <div class="mb-4 text-center">
        <h2><g:message code="report.call.receivedbydeptgroup" /></h2>
    </div>
    <div class="col-6">
        <div class=" border mt-4 ms-4 me-0 p-4">
            <div class="small-chart-container divLoader">
                <div class="loader"></div>
                <canvas id="chart-pie"></canvas>
            </div>
        </div>
    </div>
    <div class="col-6">
        <div class=" border mt-4 me-4 ms-0 p-4">
            <div class="small-chart-container divLoader">
                <div class="loader"></div>
                <canvas id="chart-bar"></canvas>
            </div>
        </div>
    </div>
</div>
<div class=" border rounded m-4 p-4">
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-received"></canvas>
    </div>
    <div id="chart-received-users-container" style="display: none;">
        <button class="btn btn-default w-100 dropdown-toggle" onclick="closeUserChart('chart-received-users')">Close</button>
        <div id="chart-received-users">
        </div>
    </div>
</div>

<div class=" border rounded m-4 p-4">
    <div class="  ms-4 me-4 mb-4 p-2 text-center">
        <h2><g:message code="report.call.answeredbydeptgroup" /></h2>
    </div>
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-answered"></canvas>
    </div>
    <div id="chart-answered-users-container" style="display: none;">
        <button class="btn btn-default w-100 dropdown-toggle" onclick="closeUserChart('chart-answered-users')">Close</button>
        <div id="chart-answered-users">
        </div>
    </div>
</div>

<div class=" rounded border m-4 p-4">
    <div class="mb-4 p-2 text-center">
        <h2><g:message code="report.call.delaibydeptgroup" /></h2>
    </div>
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-delai"></canvas>
    </div>
    <div id="chart-delai-users-container" style="display: none;">
        <button class="btn btn-default w-100 dropdown-toggle" onclick="closeUserChart('chart-delai-users')">Close</button>
        <div id="chart-delai-users">
        </div>
    </div>
</div>

<div class="rounded border m-4 p-4">
    <div class="mb-4 p-2 text-center">
        <h2><g:message code="call.report.missedcalls" /></h2>
    </div>
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-receptionreturn"></canvas>
    </div>
    <div id="chart-missed-users-container" style="display: none;">
        <button class="btn btn-default w-100 dropdown-toggle" onclick="closeUserChart('chart-missed-users')">Close</button>
        <div id="chart-missed-users">
        </div>
    </div>
</div>

<div class="rounded border m-4 p-4">
    <div class="mb-4 p-2 text-center">
        <h2><g:message code="dashboard.notconnected" /></h2>
    </div>
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-lostcall"></canvas>
    </div>
</div>

<div class="rounded border m-4 p-4">
    <div class="mb-4 p-2 text-center">
        <h2><g:message code="call.report.from.sources" /></h2>
    </div>
    <div class="chart-container divLoader">
        <div class="loader"></div>
        <canvas id="chart-sources"></canvas>
    </div>
</div>

