<g:set var="conf" bean="configService"/>
<g:set var="menuService" bean="menuService"/>
<g:set var="sec" bean="springSecurityService"/>
<g:set var="api" bean="apiService"/>
<g:set var="cookie" bean="cookieService"/>
<g:set var="user" value="${(traction.security.User) sec.currentUser}"/>
<g:set var="serveurTraction" value="${conf.get("SERVER", "server.traction.base")}"/>
<g:set var="locale" value="${org.springframework.web.servlet.support.RequestContextUtils.getLocale(request).language}"/>
<%@ page import="traction.menu.MenuLevel1; traction.WordUtils; product.ManagerProductConfig; traction.security.User; traction.UserGroup; org.apache.commons.lang3.StringUtils; traction.GroupMedia" contentType="text/html;charset=UTF-8" %>
<!doctype html>
<html lang="${locale}" class="no-js">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="robots" content="noindex">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><g:layoutTitle default="Traction"/></title>
    <asset:link rel="icon" href="/skins/favicon/${grailsApplication.config.getProperty("traction.skin") ?: ''}.svg"
                type="image/png"/>
    <asset:stylesheet src="lib/mdi/css/materialdesignicons.css"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap"
          rel="stylesheet">
    <asset:stylesheet src="layouts/mainTraction.css"/>
    <asset:stylesheet src="layouts/mainMenu.css"/>

    <asset:stylesheet src="layouts/skins/${grailsApplication.config.getProperty("traction.skin") ?: ''}.css"/>
    <asset:stylesheet src="chat/chat_manager.css"/>
    <style>
    .card-workflow [data-user-group] {
        display: none;
    }

    .card-workflow [data-user-group="${user.userGroup}"] {
        display: initial;
    }

    .checkbox-slider--a input + span:after {
        content: "<g:message code="checkbox.off"/>";
    }

    .checkbox-slider--a input:checked + span:after {
        content: "<g:message code="checkbox.on"/>";
    }
    </style>
    <script type="text/javascript">
        var tractionWebRoot = '${request.getContextPath()}';
        var zohoShowAllDocuments = ${conf.get("CONFIG", "zoho.show.all.documents") == "1"};

        var sessionTimeOutMessage = "<g:message code="sessiontimeout"/>";
        var addconfirm = "<g:message code="add.confirm"/>";
        var addconfirmproduct = "<g:message code="add.confirm.product"/>";
        var curUserName = "<sec:loggedInUserInfo field='username'/>";
        var modelContainerURL = "<g:createLink controller="container" action="modal"/>";
        var typeaheadcontainerURL = "<g:createLink controller="container" action="typeahead"/>";
        var textboxcontainerURL = "<g:createLink controller="container" action="textbox"/>";
        var datatablecontainerURL = "<g:createLink controller="container" action="datatable"/>";
        var datatabletreecheckboxshowURL = "<g:createLink controller="container" action="datatabletreecheckboxshow"/>";
        var datatableupdatetreecheckboxURL = "<g:createLink controller="container" action="datatableupdatetreecheckbox"/>";
        var datatableupdatelabeleditURL = "<g:createLink controller="container" action="datatableupdatelabeledit"/>";
        var datatableupdateattributeaddnewURL = "<g:createLink controller="container" action="datatableupdateattributeaddnew"/>";
        var datatableupdateattributeeditURL = "<g:createLink controller="container" action="datatableupdateattributeedit"/>";
        var datatableupdateattributedefeditURL = "<g:createLink controller="container" action="datatableupdateattributedefedit"/>";
        var datatableinvlinkshowURL = "<g:createLink controller="container" action="datatableinvlinkshow"/>";
        var datatableinvlinkcloseURL = "<g:createLink controller="container" action="datatableinvlinkclose"/>";
        var dataauthorcontainerURL = "<g:createLink controller="container" action="dataauthor"/>";
        var dataauthorDeleteAllURL = "<g:createLink controller="attributes" action="dataauthorDeleteAll"/>";
        var dataauthorDownloadAllURL = "<g:createLink controller="attributes" action="dataauthorDownloadAll"/>";
        var dataauthorEditURL = "<g:createLink controller="container" action="dataauthorEdit"/>";
        var dataauthorRotateURL = "<g:createLink controller="attributes" action="dataauthorRotate"/>";
        var dataauthorDeleteURL = "<g:createLink controller="attributes" action="dataauthorDelete"/>";
        var filtermenucontainerURL = "<g:createLink controller="container" action="filtermenu"/>";
        var uploadcontainerURL = "<g:createLink controller="container" action="upload"/>";
        var addallimagesURL = "<g:createLink controller="container" action="addallimages"/>";
        var browseuploadlabel = "<g:message code="file.browse"/>";
    </script>
    <script type="text/javascript" src="<g:createLink absolute="true" controller="i18N" action="javascriptMessages"
                                                      params="[version: '3.3.76', locale: locale]"/>"></script>
    <asset:javascript src="spring-websocket"/>
    <asset:javascript src="layouts/mainTraction.js"/>
    <asset:javascript src="layouts/sidebarMenu.js"/>
    <search:init/>
    <search:clientInit/>
    <search:vehicleWithClientInit/>
    <search:workOrderInit/>

    <asset:stylesheet src="client/traction-index2.css"/>
    <asset:stylesheet src="lib/intl-tel-input/intlTelInput.css"/>
    <asset:stylesheet src="libTraction/traction-intlTelInput.css"/>
    <asset:stylesheet src="changelog.css"/>
    <asset:javascript src="lib/intl-tel-input/intlTelInput.min.js"/>
    <asset:javascript src="lib/intl-tel-input/utils.js"/>
    <asset:javascript src="lib/intl-tel-input/tractionPhoneInput.js"/>
    <asset:javascript src="libTraction/HashRouter.js"/>
    <asset:javascript src="client/index.js"/>

    <script type="text/javascript">
        var curUserId = "<sec:loggedInUserInfo field='id'/>";
        window.pushNotificationIcon = tractionWebRoot + '/assets/skins/logo/${grailsApplication.config.getProperty("traction.skin")}.png';

        function navigateToVehicleCreate() {
            window.location.href = tractionWebRoot + '/vehicle/create';
        }

        $(document).ready(function () {
            if (!Push.Permission.has()) {
                Push.Permission.request(function () {
                    console.log("Push Notifications Allowed");
                    Push.create("<g:message code="pushjs.allowed.title" />", {
                        body: "<g:message code="pushjs.allowed.body" />",
                        icon: window.pushNotificationIcon,
                        onClick: function () {
                            window.focus();
                            this.close();
                        }
                    });
                }, function () {
                    console.log("Push Notifications Blocked")
                });
            }

            var nextMaintenanceTime = '${conf.getNextMaintenanceTime()}';
            if (nextMaintenanceTime) {
                $('#maintenanceDiv').html('<g:message code="next.maintenance.time"/> ' + nextMaintenanceTime).show();
                setInterval(function () {
                    $('#maintenanceDiv').toggleClass('maintenance-color-1 maintenance-color-2');
                }, 800);
            }
        });

        $(function () {
            <g:set var="isInChatWidgetGroupOrFacebookPage" value="${user.isInChatWidgetGroupOrFacebookPage()}"/>
            <g:if test="${isInChatWidgetGroupOrFacebookPage}">
            <g:if test="${user.statusChat == traction.security.User.StatusChat.AVAILABLE}">
            TractionWebSocket.addHandler('/topic/newchat', function (message) {
                var listUsers = JSON.parse(message.body);
                if (listUsers.includes(parseInt(curUserId))) {
                    TopBar.updateChatMenu();
                    menuCounter.get(true);
                    try {
                        document.getElementById("idNotifSound").play();
                    } catch (err) {
                    }
                }
            });
            </g:if>
            </g:if>
        });
    </script>
    <g:layoutHead/>
    <script type="text/javascript">
        (function (w, d, u) {
            w.$productFruits = w.$productFruits || [];
            w.productFruits = w.productFruits || {};
            w.productFruits.scrV = '2';
            let a = d.getElementsByTagName('head')[0];
            let r = d.createElement('script');
            r.async = 1;
            r.src = u;
            a.appendChild(r);
        })(window, document, 'https://app.productfruits.com/static/script.js');
    </script>
    <script type="text/javascript">
        var tractionEnv = '${grailsApplication.config.getProperty("traction.env")}';
        // Fait en sorte que si plusieurs personnes utilisent le user
        // sur des PC different, ca donne les demo product fruit a tous.
        var demoUuid = '';
        if (tractionEnv == 'demo') {
            demoUuid = UserStorage.get('demoUuid');
            if (!demoUuid) {
                demoUuid = '-' + Date.now();
                UserStorage.set('demoUuid', demoUuid);
            }
        }
        $productFruits.push(['init', 'Lz0VDyjsXuIUhKgy', '${locale}', {
            username: '${grailsApplication.config.getProperty("traction.mobile.key")}-${user.id}' + demoUuid,
            firstname: '${user.firstname}',
            lastname: '${user.lastname}',
            props: {
                host: window.location.host,
                mobileKey: '${grailsApplication.config.getProperty("traction.mobile.key")}',
                tractionEnv: tractionEnv,
                userGroup: '${user.userGroup?.name()}'
            }
        }]);
    </script>
</head>

<body data-container="body">
<div id="bigLoader" style="display: none;"></div>
<div id="bigLoaderBg" style="display: none;"></div>

<div class="menu-wrapper">

    <div class="custom-sidebar collapsed bg-emphasis">
        <div class="logo-container bg-emphasis">
            <div id="logoLoader" style="display: none;"></div>

            <div class="logo cursor" onclick="location.href = tractionWebRoot;">
                <asset:image id="logoImage" src="/skins/logo/Traction_1ligne.svg" style="height: 20px;"/>
            </div>

            <div class="logosmall cursor" onclick="location.href = tractionWebRoot;">
                <asset:image id="logoImage" src="/skins/logo/LogoTractionDKsmall_2024.svg" class="w-100"/>
            </div>
        </div>

        <div class="pt-4">
%{--            <div class="custom-section-title text-white f-16  line-height-19 px-2 mb-2"><g:message code="mainmenu.general"/></div>--}%
            <a class="custom-nav-link  p-2 mb-2" href="javascript:void(0);"
               onclick="SideBar.open('sidemenu-search', this)">
                <i class="fas fa-search custom-sideIcon" data-placement="right" title="<g:message code="mainmenu.search"/>"></i>
                <div class="custom-menu-text line-height-19" style="width: 164px;"><g:message code="mainmenu.search"/></div>
                <i class="fa-regular fa-chevron-right custom-menu-arrow"></i>
            </a>
            <a class="custom-nav-link  p-2 mb-2" href="javascript:void(0);"
               onclick="SideBar.open('sidemenu-mytaggedmenu', this)">
                <i class="fas fa-thumbtack custom-sideIcon" data-placement="right" title="<g:message code="mainmenu.mytaggedmenu"/>"></i>

                <span class="custom-menu-text"><g:message code="mainmenu.mytaggedmenu"/></span>
                <i class="fa-regular fa-chevron-right custom-menu-arrow"></i>
            </a>
        </div>

        <g:set var="menuLevel1Items" value="${menuService.getMenuLevel1Items() as List<traction.menu.MenuLevel1>}"/>
        <g:each var="menuLevel1" in="${menuLevel1Items}" status="i">
            <g:if test="${menuLevel1.separator}">
                <hr class="my-4">
            </g:if>
            <g:elseif test="${menuLevel1.isVisible()}">
                <g:set var="nextItem" value="${i < menuLevel1Items.size() - 1 ? menuLevel1Items[i + 1] : null}"/>
                <g:set var="isCurrentPage" value="${menuService.isCurrentUrlInMenuLevel1(menuLevel1)}"/>
                <a class="custom-nav-link p-2 ${nextItem?.separator ? '' : 'mb-2'} ${isCurrentPage ? 'currentpage' : ''} "
                   href="javascript:void(0);" onclick="SideBar.open('${menuLevel1.id}', this);
                setActiveClass();">
                    <i class="${menuLevel1.icon} custom-sideIcon" data-popper-placement="right" title="<g:message code="${menuLevel1.message}"/>"></i>

                    <div class="custom-menu-text">
                        <g:message code="${menuLevel1.message}"/>
                        <g:if test="${menuLevel1.menuCounter}">
                            <span menu-counter="${menuLevel1.menuCounter}"
                                  class="badge bg-primary rounded-pill mx-2 f-12 py-1 px-2"></span>
                        </g:if>
                    </div>
                    <i class="fa-regular fa-chevron-right custom-menu-arrow"></i>
                </a>
            </g:elseif>
        </g:each>
%{--        <div class="d-flex">--}%
%{--            <div class="form-check" hidden="hidden">--}%
%{--                <input class="form-check-input border-primary" type="checkbox" role="switch" id="toggleDarkMode">--}%
%{--            </div>--}%
%{--            <button class="btn btn-primary d-flex justify-content-center align-items-center" id="darkModeBtn" style="width: -webkit-fill-available;">--}%
%{--                <i id="themeIconmoon" class="fas fa-moon"></i>--}%
%{--                <span id="themeText" class="ms-2 text-truncate" style="line-height: 16px;" ><g:message code="mainmenu.darkmode"/></span>--}%
%{--            </button>--}%
%{--        </div>--}%
    </div>

    <div id="sidemenu-search" class="submenu collapsed">
        <div class="d-flex justify-content-between align-items-center fixed-subcontainer">
            <div class="submenu-title">
                <g:message code="search"/>
            </div>
            <a href="javascript:void(0);" onclick="SideBar.close()" style="height: 18px">
                <i class="fa-light fa-circle-arrow-left icon-custom-color"></i>
            </a>
        </div>
        <hr style="margin-top:56px;">

    <div class="px-4 pt-4 pb-5">
        <div class="nav nav-pills w-100 d-flex gap-2 flex-nowrap" id="search-type-tabs" role="tablist">
            <a class="nav-link-search rounded-pill flex-fill text-center active"
               id="client-tab"
               data-bs-toggle="pill"
               href="#client"
               role="tab"
               aria-controls="client"
                data-type="contact"
               aria-selected="true"
               onclick="setSearchType('client')"
               data-button-icon="fa-user"
               data-button-url="/client/modalNew"
               data-button-title="<g:message code='contact.create'/>">
                Client
            </a>

            <a class="nav-link-search rounded-pill flex-fill text-center"
               id="entreprise-tab"
               data-bs-toggle="pill"
               href="#entreprise"
               role="tab"
               aria-controls="entreprise"
               data-type="entreprise"
               aria-selected="false"
               onclick="setSearchType('entreprise')"
               data-button-icon="fa-building"
               data-button-url="/client/modalNew?enterprise=true"
               data-button-title="<g:message code='contact.create'/>">
                Entreprise
            </a>

            <a class="nav-link-search rounded-pill flex-fill text-center"
               id="produits-tab"
               data-bs-toggle="pill"
               href="#produits"
               role="tab"
               aria-controls="produits"
               data-type="produits"
               aria-selected="false"
               onclick="setSearchType('produits');"
               data-button-icon="fa-box"
               data-button-url="navigateToVehicleCreate();"
               data-button-title="<g:message code='produits.create'/>">
                <g:message code="search.vehicle"/>
            </a>

                <a class="nav-link-search rounded-pill flex-fill text-center"
                   id="bo-tab"
                   data-bs-toggle="pill"
                   href="#bo"
                   role="tab"
                   aria-controls="bo"
                   aria-selected="false"
                   onclick="setSearchType('bo');
                   hideButton();"
                   data-button-icon=""
                   data-button-url=""
                   data-button-title="">
                    R.O
                </a>
            </div>

            <div class="mt-3 mb-1 row" style="--bs-gutter-x: 0.5rem !important;">
                <div class="col">
                    <div id="search-client" class="search-input">
                        <search:clientSearch id="search-contact-input" type="text"
                                             class="form-control rounded-0 rounded-start w-100" callback="selectClient"
                                             placeholder="${g.message(code: 'contact.search')}"/>
                    </div>

                    <div id="search-entreprise" class="search-input" style="display: none;">
                        <search:clientEnterpriseSearch id="search-entreprise-input" type="text"
                                                       class="form-control rounded-0 rounded-start w-100"
                                                       callback="selectEntreprise"
                                                       placeholder="${g.message(code: 'entreprise.search')}"/>
                    </div>

                    <div id="search-produits" class="search-input" style="display: none;">
                        <search:vehicleWithClient id="search-produits-input" type="text" class="form-control rounded w-100"
                                                  callback="selectVehicle" menusearch="true" include-client="true"
                                                  placeholder="${g.message(code: 'vehicle.search')}"/>
                    </div>

                    <div id="search-bo" class="search-input" style="display: none;">
                        <search:workOrder id="search-bo-input" type="text" class="form-control rounded w-100"
                                          callback="selectClientWorkOrder"
                                          placeholder="${g.message(code: 'workorder.search')}"/>
                    </div>
                </div>

            <div class="col-auto d-flex align-items-start">
                <button id="dynamic-button"
                        class="btn btn-primary d-flex align-items-center justify-content-center rounded"
                        style="height: 39px; width: 39px;"
                        onclick="TractionModal.show({url: '/client/modalNew', data: {clientValue: $('#search-contact-input').val()}});">
                    <i class="fa fa-user" style="font-size: 16px; padding-right:1px;"></i>
                    +
                </button>
            </div>
        </div>
    </div>
    <hr class="mx-4">

        <div id="clientNavigationHistory">
            <div class="f-16 text-white mt-5 mb-3  px-4" role="presentation">
                <span><g:message code="client.navigationhistory"/></span>
            </div>
            <ul class="nav nav-justified mb-3 px-4 gap-4">
                <li>
                    <a class="nav-link active pt-0" style="width:123px; text-align: center;" data-bs-toggle="tab"
                       onclick="NavigationHistory.tabAll()" href="#navigationHistoryAll">
                        <g:message code="navigationhistory.type.all"/>
                    </a>
                </li>
                <li>
                    <a class="nav-link pt-0" data-bs-toggle="tab" style="width:123px; text-align: center;"
                       onclick="NavigationHistory.tabSearched()" href="#navigationHistorySearched">
                        <g:message code="navigationhistory.type.searched"/>
                    </a>
                </li>
            </ul>

            <div class="tab-pane collapse active show mx-4" id="navigationHistoryAll"></div>

            <div class="tab-pane collapse mx-4" id="navigationHistorySearched"></div>
        </div>
    </div>

    <div id="sidemenu-mytaggedmenu" class="submenu collapsed">
        <div class="d-flex justify-content-between align-items-center fixed-subcontainer">
            <div class="submenu-title">
                <g:message code="main.pinmenu"/>
            </div>
            <a href="javascript:void(0);" style="height: 18px" onclick="SideBar.close()">
                <i class="fa-light fa-circle-arrow-left icon-custom-color"></i>
            </a>
        </div>

        <hr style="margin-top:56px;">

        <div class="mt-3" id="submenuContainer">
        </div>
    </div>

    <g:set var="pinnedMenuIds" value="${menuService.getPinnedMenuIds() as List<String>}"/>
    <g:each var="menuLevel1" in="${menuLevel1Items}">
        <g:if test="${menuLevel1.isVisible()}">
            <div id="${menuLevel1.id}" class="submenu collapsed">
                <div class="d-flex justify-content-between align-items-center fixed-subcontainer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="submenu-title">
                            <g:message code="${menuLevel1.message}"/>
                        </div>
                        <a href="javascript:void(0);" style="height: 18px" onclick="SideBar.close()">
                            <i class="fa-light fa-circle-arrow-left icon-custom-color"></i>
                        </a>
                    </div>
                </div>

                <div style="margin-top: 56px">
                    <g:each var="menuLevel2" in="${menuLevel1.items}" status="index">
                        <g:if test="${menuLevel2.isVisible(menuLevel1.permissions)}">
                            <div class="submenu-group-title f-16 mb-3" role="presentation">
                                <g:if test="${index == 0}">
                                    <hr style="margin:0;">

                                    <div class="mt-3 px-4 line-height-19">
                                        <span><g:message code="${menuLevel2.message}"/></span>
                                    </div>
                                </g:if>
                                <g:else>
                                    <div class="px-4 line-height-19">
                                        <hr class="pb-5  mt-4 mb-0">
                                        <span><g:message code="${menuLevel2.message}"/></span>
                                    </div>
                                </g:else>
                            </div>
                            <g:each var="menuLevel3" in="${menuLevel2.items}">
                                <g:if test="${menuLevel3.isVisible(menuLevel1.permissions)}">
                                    <div class="submenu-subtitle d-flex flex-row line-height-19 py-2 mb-2 px-4 ${pinnedMenuIds.contains(menuLevel3.id) ? 'submenu-pinned' : ''} ${menuService.isCurrentUrlInMenuLevel3(menuLevel3) ? 'active' : ''}"
                                         data-menu-id="${menuLevel3.id}">
                                        <div class="flex-grow-1">
                                            <a class="submenu-subtitle-link" href="${menuLevel3.href}">
                                                <g:message code="${menuLevel3.message}"/>
                                                <g:if test="${menuLevel3.menuCounter}">
                                                    <span menu-counter="${menuLevel3.menuCounter}" class="badge bg-primary rounded-pill mx-2 f-12 py-1 px-2"></span>
                                                </g:if>
                                            </a>
                                        </div>

                                        <div>
                                            <g:if test="${menuLevel3.id?.contains('workflow.index')}">
                                                <i onclick="openWorkflowDataList(${menuLevel3.id?.split('\\.')?.last()})"
                                                   class="fa-regular fa-list submenu-subtitle-btn  submenu-btn me-2"
                                                   role="button"></i>
                                                <i onclick="openWorkflowCalendar(${menuLevel3.id?.split('\\.')?.last()})"
                                                   class="fa-regular fa-calendar-clock submenu-subtitle-btn submenu-btn me-2"
                                                   role="button"></i>
                                            </g:if>
                                            <i class="fa-regular fa-light fa-thumbtack pin-icon"
                                               onclick="SideBar.togglePinnedMenu(event)" role="button"></i>
                                        </div>
                                    </div>
                                </g:if>
                            </g:each>
                            <div class="mb-3"></div>

                        </g:if>
                    </g:each>
                </div>
            </div>
        </g:if>
    </g:each>

</div>
<div class="page-wrapper">
    <div class="container-fluid m-0 p-0 w-100">
        <div class="row g-0">
            <div id="main-traction-navbar" class="navbar nav border-bottom justify-content-end flex-nowrap">
                <div class="flex-grow-1 d-flex py-3 px-4 nav-tabs border-0">
                    <div class="nav-item dropdown">
                        <a id="top-bar-callnotes"
                           class="nav-link rounded-0 text-body p-0 pe-5 d-flex align-items-center justify-content-center"
                           style="height: 20px;" href="javascript:void(0);" data-bs-toggle="dropdown"
                           onclick="toggleChevron(this);
                           toggleDropdown(this);">
                            <span menu-counter="notesLate" class="top-bar-badge bg-danger px-2 f-12"
                                  style="color: white !important;"></span>
                            <span menu-counter="notesSoon" class="top-bar-badge bg-warning px-2 f-12"></span>
                            <span menu-counter="notesAlluser" class="top-bar-badge bg-primary px-2 f-12"></span>
                            <span class="d-inline d-xl-none f-16" style="line-height: 14px;">
                                <i class="fa-regular fa-pen-field" data-toggle="tooltip"
                                   title="<g:message code='callmanager.notes'/>" style="padding-right: 4px;"></i>
                            </span>
                            <span class="d-none d-xl-inline f-16 me-2" style="line-height: 14px;">
                                <g:message code="callmanager.notes"/>
                            </span>
                            <i class="mdi mdi-chevron-down chevron-icon f-12" style="line-height: 14px;"></i>
                        </a>

                        <div class="dropdown-menu custom-dropdown" aria-labelledby="top-bar-callnotes"
                             style="left:20px; min-width: 900px; border-radius: 4px;">
                        </div>
                    </div>

                    <div class="nav-item dropdown">
                        <a id="top-bar-communications"
                           class="nav-link rounded-0 text-body p-0 pe-5 d-flex align-items-center justify-content-center"
                           style="height: 20px;" href="javascript:void(0);" onclick="toggleChevron(this);
                        toggleDropdown(this);"
                           data-bs-toggle="dropdown">
                            <span menu-counter="unreadCommunication" class="top-bar-badge bg-danger px-2 f-12 text-white"></span>
                            <span class="d-inline d-xl-none">
                                <i class="fa-regular fa-comments" data-toggle="tooltip"
                                   title="<g:message code='communications'/>"></i>
                            </span>
                            <span class="d-none d-xl-inline f-16 me-2" style="line-height: 14px;">
                                <g:message code="communications"/>
                            </span>
                            <i class="mdi mdi-chevron-down chevron-icon f-12" style="line-height: 14px;"></i>
                        </a>

                        <div class="dropdown-menu custom-dropdown"
                             style="min-width: 900px; left:20px; border-radius: 4px;"
                             aria-labelledby="top-bar-communications">
                        </div>
                    </div>

                    <div class="nav-item dropdown">
                        <a id="top-bar-tasks"
                           class="nav-link rounded-0 p-0 pe-5 text-body d-flex align-items-center justify-content-center"
                           style="height: 20px;" href="javascript:void(0);" onclick="toggleChevron(this);
                        toggleDropdown(this);" data-bs-toggle="dropdown">
                            <span menu-counter="taskLate"
                                  class="top-bar-badge bg-danger px-2 f-12 d-flex align-items-center justify-content-center"
                                  style="color: white !important;"></span>
                            <span menu-counter="taskToday"
                                  class="top-bar-badge bg-warning px-2 f-12 d-flex align-items-center justify-content-center"></span>
                            <span class="d-inline d-xl-none d-flex align-items-center ">
                                <i class="fa-regular fa-list-check ps-0" data-toggle="tooltip"
                                   style="padding-right: 4px;" title="<g:message code='tasks'/>"></i>
                            </span>
                            <span class="d-none d-xl-inline f-16 d-flex align-items-center me-2"
                                  style="line-height: 14px;">
                                <g:message code="tasks"/>
                            </span>

                            <i class="mdi mdi-chevron-down chevron-icon f-12 d-flex align-items-center"
                               style="line-height: 14px;"></i>
                        </a>

                        <div class="dropdown-menu custom-dropdown p-0"
                             style="min-width: 1250px; left: 20px; border-radius: 4px;" aria-labelledby="top-bar-tasks">
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a id="top-bar-workflow"
                           class="nav-link rounded-0 p-0 pe-5 text-body d-flex align-items-center justify-content-center"
                           style="height: 20px;" href="javascript:void(0);" onclick="toggleChevron(this);
                        toggleDropdown(this);" data-bs-toggle="dropdown">
                            <span menu-counter="noEndingWorkflowDatasCount"
                                  class="top-bar-badge bg-success px-2 f-12 d-flex align-items-center justify-content-center"
                                  style="color: white !important;"></span>
                            <span class="d-inline d-xl-none d-flex align-items-center ">
                                <i class="fas fa-clipboard-list ps-0" data-toggle="tooltip"
                                   style="padding-right: 4px;" title="<g:message code='main.workflow'/>"></i>
                            </span>
                            <span class="d-none d-xl-inline f-16 d-flex align-items-center me-2"
                                  style="line-height: 14px;">
                                <g:message code="main.workflow"/>
                            </span>

                            <i class="mdi mdi-chevron-down chevron-icon f-12 d-flex align-items-center"
                               style="line-height: 14px;"></i>
                        </a>

                        <div class="dropdown-menu custom-dropdown p-0"
                             style="min-width: 1250px; left: 20px; border-radius: 4px;" aria-labelledby="top-bar-workflow">
                        </div>
                    </div>
                    <div class="nav-item dropdown">
                        <a id="top-bar-opportunity"
                           class="nav-link rounded-0 p-0 pe-5 text-body d-flex align-items-center justify-content-center"
                           style="height: 20px;" href="javascript:void(0);" onclick="toggleChevron(this);
                        toggleDropdown(this);" data-bs-toggle="dropdown">
                            <span menu-counter="opportunityCount"
                                  class="top-bar-badge bg-success px-2 f-12 d-flex align-items-center justify-content-center"
                                  style="color: white !important;"></span>
                            <span class="d-inline d-xl-none d-flex align-items-center ">
                                <i class="fas fa-car ps-0" data-toggle="tooltip"
                                   style="padding-right: 4px;" title="<g:message code="opportunity"/>"></i>
                            </span>
                            <span class="d-none d-xl-inline f-16 d-flex align-items-center me-2"
                                  style="line-height: 14px;">
                                <g:message code="opportunity"/>
                            </span>

                            <i class="mdi mdi-chevron-down chevron-icon f-12 d-flex align-items-center"
                               style="line-height: 14px;"></i>
                        </a>

                        <div class="dropdown-menu custom-dropdown p-0"
                             style="min-width: 1250px; left: 20px; border-radius: 4px;" aria-labelledby="top-bar-opportunity">
                        </div>
                    </div>
                    <g:if test="${user.isInPoolAssignmentRule()}">
                        <div class="nav-item dropdown">
                            <a id="top-bar-pool"
                               class="nav-link rounded-0 p-0 text-body d-flex align-items-center justify-content-center"
                               style="height: 20px;" href="javascript:void(0);" onclick="toggleChevron(this);
                            toggleDropdown(this);" data-bs-toggle="dropdown">
                                <span menu-counter="pool"
                                      class="top-bar-badge bg-success text-white px-2 f-12 d-flex align-items-center justify-content-center"></span>
                                <span class="d-inline d-xl-none d-flex align-items-center ">
                                    <i class="fa-regular fa-swimming-pool ps-0" data-toggle="tooltip"
                                       style="padding-right: 4px;" title="<g:message code='opportunity.pool'/>"></i>
                                </span>
                                <span class="d-none d-xl-inline f-16 d-flex align-items-center me-2"
                                      style="line-height: 14px;">
                                    <g:message code="opportunity.pool"/>
                                </span>

                                <i class="mdi mdi-chevron-down chevron-icon f-12 d-flex align-items-center"
                                   style="line-height: 14px;"></i>
                            </a>

                            <div class="dropdown-menu custom-dropdown"
                                 style="min-width: 750px; left: 20px; border-radius: 4px;"
                                 aria-labelledby="top-bar-pool">
                            </div>
                        </div>
                    </g:if>
                </div>

                <div class="d-flex align-content-center py-3 px-4 gap-4 nav-tabs border-0">
                    <div class="nav-item align-content-center">
                        <a class="nav-link text-body d-flex justify-content-center align-items-center p-0"
                           href="javascript:void(0);"
                           onclick="TractionModal.show({
                               url: '/menu/activeCallSidePanel',
                               sidePanel: true
                           });">
                            <i class="fa-regular fa-phone align-content-center f-20" style="color: #80C2F7;"></i>
                            <span menu-counter="activeCalls" class="top-bar-badge bg-success text-white ms-1 me-0 px-2 f-12"></span>
                        </a>
                    </div>
                        <div class="nav-item dropdown align-content-center">
                            <a id="top-bar-chat"
                               class="nav-link text-body d-flex justify-content-center align-items-center p-0"
                               href="javascript:void(0);"
                               data-bs-toggle="dropdown">
                                <i class="fa-regular fa-comment f-20" style="color: #80C2F7;" data-toggle="tooltip"
                                   title="<g:message code='client.chats'/>"></i>
                                <span menu-counter="newChat" class="top-bar-badge bg-danger text-white ms-1 me-0 px-2 f-12"></span>
                            </a>

                            <div class="dropdown-menu  m-0 p-0" aria-labelledby="top-bar-chat"
                                 style="position:fixed; width:467px; right: 24px; top: 56px; min-height: 250px; overflow: scroll; box-shadow: -4px 4px 4px 0px rgba(0, 0, 0, 0.15); border-radius: 4px;">
                            </div>
                        </div>
                    <div class="nav-item align-content-center">
                        <a class="nav-link text-body d-flex justify-content-center align-items-center p-0"
                           href="javascript:void(0);" onclick="NotificationMenu.open();">
                            <div class="d-flex justify-content-center align-content-center">
                                <i id="notificationIcon" class="fa-regular fa-bell f-20" style="color:#B3B3B3;"></i>
                            </div>
                            <span id="notificationCount" class="top-bar-badge text-white bg-danger me-0 ms-1 px-2 f-12">${user.getNotificationCount() ?: ''}</span>
                        </a>
                    </div>

                    <div class="vr"></div>

                    <div class="nav-item dropdown">
                        <a id="top-bar-status"
                           class="nav-link text-body d-flex justify-content-center align-items-center p-0"
                           href="javascript:void(0);" data-bs-toggle="dropdown" onclick="toggleChevron(this)">
                            <span id="usersStatusIcon" style="margin-right: 4px; color:${user?.status?.iconColor}"
                                  class="fa-solid ${user?.status?.icon}">
                            </span>
                            <span id="usersStatusMessage" class="me-2 text-black f-16">
                                <g:message code="${user?.status?.message}"/>
                            </span>
                            <i class="mdi mdi-chevron-down chevron-icon f-12 text-black"></i>
                        </a>

                        <div class="dropdown-menu p-0" aria-labelledby="top-bar-status"
                             style="width: 15em; right:218px; top:62px;">
                        </div>

                    </div>

                    <div class="nav-item">
                        <a class="nav-link text-body d-flex align-items-center px-0" style="height: 24px;"
                           href="javascript:void(0);" onclick="TractionModal.show({
                            url: '/menu/userSidePanel',
                            sidePanel: true
                        });">
                            <communication:avatar user="${user}" class="communication-avatar-sm"/>
                            <i class="mdi mdi-chevron-down ms-1"></i>
                        </a>
                    </div>

                    <div class="vr" style="margin-right: 100px;"></div>

                </div>
            </div>

            <div class="col-12 tractionBody" style="margin-top: 51px;">
                <g:layoutBody/>
                %{--                <div id="copyright" class="col-12  border-top p-4 pt-5 f-12 text-body-tertiary">--}%
                %{--                    <p class="text-center"><g:message code="${grailsApplication.config.getProperty("traction.skin")}.copyright"/></p>--}%
                %{--                </div>--}%
            </div>
        </div>
    </div>
</div>

<div id="modalcontainer">
    <div class="modal fade" id="containerModal" style="z-index: 10000 !important;" tabindex="-1" role="dialog"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content" id="modalbody">
            </div>
        </div>
    </div>
</div>

<div class="modal" id="traction-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-body"></div>
    </div>
</div>

<div class="modal fade" id="modalConfirmDelete" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document"
         style="word-break: break-word; min-width: 750px;">
        <div class="modal-content">
            <div class="modal-header justify-content-center d-none">
            </div>

            <div class="modal-body text-center lh-1 pt-5 mt-4 px-5 mx-4 pb-0">
                <i class="fa-light fa-circle-question fa-3x mb-4 text-info-secondary-emphasis"></i>

                <p id="modal-confirm-title" class="h4 text-uppercase mb-2"></p>

                <p id="modal-confirm-text" class="mb-5 text-body-emphasis"></p>
            </div>

            <div class="modal-footer justify-content-center flex-nowrap pb-5 mb-4 mx-4 px-5 pt-0">
                <button type="button" id="modalConfirmNo"
                        class="btn btn-secondary bg-body fw-bold text-primary border-primary w-50"
                        style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"><g:message
                        code="default.button.no"/></button>
                <button type="button" id="modalConfirmYes" class="btn btn-danger w-50 fw-bold"
                        style="box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);"><g:message
                        code="default.button.yes"/></button>
                <button type="button" id="modalConfirmOk" class="btn btn-primary"><g:message
                        code="default.button.ok"/></button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalZohoTraining" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document"
         style="word-break: break-word;">
        <div class="modal-content">
            <h2 class="modal-header fw-bold"></h2>

            <div class="modal-body"></div>

            <div class="modal-footer flex-center">
                <button type="button" class="btn btn-default" data-bs-dismiss="modal"><g:message
                        code="close"/></button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modalChangelog" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document"
         style="word-break: break-word;">
        <div class="modal-content">
            <h2 class="modal-header fw-bold"><g:message code="changelog"/></h2>

            <div class="modal-body">
            <div class="text-changelog">
                <g:if test="${api.getLastChangelog() instanceof LinkedHashMap}">
                    <g:set var="changelogVersion" value="${conf.get("SERVER", "server.traction.changelog.version")}"/>
                    <g:set var="changelogList" value="${api.getLastChangelog().listChangelog}"/>
                    <g:if test="${cookie.getCookie("changelog-size") && cookie.getCookie("changelog-version")}">
                        <g:if test="${cookie.getCookie("changelog-size") != api.getLastChangelog().listChangelog.size() && cookie.getCookie('changelog-version') != api.getLastChangelog().version && api.getLastChangelog().lastViewChangelog > 0}">
                            <g:set var="changelogList"
                                   value="${api.getLastChangelog().listChangelog.toList().subList(0, api.getLastChangelog().lastViewChangelog)}"/>
                        </g:if>
                    </g:if>
                    <g:each var="textChangelog" status="i" in="${changelogList}">
                        <g:if test="${textChangelog.startsWith("## ")}">
                            <g:if test="${api.getLastChangelog().listChangelog[i - 1].startsWith("-----")}">
                                </ul>
                                </div>
                            </g:if>
                            <h2 class="mb-0 fw-bold">${textChangelog}</h2>
                            <div class="changelog-block">
                        </g:if>
                        <g:elseif test="${textChangelog.startsWith("- ") || textChangelog.startsWith("###")}">
                            <g:if test="${textChangelog.startsWith("###") || (textChangelog.startsWith("- ") && !api.getLastChangelog().listChangelog[i - 1].startsWith("- "))}">
                                <g:if test="${(textChangelog.startsWith("###") && api.getLastChangelog().listChangelog[i - 1].startsWith("- "))}">
                                    </ul>
                                </g:if>
                                <g:if test="${!textChangelog.startsWith("###")}">
                                    <ul>
                                </g:if>
                            </g:if>
                            <g:if test="${!textChangelog.startsWith("###")}">
                                <li>${textChangelog}</li>
                            </g:if>
                            <g:else>
                                <p>${textChangelog.replace('### ', "")}</p>
                            </g:else>
                        </g:elseif>
                    </g:each>
                </g:if>
            </div>
            </div>

            <div class="modal-footer flex-center">
                <button type="button" class="btn btn-default" data-bs-dismiss="modal"><g:message
                        code="close"/></button>
            </div>
        </div>
    </div>
</div>

<div id="chatBox" class="row fixed-bottom justify-content-end align-items-end" style="left: initial; margin-right: 5%;">
</div>
<audio controls id="idNotifSound" src="${assetPath(src: '/sound/point-blank-589.mp3')}" type="audio/mpeg" hidden>
</audio>
<g:if test="${notify}">
    <g:javascript>$.notify("${notify.text}", "${notify.type}", {hideDuration: 1000});</g:javascript>
</g:if>
<script type="text/javascript" id="zsiqchat">var $zoho = $zoho || {};
$zoho.salesiq = $zoho.salesiq || {
    widgetcode: "c21ed28b2ac06c7241b922474e4219f1b08dd72e9639ebf7554145e49503a1c2",
    values: {},
    ready: function () {
    }
};
var d = document;
s = d.createElement("script");
s.type = "text/javascript";
s.id = "zsiqscript";
s.defer = true;
s.src = "https://salesiq.zoho.com/widget";
t = d.getElementsByTagName("script")[0];
t.parentNode.insertBefore(s, t);</script>
<script>
    $zoho.salesiq.ready = function () {
        $zoho.salesiq.visitor.name('${user.getFullName()}');
        $zoho.salesiq.visitor.email('${raw(user.email)}');
        $zoho.salesiq.visitor.info({
            'Traction mobile key': '${grailsApplication.config.getProperty("traction.mobile.key")}',
            'Traction user id': '${user.id}'
        });
        $zoho.salesiq.visitor.id('${grailsApplication.config.getProperty("traction.mobile.key")}-${user.id}');
    }
</script>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-0606ET5KRS"></script>
<script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }

    gtag('js', new Date());

    gtag('config', 'G-0606ET5KRS');
    $(document).ready(function () {
        <g:if test="${api.getLastChangelog() instanceof LinkedHashMap}">
        if (Cookies.get('changelog-size') != ${api.getLastChangelog().listChangelog.size()} && Cookies.get('changelog-version') != '${api.getLastChangelog().version}') {
            Cookies.set('changelog-size', '${api.getLastChangelog().listChangelog.size()}', {expires: 100000});
            Cookies.set('changelog-version', '${api.getLastChangelog().version}', {expires: 100000});
            $('#modalChangelog').modal('show');
        }
        </g:if>
    });
</script>
</body>
</html>
