<!doctype html>
<html lang="en" class="no-js">
<head>
    <asset:stylesheet src="lib/mdi/css/materialdesignicons.css"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="robots" content="noindex">
    <title>
        <g:layoutTitle default="Traction"/>
    </title>

    <!--Let browser know website is optimized for mobile-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="icon" href="${resource(dir: 'assets', file: 'favicon.png', absolute: true)}" type="image/png" />

    <link rel="stylesheet" href="${resource(dir: 'assets/layouts', file: 'mainTraction.css', absolute: true)}" />
    <script src="${resource(dir: 'assets/lib', file: 'jquery-3.7.1.min.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib/bootstrap-5.3.2/dist/js', file: 'bootstrap.bundle.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib', file: 'bootstrap-multiselect.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib/notify', file: 'notify.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib', file: 'jquery-sortable.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib', file: 'jquery-draggable.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib/handlebars', file: 'handlebars.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib/favico', file: 'favico-0.3.10.min.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib/pushjs', file: 'push.min.js', absolute: true)}"></script>
    <script src="${resource(dir: 'assets/lib', file: 'js.cookie.js', absolute: true)}"></script>

    <style>
    .tooltip-inner {
        max-width: none !important;
    }
    </style>

    <script type="text/javascript">
        var tractionWebRoot = '<g:createLink absolute='true' uri=''/>';
    </script>
    <g:layoutHead/>
</head>

<body>

<g:layoutBody/>

<div style="display: none" class="bottom">
    <div class="col-12  border p-0">
        <p style="font-size: 12px;" class="text-center"><g:message code="${grailsApplication.config.getProperty("traction.skin")}.copyright"/></p>
    </div>
</div>
</body>
</html>
