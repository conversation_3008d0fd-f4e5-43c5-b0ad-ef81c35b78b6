<asset:javascript src="cardType/cardType.js" />
<%@ page import="traction.I18N" %>
<div class="d-flex align-items-center mb-3">
    <span class="f-16 text-emphasis me-3 line-height-19" style="min-width:153px;"><g:message code="cardtype"/></span>
    <g:select name="cardType.title" id="cardTypeSelect" from="${cardTypes}" noSelection="${[ '0' : g.message(code: 'cardtype.create') ]}" optionValue="title" optionKey="id" value="id" onchange="updateSelectedCardType()" class="form-select"/>
</div>
<div class="d-flex align-items-center mb-3">
    <span class="f-16 text-emphasis me-3 line-height-19" style="min-width:153px;"><g:message code="icon.title"/></span>
    <input type="text" class="form-control" placeholder="${I18N.m('text.input.placeholder')}" id="cardTypeTitle">
</div>
<div class="d-flex align-items-center mb-3">
    <span class="f-16 text-emphasis me-3 line-height-19" style="min-width:153px;"><g:message code="icon.color"/></span>
    <input type="text" class="form-control jscolor border-end-0-" style="border-top-right-radius: 0; border-bottom-right-radius: 0;"  id="cardTypeColor">
    <span class="input-group-text ps-0 bg-white border-start-0 text-muted"
          style="padding-right:12px !important; border-bottom-left-radius: 0px; border-top-left-radius: 0px; height: 38px;"><i class="fa-regular fa-palette text-primary f-16"></i></span>
</div>
<div class="d-flex gap-2 align-items-center justify-content-end" style="border-color: #B3DBFB !important;">
    <button class="btn btn-outline-primary shadow-sm fw-bold" onclick="deleteCardType()"><g:message code="default.button.delete.label"/></button>
    <button class="btn btn-primary shadow-sm fw-bold" onclick="saveCardType()"><g:message code="default.button.save.label"/></button>
</div>

