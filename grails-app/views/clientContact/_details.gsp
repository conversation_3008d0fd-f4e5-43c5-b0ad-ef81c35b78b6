<h4 class="mb-5 text-uppercase"><g:message code="client.contact.details"/></h4>

<div class="row">
    <div class="col-lg-12 col-xxl-8">
        <div class=" border rounded p-4 mb-5">
            <h5 id="main-contact" class="text-uppercase"><g:message code="client.contact.principal"/></h5>
            <contactInformationTagLib:mainContact client="${client}"/>
        </div>

        <g:if test="${client?.isEnterprise()}">
            <h5 class="text-uppercase">
                <g:message code="enterprisecontacts"/>
                <a onclick="ClientIndex.EnterpriseContact.create(${client?.id});" class="icon-link icon-link-hover icon-link-hover-grow ms-2 fs-4" role="button">
                    <i class="bi fa-regular fa-circle-plus"></i>
                </a>
            </h5>
            <div id="enterpriseContacts" class="row">
                <g:each var="enterpriseContact" in="${client?.asEnterprise()?.contacts}">
                    <contactInformationTagLib:enterpriseContact client="${client}"
                                                                enterpriseContact="${enterpriseContact}"/>
                </g:each>
            </div>
        </g:if>
        <g:if test="${client}">
            <div class=" border rounded p-4 mb-5">
                <h5 class="text-uppercase mb-3"><g:message code="client.interests"/></h5>
                <contactInformationTagLib:interests client="${client}"/>
            </div>
        </g:if>
    </div>

    <div class="col-lg-12 col-xxl-4">
        <g:if test="${client}">
            <h5 class="d-flex align-items-center mb-3">
                <p class="text-uppercase mb-0"><g:message code="${client.class.getSimpleName()}.relatedClients"/></p>

                <a onclick="ClientIndex.RelatedClient.search(${client?.id});" class="btn btn-primary fs-6 ms-auto" role="button">
                    <i class="bi fa-solid fa-circle-plus me-2"></i>
                    <g:message code="${client.class.getSimpleName()}.relatedClients.associate"/>
                </a>
            </h5>
            <div id="relatedClients" class="row pb-2">
                <g:each var="relationship" in="${relationships}">
                    <g:include controller="relationship" action="card" params="[id: relationship.id, client: client?.id]"/>
                </g:each>
            </div>
        </g:if>
    </div>
</div>

<div class="row" style="padding-bottom: 20px;">
    <g:if test="${client}">
        <div class="col-lg-12 col-xxl-8">
            <h5 class="mb-3 d-flex align-items-center">
                <p class="text-uppercase mb-0 f-18"><g:message code="client.contactInformations"/></p>
                <a onclick="ClientIndex.AdditionalContact.create(${client?.id});" class="btn btn-primary fs-6 ms-auto" role="button">
                    <i class="bi fa-solid fa-circle-plus me-2"></i>
                    <g:message code="client.contactInformations.add"/>
                </a>
            </h5>
            <div id="contactInformationRow" class="row">
                <g:each var="contactInformation" in="${client?.contactInformations.sort {a,b -> b.id.compareTo(a.id) }}">
                    <contactInformationTagLib:additionalContact contactInformation="${contactInformation}"/>
                </g:each>
            </div>
        </div>
    </g:if>
</div>