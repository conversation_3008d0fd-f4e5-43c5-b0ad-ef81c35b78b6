<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="support"/></title>
</head>

<body>
<div class="m-4">
    <div class="clearfix">
        <h2 class="w-100"><g:message code="support"/></h2>
    </div>
</div>

<div class="m-4">
    <ul class="list-group">
        <li class="list-group-item list-group-item-action cursor d-flex align-items-center" onclick="openChat()">
            <g:message code="support.any.agent"/>
            <button type="button" class="btn btn-primary float-right ms-auto">
                <g:message code="support.startchat"/>
            </button>
        </li>
        <g:each var="agent" in="${agents}">
            <li class="list-group-item list-group-item-action cursor d-flex align-items-center" onclick="openChat('${agent.name}', '${agent.email}')">
                ${agent.name}
                <button type="button" class="btn btn-primary float-right ms-auto">
                    <g:message code="support.startchat"/>
                </button>
            </li>
        </g:each>
    </ul>
</div>
<script>
    function openChat(name, email) {
        if (email) {
            $zoho.salesiq.chat.agent(email);
            $zoho.salesiq.visitor.question('Question pour ' + name + ': ');
            $zoho.salesiq.chat.forward(false);
        }
        $('#zsiq_float').click();
    }
</script>
</body>
</html>