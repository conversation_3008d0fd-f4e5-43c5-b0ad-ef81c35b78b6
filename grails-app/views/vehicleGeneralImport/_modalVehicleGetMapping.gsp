<script>
    var endofcustom = 0;
    function VehicleGeneralImportIndexAddMapping(){
        var body = document.getElementById("mapping_body");

        var elefirstdiv = document.createElement("div");
        elefirstdiv.className = "row";
        var elediv1 = document.createElement("div");
        elediv1.className = "col-xs-12 col-lg-6";
        var eleinput1 = document.createElement("input");
        eleinput1.id = "mapping_name_custommapping_"+endofcustom;
        eleinput1.type = "text";
        eleinput1.value = "";
        eleinput1.className = "fw-normal border-1 w-100";

        elediv1.appendChild(eleinput1);
        elefirstdiv.appendChild(elediv1);

        var elediv2 = document.createElement("div");
        elediv2.className = "col-xs-12 col-lg-6"
        var eleinput2 = document.createElement("input");
        eleinput2.id = "mapping_value_custommapping_"+endofcustom;
        eleinput2.type = "text";
        eleinput2.value = "";
        eleinput2.className = "fw-normal border-1 w-100";

        elediv2.appendChild(eleinput2);
        elefirstdiv.appendChild(elediv2);

        body.appendChild(elefirstdiv);
        endofcustom = endofcustom + 1
    }

</script>

<div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title fw-bold"><g:message code="productmanager.search.filter"/></h2>
            <button type="button" id="modaldismiss" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="d-flex flex-row">
                <div class="col col-md-12 me-4" id="mapping_body">
                    <g:each var="mapp" in="${mapping}">
                        <div class="row">
                            <div class="col-xs-12 col-lg-6">
                                <input id="mapping_name_${mapp}" type="text" class="fw-normal border-1 w-100" value="${mapp}" disabled></input>
                            </div>
                            <div class="col-xs-12 col-lg-6">
                                <input id="mapping_value_${mapp}" type="text" class="fw-normal border-1 w-100" value="${valuearr[mapp]}"></input>
                            </div>
                        </div>
                    </g:each>
                    <g:if test="${valuearrcustom}">
                        <g:each in="${0..mapi}" var="c" >
                            <div class="row">
                                <div class="col-xs-12 col-lg-6">
                                    <input id="mapping_name_custommapping_0" type="text" class="fw-normal border-1 w-100" value="${valuearrcustom[c+'_0']}" disabled>
                                </div>
                                <div class="col-xs-12 col-lg-6">
                                    <input id="mapping_value_custommapping_0" type="text" class="fw-normal border-1 w-100" value="${valuearrcustom[c+'_1']}">
                                </div>
                            </div>
                            <script>
                                endofcustom = endofcustom + 1;
                            </script>
                        </g:each>
                    </g:if>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-link w-100 fw-bold border-secondary" onclick="VehicleGeneralImportIndexAddMapping();"><g:message code="vehicle.feed.map.new"/></button>
            <button type="button" class="btn btn-sm btn-primary fw-normal border-0 w-100 my-4" onclick="VehicleGeneralImportActionIndexCreate('${mapping}',endofcustom);"><g:message code="vehicle.feed.map.save"/></button>
        </div>
    </div>
</div>

