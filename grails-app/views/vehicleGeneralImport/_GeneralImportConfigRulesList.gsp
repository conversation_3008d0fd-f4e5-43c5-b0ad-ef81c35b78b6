<g:each var="GIRConfig" in="${generalImportRuleConfig}">
    <li id="li_GIRConfig_${GIRConfig.rule}" class="<g:if test="${selected.equals(GIRConfig.rule)}">opt_GIRConfig_column_selected px-sm-2 py-2 bg-info-subtle</g:if><g:else>px-sm-2 py-2</g:else>" onclick="VehicleGeneralImportConfigIndexUpdate(${GIRConfig?.generalImportConfig?.id},'${GIRConfig.rule}');">${GIRConfig.rule}
    <i style="" class="text-primary GIRConfig_list_icons GIRConfig_list_icons_${GIRConfig.rule} my-1 mx-sm-2 fa-light fa-trash float-right" onclick="VehicleGeneralImportConfigRuleIndexDelete('${GIRConfig.rule}');"></i>
    </li>
</g:each>