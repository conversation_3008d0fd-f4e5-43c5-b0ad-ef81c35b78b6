<search:clientInit/>
<div class="rating-datatable-div">
    <h1>
        <g:message code="communication.rating"/>
    </h1>

    <div class="row open-multi-select py-2" id="filters${uuid}" style="display: none;">
        <div class="col-3">
            <h5 class="bd-bottom-grey m-0"><g:message code="user"/></h5>
            <label class="m-0"><g:message code="communication.rating.evaluator"/></label>
            <select id="evaluators${uuid}" multiple="multiple">
                <g:each var="user" in="${users}">
                    <option value="${user.id}">${user.fullName}</option>
                </g:each>
            </select>
            <label class="m-0"><g:message code="communication.rating.user"/></label>
            <select id="users${uuid}" multiple="multiple">
                <g:each var="user" in="${users}">
                    <option value="${user.id}">${user.fullName}</option>
                </g:each>
            </select>
        </div>

        <div class="col-3">
            <h5 class="bd-bottom-grey m-0"><g:message code="communication.rating.rating"/></h5>
            <label class="m-0"><g:message code="communication.rating.rating.min"/></label>
            <g:render template="/tagsTemplates/rating5Stars" model="[id: 'ratingMin' + uuid]"/>
            <label class="m-0"><g:message code="communication.rating.rating.max"/></label>
            <g:render template="/tagsTemplates/rating5Stars" model="[id: 'ratingMax' + uuid]"/>
            <label class="m-0"><g:message code="communication.rating.rating.eq"/></label>
            <g:render template="/tagsTemplates/rating5Stars" model="[id: 'ratingEq' + uuid]"/>
        </div>
        <div class="col-3">
            <h5 class="bd-bottom-grey m-0"><g:message code="date"/></h5>
            <input class="form-control mt-2" id="date${uuid}"/>
            <h5 class="bd-bottom-grey m-0 mt-1"><g:message code="client"/></h5>
            <search:contactSearch id="clientSearch${uuid}" type="text" class="form-control mt-2"
                                 placeholder="${g.message(code: "communication.rating.searchClient")}"/>
        </div>
    </div>
    <table class="table rating-datatable" style="width: 100%;" cellspacing="0" id="${uuid}">
        <thead>
        <tr>
            <th class="date"><g:message code="date"/></th>
            <th class="user min-w-150"><g:message code="communication.rating.user"/></th>
            <th class="evaluator min-w-150"><g:message code="communication.rating.evaluator"/></th>
            <th class="rating min-w-150"><g:message code="communication.rating.rating"/></th>
            <th class="comment min-w-400"><g:message code="communication.rating.comment"/></th>
            <th class="communication min-w-400"><g:message code="communication.rating.communication"/></th>
        </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>
