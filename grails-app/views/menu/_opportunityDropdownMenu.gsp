<div class="d-flex align-items-center justify-content-between px-4 my-3">
    <h5 class="text-uppercase m-0">
        <g:message code="opportunities"/>
    </h5>
</div>
<div class="d-flex justify-content-between px-4 my-3" style="max-height: 650px; overflow-y: scroll;">
    <div class="d-flex flex-column gap-2 col-3" style="width: 385px;">
        <h3 class="fw-bold text-black f-16 line-height-19 m-0">
            <g:message code="OpportunityCompiledData.top.webOnlyCommunicationStatusUserNoContact"/> (${notContactedOpportunities.size()})
        </h3>
        <g:if test="${notContactedOpportunities}">
            <g:each var="opportunity" in="${notContactedOpportunities}">
                <a href="<g:createLink controller="opportunity" action="index" id="${opportunity.id}"/>" class="card link-body-emphasis px-3 py-2 shadow-sm-hover">
                    <div class="d-flex justify-content-end text-muted">
                        ${traction.DateUtils.getShortDurationString(opportunity.date)}
                    </div>
                    <div class="d-flex">
                        <i class="fa-regular fa-user text-secondary me-2"></i>
                        <span class="line-height-19">${opportunity.client?.fullName}</span>
                    </div>
                    <div class="d-flex">
                        <i class="fa-regular fa-car text-secondary me-2"></i>
                        <span class="line-height-19">${opportunity.name}</span>
                    </div>
                </a>
            </g:each>
        </g:if>
        <g:else>
            <div class="d-flex justify-content-start text-body-tertiary">
                <g:message code="empty"/>
            </div>
        </g:else>
    </div>


    <div class="d-flex flex-column gap-2 col-3" style="width: 385px;">
        <h3 class="fw-bold text-black f-16 line-height-19 m-0">
            <g:message code="notification.event.opportunity.assigned"/> (${opportunityAssignedNotifications.size()})
        </h3>
        <g:if test="${opportunityAssignedNotifications}">
            <g:each var="notification" in="${opportunityAssignedNotifications}">
                <a href="<g:createLink controller="opportunity" action="index" id="${notification.opportunity?.id}"/>" class="card link-body-emphasis px-3 py-2 shadow-sm-hover">
                    <div class="d-flex justify-content-end text-muted">
                        ${traction.DateUtils.getShortDurationString(notification.date)}
                    </div>
                    <div class="d-flex">
                        <i class="fa-regular fa-user text-secondary me-2"></i>
                        <span class="line-height-19">${notification.opportunity?.client?.fullName}</span>
                    </div>
                    <div class="d-flex">
                        <i class="fa-regular fa-car text-secondary me-2"></i>
                        <span class="line-height-19">${notification.opportunity?.name}</span>
                    </div>
                </a>
            </g:each>
        </g:if>
        <g:else>
            <div class="d-flex justify-content-start text-body-tertiary ">
                <g:message code="empty"/>
            </div>
        </g:else>
    </div>

    <div class="d-flex flex-column gap-2 col-3" style="width: 385px;">
        <h3 class="fw-bold text-black f-16 line-height-19 m-0">
            <g:message code="leads.topStat.visitLog"/> (${visits.size()})
        </h3>
        <g:if test="${visits}">
            <g:each var="visit" in="${visits}">
                <div class="card px-3 py-2">
                        <div class="d-flex justify-content-end text-muted">
                            ${traction.DateUtils.getShortDurationString(visit.date)}
                        </div>
                        <g:if test="${visit.client}">
                            <a href="<g:createLink controller="client" action="index" id="${visit.clientId}"/>" class="d-flex link-body-emphasis">
                                <i class="fa-regular fa-user text-secondary me-2"></i>
                                <span class="line-height-19">${visit.client?.fullName}</span>
                            </a>
                        </g:if>
                        <g:else>
                            <div class="d-flex">
                                <i class="fa-regular fa-notebook text-secondary me-2"></i>
                                <span class="line-height-19">${visit.noteOnClient}</span>
                            </div>
                        </g:else>
                    </div>
            </g:each>
        </g:if>
        <g:else>
            <div class="d-flex justify-content-start text-body-tertiary">
                <g:message code="empty"/>
            </div>
        </g:else>
    </div>
</div>
