<div style="position: relative; overflow: hidden;">
    <div class="d-flex flex-wrap justify-content-between align-items-center px-4 py-3">
        <h4 class="text-uppercase m-0">
            <g:message code="workflow.data.noend"/>
        </h4>
    </div>
    <table class="table table-sm table-hover m-0 w-100" id="workflow-noend-table">
        <thead>
        <tr>
            <th><g:message code="workflow"/></th>
            <th><g:message code="client"/></th>
            <th><g:message code="opportunity"/></th>
        </tr>
        </thead>
        <tbody>
        <g:each var="data" in="${datas}">
            <g:render template="workflowDropdownMenuRow" model="${[data: data]}"/>
        </g:each>
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        var dataTable = TractionDataTable.init('#workflow-noend-table', {
            id: 'workflow-noend-table',
            enableState: true,
            enableScrollBar: true,
            dataTableOptions: {
                "serverSide": false,
                "order": [[1, "desc"]],
                "paging": false,
                "fixedHeader": {
                    header: true,
                },
                buttons: []
            }
        });
        dataTable.on('init.db', function () {
            setTimeout(function () {
                dataTable.columns.adjust().responsive.recalc();
            }, 500);
        });
    });
</script>
<style>
#workflow-noend-table_wrapper .dt-scroll {
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-bottom: none;
}
#workflow-noend-table_wrapper .dt-scroll-body {
    max-height: 350px;
}
#workflow-noend-table_wrapper .dt-scroll-body td{
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}
#workflow-noend-table_wrapper .dt-scroll-head th{
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}
</style>