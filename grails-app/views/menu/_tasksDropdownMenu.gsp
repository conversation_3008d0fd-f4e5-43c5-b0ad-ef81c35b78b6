<%@ page import="traction.task.Task;" %>
<div>
    <div class="d-flex align-items-center justify-content-between px-4 py-3">
        <h5 class="text-uppercase m-0">
            <g:message code="my.tasks"/>
        </h5>
        <div class="ms-3">
            <select id="taskFilterSelect" class="form-select" onchange="filterTasks(this.value)">
                <option value="next7DaysAndLate"><g:message code="task.filter.next7days.late"/></option>
                <option value="all"><g:message code="all"/></option>
                <option value="moreThan7days"><g:message code="task.filter.moreThan7days"/></option>
            </select>
        </div>
    </div>
    <div class="d-flex justify-content-between px-4" style="max-height: 650px; overflow-y: scroll;">

        <g:if test="${!list.isEmpty()}">
            <div id="all-container" class="d-flex flex-column gap-2 col-3" style="width: 385px;">
                <h3 class="fw-bold text-black f-16 line-height-19 m-0">
                    <g:message code="all.task"/> (${list.findAll { it.category != Task.Category.INTERNAL_COMMUNCATION && it.category != Task.Category.WITHOUT_CLIENT && it.category != Task.Category.DELIVERY }.size()})
                </h3>
                <g:each var="task" in="${list.findAll { it.category != Task.Category.INTERNAL_COMMUNCATION && it.category != Task.Category.WITHOUT_CLIENT && it.category != Task.Category.DELIVERY }}" status="index">
                    <taskTagLib:card task="${task}" isDropdown="true" data-category="all"/>
                    <script>
                        $('#card-task-${task.id}').on('cardTaskChange', function() {
                            TopBar.updateCurrentDropdown();
                        });
                    </script>
                </g:each>
                <div class="d-flex justify-content-start text-body-tertiary empty-message d-none">
                    <g:message code="empty"/>
                </div>
            </div>
            <div id="delivery-container" class="d-flex flex-column gap-2 col-3" style="width: 385px;">
                <h3 class="fw-bold text-black f-16 line-height-19 m-0">
                    <g:message code="task.delivery"/> (${list.findAll { it.category == Task.Category.DELIVERY }.size()})
                </h3>
                <g:if test="${list.any { it.category == Task.Category.DELIVERY }}">
                    <g:each var="task" in="${list.findAll { it.category == Task.Category.DELIVERY }}" status="index">
                        <taskTagLib:card task="${task}" isDropdown="true" data-category="delivery"/>
                        <script>
                            $('#card-task-${task.id}').on('cardTaskChange', function() {
                                TopBar.updateCurrentDropdown();
                            });
                        </script>
                    </g:each>
                </g:if>
                <g:else>
                    <div class="d-flex justify-content-start text-body-tertiary empty-list">
                        <g:message code="empty"/>
                    </div>
                </g:else>
                <div class="d-flex justify-content-start text-body-tertiary empty-message d-none">
                    <g:message code="empty"/>
                </div>
            </div>
            <div id="internalOrWithoutClient-container" class="d-flex flex-column gap-2 col-3" style="width: 385px;">
                <h3 class="fw-bold text-black f-16 line-height-19 m-0">
                    <g:message code="task.internalOrWithoutClient"/> (${list.findAll { it.category == Task.Category.INTERNAL_COMMUNCATION || it.category == Task.Category.WITHOUT_CLIENT }.size()})
                </h3>
                <g:if test="${list.any { it.category == Task.Category.INTERNAL_COMMUNCATION || it.category == Task.Category.WITHOUT_CLIENT }}">
                    <g:each var="task" in="${list.findAll { it.category == Task.Category.INTERNAL_COMMUNCATION || it.category == Task.Category.WITHOUT_CLIENT }}" status="index">
                        <taskTagLib:card task="${task}" isDropdown="true" data-category="internalOrWithoutClient"/>
                        <script>
                            $('#card-task-${task.id}').on('cardTaskChange', function() {
                                TopBar.updateCurrentDropdown();
                            });
                        </script>
                    </g:each>
                </g:if>
                <g:else>
                    <div class="d-flex justify-content-start text-body-tertiary empty-list">
                        <g:message code="empty"/>
                    </div>
                </g:else>
                <div class="d-flex justify-content-start text-body-tertiary empty-message d-none">
                    <g:message code="empty"/>
                </div>
            </div>
        </g:if>
        <g:else>
            <div class="d-flex justify-content-center align-items-center text-body-tertiary" style="height: 100%;">
                <g:message code="tasks.noTasks"/>
            </div>
        </g:else>
    </div>

    <div class="d-flex align-items-center justify-content-between px-4 py-3">
        <div class="d-flex">
            <button onclick="TaskSidePanel.show({userId: curUserId});" class="btn btn-primary me-2">
                <i class="fa fa-circle-plus me-2"></i>
                <span>
                    <g:message code="task.create"/>
                </span>
            </button>
            <a class="btn btn-outline-primary" href="<g:createLink controller="task" action="calendar"/>">
                <i class="fa-solid fa-calendar me-2"></i>
                <g:message code="task.calendar"/>
            </a>
        </div>
        <div class="d-flex">
            <a class="btn btn-link" href="<g:createLink controller="task" action="mySubscriptions"/>">
                <i class="fa-solid fa-arrow-up-right-from-square me-2"></i>
                <g:message code="my.task.subscriptions"/>
            </a>
            <a class="btn btn-link" href="<g:createLink controller="task" action="myAssignations"/>">
                <i class="fa-solid fa-arrow-up-right-from-square me-2"></i>
                <g:message code="my.task.assigned"/>
            </a>
        </div>
    </div>
</div>

<script>
    function filterTasks(filter) {
        const today = new Date();
        const next7Days = new Date();
        next7Days.setDate(today.getDate() + 7);

        const categories = {
            all: [],
            delivery: [],
            internalOrWithoutClient: []
        };

        $('.card-task').each(function() {
            const card = $(this);
            const taskDate = new Date(card.data('expected-date'));

            let show = false;

            if (filter === 'all') {
                show = true;
            } else if (filter === 'next7DaysAndLate' && (taskDate <= next7Days && taskDate >= today || taskDate < today)) {
                show = true;
            } else if (filter === 'moreThan7days' && taskDate > next7Days) {
                show = true;
            }

            card.toggle(show);

            if (show) {
                const category = card.data('category');
                if (category === 'task.category.delivery') {
                    categories.delivery.push(card);
                } else if (category === 'task.category.internalcommunication' || category === 'task.category.withoutclient') {
                    categories.internalOrWithoutClient.push(card);
                } else {
                    categories.all.push(card);
                }
            }
        });

        ['all', 'delivery', 'internalOrWithoutClient'].forEach(category => {
            const container = $('#${category}-container');
            if (categories[category].length > 0) {
                if(category === 'all'){
                    $('#all-container').find('.empty-message').addClass('d-none');
                }
                if(category === 'delivery'){
                    $('#delivery-container').find('.empty-message').addClass('d-none');
                }
                if(category === 'internalOrWithoutClient'){
                    $('#internalOrWithoutClient-container').find('.empty-message').addClass('d-none');
                }
            } else {
                if(category === 'all'){
                    $('#all-container').find('.empty-message').removeClass('d-none');
                }
                if (category === 'delivery') {
                    const deliveryContainer = $('#delivery-container');
                    if (deliveryContainer.find('.empty-list').length === 0) {
                        deliveryContainer.find('.empty-message').removeClass('d-none');
                    }
                }
                if(category === 'internalOrWithoutClient'){
                    const internalOrWithoutClientContainer = $('#internalOrWithoutClient-container');
                    if (internalOrWithoutClientContainer.find('.empty-list').length === 0) {
                        internalOrWithoutClientContainer.find('.empty-message').removeClass('d-none');
                    }
                }
            }
        });
    }
</script>