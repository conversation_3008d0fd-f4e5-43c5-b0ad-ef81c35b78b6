<div class="h-100 overflow-scroll  border rounded-2 p-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
       <h3 class="text-emphasis fw-bold f-18 text-uppercase"><g:message code="financing.scenario.define"/></h3>
       <button class="btn btn-link text-decoration-underline" id="addFinancingScenario" onclick="TractionModal.show({url: '/config/modalCreatefinancingScenario'});"><i class="fa-solid fa-circle-plus text-primary me-2"></i><g:message code="financing.scenario.create"/></button>
   </div>
    <div class="d-flex flex-wrap">
        <g:each in="${scenarios}" var="scenario">
            <traction:scenarioCard scenario="${scenario}"/>
        </g:each>
    </div>
</div>