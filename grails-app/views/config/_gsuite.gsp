<div class="h-100 overflow-scroll  border rounded-2 p-4">
    <h5 class="text-emphasis fw-bold f-18 text-uppercase mb-3"><g:message code="gsuite.config"></g:message></h5>

    <div class="row  border rounded-2 mb-3" style="--bs-gutter-x: 0;">
        <div class="d-flex align-items-center justify-content-between border-bottom  px-4 py-3">
            <h2 class="f-16 fw-bold text-black m-0"><g:message code="gsuite.watch.email"/></h2>
            <i class="fa-solid fa-circle-plus f-16 text-primary cursor" onclick="TractionModal.show({
                url: '/config/modalwatchemails'
            });"></i>
        </div>
        <g:each var="email" in="${watchemails}">
            <div class="d-flex align-items-center justify-content-between  px-4 py-3" id="${email}">
                <span class="f-16 text-emphasis line-height-19">${email}</span>
                <i class="fa-light fa-trash f-16 text-primary" onclick="deleteWatchEmail('${email}')"></i>
            </div>
        </g:each>
    </div>

    <div class="row  border rounded-2 mb-3" style="--bs-gutter-x: 0;">
        <div class="d-flex align-items-center justify-content-between border-bottom px-4 py-3">
            <h2 class="f-16 fw-bold text-black m-0"><g:message code="config.email.ignore"/></h2>
            <i class="fa-solid fa-circle-plus f-16 text-primary cursor" onclick="TractionModal.show({
                url: '/config/modalignoreemails'
            });"></i>
        </div>
        <g:each var="email" in="${ignoreemails}">
            <div class="d-flex align-items-center justify-content-between px-4 py-3" id='${email}'>
                <span class="f-16 text-emphasis line-height-19">${email}</span>
                <i class="fa-light fa-trash f-16 text-primary" onclick="deleteIgnoreEmail('${email}')"></i>
            </div>
        </g:each>
    </div>

    <div class="row  border rounded-2 px-4 pt-4 pb-3 mb-3" style="--bs-gutter-x: 0;">
        <h2 class="f-16 fw-bold text-black mb-2"><g:message code="gsuite.credentials"/></h2>
        <textarea class="form-control mb-2" id="credentials" rows="5">${credentials}</textarea>
        <h2 class="f-16 fw-bold text-black mb-2"><g:message code="gsuite.scope"/></h2>
        <textarea class="form-control" id="scope" rows="5">${scope}</textarea>
    </div>

    <div class="row  border rounded-2 px-4 pt-4 pb-3 mb-3" style="--bs-gutter-x: 0;">
        <h2 class="f-16 fw-bold text-black mb-2"><g:message code="other.emails"/></h2>
        <div class="d-flex align-items-center mb-3">
            <span class="f-16 text-emphasis me-3 line-height-19" style="min-width: 153px;"><g:message code="gsuite.api.user"/></span>
            <input type="text" class="form-control" id="apiuser" value="${apiuser}">
        </div>
        <div class="d-flex align-items-center mb-3">
            <span class="f-16 text-emphasis me-3 line-height-19" style="min-width: 153px;"><g:message code="gsuite.system.user"/></span>
            <input type="text" class="form-control" id="systemuser" value="${systemuser}">
        </div>
        <div class="d-flex align-items-center mb-3">
            <span class="f-16 text-emphasis me-3 line-height-19" style="min-width: 153px;"><g:message code="gsuite.api.domain"/></span>
            <input type="text" class="form-control" id="domain" value="${domain}">
        </div>
        <div class="d-flex align-items-center mb-3">
            <span class="f-16 text-emphasis me-3 line-height-19" style="min-width: 153px;"><g:message code="gsuite.api.attachment"/></span>
            <input type="text" class="form-control" id="apiattachment" value="${apiattachment}">
        </div>
        <div class="d-flex align-items-center">
            <span class="f-16 text-emphasis me-3 line-height-19" style="min-width: 153px;"><g:message code="gsuite.api.watcher.topicname"/></span>
            <input type="text" class="form-control" id="topicname" value="${topicname}">
        </div>
    </div>

    <div class="d-flex justify-content-end">
        <button type="button" onclick="saveGsuite()" class="btn btn-primary shadow-sm fw-bold"><g:message code="gsuite.save"/></button>
    </div>
</div>