<%@ page contentType="text/html;charset=UTF-8" %>

<html>
<head>
    <meta name="layout" content="mainTraction"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><g:message ocde="user.edit"/></title>
    <asset:javascript src="formEvent/edit.js"/>

    <asset:stylesheet src="form/form.css"/>
    <script>
        var deleteEventMessage = "<g:message code="form.event.delete.confirm" />";
        var url = "<g:createLink absolute="true" controller="event"/>";
        var event_id = ${event?.id};
        var urlFillInformation = "<g:createLink absolute="true" controller="formEvent" action="displayPreviewModal"/>";
    </script>
</head>

<body>
<g:form name="edit" action="save">
    <div class="m-4">
        <div class="row gx-0">
            <div class="d-flex mb-3">
                <h2 class="me-auto"><g:message code="form.event.edit"/> ${event?.id}</h2>
                <div class="d-flex flex-row-reverse">
                    <button type="submit" id="editButton" class="btn btn-primary fw-bold">
                        <g:message code="client.save"/>
                    </button>
                    <g:if test="${event?.id}">
                        <button type="button" class="btn btn-danger fw-bold me-2" onclick="deleteEvent(${event.id});">
                            <g:message code="default.button.delete.label"/>
                        </button>
                    </g:if>
                </div>
            </div>
            <div class="col-12 px-0">
                <!-- Menu -->
                <div class="row">
                    <div class="col-12">

                    </div>
                </div>
                <!-- END Menu -->
                <!-- From -->
                <input type="hidden" name="id" value="${event?.id}">

                <div class="mb-3 col-sm-12 col-md-12 mx-0">
                    <div class="col-sm-12 col-md-12 mx-0">
                        <div id="errorNotificationEmail" class="alert alert-danger">
                            <g:message code="form.event.notificationEmail.error"/>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <g:if test="${event != null}">
                            <div class="col-sm-12 col-md-12 pe-2">
                                    <label><g:message code="form.profile.url"/> / <g:message code="form.event.widget"/></label>

                                    <div class="input-group mb-3">
                                        <input id="urlEvent" type="text" class="form-control" readonly name="urlEvent"
                                               value="${baseURL}/event/${event?.url}">
                                        <input id="widgetEvent" type="text" class="form-control d-none" readonly name="widgetEvent"
                                               value="<iframe id='tractionFormIframe' width='100%' height='100%' frameborder='0' src='${baseURL}/event/${event?.url}'></iframe><script>document.onload={var iFrameID=document.getElementById('tractionFormIframe'); if(iFrameID){iFrameID.height=''; iFrameID.height=(iFrameID.contentWindow.document.body.scrollHeight+30)+'px';}}</script>">

                                            <button class="btn btn-outline-primary" type="button"
                                                    onclick="copyTextElement('urlEvent', '<i class=\'fas fa-check\'></i>')"><i
                                                    class="fas fa-clipboard"></i> <g:message code="form.profile.url"/></button>
                                            <button class="btn btn-outline-primary" type="button"
                                                    onclick="copyTextElement('widgetEvent', '<i class=\'fas fa-check\'></i>')"><i
                                                    class="fas fa-clipboard"></i> <g:message code="form.event.widget"/></button>
                                    </div>

                            </div>
                        </g:if>
                        <div class="col-sm-12 col-md-6 pe-2">
                                <label><g:message code="form.profile.name"/></label>
                                <input type="text" class="form-control" id="name" name="name" value="${event?.name}">
                        </div>

                        <div class="col-sm-12 col-md-6">
                                <label><g:message code="form.event.profile"/></label>
                                <select class="form-select" name="profile">
                                    <g:each var="profile" in="${profiles}">
                                        <g:if test="${event?.profile?.id == profile.id}">
                                            <option value="${profile.id}" selected>${profile.name}</option>
                                        </g:if>
                                        <g:elseif test="${profileCreatedFrom?.id == profile.id}">
                                            <option value="${profile.id}" selected>${profile.name}</option>
                                        </g:elseif>
                                        <g:else>
                                            <option value="${profile.id}">${profile.name}</option>
                                        </g:else>
                                    </g:each>
                                </select>
                        </div>

                        <div class="col-sm-12 col-md-6 pe-2">
                                <label><g:message code="form.event.fromdate"/></label>
                                <input type="date" class="form-control" name="fromDate" value="${fromDate}">
                        </div>

                        <div class="col-sm-12 col-md-6 pe-2">
                                <label><g:message code="form.event.todate"/></label>
                                <input type="date" class="form-control" name="toDate" value="${toDate}">
                        </div>

                        <div class="col-sm-12 col-md-12 pe-2 mt-3 mb-3">
                            <div class="d-flex">
                                <input id="event-depts-sendworkflow" role="switch" class="form-check-input border-primary me-2" ${config?.defaultWorkflowId ? "checked" : ""}
                                       type="checkbox">
                                <span><g:message code="form.event.sendworkflow"/></span>
                            </div>
                        </div>

                        <div id="event-depts" class="col-sm-12 col-md-12 ${config?.defaultWorkflowId ? "" : "d-none"}">
                            <div class="row">
                                <div class="col-sm-12 col-md-6">
                                        <label><g:message code="form.event.workflowdefault"/></label>
                                        <select class="form-control" name="workflowDefault">
                                            <option value=""><g:message code="form.event.workflow.select"/></option>
                                            <g:each var="workflow" in="${workflows}">
                                                <option value="${workflow.id}" ${config?.defaultWorkflowId == workflow.id.toString() ? "selected" : ""}>${workflow.name}</option>
                                            </g:each>
                                        </select>
                                </div>

                                <div class="col-sm-12 col-md-12 pe-2 mt-3 mb-3">
                                    <div class="form-check">
                                        <input id="event-depts-sendworkflows" role="switch" class="form-check-input border-primary" ${config?.departments ? "checked" : ""}
                                               type="checkbox">
                                        <span><g:message code="form.event.sendworkflowdep"/></span>
                                    </div>
                                </div>

                                <g:each var="department" in="${departments}">
                                    <div id="event-dept-${department.id}"
                                         class="col-sm-12 col-md-4 ${config?.departments ? "" : "d-none"}">
                                            <label>
                                                <g:message
                                                        code="form.event.workflow"/> - <strong>${department.name}</strong>
                                            </label>
                                            <select class="form-control" name="workflow-dept-${department.id}">
                                                <option value=""><g:message code="form.event.workflow.select"/></option>
                                                <g:each var="workflow" in="${workflows}">
                                                    <option value="${workflow.id}" ${config && config['departments'][department.id.toString()] == workflow.id.toString() ? "selected" : ""}>${workflow.name}</option>
                                                </g:each>
                                            </select>
                                    </div>
                                </g:each>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <input id="users" type="hidden" name="users" value="${event?.users.collect { it.id }}">
        <input id="notificationUsers" type="hidden" name="notificationUsers"
               value="${event?.notificationUsers.collect { it.id }}">

        <div class="row g-0">
            <div class="col-12 px-0">
                <ul class="nav nav-tabs md-tabs nav-2 nav-pills flex-column flex-sm-row mb-3"
                    role="tablist">
                    <li class="nav-item">
                        <span class="cursor nav-link active" data-bs-toggle="pill" href="#tabUsers"
                           role="tab" aria-controls="tabUsers"><g:message code="form.event.users"/></span>

                        <div class="slide"></div>
                    </li>
                    <li class="nav-item">
                        <span class="cursor nav-link" data-bs-toggle="pill" href="#tabNotificationUsers" role="tab"
                           aria-controls="tabNotificationUsers"><g:message code="form.event.notificationUsers"/></span>

                        <div class="slide"></div>
                    </li>
                </ul>

                <div class="tab-content" id="v-pills-tabContent">
                    <div class="tab-pane fade show active" id="tabUsers" role="tabpanel" aria-labelledby="tabUsers">
                        <div class="row gx-0">
                            <div class="col-5 border rounded">
                                <nav style="height: 400px;overflow-y: scroll; scrollbar-width: none;">
                                    <ul id="listSubscribe" class="list-group list-group-flush">
                                        <g:each var="u" in="${traction.security.User.list().sort { it.firstname }}">
                                            <g:if test="${u.firstname != '' && u.lastname != ''}">
                                                <g:if test="${event && !event.users.collect { it.id }.contains(u.id)}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        elementid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:if>
                                                <g:elseif test="${!event}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        elementid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:elseif>
                                            </g:if>
                                        </g:each>
                                    </ul>
                                </nav>
                            </div>

                            <div class="col-2">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="mdi mdi-arrow-right-thick f-36" style="margin-top:100px;"></i>
                                    <i class="mdi mdi-arrow-left-thick f-36"></i>
                                </div>
                            </div>

                            <div class="col-5 border rounded">
                                <nav style="height: 400px;overflow-y: scroll; scrollbar-width: none;">
                                    <ul id="listUnSubscribe" class="list-group">
                                        <g:each var="u" in="${traction.security.User.list()}">
                                            <g:if test="${u.firstname != '' && u.lastname != ''}">
                                                <g:if test="${event && event.users.collect { it.id }.contains(u.id)}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        dataid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:if>
                                            </g:if>
                                        </g:each>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="tabNotificationUsers" role="tabpanel"
                         aria-labelledby="tabNotificationUsers">
                        <div class="row gx-0">
                            <div class="col-5 border rounded">
                                <nav style="height: 400px;overflow-y: scroll; scrollbar-width: none;">
                                    <ul id="listNotification" class="list-group list-group-flush">
                                        <g:each var="u" in="${traction.security.User.list().sort { it.firstname }}">
                                            <g:if test="${u.firstname != '' && u.lastname != ''}">
                                                <g:if test="${event && !event.notificationUsers.collect {
                                                    it.id
                                                }.contains(u.id)}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        elementid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:if>
                                                <g:elseif test="${!event}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        elementid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:elseif>
                                            </g:if>
                                        </g:each>
                                    </ul>
                                </nav>
                            </div>

                            <div class="col-2">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="mdi mdi-arrow-right-thick f-36" style="margin-top:100px;"></i>
                                    <i class="mdi mdi-arrow-left-thick f-36"></i>
                                </div>
                            </div>

                            <div class="col-5 border rounded">
                                <nav style="height: 400px;overflow-y: scroll; scrollbar-width: none;">
                                    <ul id="listUnNotification" class="list-group">
                                        <g:each var="u" in="${traction.security.User.list()}">
                                            <g:if test="${u.firstname != '' && u.lastname != ''}">
                                                <g:if test="${event && event.notificationUsers.collect {
                                                    it.id
                                                }.contains(u.id)}">
                                                    <li class="list-group-item list-group-item-action" userid="${u.id}"
                                                        dataid="${event?.id}">${u.firstname} ${u.lastname}</li>
                                                </g:if>
                                            </g:if>
                                        </g:each>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <asset:javascript src="form/formEvent.js"/>
</g:form>
<div class="modal fade" id="modalForm" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle"
     aria-hidden="true">
</div>

<!-- END From -->

</body>
</html>
