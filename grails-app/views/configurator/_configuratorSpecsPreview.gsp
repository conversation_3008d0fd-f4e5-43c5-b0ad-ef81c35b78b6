<g:set var="uuid" value="${UUID.randomUUID().toString()}"/>
<div id="vehicle-specs-preview">
    <g:if test="${specifications}">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <g:each var="groupedSpec" in="${specifications}" status="index">
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${index == 0 ? 'active' : ''}" id="tab-preview-spec-${index}" data-bs-toggle="tab" data-bs-target="#preview-spec-${index}-${uuid}" type="button" role="tab" aria-controls="home" aria-selected="true">
                        ${groupedSpec.key}
                    </button>
                </li>
            </g:each>
        </ul>
        <div class="tab-content" id="myTabContent">
            <g:each var="groupedSpec" in="${specifications}" status="index">
                <div class="tab-pane fade p-4 border-start border-end border-bottom ${index == 0 ? 'show active' : ''}" id="preview-spec-${index}-${uuid}" role="tabpanel" aria-labelledby="tab-preview-spec-${index}-${uuid}">
                    <g:each var="spec" in="${groupedSpec.value}">
                        <div class="row mb-2">
                            <div class="col-2">
                                <strong>${spec.name}:</strong>
                            </div>
                            <div class="col-10">
                                ${spec.value}
                            </div>
                        </div>
                    </g:each>
                </div>
            </g:each>
        </div>
    </g:if>
</div>