<asset:javascript src="configurator/configuratorGroups.js"/>
<style>
.btn-sm {
    border-radius: 0.2rem !important;
}
</style>
<h3 class="mb-3"><g:message code="productmanager.configurator.mapping.ui"/></h3>
<div class="input-group mb-2 me-sm-2">
    <div class="d-flex flex-grow-1">
        <input type="text" id="configuratorGroupName" class="form-control"
               placeholder="<g:message code="productmanager.configurator.group.name.placeholder"/>"/>
    </div>

    <i role="button" class="fa-solid fa-plus-large text-primary align-content-center ms-2" onclick="ConfiguratorGroup.addConfiguratorGroup()"></i>
</div>
<table id="configuratorGroups" class="table table-striped border w-100">
    <thead>
    <tr>
        <th class="name"><g:message code="productmanager.configurator.group.name"/></th>
        <th class="actions"><g:message code="actions"/></th>
    </tr>
    </thead>
    <tbody>
    <g:each var="group" in="${groups}">
        <tr>
            <td>${group.name}</td>
            <td>
                <button class="btn btn-danger btn-sm float-right"
                        onclick="ConfiguratorGroup.deleteConfiguratorGroup('${group.id}');">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        </tr>
    </g:each>
    </tbody>
</table>

<script>

</script>