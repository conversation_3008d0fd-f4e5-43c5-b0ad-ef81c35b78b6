<asset:stylesheet src="container/openmultiselect.css"/>
<asset:stylesheet src="configurator/vehicleMapping.css"/>
<asset:javascript src="configurator/vehicleMapping.js"/>
<h3 class="mb-3"><g:message code="productmanager.configurator.mapping.ui"/></h3>

<div class="input-group mb-2 me-sm-2">
        <div class="input-group-text"><g:message code="configurator.linkedVariable"/></div>

    <div class="d-flex flex-grow-1">
        <select name="configuratorMappingVariable" id="configuratorMappingVariable" class="form-control">
            <g:each var="cell" in="${allcells}">
                <option value="${cell}">${cell}</option>
            </g:each>
        </select>
    </div>

    <button class="btn text-white align-content-center bg-primary px-2 fw-bold border-primary rounded-end" onclick="ConfiguratorMapping.addVehicleMapping('${group.id}')"><i class="fa-solid fa-plus-large"></i></button>
</div>
<table id="vehicleMapping" class="table table-striped border w-100">
    <thead>
    <tr>
        <th class="check"></th>
        <th class="pictures"><g:message code="vehicle.pictures"/></th>
        <th class="make"><g:message code="vehicle.make"/></th>
        <th class="model"><g:message code="vehicle.model"/></th>
        <th class="year"><g:message code="vehicle.year"/></th>
        <th class="stockNumber min-w-150"><g:message code="vehicle.stockNumber"/></th>
        <th class="modelCode"><g:message code="vehicle.modelCode"/></th>
        <th class="category"><g:message code="vehicle.category"/></th>
        <th class="subCategory"><g:message code="vehicle.subCategory"/></th>
    </tr>
    </thead>
</table>