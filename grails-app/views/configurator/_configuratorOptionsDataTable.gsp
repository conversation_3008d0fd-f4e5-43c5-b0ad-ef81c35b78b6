<script>
    $(document).ready(function () {

    })
</script>

<div class="container p-4">
    <h3 class="text-uppercase mb-4"><g:message code="vehicleoption.title"/></h3>
    <table id="configurator-options-table" class="table table-striped border w-100 display vehicle-table">
        <thead>
            <tr>
                <th class="order text-start"><g:message code="vehicleoption.order"/></th>
                <th class="image text-start"><g:message code="vehicleoption.images"/></th>
                <th class="name text-start"><g:message code="vehicleoption.name"/></th>
                <th class="description text-start"><g:message code="vehicleoption.description"/></th>
                <th class="price text-start"><g:message code="vehicleoption.price"/></th>
                <th class="optional text-start"><g:message code="vehicleoption.optional"/></th>
                <th class="action text-start"><g:message code="vehicleoption.actions"/></th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</div>