package traction.security

import grails.gorm.transactions.NotTransactional
import grails.gorm.transactions.Transactional
import traction.*
import traction.call.CallService
import traction.chat.ChatService
import traction.chat.ChatWidgetGroup
import traction.chat.ChatWidgetGroupService
import traction.client.ClientAttribute
import traction.communication.*
import traction.communication.email.EmailUtils
import traction.department.Department
import traction.facebook.FacebookPage
import traction.facebook.FacebookPageService
import traction.goal.DailyUserData
import traction.goal.UserPointTarget
import traction.goal.UserSalesTarget
import traction.history.History
import traction.history.HistoryService
import traction.menu.UserTopMenuModel
import traction.notification.WebSocketService
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityAssigned
import traction.opportunity.OpportunityService
import traction.permissions.PermissionLevel
import traction.permissions.DefaultRoleAuthority
import traction.permissions.Permission
import traction.permissions.Permissions
import traction.service.WorkScheduleService
import traction.status.StatusBusy
import traction.task.Task
import traction.task.TaskAssigned
import traction.task.TaskAssignedQuery
import traction.workflow.WorkflowDataService

@Transactional
class UserService {

    ExtensionService extensionService
    UserPointService userPointService
    UserGoalService userGoalService
    HistoryService historyService
    CallService callService
    OpportunityService opportunityService
    WorkflowDataService workflowDataService
    ConfigService configService
    FacebookMessageService facebookMessageService
    ChatService chatService
    FacebookPageService facebookPageService
    ChatWidgetGroupService chatWidgetGroupService
    WebSocketService webSocketService
    EnvironmentService environmentService
    WorkScheduleService workScheduleService
    RoleService roleService
    def dataSource

    @NotTransactional
    List<User> searchUsers(String name, int max) {
        String s = "%" + name + "%"
        return User.createCriteria().list(max: max) {
            or {
                ilike("username", s)
                ilike("firstname", s)
                ilike("lastname", s)
            }
            eq("deleted", false)
            eq("enabled", true)
        }
    }

    /**
     * Method to get all the users un a department and a usergroup
     * @param departements
     * @param UserGroups
     * @return
     */
    @NotTransactional
    List<User> getAllDepartmentUserGroup(List<Department> departments, List<UserGroup> userGroups) {
        List<User> users = new ArrayList<>()
        if (departments.size() > 0) {
            for (Department department : departments) {
                users.addAll(department.getUsers().findAll { !it.deleted })
            }
        } else {
            users = getAll()
        }
        List<User> clone = users.clone()
        if (userGroups.size() > 0) {
            for (User user : clone) {
                if (!userGroups.contains(user.userGroup)) {
                    users.remove(user)
                }
            }
        }

        return users
    }

    @NotTransactional
    User getByEmail(String email) {
        User user
        //log.debug "getUser:"+username
        if (email) {
            user = User.createCriteria().get() {
                ilike("email", email)
                maxResults(1)
            }
        }
        return user
    }

    @NotTransactional
    User findByExt(String ext) {
        if (ext) {
            List<User> users = Extension.createCriteria().list {
                eq("ext", ext)
                isNotNull("user")
                projections {
                    property("user")
                }
            }
            if (users) {
                User available = users.find { it.status != StatusBusy.NOTAVAILABLE && it.status != StatusBusy.VACATION }
                return available ?: users.first()
            }
        }
        return null
    }

    @NotTransactional
    def save(User newUser) {
        //log.debug "save(User ${newUser})"
        def ret = [success: "error", message: 'Can\'t save null User.']
        if (newUser) {
            if (!newUser.defaultFromEmail || EmailUtils.isValid(newUser.defaultFromEmail)) {
                if (newUser.username && newUser.password && newUser.firstname && newUser.lastname) {
                    if (newUser.username.length() <= 30) {
                        if (EmailUtils.isValid(newUser.username + "@gmail.com")) {
                            if (newUser.businessUnits) {
                                User.withTransaction {
                                    newUser.dateModif = new Date()
                                    try {
                                        if (newUser?.isDirty("statusChat")) {
                                            webSocketService.communicationChannelUserUpdate(newUser)
                                        }
                                    } catch (Exception e) {
                                        log.debug('user isDirty("statusChat") not working')
                                    }

                                    try {
                                        if (newUser?.isDirty("password")) {
                                            newUser.dateSecurityModif = new Date()
                                        }
                                    } catch (Exception e) {
                                        log.debug('user isDirty("password") not working')
                                    }

                                    if (GormEntityUtils.save(newUser)) {
                                        ret = [success: "success", message: "User " + newUser.username + " saved."]
                                    } else {
                                        ret.message = "Error saving user."
                                    }
                                }
                            } else {
                                ret.message = "Please select at least one business unit."
                            }
                        } else {
                            ret.message = "Username has to be valid in an email."
                        }
                    } else {
                        ret.message = "Username exceeds the 30 characters limit."
                    }
                } else {
                    ret.message = "Please enter non empty username, password, firstname and lastname."
                }
            } else {
                ret.message = "The user's Traction email is not valid."
            }
        }
        return ret
    }

    @Transactional
    UserAttribute setUserAttribute(User user, String name, String value) {
        UserAttribute userAttribute
        if (user && name && value) {
            userAttribute = UserAttribute.findByUserAndName(user, name)
            if (userAttribute) {
                if (userAttribute.value != value) {
                    userAttribute.value = value
                    GormEntityUtils.save(userAttribute)
                }
            } else {
                userAttribute = new UserAttribute()
                userAttribute.name = name
                userAttribute.value = value
                userAttribute.user = user
                GormEntityUtils.save(userAttribute)
            }
        }
        return userAttribute
    }

    @NotTransactional
    UserAttribute getUserAttribute(User user, String attrname) {
        UserAttribute attribute = UserAttribute.findByNameAndUser(attrname, user)
        return attribute
    }

    @Transactional
    def removeUserAttribute(User user, String attrName) {
        log.debug "RemoveUserAttribute with " + user + ":" + attrName
        List<ClientAttribute> attrs = UserAttribute.createCriteria().list(max: 1) {
            eq("name", attrName)
            eq("user", user)
        }
        if (attrs.size() > 0)
            attrs.first().delete()
    }

    @NotTransactional
    List<User> getAll() {
        return getAll(false)
    }

    @NotTransactional
    List<User> getAllDeleted() {
        return getAll(true)
    }

    @NotTransactional
    List<User> getAll(boolean isDeleted) {
        List<User> users = User.createCriteria().list() {
            eq("deleted", isDeleted)
            order("username", "asc")
        }
        return getListWithoutDevAndAPIUsers(users)
    }

    @NotTransactional
    List<User> getSalesManagersList(List<Department> departmentList = null) {
        return UserRole.createCriteria().list {
            createAlias('role', '_role')
            createAlias('user', '_user')
            eq("_role.authority", DefaultRoleAuthority.ROLE_SALESMANAGER.name())
            eq("_user.deleted", false)
            eq("_user.enabled", true)
            if (departmentList) {
                createAlias('_user.departments', '_d')
                'in'("_d.id", departmentList.id)
            }
            projections {
                property("user")
            }
        }
    }

    @NotTransactional
    User get(long id) {
        return User.get(id)
    }

    @Transactional
    boolean delete(User user) {
/*
        //user.businessUnits.each() { BusinessUnit bu ->
        //    user.removeFromBusinessUnits(bu)
        //}
        */
        /*
        if (user.facebookId) {
            log.info("Deleting User Persona......")
            Connection.Response response = Jsoup.connect("https://graph.facebook.com/${user.facebookId}?access_token=${configService.get("FACEBOOK", "facebook.messenger.token")}")
                    .ignoreHttpErrors(true)
                    .ignoreContentType(true)
                    .method(Connection.Method.DELETE)
                    .execute()

            Document leadsResponseDoc = response.parse()
            JSONObject responseJson = new JSONObject(leadsResponseDoc.body().text())
            if (responseJson.getString('success')) {
                log.info("Personne deleted with success")
            } else if (leadsResponseDoc.toString().contains("error")) {
                AntiSpamLogger.error("Error deleteing persona");
            }
        }
         */
        def urs = UserRole.findAllByUser(user)
        urs.each {
            it.delete(flush: true)
        }
        CommunicationChannel.createCriteria().list {
            eq("user", user)
            isNull("dateCompleted")
        }.each { CommunicationChannel channel ->
            channel.dateCompleted = new Date()
        }
        if (user.isInChatWidgetGroupOrFacebookPage()) {
            FacebookPage.getAll().each { u ->
                if (u.users.removeAll { it.id == user.id }) {
                    facebookPageService.save(u)
                }
            }
            ChatWidgetGroup.getAll().each { group ->
                if (group.users.removeAll { it.id == user.id }) {
                    chatWidgetGroupService.save(group)
                }
            }
        }
        Extension.findAllByUser(user).each {
            extensionService.delete(it)
        }
        workScheduleService.deleteAllByUser(user)
        user.username += "-DELETED-${new Date()}"
        user.save(flush: true)
        GormEntityUtils.save(user)
        return GormEntityUtils.delete(user)
    }

    @Transactional
    boolean unDelete(User user) {
        user.unDelete()
        return GormEntityUtils.save(user) ? true : false
    }

    @NotTransactional
    List<User> getUsersfromUserGroup(UserGroup userGroup) {
        return User.createCriteria().list {
            eq("userGroup", userGroup)
            eq("enabled", true)
            eq("deleted", false)
            order("username")
        }
    }

    /**
     * Set the currentBusinessUnit of the user
     *
     * @param user User
     * @param bu BusinessUnit
     * @return void
     */
    @Transactional
    def setCurrentBusinessUnit(User user, BusinessUnit bu) {
        if (user && bu && user.businessUnits.contains(bu)) {
            user.currentBusinessUnit = bu
            user.save(flush: true)
        }
    }

    @Transactional
    def setCurrentDepartment(User user, Department department) {
        if (user && department && user.departments.contains(department)) {
            user.currentDepartment = department
            user.save(flush: true)
        }
    }

    // For Bootstrap.groovy
    @Transactional
    def init() {
        //log.debug "UserService.init"
        BusinessUnit bu = BusinessUnit.getRandom()
        log.debug "Current BU : " + bu
        Permission.values().each {
            Permissions permission = Permissions.findByPermission(it)
            if (!permission) {
                GormEntityUtils.save(new Permissions(it))
            }
        }
        List<Role> defaultRoles = []
        DefaultRoleAuthority.values().each {
            Role role = Role.findByAuthority(it)
            if (!role) {
                role = new Role(it.name(), I18N.m(it.name())).save()
                it.permissions.each {
                    Permissions permission = Permissions.findByPermission(it)
                    role.addToPermissions(permission)
                }
                roleService.save(role)
            }
            defaultRoles.add(role)
        }

        Department department = Department.first()

        User tractionUser = User.findByUsername("traction")
        if (!tractionUser) {
            tractionUser = new User('traction', 'trackcore14')
            tractionUser.email = "<EMAIL>"
            tractionUser.firstname = "Traction"
            tractionUser.lastname = "DK"
            tractionUser.currentBusinessUnit = BusinessUnit.first()
            tractionUser.currentDepartment = department
            tractionUser.permissionLevel = PermissionLevel.ADMIN
            tractionUser.addToDepartments(department)
            tractionUser.addToBusinessUnits(BusinessUnit.first())
            tractionUser.save()
            defaultRoles.each {
                UserRole.create(tractionUser, it, true)
            }
        }

        User apiUser = User.findByUsername("api")
        if (!apiUser) {
            apiUser = new User('api', 'f7Q@lwOyKwth')
            apiUser.currentBusinessUnit = bu
            apiUser.currentDepartment = department
            apiUser.addToDepartments(department)
            apiUser.save()
            UserRole.create(apiUser, Role.findByAuthority(DefaultRoleAuthority.ROLE_API), true)
        }

        if (environmentService.isDevelopment()) {
            User appleUser = User.findByUsername("apple")
            if (!appleUser) {
                appleUser = new User('apple', 'Tr@ction')
                appleUser.currentDepartment = department
                appleUser.currentBusinessUnit = bu
                appleUser.userGroup = UserGroup.SALES
                appleUser.addToDepartments(department)
                appleUser.save()
                UserRole.create(appleUser, Role.findByAuthority(DefaultRoleAuthority.ROLE_USER), true)
            }

            User androidUser = User.findByUsername("android")
            if (!androidUser) {
                androidUser = new User('android', 'Tr@ction')
                androidUser.currentDepartment = department
                androidUser.currentBusinessUnit = bu
                androidUser.userGroup = UserGroup.SALES
                androidUser.addToDepartments(department)
                androidUser.save()
                UserRole.create(androidUser, Role.findByAuthority(DefaultRoleAuthority.ROLE_USER), true)
            }
        }

        //log.debug "UserService.init end"
    }

    @NotTransactional
    def compileDailyUserData(Date date, User user) {
        if (!user) return
        ExecutionTime executionTime = new ExecutionTime()
        Date dayStart = date.clone()
        dayStart.clearTime()
        Date dayEnd = dayStart + 1
        log.debug "compileDailyUserData : " + dayStart + " for " + user
        double dayPointsTarget = 0
        double monthPointsTarget = 0
        double yearPointsTarget = 0
        UserPointTarget target = userPointService.getUserPointTarget(user, dayEnd)
        if (target) {
            dayPointsTarget = target.day
            monthPointsTarget = target.month * 1.0 / 30
            yearPointsTarget = target.year * 1.0 / 365
        }
        int points = historyService.getUserPoints(dayStart, dayEnd, user)
        double daySalesTarget = 0
        double monthSalesTarget = 0
        double yearSalesTarget = 0
        UserSalesTarget targetSales = userGoalService.getUserSalesTarget(user, dayEnd)
        if (targetSales) {
            daySalesTarget = targetSales.day
            monthSalesTarget = targetSales.month * 1.0 / 30
            yearSalesTarget = targetSales.year * 1.0 / 365
        }
        int sales = historyService.countUserHistories(user, dayStart, dayEnd, [History.Status.OPPORTUNITYSOLD])
        int lateTask
        int scheduledTask
        if (dayStart + 2 > new Date()) {
            lateTask = TaskAssignedQuery.searchLate(user, TaskAssigned.Type.ASSIGNED).count()
            scheduledTask = TaskAssignedQuery.searchSoon(user, TaskAssigned.Type.ASSIGNED).count()
        }
        DailyUserData compiled = DailyUserData.createCriteria().get {
            eq("date", dayStart)
            eq("user", user)
        }
        if (!compiled) {
            compiled = new DailyUserData(
                    date: dayStart,
                    user: user
            )
        }
        compiled.dayPointsTarget = dayPointsTarget
        compiled.monthPointsTarget = monthPointsTarget
        compiled.yearPointsTarget = yearPointsTarget
        compiled.points = points
        compiled.daySalesTarget = daySalesTarget
        compiled.monthSalesTarget = monthSalesTarget
        compiled.yearSalesTarget = yearSalesTarget
        compiled.sales = sales
        if (lateTask != null) compiled.lateTask = lateTask
        if (scheduledTask != null) compiled.scheduledTask = scheduledTask
        this.saveDailyUserData(compiled)
        log.debug "Execution in : ${executionTime.executionTime()} ms"
    }

    @Transactional
    DailyUserData saveDailyUserData(DailyUserData dailyUserData) {
        return GormEntityUtils.save(dailyUserData)
    }

    @NotTransactional
    Map getMenuCounts(User user) {
        return new UserTopMenuModel(user).getMenuCounters()
    }

    @NotTransactional
    Map getTaskDropdownMenu(def id, def subscribed, def only) {
        List<Map> tasksTodo = []
        List<Map> tasksSoon = []
        List<Map> tasksProvider = []
        List<Map> tasksDelivery = []
        List<Task> allTasksNotif = []
        boolean todo = true
        boolean soon = true
        boolean provider = true
        boolean delivery = true
        TaskAssigned.Type assignedType = subscribed ? TaskAssigned.Type.SUBSCRIBED : TaskAssigned.Type.ASSIGNED
        User user

        //TODO: Faire menage ici, semble utilisé que par le mobile maintenant.
        // Techniquement plus besoin du param 'only' et seulement besoin des tasks dans TaskAssignedSearch
        if (id) {
            user = get(id as long)
            if (user) {
                if (only) {
                    if (only.equals('todo')) {
                        soon = false
                        provider = false
                        delivery = false
                    } else if (only.equals('soon')) {
                        todo = false
                        provider = false
                        delivery = false
                    } else if (only.equals('provider')) {
                        todo = false
                        soon = false
                        delivery = false
                    } else if (only.equals('delivery')) {
                        todo = false
                        soon = false
                        provider = false
                    }
                }
                Date now = new Date()

                // ** tasksTodo retourne les tasks en retard et tasksSoon retourne toutes les tasks ayant un status Status.TOD0 qui ne sont pas en retard.
                if (soon) {
                    TaskAssignedQuery.searchSoon(user, assignedType).list().each { TaskAssigned assigned ->
                        Task task = assigned.task
                        allTasksNotif.add(task)
                        tasksSoon.add([
                                task       : task,
                                isToday    : DateUtils.areInSameDay(task.expectedDate, now),
                                iconTask   : task.getIcon(),
                                iconStatus : task.status.icon,
                                isTodo     : task.isTodo(),
                                tooltip    : I18N.m(task.getMessage()) + " - " + I18N.m(task.status.message),
                                description: task.description
                        ])
                    }
                }
                if (todo) {
                    TaskAssignedQuery.searchLate(user, assignedType).list().each { TaskAssigned assigned ->
                        Task task = assigned.task
                        if (task.status == Task.Status.TODO) {
                            allTasksNotif.add(task)
                            tasksTodo.add([
                                    task       : task,
                                    iconTask   : task.getIcon(),
                                    iconStatus : task.status.icon,
                                    isTodo     : task.isTodo(),
                                    tooltip    : I18N.m(task.getMessage()) + " - " + I18N.m(task.status.message),
                                    description: task.description
                            ])
                        }
                    }
                }
                if (provider) {
                    new TaskAssignedQuery(
                            user: user,
                            type: assignedType,
                            taskCategory: Task.Category.PROVIDER,
                            status: Task.Status.TODO
                    ).list().each { TaskAssigned assigned ->
                        Task task = assigned.task
                        allTasksNotif.add(task)
                        tasksProvider.add([
                                task       : task,
                                isToday    : DateUtils.areInSameDay(task.expectedDate, now),
                                iconTask   : task.getIcon(),
                                iconStatus : task.status.icon,
                                isTodo     : task.isTodo(),
                                tooltip    : I18N.m(task.getMessage()) + " - " + I18N.m(task.status.message),
                                description: task.description
                        ])
                    }
                }
                if (delivery) {
                    new TaskAssignedQuery(
                            user: user,
                            type: assignedType,
                            status: Task.Status.TODO,
                            taskCategory: Task.Category.DELIVERY
                    ).list().each { TaskAssigned assigned ->
                        Task task = assigned.task
                        allTasksNotif.add(task)
                        tasksDelivery.add([
                                task       : task,
                                isToday    : DateUtils.areInSameDay(task.expectedDate, now),
                                iconTask   : task.getIcon(),
                                iconStatus : task.status.icon,
                                isTodo     : task.isTodo(),
                                tooltip    : I18N.m(task.getMessage()) + " - " + I18N.m(task.status.message),
                                description: task.description
                        ])
                    }
                }
            }
        }
        return [
                tasksTodo    : tasksTodo,
                tasksSoon    : tasksSoon,
                tasksProvider: tasksProvider,
                tasksDelivery: tasksDelivery,
                allTasksNotif: allTasksNotif,
                todo         : todo,
                soon         : soon,
                provider     : provider,
                delivery     : delivery
        ]
    }

    @NotTransactional
    List<User> getListWithoutDevAndAPIUsers(list) {
        if (!list) return []
        def finalList = []
        Role roleDev = Role.findByAuthority("ROLE_DEV")
        Role roleApi = Role.findByAuthority("ROLE_API")
        list.each {
            if (!UserRole.exists(it.id, roleDev.id) && !UserRole.exists(it.id, roleApi.id)) {
                finalList.add(it)
            }
        }
        return finalList
    }

    // TODO: Optimiser, cette loop est beaucoup trop longue!
    @NotTransactional
    def cleanUserAssigned(User user) {
        log.debug "cleanUserAssigned($user)"
        if (!user) return
        List<Long> oppListId = OpportunityAssigned.createCriteria().list {
            eq("user", user)
            projections {
                property("opportunity.id")
            }
        }
        oppListId.each {
            Opportunity opp = Opportunity.get(it)
            if (opp) {
                cleanOpportunityAssigned(opp, user)
            }
        }


    }

    @NotTransactional
    def cleanOpportunityAssigned(Opportunity opp, User userToRemove) {
        log.debug "cleanOpportunityAssigned($opp, $userToRemove)"
        if (!opp) return

        boolean oppNeedsSave = false
        UserGroup.findAll().each { UserGroup ug ->
            Set<OpportunityAssigned> assignedSet = OpportunityAssigned.createCriteria().list {
                createAlias("user", "_user")
                eq("opportunity", opp)
                eq("_user.userGroup", ug)
                order("id", "desc")
                order("type", "asc")
            }

            //if sales we need only one primary and one secondary
            //if not sales we need no secondary and one primary
            if (ug != UserGroup.SALES) {
                OpportunityAssigned opportunityAssigned
                // we try to find the most matching assignation
                assignedSet.each {
                    if (opportunityAssigned == null || opportunityAssigned.user == userToRemove || !opportunityAssigned.isPrimary()) {
                        opportunityAssigned = it
                    }
                }
                if (opportunityAssigned) {
                    //if the most matching is secondary we make it primary
                    if (!opportunityAssigned.isPrimary()) {
                        opportunityAssigned.type = OpportunityAssigned.Type.PRIMARY
                        saveOppAssign(opportunityAssigned)
                    }
                    //we remove any other assignation for this usergroup
                    assignedSet.each {
                        if (opportunityAssigned != it) {
                            opp.removeFromAssigned(it)
                            oppNeedsSave = true
                        }
                    }
                }
            } else {
                OpportunityAssigned primaryAssigned
                OpportunityAssigned secondaryAssigned
                // we try to find the most matching assignations
                assignedSet.each {
                    if (it.isPrimary()) {
                        if (primaryAssigned == null || primaryAssigned.user == userToRemove) {
                            primaryAssigned = it
                        }
                    } else {
                        if (secondaryAssigned == null || secondaryAssigned.user == userToRemove) {
                            secondaryAssigned = it
                        }
                    }
                }
                if (primaryAssigned != null || secondaryAssigned != null) {
                    //if only secondary we make it primary
                    if (primaryAssigned == null && secondaryAssigned != null) {
                        secondaryAssigned.type = OpportunityAssigned.Type.PRIMARY
                        saveOppAssign(secondaryAssigned)
                    } else if (secondaryAssigned == null && primaryAssigned != null) {
                        //if the secondary is empty we can try to fill it
                        assignedSet.each {
                            if (it != primaryAssigned && (secondaryAssigned == null || secondaryAssigned.user == userToRemove)) {
                                secondaryAssigned = it
                            }
                        }
                        if (secondaryAssigned != null && secondaryAssigned.isPrimary()) {
                            secondaryAssigned.type = OpportunityAssigned.Type.SECONDARY
                            saveOppAssign(secondaryAssigned)
                        }
                    }
                    //we remove any other assignation for this usergroup
                    assignedSet.each {
                        if (it != primaryAssigned && it != secondaryAssigned) {
                            opp.removeFromAssigned(it)
                            oppNeedsSave = true
                        }
                    }
                }
            }
        }
        if (oppNeedsSave) {
            opportunityService.quickSave(opp)
        }
    }

    @NotTransactional
    opportunityAssignegKey(OpportunityAssigned opportunityAssigned) {
        return "${opportunityAssigned.user.userGroup.message}:${opportunityAssigned.type.id}"
    }

    @Transactional
    def saveOppAssign(OpportunityAssigned opportunityAssigned) {
        return GormEntityUtils.save(opportunityAssigned)
    }
}
