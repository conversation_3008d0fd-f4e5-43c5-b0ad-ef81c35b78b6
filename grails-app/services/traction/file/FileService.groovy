package traction.file

import grails.gorm.transactions.NotTransactional
import grails.gorm.transactions.Transactional
import grails.io.IOUtils
import groovy.transform.Synchronized
import net.coobird.thumbnailator.Thumbnails
import org.apache.catalina.connector.ClientAbortException
import org.apache.commons.imaging.Imaging
import org.apache.commons.imaging.common.ImageMetadata
import org.apache.commons.imaging.formats.jpeg.JpegImageMetadata
import org.apache.commons.imaging.formats.jpeg.iptc.JpegIptcRewriter
import org.apache.commons.io.FileUtils
import org.apache.pdfbox.Loader
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.rendering.PDFRenderer
import org.springframework.web.multipart.MultipartFile
import product.ManagerProductConfig
import traction.AntiSpamLogger
import traction.GormEntityUtils
import traction.category.CategoryFile
import traction.client.Client
import traction.communication.MailMessage
import traction.communication.email.AbstractMessageAttachment
import traction.config.ImageConfig
import traction.history.History
import traction.image.JpegReader
import traction.security.User
import traction.status.StatusFile
import traction.training.TrainingDoc
import traction.vehicle.Vehicle
import traction.vehicle.VehicleWatermarkService
import traction.vendor.BlobDataService
import traction.vendor.StorageService

import javax.annotation.PreDestroy
import javax.imageio.ImageIO
import javax.servlet.http.HttpServletResponseWrapper
import java.awt.*
import java.awt.image.BufferedImage
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import java.util.List
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.zip.ZipOutputStream
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadFactory
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

@Transactional
class FileService {
    private static final int MAX_QUEUE_SIZE = 100
    private static final int MAX_THREADS = 2
    private static final int KEEP_ALIVE = 60

    ExecutorService executorHEICThread = new ThreadPoolExecutor(
            MAX_THREADS,
            MAX_THREADS,
            KEEP_ALIVE,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(MAX_QUEUE_SIZE),
            { Runnable r ->
                Thread t = new Thread(r)
                t.setName("HEIC-Worker-" + UUID.randomUUID())
                return t
            } as ThreadFactory,
            new ThreadPoolExecutor.AbortPolicy()
    )
    ExecutorService executorMp4Thread = new ThreadPoolExecutor(
            MAX_THREADS,
            MAX_THREADS,
            KEEP_ALIVE,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(MAX_QUEUE_SIZE),
            { Runnable r ->
                Thread t = new Thread(r)
                t.setName("MP4-Worker-" + UUID.randomUUID())
                return t
            } as ThreadFactory,
            new ThreadPoolExecutor.AbortPolicy()
    )

    def environmentService
    def assetResourceLocator
    def configService
    def historyService
    def trainingService
    def springSecurityService
    def containerService
    def managerProductConfigService
    def managerProductService
    def attributesService
    def vehicleService
    StorageService storageService
    BlobDataService blobDataService
    VehicleWatermarkService vehicleWatermarkService

    @Transactional
    private FileData transactionalSave(FileData fileData) {
        return GormEntityUtils.save(fileData)
    }

    @NotTransactional
    FileData save(FileData fileData) {
        log.debug "save ${fileData}"
        if (fileData.asBlobData()) {
            if (storageService.bucketConfigured()) {
                blobDataService.save(fileData.asBlobData())
            }
        } else {
            fileData = transactionalSave(fileData)
        }
        if (!fileData) return null

        return fileData
    }

    @NotTransactional
    def get(id) {
        return FileData.get(id)
    }
    @NotTransactional
    def processFile(FileData file, HttpServletResponseWrapper response, String action) {
        if (!file) return
        response.setHeader("Content-disposition", "${action == 'download' ? 'attachment' : 'inline'}; filename=\"${file.name}\"")
        response.contentType = file.status
        if (file.asBlobData()) {
            try {
                byte[] bytes = storageService.getBytes(file.asBlobData())
                if (bytes) {
                    response.setContentLengthLong(bytes.length)
                    response.outputStream << bytes
                } else {
                    if (!environmentService.isDevelopment()) {
                        log.debug "FileData " + file.id + " null in blob"
                    }
                }
                response.outputStream.flush()
            } catch (ClientAbortException e) {
            } catch (Exception e) {
                e.printStackTrace()
                log.warn "sendFileData: Cannot get the file ${file} : ${e}"
            }
        } else {
            File f = new File(file.path)
            try {
                if (f.exists() && f.isFile()) {
                    response.setContentLengthLong(f.length())
                    response.outputStream << f.bytes
                } else {
                    if (!environmentService.isDevelopment()) {
                        log.debug "FileData " + file.id + " null on system"
                    }
                }
                response.outputStream.flush()
            } catch (ClientAbortException e) {
            } catch (Exception e) {
                e.printStackTrace()
                log.warn "sendFileData: Cannot get the file ${file} : ${e}"
            }
        }
    }

    @NotTransactional
    def convertPngWbepToJpg(FileData image, ImageConfig config, def params) {
        if (image.status == StatusFile.PNG.message || image.status == StatusFile.GIF.message || image.status == "image/heic" || image.status == StatusFile.WBEP.message || image.status == StatusFile.HEIC.message || image.status == StatusFile.BMP.message) {
            String newPath = image.path.substring(0, image.path.lastIndexOf('.')) + "." + StatusFile.JPG.ext
            log.debug "Convert " + image.path + " to " + newPath
            BufferedImage bufferedImage

            if (image.status == StatusFile.WBEP.message) {
                log.debug "WBEP convert"
                /*
                String pngpath = image.path.substring(0, image.path.lastIndexOf('.')) + "." + StatusFile.PNG.ext
                WebpIO.create().toNormalImage(image.path, pngpath);
                image.path = pngpath
                bufferedImage = ImageIO.read(new File(image.path));
                */
                return false
            }
            else {
                if (image.status == StatusFile.HEIC.message || image.status == "image/heic") {
                    log.debug "HEIC convert=" + image.path
                    log.debug "jpgpath=" + newPath

                    executorHEICThread.execute {
                        FileData.withNewSession {
                            if (image.path) {
                                try {
                                    log.debug "convertHEICToJPG START"
                                    String script = "/usr/local/bin/magick " + image.path + " " + newPath
                                    log.debug "RUNNING:" + script
                                    Process process = script.execute()
                                    def out = new StringBuffer()
                                    def err = new StringBuffer()
                                    process.consumeProcessOutput(out, err)
                                    process.waitForOrKill(60000)
                                    log.debug "EXIT VAL : " + process.exitValue()

                                    if (process.exitValue() != 0) {
                                        try {
                                            File errorFile = new File("/big_data/error/heicconversion/" + image.path.substring(image.path.lastIndexOf("/") + 1, image.path.length()))
                                            errorFile.getParentFile().mkdirs()
                                            FileUtils.copyFile(new File(image.path), errorFile)
                                        }
                                        catch (Exception e) {
                                            AntiSpamLogger.error "Not able to convert HEIC ${e}"
                                        }

                                        AntiSpamLogger.error "Not able to convert HEIC " + image.path + " to jpg"
                                        return false
                                    }
                                    log.debug "convertHEICToJPG STOP"
                                } catch (Exception ex) {
                                    AntiSpamLogger.error "Error in convertHEICToJPG thread : " + ex.getMessage()
                                    try {
                                        File errorFile = new File("/big_data/error/heicconversion/" + image.path.substring(image.path.lastIndexOf("/") + 1, image.path.length()))
                                        errorFile.getParentFile().mkdirs()
                                        FileUtils.copyFile(new File(image.path), errorFile)
                                    }
                                    catch (Exception e) {
                                        AntiSpamLogger.error "Not able to convert HEIC ${e}"
                                    }
                                    this.delete(image)
                                    return false
                                }
                                try {
                                    bufferedImage = ImageIO.read(new File(newPath));
                                    BufferedImage newBufferedImage = new BufferedImage(bufferedImage.getWidth(),
                                            bufferedImage.getHeight(), BufferedImage.TYPE_INT_RGB);
                                    def graphic = newBufferedImage.createGraphics()
                                    try {
                                        graphic.drawImage(bufferedImage, 0, 0, Color.WHITE, null)
                                    } finally {
                                        graphic.dispose()
                                    }
                                    // write to jpeg file
                                    ImageIO.write(newBufferedImage, StatusFile.JPG.ext, new File(newPath));

                                    new File(image.path).delete();
                                    image.path = newPath;
                                    image.dataSize = getFile(image).length()
                                    image.status = StatusFile.JPG.message
                                    image.previewPath = image.path
                                    if (image.name.lastIndexOf('.') > 0) {
                                        image.name = image.name.substring(0, image.name.lastIndexOf('.')) + "." + StatusFile.JPG.ext
                                    }
                                    BufferedImage bimg = getBufferedImage(image)
                                    if (bimg != null) {
                                        image.width = bimg.getWidth()
                                        image.height = bimg.getHeight()
                                        bimg.flush()
                                    }
                                    if (this.save(image)) {
                                        if (params && params.features && params.features.contains("[noresize]")) {
                                            log.debug "noresize"
                                        } else {
                                            resize(image, config)
                                        }
                                    }
                                    makePreview(image)
                                    return true;
                                } catch (Exception e) {
                                    log.debug "Image not converted : " + image.path + " : " + e.getMessage()
                                    return false;
                                } finally {
                                    if (bufferedImage != null) {
                                        bufferedImage.flush()
                                        bufferedImage = null
                                    }
                                }
                            }
                        }
                    }
                } else {
                    try {
                        bufferedImage = ImageIO.read(new File(image.path));
                        BufferedImage newBufferedImage = new BufferedImage(bufferedImage.getWidth(),
                                bufferedImage.getHeight(), BufferedImage.TYPE_INT_RGB);
                        newBufferedImage.createGraphics().drawImage(bufferedImage, 0, 0, Color.WHITE, null);

                        // write to jpeg file
                        ImageIO.write(newBufferedImage, StatusFile.JPG.ext, new File(newPath));

                        new File(image.path).delete();
                        image.path = newPath;
                        image.dataSize = getFile(image).length()
                        image.status = StatusFile.JPG.message
                        if (image.name.lastIndexOf('.') > 0) {
                            image.name = image.name.substring(0, image.name.lastIndexOf('.')) + "." + StatusFile.JPG.ext
                        }
                        this.save(image)
                        return true;
                    } catch (Exception e) {
                        log.debug "Image not converted : " + image.path + " : " + e.getMessage()
                        return false;
                    } finally {
                        if (bufferedImage != null) {
                            bufferedImage.flush()
                            bufferedImage = null
                        }
                    }
                }
            }
        }
        return true
    }

    @NotTransactional
    def rotateImage(FileData image, String rotation, ImageConfig config) {
        log.debug "rotateImage=" + image + "/" + rotation + "/" + config
        if (image) {
            BufferedImage bimg = getBufferedImage(image)
            if (bimg != null) {
                bimg.flush()
                String formatName = image.name.substring(image.name.lastIndexOf(".") + 1)
                File oldrotImage = new File(config.location + "ROT/" + image.id + "." + formatName)
                log.debug config.location + "ROT/" + image.id + "." + formatName
                oldrotImage.getParentFile().mkdirs()

                if (!oldrotImage.exists()) {
                    FileUtils.copyFile(new File(image.path), oldrotImage)
                }

                if (oldrotImage.exists()) {
                    bimg = ImageIO.read(oldrotImage);
                    int width = bimg.getWidth()
                    int height = bimg.getHeight()
                    bimg.flush()
                    int angdeg = image.angdeg
                    int t = 90
                    switch (rotation) {
                        case "L":
                            angdeg = angdeg - t
                            break
                        case "R":
                            angdeg = angdeg + t
                            break
                    }

                    int finalwidth = 0
                    int finalheight = 0

                    if (angdeg < 0) {
                        angdeg = 270
                    }

                    if (angdeg < 1) {
                        angdeg = 0
                        finalwidth = width
                        finalheight = height
                    }
                    if (angdeg == 90) {
                        finalwidth = height
                        finalheight = width
                    }
                    if (angdeg == 180) {
                        finalwidth = width
                        finalheight = height
                    }
                    if (angdeg == 270) {
                        finalwidth = height
                        finalheight = width
                    }
                    if (angdeg > 359) {
                        angdeg = 0
                        finalwidth = width
                        finalheight = height
                    }
                    log.debug "angdeg=" + angdeg + "/" + finalwidth + "/" + finalheight

                    String to = image.path
                    String basepath = to.substring(0, to.lastIndexOf("/") + 1)
                    if (angdeg.equals(0) == false) {
                        to = basepath + Integer.toString(angdeg) + "." + image.id + "." + formatName
                    } else {
                        to = basepath + image.id + "." + formatName
                    }
                    File newFileName = new File(to)

                    Thumbnails.of(oldrotImage).forceSize(finalwidth, finalheight).rotate(angdeg).outputFormat(formatName).toFile(newFileName);

                    image.width = finalwidth
                    image.height = finalheight
                    image.angdeg = angdeg
                    image.path = to
                    image.previewPath = to
                    this.save(image)
                    log.debug "image?.vehicle=" + image?.vehicle
                    if (image?.vehicle) {
                        vehicleService.updateDateModified(image.vehicle)
                        vehicleService.build(image.vehicle)
                    }
                }
            }
        }
    }

    @Synchronized
    @NotTransactional
    boolean synchResize(FileData image, ImageConfig config) {
        return resize(image, config)
    }

    @NotTransactional
    boolean resize(FileData image, ImageConfig config) {
        log.debug "ImageService.resize with config " + configService.JSONString(config)
        if (image) {
            BufferedImage bimg = getBufferedImage(image)

            if (bimg != null) {
                bimg.flush()
                int widthlimit = config.widthLimit
                int heightlimit = config.heightLimit
                CategoryFile.getAllVehicle().each {
                    if (image.category.equals(it.message)) {
                        widthlimit = vehicleWatermarkService.getBaseWidth()
                        heightlimit = vehicleWatermarkService.getBaseHeight()
                    }
                }
                // Check if image is too big
                if (image.width > widthlimit || image.height > heightlimit || image.dataSize > config.sizeLimit) {

                    // Create temp file
                    String formatName = image.name.substring(image.name.lastIndexOf(".") + 1);
                    File newImage = new File(config.location + "document.temporary/" + image.id + "." + formatName)
                    newImage.getParentFile().mkdirs()

                    // Get resize width and height values
                    int finalwidth = image.width
                    int finalheight = image.height
                    if (finalwidth > widthlimit) {
                        float width2 = finalwidth;
                        float height2 = finalheight;
                        float newwidth = (widthlimit / width2);
                        finalwidth = widthlimit;
                        finalheight = (int) (height2 * newwidth);
                    }
                    if (finalheight > heightlimit) {
                        float width2 = finalwidth;
                        float height2 = finalheight;
                        float newheight = (heightlimit / height2);
                        finalwidth = (int) (width2 * newheight);
                        finalheight = heightlimit;
                    }

                    // Resize width and height
                    try {
                        ImageMetadata curmetadataimg = Imaging.getMetadata(getFile(image));
                        JpegImageMetadata curmetadata = (JpegImageMetadata) curmetadataimg
                        Thumbnails.of(getFile(image)).forceSize(finalwidth, finalheight).outputFormat(formatName).toFile(newImage);
                        attributesService.copyMetadataIPTC(curmetadata, newImage, null)
                    } catch (Exception e) {
                        Thumbnails.of(getFile(image)).forceSize(finalwidth, finalheight).outputFormat(formatName).toFile(newImage);
                    }

                    if (image.status.equalsIgnoreCase(StatusFile.PNG.message)) {
                    } else {
                        // Reduce file size until > config.sizeLimit
                        double intqal = 1.00;
                        while (newImage.length() > config.sizeLimit) {
                            log.debug "image size:" + newImage.length()
                            log.debug "intqal:" + intqal
                            intqal = (intqal - 0.15);
                            if (intqal < 0) {
                                log.warn "Cannot reduce file to config.sizeLimit(${config.sizeLimit}) " + image
                                return false
                            }

                            try {
                                ImageMetadata curmetadataimg = Imaging.getMetadata(getFile(image));
                                JpegImageMetadata curmetadata = (JpegImageMetadata) curmetadataimg
                                Thumbnails.of(getFile(image)).forceSize(finalwidth, finalheight).outputFormat(formatName).outputQuality(intqal).toFile(newImage);
                                attributesService.copyMetadataIPTC(curmetadata, newImage, null)
                            } catch (Exception e) {
                                Thumbnails.of(getFile(image)).forceSize(finalwidth, finalheight).outputFormat(formatName).outputQuality(intqal).toFile(newImage);
                            }
                        }
                    }

                    // Replace original image with new one and save all changes
                    Path from = newImage.toPath()
                    Path to = getFile(image).toPath()
                    log.debug "from:" + from + " to:" + to
                    Files.move(from, to, StandardCopyOption.REPLACE_EXISTING)
                    bimg = getBufferedImage(image)
                    image.width = bimg.getWidth()
                    image.height = bimg.getHeight()
                    image.dataSize = getFile(image).length()
                    bimg.flush()
                    this.save(image)
                    return true
                }
            }
        }
        return false
    }

    @NotTransactional
    File getFile(FileData image) {
        if (!image) return null
        try {
            return new File(image.path)
        }
        catch (Exception e) {
            log.debug "Error getting file:" + image.path
        }
        return null
    }

    @NotTransactional
    BufferedImage getBufferedImage(FileData image) {
        if (!image) return null
        if (image.asBlobData()) {
            InputStream is = new ByteArrayInputStream(storageService.getBytes(image.asBlobData()))
            return ImageIO.read(is)
        }
        File imageFile = getFile(image)
        if (!imageFile) return null
        try {
            def modifiedInLastSecond = imageFile.lastModified() <= System.currentTimeMillis() - 1000
            if ((imageFile.name.contains(".jpg") || imageFile.name.contains(".jpeg")) && !modifiedInLastSecond) {
                return Thumbnails.of(imageFile).scale(1).asBufferedImage()
            }
            return ImageIO.read(imageFile)
        }
        catch (Exception e) {
            try {
                JpegReader aObj = new JpegReader()
                BufferedImage bimg = aObj.readImage(imageFile)
                log.debug "bug image:" + image.path
                BufferedImage dimg = new BufferedImage(bimg.getWidth(), bimg.getHeight(), bimg.getType())
                Graphics2D g = dimg.createGraphics()
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR)
                g.drawImage(bimg, 0, 0, bimg.getWidth(), bimg.getHeight(), 0, 0, bimg.getWidth(), bimg.getHeight(), null)
                g.dispose()
                File outputfile = new File(image.path)
                ImageIO.write(dimg, "jpg", outputfile)
                dimg.flush()
                bimg.flush()
            }
            catch (Exception e2) {
                return null
            }
        }
        return ImageIO.read(imageFile)
    }

    @NotTransactional
    def convertToMP4() {
        executorMp4Thread.execute {
            FileData.withNewSession {
                for (int i = 0; i < 100; i++) {
                    def fileList = FileData.createCriteria().list(max: 1) {
                        eq("status", StatusFile.CONVERTING_TO_MP4.message)
                    }
                    if (fileList) {
                        FileData file = fileList.first()
                        try {
                            log.debug "convertToMP4 START"
                            int index = file.path.lastIndexOf('.')
                            if (index > 0) {
                                String mp4Path = file.path.substring(0, index) + "." + StatusFile.MP4.ext
                                log.debug "New- - mp4 path = " + mp4Path

                                String script = "ffmpeg -i " + file.path + " -vcodec h264 -acodec aac -strict -2 " + mp4Path
                                log.debug "RUNNING:" + script
                                Process process = script.execute()
                                def out = new StringBuffer()
                                def err = new StringBuffer()
                                process.consumeProcessOutput(out, err)
                                process.waitForOrKill(60000)
                                log.debug "EXIT VAL : " + process.exitValue()
                                if (process.exitValue() != 0) {
                                    try {
                                        File errorFile = new File("/big_data/error/mp4conversion/" + file.path.substring(file.path.lastIndexOf("/") + 1, file.path.length()))
                                        errorFile.getParentFile().mkdirs()
                                        FileUtils.copyFile(new File(mp4Path), errorFile)
                                    }
                                    catch (Exception e) {
                                        log.warn "Not able to convert FileData ${e}"
                                    }

                                    log.warn "Not able to convert FileData " + file + " to mp4"
                                    deleteFromDisk(mp4Path)
                                    return false
                                }
                                deleteFromDisk(file.path)
                                file.path = mp4Path
                                file.status = StatusFile.MP4.message
                                index = file.name.lastIndexOf('.')
                                if (index > 0) {
                                    file.name = file.name.substring(0, index) + "." + StatusFile.MP4.ext
                                } else {
                                    file.name = file.name + "." + StatusFile.MP4.ext
                                }
                                file.dataSize = getFile(file).length()
                                this.save(file)

                                TrainingDoc trainingDoc = TrainingDoc.createCriteria().get {
                                    files {
                                        eq("id", file.id)
                                    }
                                }
                                if (trainingDoc) {
                                    if (trainingDoc.files) {
                                        int o = 1;
                                        trainingDoc.files = trainingDoc.files.sort { it.ordre }
                                        trainingDoc.files.each {
                                            if (it.ordre != o) {
                                                it.ordre = o
                                                GormEntityUtils.save(it)
                                            }
                                            log.debug "SAVED:" + o
                                            o++;
                                        }
                                    }
                                }
                            }
                            log.debug "convertToMP4 STOP"
                        }
                        catch (Exception ex) {
                            log.warn "Error in convertToMP4 thread : " + ex.getMessage()
                        }
                    } else {
                        break
                    }
                }
            }
        }
        return true
    }

    @Transactional
    private void deleteTransactional(FileData image) {
        image.delete(flush: true)
    }

    @NotTransactional
    boolean delete(FileData image, boolean makeHistory = true) {
        log.debug "ImageService.delete:" + image?.id
        if (!image) return false
        if (image.asBlobData()) {
            storageService.deleteBlobData(image.asBlobData())
        } else {
            deleteFromDisk(image.path)
        }

        History.withTransaction {
            History.executeUpdate("update History set files = ?0 where files = ?1", [null, image])
        }
        Vehicle vehicle = image.vehicle
        if (image.cnf) {
            ManagerProductConfig cnf = image.cnf
            image.cnf = null
            ManagerProductConfig.withTransaction {
                if (cnf) {
                    cnf.img = null
                    cnf.save(flush: true)
                }
            }
        }

        if (!image.asBlobData()) {
            deleteTransactional(image)
        }

        if (vehicle) {
            vehicleService.updateDateModified(vehicle)
        }

        return true
    }

    @NotTransactional
    def deleteFromDisk(String filePath) {
        try {
            log.debug "delete path=" + filePath
            File f = new File(filePath)
            log.debug "delete:" + f.delete()
        }
        catch (Exception e) {
            log.debug "File not deleted from system"
        }
    }

    @NotTransactional
    ImageConfig getImageConfig() {
        return new ImageConfig(configService.get("CONFIG", "IMAGE"))
    }

    @Transactional
    def addFileToDisk(def params) {
        def result = [:]
        result.filename = params?.filecontentname
        result.filetype = params?.filecontenttype
        result.success = false

        if (params.filecontentname && params.filecontenttype && params.iprofile && params.iprofile.equals("") == false) {
            File directory = new File("/big_data/import/" + params.iprofile + "/")

            if (!directory.exists()) {
                directory.mkdir()
            }

            String filepath = "/big_data/import/" + params.iprofile + "/" + params.filecontentname
            File file = new File(filepath)
            file.getParentFile().mkdirs()
            try {
                if (params.filecontentbytes) {
                    FileUtils.writeByteArrayToFile(file, params.filecontentbytes)
                    result.success = true
                }
            }
            catch (Exception e) {
                AntiSpamLogger.error "addFile Error: ${e}"
                e.printStackTrace()
                result.success = false
            }
        }
        return result
    }

    @Transactional
    def addPicturestoProduct(File pic, Vehicle product, int ordrepic) {
        if (pic.exists() && product) {
            int skipimage = 1
            def ext = pic.path.substring(pic.path.lastIndexOf('.') + 1)
            def name = pic.path.substring(pic.path.lastIndexOf('\\') + 1)
            if (ext.equalsIgnoreCase("jpg") || ext.equalsIgnoreCase("bmp") || ext.equalsIgnoreCase("png") || ext.equalsIgnoreCase("heic")) {
                skipimage = 0
            }
            if (skipimage.equals(0)) {
                FileData newfile = new FileData()
                ImageConfig config = configService.get("CONFIG", "IMAGE")
                newfile.author = springSecurityService.currentUser
                if(!newfile.author) {
                    newfile.author = User.findByUsername("api")
                }
                if (ext.equalsIgnoreCase("jpg")) {
                    newfile.status = StatusFile.JPG.message
                }
                if (ext.equalsIgnoreCase("png")) {
                    newfile.status = StatusFile.PNG.message
                }
                if (ext.equalsIgnoreCase("heic")) {
                    newfile.status = StatusFile.HEIC.message
                }

                newfile.vehicle = product
                newfile.ordre = ordrepic
                newfile.date = new Date()
                newfile.name = name
                newfile.category = CategoryFile.VEHICLE_PICTURE.message
                newfile = this.save(newfile)
                if (newfile) {
                    int indexPt = newfile.name.lastIndexOf(".");
                    String fileName = (indexPt == -1) ? newfile.id : newfile.id + newfile.name.substring(indexPt)
                    newfile.path = config.location + newfile.category + "/" + fileName
                    // Create directory
                    File file = new File(newfile.path)
                    file.getParentFile().mkdirs()

                    // Write file
                    try {
                        try {
                            java.nio.file.Files.copy(pic.toPath(), file.toPath(), StandardCopyOption.REPLACE_EXISTING)
                        }
                        catch (Exception e2) {
                            e2.printStackTrace()
                        }
                    }
                    catch (Exception e) {
                        AntiSpamLogger.error "addFile Error: ${e}"
                        e.printStackTrace()
                    }
                    newfile.dataSize = file.length()
                    this.save(newfile)

                    if (ext.equalsIgnoreCase("heic")) {
                        convertPngWbepToJpg(newfile, config, null)
                    } else {
                        if (ext.equalsIgnoreCase("png")) {
                            convertPngWbepToJpg(newfile, config, null)
                        }
                        if (ext.equalsIgnoreCase("webp")){
                            convertPngWbepToJpg(newfile, config, null)
                        }
                        if (ext.equalsIgnoreCase("bmp")) {
                            convertPngWbepToJpg(newfile, config, null)
                        }
                        if (newfile.status.startsWith("image/")) {
                            newfile.previewPath = newfile.path
                            BufferedImage bimg = getBufferedImage(newfile)
                            if (bimg != null) {
                                newfile.width = bimg.getWidth()
                                newfile.height = bimg.getHeight()
                                bimg.flush()
                                if (this.save(newfile)) {
                                    resize(newfile, config)
                                }
                            }
                        }
                        makePreview(newfile)
                    }
                }
            }
        }
    }

    @NotTransactional
    FileData addFile(Map params) {
        FileData newfile

        if ((params.filecontent || (params.filecontenttype && params.filecontentname && params.filecontentbytes)) && params.category) {
            log.debug "Creation of new file..."

            if (storageService.bucketConfigured() && params.storageType == "BlobData") {
                newfile = new BlobData()
            } else {
                newfile = new FileData()
            }
            ImageConfig config = this.getImageConfig()
            newfile.author = springSecurityService.currentUser
            if (params.vehicleWatermark) {
                newfile.vehicleWatermark = params.vehicleWatermark
                FileData oldFile = newfile.vehicleWatermark.getFileData()
                delete(oldFile)
            }
            if (params.client) {
                newfile.client = params.client
            }
            if (params.ordre) {
                newfile.ordre = params.ordre
            }
            if (params.draftId) {
                newfile.draftId = params.draftId
            }
            if (params.date) {
                newfile.date = params.date
            }
            if (params.profile) {
                newfile.profile = params.profile
            }
            if (params.vehicle) {
                newfile.vehicle = params.vehicle
            }
            if (params.task) {
                newfile.task = params.task
            }
            if (params.vehicleInspectionItem) {
                newfile.vehicleInspectionItem = params.vehicleInspectionItem
            }
            if (params.opportunityOption) {
                newfile.opportunityOption = params.opportunityOption
            }
            if (params.workflowBoard) {
                newfile.workflowBoard = params.workflowBoard
            }
            if (params.opportunity) {
                newfile.opportunity = params.opportunity
            }
            if (params.mailTemplate) {
                newfile.mailTemplate = params.mailTemplate
            }
            if (params.communication) {
                newfile.communication = params.communication
            }
            if (params.callNotes) {
                newfile.callNotes = params.callNotes
            }
            if (params.videoRoom) {
                newfile.videoRoom = params.videoRoom
            }
            if (params.clientWorkOrderJob) {
                newfile.clientWorkOrderJob = params.clientWorkOrderJob
            }
            if (params.vehicle) {
                newfile.vehicle = params.vehicle
                if (!params.ordre) {
                    Integer max = 0
                    try{
                        if (newfile.vehicle && newfile.vehicle.pictures){
                            max = newfile.vehicle.pictures.ordre.max()
                        }
                    }catch(Exception e){
                        max = 0
                    }
                    newfile.ordre = max && max > 0 ? max + 1 : 1
                }
            }
            if (params.filecontenttype) {
                newfile.status = params.filecontenttype
            } else {
                newfile.status = params.filecontent.getContentType()
            }
            if (params.filecontentname) {
                newfile.name = params.filecontentname
            } else {
                newfile.name = params.filecontent.getOriginalFilename()
            }
            newfile.category = params.category

            if (newfile.category.equals(CategoryFile.DRAFT_ATTACHMENTS.message)) {
                newfile.description = getDraftAttachementDescription(newfile.client)
            }

            if (storageService.bucketConfigured() && newfile.asBlobData() && params.storageType == "BlobData") {
                if (params.filecontent) {
                    newfile = storageService.create(newfile.asBlobData(), params.filecontent)
                } else if (params.filecontenttype && params.filecontentname && params.filecontentbytes) {
                    newfile = storageService.createFromBytes(newfile.asBlobData(), params.filecontentbytes)
                }
            }
            else {
                newfile = this.save(newfile)

                if (newfile) {
                    if (params.features?.contains("[sort]")) {
                        params.method = "COUNT"
                        def data = containerService.getContainerFormulaResult(params)
                        newfile.ordre = data
                    }
                    if (params.doc) {
                        params.doc.files.add(newfile)
                        newfile.ordre = params.doc.files.size()
                        TrainingDoc.withTransaction {
                            params.doc.save(flush: true)
                        }
                    }

                    newfile.path = config.getFileDataPath(newfile)
                    // Create directory
                    File file = new File(newfile.path)
                    file.getParentFile().mkdirs()
                    // Write file
                    InputStream inputStream
                    try {
                        if (params.filecontentbytes) {
                            FileUtils.writeByteArrayToFile(file, params.filecontentbytes)
                        } else {
                            inputStream = params.filecontent.getInputStream()
                            java.nio.file.Files.copy(inputStream, file.toPath(), StandardCopyOption.REPLACE_EXISTING)
                            IOUtils.closeQuietly(inputStream)
                        }
                    }
                    catch (Exception e) {
                        log.warn "addFile Error: ${e}"
                        e.printStackTrace()
                    } finally {
                        if (inputStream) {
                            inputStream.close()
                        }
                    }

                    if(params.category == CategoryFile.VEHICLE_PICTURE.message){
                        // look out for jpg || jpeg only
                        if(!(newfile.status in [StatusFile.JPG.message, StatusFile.JPEG.message,StatusFile.JPG2.message, StatusFile.JPEG2.message])){
                            deleteFromDisk(newfile.path)
                            delete(newfile, false)
                            log.debug "VEHICLES ONLY SUPPORT JPEG MEDIA"

                            return null
                        }
                    }

                    // Set file informations
                    newfile.dataSize = file.length()
                    this.save(newfile)
                    if (newfile.status.startsWith("image/")) {
                        newfile.previewPath = newfile.path
                        BufferedImage bimg = getBufferedImage(newfile)
                        if (bimg != null) {
                            newfile.width = bimg.getWidth()
                            newfile.height = bimg.getHeight()
                            bimg.flush()
                            if (this.save(newfile)) {
                                if (params.features && params.features.contains("[noresize]")) {
                                    log.debug "noresize"
                                } else {
                                    resize(newfile, config)
                                }
                            }
                        }
                    } else if (newfile.status.startsWith("video/") && newfile.category == CategoryFile.TRAINING.message) {
                        if (newfile.status != StatusFile.MP4.message) {
                            newfile.status = StatusFile.CONVERTING_TO_MP4.message
                            this.save(newfile)
                            convertToMP4()
                        }
                    }
                    makePreview(newfile)
                }
            }
        }

        return newfile
    }

    @NotTransactional
    def deleteAttachment(params) {
        def ret = [success: false, message: "can not delete attachment"]
        if (params.id) {
            def file = FileData.get(params.id)
            if (params.trainingDocid) {
                def doc = trainingService.getDoc(params.trainingDocid)
                if (doc && file) {
                    doc.removeFromFiles(file)
                    trainingService.saveDoc(doc)
                    int o = 1
                    doc.files.each {
                        if (it.ordre != o) {
                            it.ordre = o
                            this.save(it)
                        }
                        o++
                    }
                }
            }
            if (file) {
                // if we want to be sure it's totally deleted
                if (params.isTotal) {
                    this.delete(file)
                } else if (file.communication) {
                    file.category = CategoryFile.COMMUNICATION.message
                    this.save(file)
                } else if (file.task) {
                    file.category = CategoryFile.TASK_ATTACHMENTS.message
                    this.save(file)
                } else {
                    this.delete(file)
                }
                ret = [success: true, message: "attachment deleted"]
            }
        }
        return ret
    }

    @NotTransactional
    def addFileDataAuthor(def params) {
        int importmode = 0
        if (params?.filecontent && params?.filecontent.equals("importPrdManager")) {
            importmode = 1
        }

        int skipimage = 0
        if (importmode.equals(1)) {
            skipimage = 0
        } else {
            if (params?.filecontent && params?.filecontent?.getContentType()) {
//                if (params?.filecontent.getContentType() == StatusFile.WBEP.message || params?.filecontent.getContentType() == StatusFile.HEIC.message)
                if (params?.filecontent.getContentType() == StatusFile.WBEP.message) {
                    skipimage = 1
                }
            }
        }

        if (skipimage.equals(0)) {
            if (params?.features?.contains("[unique]")) {
                if (params?.prdwatermark) {
                    List<FileData> result = FileData.createCriteria().list() {
                        eq("vehicleWatermark", params?.prdwatermark)
                        eq("category", params?.category)
                    }
                    result.each {
                        delete(it)
                    }
                }
                if (params?.cnf) {
                    List<FileData> result = FileData.createCriteria().list() {
                        eq("cnf", params?.cnf)
                        eq("category", params?.category)
                    }
                    result.each {
                        delete(it)
                    }
                }
                if (params?.packcustom) {
                    List<FileData> result = FileData.createCriteria().list() {
                        eq("options", params?.packcustom)
                        eq("category", params?.category)
                    }
                    result.each {
                        delete(it)
                    }
                }
            }
            def newfile
            int iswbep = 0
            boolean isJfif = false
            int isjpgorpng = 0
            if (params.filecontenttype) {
                if (params.filecontenttype.equals(StatusFile.WBEP.message)) {
                    iswbep = 1
                }
            } else {
                if (params.filecontent.getContentType().equals(StatusFile.WBEP.message)) {
                    iswbep = 1
                }
            }

            try {
                if (params.filecontentname) {
                    isJfif = params.filecontentname.contains("jfif")
                } else {
                    isJfif = params.filecontent.getOriginalFilename().contains("jfif")
                }
            } catch (Exception e) {
                log.debug("filecontent.getOriginalFilename() called on non file")
            }

            if (params.prdproduct) {
                if (params.filecontenttype) {
                    if (params.filecontenttype.equals(StatusFile.PNG.message) || params.filecontenttype.equals(StatusFile.BMP.message) || params.filecontenttype.equals(StatusFile.JPG.message) || params.filecontenttype.equals(StatusFile.JPG2.message) || params.filecontenttype.equals(StatusFile.JPEG.message) || params.filecontenttype.equals(StatusFile.JPEG2.message)  && !isJfif) {
                        isjpgorpng = 1
                    }
                } else {
                    if (params.filecontent.getContentType().equals(StatusFile.PNG.message) || params.filecontent.getContentType().equals(StatusFile.BMP.message) || params.filecontenttype.equals(StatusFile.JPG.message) || params.filecontenttype.equals(StatusFile.JPG2.message) || params.filecontenttype.equals(StatusFile.JPEG.message) || params.filecontenttype.equals(StatusFile.JPEG2.message) && !isJfif) {
                        isjpgorpng = 1
                    }
                }
            } else {
                isjpgorpng = 1
            }
            if (params.filecontent && params.category && iswbep.equals(0) && isjpgorpng.equals(1)) {
                newfile
                if (storageService.bucketConfigured() && params.storageType && params.storageType == "BlobData" && params.filecontent) {
                    newfile = new BlobData()
                } else {
                    newfile = new FileData()
                }
                ImageConfig config = configService.get("CONFIG", "IMAGE")
                newfile.author = springSecurityService.currentUser

                if (params.filecontenttype) {
                    newfile.status = params.filecontenttype
                } else {
                    newfile.status = params.filecontent.getContentType()
                }
//                if (params.prdproduct) {
//                    newfile.managerproduct = params.prdproduct
//                }
                if (params.client) {
                    newfile.client = params.client
                }
                if (params.vehicle) {
                    newfile.vehicle = params.vehicle
                }
//                if (params.productOption) {
//                    newfile.productOption = params.productOption
//                }
                if (params.opportunityOption) {
                    newfile.opportunityOption = params.opportunityOption
                }
                if (params.workflowBoard) {
                    newfile.workflowBoard = params.workflowBoard
                }
                if (params.oppor) {
                    newfile.opportunity = params.oppor
                }
                if (params.cnf) {
                    newfile.cnf = params.cnf
                }
//                if (params.packcustom) {
//                    newfile.options = params.packcustom
//                }
                if (params.ordre) {
                    newfile.ordre = params.ordre
                }
                if (params.date) {
                    newfile.date = params.date
                }
                if (params.filecontentname) {
                    newfile.name = params.filecontentname
                } else {
                    newfile.name = params.filecontent.getOriginalFilename()
                }
                newfile.category = params.category

                if (storageService.bucketConfigured() && newfile.asBlobData() && params.filecontent) {
                    return storageService.create(newfile.asBlobData(), params.filecontent)
                }

                newfile = this.save(newfile)
                if (newfile) {
                    log.debug "newfile desc" + newfile.description
                    if (params.features?.contains("[sort]")) {
                        def containerformulasort = containerService.getContainerFormula(params, 0)
                        containerformulasort.method = "COUNT"
                        def data = containerService.getContainerFormulaResult(containerformulasort)
                        newfile.ordre = data
                    }
                    // Create file name
                    int indexPt = newfile.name.lastIndexOf(".");
                    String fileName = (indexPt == -1) ? newfile.id : newfile.id + newfile.name.substring(indexPt)
                    newfile.path = config.location + newfile.category + "/" + fileName
                    // Create directory
                    File file = new File(newfile.path)
                    file.getParentFile().mkdirs()
                    // Write file
                    try {
                        if (params.filecontentbytes) {
                            FileUtils.writeByteArrayToFile(file, params.filecontentbytes)
                        } else {
                            InputStream is
                            try {
                                is = params.filecontent.getInputStream()
                                java.nio.file.Files.copy(is, file.toPath(), StandardCopyOption.REPLACE_EXISTING)
                                IOUtils.closeQuietly(is)
                            }
                            catch (Exception e) {
                                e.printStackTrace()
                            }
                            finally {
                                if (is != null) {
                                    is.close()
                                }
                            }
                        }
                    }
                    catch (Exception e) {
                        AntiSpamLogger.error "addFile Error: ${e}"
                        e.printStackTrace()
                    }
                    // Set file informations
                    newfile.dataSize = file.length()
                    this.save(newfile)

                    convertPngWbepToJpg(newfile, config, params)

                    if (newfile.status == StatusFile.HEIC.message || newfile.status == "image/heic") {
                    } else {
                        if (newfile.status.startsWith("image/")) {
                            newfile.previewPath = newfile.path
                            BufferedImage bimg = getBufferedImage(newfile)
                            if (bimg != null) {
                                newfile.width = bimg.getWidth()
                                newfile.height = bimg.getHeight()
                                bimg.flush()
                                if (this.save(newfile)) {
                                    if (params.synchresize) {
                                        synchResize(newfile, config)
                                    } else if (params.features && params.features.contains("[noresize]")) {
                                        log.debug "noresize"
                                    } else {
                                        resize(newfile, config)
                                    }
                                }
                            }
                        }
                        makePreview(newfile)
                    }
                }
            }
            return newfile
        } else {
            return null
        }
    }

    @Transactional
    def saveFile(FileData file, String name) {
        if (!name) return null
        def format = file.name.substring(file.name.lastIndexOf(".") + 1);
        def formatname
        if (name.contains(".")) {
            formatname = name.substring(name.lastIndexOf(".") + 1);
        }

        if (!formatname) {
            file.name = name + "." + format
        } else {
            if (format.equalsIgnoreCase(formatname)) {
                file.name = name
            } else {
                file.name = name + "." + format
            }
        }

        if (!transactionalSave(file)) return null
        return file
    }

    @Transactional
    void cloneVehiclePictures(Vehicle vehicle, Vehicle clone) {
        vehicle.pictures.each {
            cloneVehiclePicture(clone, it)
        }
    }

    @Transactional
    private void cloneVehiclePicture(Vehicle vehicle, FileData fileData) {
        FileData cloneFile = clone(fileData, null)
        cloneFile.vehicle = vehicle
        save(cloneFile)
    }

    @NotTransactional
    String getDraftAttachementDescription(Client client) {
        return "Draft Attachement for Client : " + client?.id
    }

    @NotTransactional
    def makePreview(FileData file) {
        try {
            switch (file.status) {
                case StatusFile.PDF.message:
                    file.previewPath = file.path + ".prev.jpg"
                    PDDocument document = Loader.loadPDF(getFile(file));
                    PDFRenderer pdfRenderer = new PDFRenderer(document);

                    BufferedImage bim = pdfRenderer.renderImage(0);

                    File outputfile = new File(file.previewPath);
                    outputfile.createNewFile()
                    Thumbnails.of(bim).forceSize(500, 500).outputFormat("jpg").toFile(outputfile);
                    document.close()
                    break
                case StatusFile.MP4.message:
                    file.previewPath = file.path + ".prev.jpg"
                    String script = "ffmpeg -i " + file.path + " -vframes 1 " + file.previewPath
                    log.debug "RUNNING:" + script
                    Process process = script.execute()
                    def out = new StringBuffer()
                    def err = new StringBuffer()
                    process.consumeProcessOutput(out, err)
                    process.waitForOrKill(30000) // 30 seconds
                    log.debug "EXIT VAL : " + process.exitValue()
                    if (process.exitValue() != 0) {
                        deleteFromDisk(file.previewPath)
                        file.previewPath = ""
                        log.warn "Not able to make preview file for FileData (MP4) " + file + " with command : " + script
                    }
                    break
            }
        }
        catch (Exception e) {
            log.warn "Exception making preview for file ${file}. ${e.getMessage()}"
        }
        this.save(file)
    }

    @NotTransactional
    def getFilebyFormula(def containerformula) {
        //log.debug "getFilebyFormula ${containerformula}"
        int offsetsize = -1
        int tblSize = -1
        if (containerformula?.tblSize) {
            tblSize = containerformula?.tblSize
        }
        if (containerformula?.page) {
            if (containerformula.page > -1) {
                offsetsize = containerformula.page * tblSize
            }
        }
        def result
        switch (containerformula.formula) {
            case "DOCUMENTBYPRDWATERMARK":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("vehicleWatermark", containerformula?.prdwatermark)
                    eq("category", containerformula.category)
                    order("ordre", "asc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTOPTIONSBYCATEGORY":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("options", containerformula?.packcustom)
                    eq("category", containerformula.category)
                    order("ordre", "asc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "FILEBYCATEGORY":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("category", containerformula.category)
                    order("date", "desc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DRAFT_ATTACHMENTS":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("category", containerformula.category)
                    eq("author", containerformula.user)
                    if (containerformula.client) {
                        eq("client", containerformula.client)
                    } else {
                        isNull("client")
                    }
                    order("date", "desc")
                    if (containerformula.method.equalsIgnoreCase("ID")) {
                        projections {
                            property("id")
                        }
                    }
                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYCATEGORY_PRODUCTMANAGER":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("cnf", containerformula?.cnf)
                    eq("category", containerformula.category)
                    order("ordre", "asc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYCATEGORY":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("category", containerformula.category)
                    order("date", "desc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYVEHICLE":
                log.debug("DOCUMENTBYVEHICLE:$containerformula")
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("vehicle", containerformula.vehicle)
                    //gt("dataSize", 0)
                    order("ordre", "asc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYCLIENTBYCATEGORY":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("client.id", containerformula.client.id)
                    or {
                        eq("category", containerformula.category)
                        eq("category", containerformula.category2)
                    }
                    ne("status", StatusFile.DANGEROUS.message)
                    order("date", "desc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                if (!containerformula.method.equalsIgnoreCase("COUNT")) {
                    result = result.findAll { !it.communication || it.communication.userCanAccess(springSecurityService.currentUser) }
                }

                break
            case "DocumentAdding":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("client", containerformula.client)
                    or {
                        eq("category", containerformula.category)
                        eq("category", containerformula.category2)
                    }
                    //TODO :TEST ca
//                    if (containerformula.product) {
//                        eq("product", containerformula.product)
//                    }
                    if (containerformula.oppor) {
                        eq("opportunity", containerformula.oppor)
                    }
                    if (containerformula.productOption) {
                        eq("productOption", containerformula.productOption)
                    }
                    if (containerformula.opportunityOption) {
                        eq("opportunityOption", containerformula.opportunityOption)
                    }
                    ne("status", StatusFile.DANGEROUS.message)
                    order("date", "desc")

                    projections {
                        if (containerformula.method.equalsIgnoreCase("COUNT")) {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYFORMPROFILE":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("profile", containerformula.profile)
                    order("date", "desc")

                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "DOCUMENTBYWORKFLOWBOARD":
                result = FileData.createCriteria().list([max: tblSize, offset: offsetsize]) {
                    eq("workflowBoard", containerformula.workflowBoard)
                    order("date", "desc")
                    if (containerformula.method.equalsIgnoreCase("COUNT")) {
                        projections {
                            countDistinct "id"
                        }
                    }
                }
                break
            case "FILEBYTRAININGDOC":
                result = containerformula.doc?.files

                if (containerformula.method.equalsIgnoreCase("COUNT")) {
                    if (result) {
                        result = [result.size()]
                    } else {
                        result = [0]
                    }
                }
                break
        }

        if (containerformula.method.equalsIgnoreCase("COUNT")) {
            if (result) {
                result = result.first()
            } else {
                result = 0
            }
        } else {
            if (containerformula.features.contains("[sort]") && result) {
                result = result.sort { it.ordre }
            }
        }
        return result
    }

    @NotTransactional
    FileData saveAttachement(MailMessage mailMessage, AbstractMessageAttachment emailMessageAttachment) {
        if (!mailMessage || !emailMessageAttachment) return null
        FileData fileData = addFile([
                filecontenttype : emailMessageAttachment.getMimeType(),
                filecontentname : emailMessageAttachment.getFilename(),
                filecontentbytes: emailMessageAttachment.getFileByteArray(),
                category        : CategoryFile.CLIENT_ATTACHMENTS.message,
                description     : "Email Attachement",
                communication   : mailMessage,
                client          : mailMessage.client,
                storageType     : "BlobData",
        ]);
        String extension = fileData.name.replaceAll(".*\\.", "").toLowerCase()
        log.debug "extension:" + extension
        if (configService.get("MAIL", "dangerous.extensions").contains(extension)) {
            fileData.status = StatusFile.DANGEROUS.message
            log.debug "DANGEROUS FILE FOUND"
        }
        if (fileData.status.startsWith("image/")) {
            fileData.previewPath = fileData.path
            BufferedImage bimg = getBufferedImage(fileData)
            if (bimg != null) {
                fileData.width = bimg.getWidth()
                fileData.height = bimg.getHeight()
                bimg.flush()
            }
        }
        return save(fileData)
    }

    @NotTransactional
    private File writeAttachmentFile(MailMessage mailMessage, AbstractMessageAttachment emailMessageAttachment) {
        File attachmentStorageFolder = createOrGetAttachmentFolder(mailMessage)
        File file = new File(attachmentStorageFolder, emailMessageAttachment.getFilename())
        int index = 0
        while (file.exists()) {
            index++
            file = new File(attachmentStorageFolder, "($index)${emailMessageAttachment.getFilename()}")
        }
        FileOutputStream fileOutFile = new FileOutputStream(file, false)
        fileOutFile.write(emailMessageAttachment.getFileByteArray())
        fileOutFile.close()
        return file
    }

    @NotTransactional
    private File createOrGetAttachmentFolder(MailMessage mailMessage) {
        File folder = new File(getAttachmentFolderPath(mailMessage))
        if (!folder.exists()) {
            folder.mkdirs()
        }
        return folder
    }

    @NotTransactional
    private String getAttachmentFolderPath(MailMessage mailMessage) {
        String attPath = configService.get("GOOGLE", "google.attachement.path")
        String folder1 = mailMessage.extID.substring(0, 2)
        String folder2 = mailMessage.extID.substring(2, 4)
        return "$attPath/$folder1/$folder2/${mailMessage.extID}"
    }

    /**
     * Method to get the filedata of a specific category and of a specific parent
     * @param category <String>
     * @return
     */
    @NotTransactional
    List<FileData> filesByCategory(String category) {
        List<FileData> result = FileData.createCriteria().list() {
            eq("category", category)
            order("date", "desc")
        }
        return result
    }

    @NotTransactional
    List<FileData> addFilesWithCategory(String category, List<MultipartFile> files) {
        List<FileData> fileDatas = new ArrayList<>()
        for (MultipartFile file : files) {
            FileData fileData = addFile([filecontenttype: file.contentType, filecontentname: file.originalFilename, filecontentbytes: file.bytes, category: category])
            if (fileData) {
                fileDatas.add(fileData)
            }
        }
        return fileDatas
    }

    @NotTransactional
    FileData clone(FileData fileData, String replacementCategory) {
        FileData clone
        clone = fileData.clone()
        clone.date = new Date()
        clone.id = null
        clone.mailTemplate = null
        clone.path = ""
        clone.previewPath = ""
        if (replacementCategory) {
            clone.category = replacementCategory
        }
        if (!save(clone)) return null
        clone.path = this.getImageConfig().getFileDataPath(clone)
        save(clone)
        File initialFile = getFile(fileData)
        File fileClone = getFile(clone)
        fileClone.getParentFile().mkdirs()
        try {
            java.nio.file.Files.copy(initialFile.toPath(), fileClone.toPath(), StandardCopyOption.REPLACE_EXISTING)
        }
        catch (Exception e) {
            log.warn "clone copy error: ${e}"
            e.printStackTrace()
            return null
        }
        return clone
    }

    @NotTransactional
    void setMetaDataIPTC(FileData fileData, FileDataMetaDataIptc fileDataMetaDataIptc) {
        File file = getFile(fileData)
        File tempFile

        if (!file) return
        try {

            tempFile = new File(file.path + ".tmp")
            new JpegIptcRewriter().writeIPTC(file, tempFile.newOutputStream(), fileDataMetaDataIptc.getPhotoshopApp13Data())
            FileUtils.copyFile(tempFile, file)
        }
        catch(Exception e) {
            e.printStackTrace() // TODO bug not valid  JPEG image from save metaData
        }
        finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @NotTransactional
    FileDataMetaDataIptc getMetaDataIPTC(FileData fileData) {
        return new FileDataMetaDataIptc(getFile(fileData))
    }

    @PreDestroy
    void shutdown() {
        executorMp4Thread.shutdownNow()
        executorHEICThread.shutdownNow()
        executorMp4Thread.awaitTermination(10, TimeUnit.SECONDS)
        executorHEICThread.awaitTermination(10, TimeUnit.SECONDS)
    }

    void writeFileToZip(FileData fileData, ZipOutputStream zipOut) {
        if (fileData.asBlobData()) {
            byte[] bytes = storageService.getBytes(fileData.asBlobData())
            zipOut.write(bytes)
        } else {
            File file = new File(fileData.path)
            if (file.exists() && file.isFile()) {
                file.withInputStream { inputStream ->
                    zipOut << inputStream
                }
            } else {
                log.error "File ${fileData.id} does not exist in blob or on disk"
            }
        }
    }

}
