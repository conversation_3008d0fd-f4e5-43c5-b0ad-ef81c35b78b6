var CommunicationInbox = {
    filter: null,
    userId: null,
    clientId: null,
    selectedClientId: null,
    contactId: null,
    selectedContactId: null,
    channelId: null,
    onlyChat: null,
    max: 20,
    init: function (divSelector, clientId, userId) {
        CommunicationInbox.userId = userId;
        CommunicationInbox.divSelector = divSelector;
        CommunicationInbox.clientId = clientId;
        if (!CommunicationInbox.showInbox) {
            $(divSelector).hide();
        }

        if(!CommunicationInbox.filter){
            CommunicationInbox.filter = new CommunicationFilter();
            CommunicationInbox.filter.users = [userId];
        }
        $.post({
            url: tractionWebRoot + '/communication/communicationInbox',
            success: function (data) {
                $(CommunicationInbox.divSelector).html(data);
                TractionWebSocket.addHandler('/user/topic/communication', function (message) {
                    let body = JSON.parse(message.body);
                    if (body.event == 'new') {
                        CommunicationInbox.newCommunication(body.communication);
                    } else if (body.event == 'newchat') {
                        CommunicationInbox.newCommunication(body.communication);
                    } else if (body.event == 'update') {
                        CommunicationInbox.updateCommunication(body.communication);
                    }
                });
                TractionWebSocket.addHandler('/topic/clientMerged', function (m) {
                    var merge = JSON.parse(m.body);
                    if ($('.conversation[data-client-id="' + merge.oldId + '"]').length) {
                        CommunicationInbox.filter.applyFilters(false);
                        if (CommunicationBox.clientId == merge.oldId) {
                            $(divSelector).html('');
                            setTimeout(function () {
                                $('.conversation[data-client-id="' + merge.newId + '"]').click();
                            }, 500);
                        }
                    }
                })
                TractionWebSocket.addHandler('/topic/clientLinked', function (m) {
                    var linked = JSON.parse(m.body);
                    if ($('.conversation[data-channel-id="' + linked.channelId + '"]').length) {
                        CommunicationInbox.filter.applyFilters(false);
                        if (CommunicationBox.channelId == linked.channelId) {
                            $(divSelector).html('');
                            setTimeout(function () {
                                $('.conversation[data-client-id="' + linked.clientId + '"]').click();
                            }, 500);
                        }
                    }
                });
                $('#conversation-list').on('click', 'div.conversation', function () {
                    CommunicationInbox.updateSelectedConversation($(this));
                });
                CommunicationInbox.getInbox(true);
            }
        });
    },
    updateSelectedConversation: function ($conversation) {
        $('#conversation-list .selected').removeClass('selected');
        $conversation.addClass('selected');
        CommunicationInbox.selectedClientId = $conversation.data('client-id');
        CommunicationInbox.selectedContactId = $conversation.data('contact-id');
        CommunicationBox.init('#communication-box', $conversation.data('client-id'), $conversation.data('contact-id'), CommunicationInbox.userId, null, false, $conversation.data('channel-id'), this.onlyChat);
    },
    loadMore: function () {
        CommunicationInbox.max += 20;
        CommunicationInbox.getInbox();
    },
    setIsImportant: function (clientId, contactId) {
        showBigLoader();
        let isImportant = !($('.conversation[data-client-id="' + clientId + '"] .favorite-btn').attr('data-is-important') === 'true');
        if (isImportant === undefined) {
            isImportant = !($('.conversation[data-contact-id="' + clientId + '"] .favorite-btn').attr('data-is-important') === 'true');
        }

        $.post({
            url: tractionWebRoot + '/communication/setClientIsImportant',
            data: {
                clientId: clientId,
                contactId: contactId,
                isImportant: isImportant
            },
            success: function (data) {
                if (data.success) {
                    CommunicationInbox.getInbox();
                } else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader
        });
    },
    getInbox: function (selectFirst = false, showLoader = true) {
        let postData = {
            max: CommunicationInbox.max,
            onlyChat: this.onlyChat,
            clientId: CommunicationInbox.clientId
        }
        if (!$.isEmptyObject(CommunicationInbox.filter)) {
            postData.msgKeyWords = CommunicationInbox.filter.keyWords;
            postData.msgTimePeriod = CommunicationInbox.filter.timePeriod;
            postData.msgImportant = CommunicationInbox.filter.msgImportant;
            postData.msgPrivate = CommunicationInbox.filter.msgPrivate;
            postData.msgNotified = CommunicationInbox.filter.msgNotified;
            postData.msgIncoming = CommunicationInbox.filter.msgIncoming;
            postData.msgOutgoing = CommunicationInbox.filter.msgOutgoing;
            postData.msgRead = CommunicationInbox.filter.msgRead;
            postData.msgUnread = CommunicationInbox.filter.msgUnread;
            postData.msgDepartments = CommunicationInbox.filter.departments;
            postData.msgUserGroups = CommunicationInbox.filter.userGroups;
            postData.msgUsers = CommunicationInbox.filter.users;
            postData.msgTypes = CommunicationInbox.filter.types;
            postData.msgClient = CommunicationInbox.filter.msgClient;
            postData.msgClientImportant = CommunicationInbox.filter.clientImportant;
        }
        if (showLoader)
            showBigLoader();
        $.post({
            url: tractionWebRoot + '/communication/getInbox',
            data: postData,
            success: function (data) {
                data.conversations.forEach(function (convo) {
                    CommunicationInbox.updateConversationElement(convo);
                });
                CommunicationInbox.selectedClientId = $('#conversation-list .conversation').first().data('clientId');
                CommunicationInbox.selectedContactId = $('#conversation-list .conversation').first().data('contactId');


                if (selectFirst && CommunicationInbox.selectedClientId) {
                    if ($('.conversation[data-client-id="' + CommunicationInbox.selectedClientId + '"]').length) {
                        $('#communication-box').html('');
                        setTimeout(function () {
                            $('.conversation[data-client-id="' + CommunicationInbox.selectedClientId + '"]').click();
                        }, 500);
                    }
                } else if (selectFirst && CommunicationInbox.selectedContactId) {
                    if ($('.conversation[data-contact-id="' + CommunicationInbox.selectedContactId + '"]').length) {
                        $('#communication-box').html('');
                        setTimeout(function () {
                            $('.conversation[data-contact-id="' + CommunicationInbox.selectedContactId + '"]').click();
                        }, 500);
                    }
                } else if (selectFirst && CommunicationInbox.channelId) {
                    if ($('.conversation[data-channel-id="' + CommunicationInbox.channelId + '"]').length) {
                        $('#communication-box').html('');
                        setTimeout(function () {
                            $('.conversation[data-channel-id="' + CommunicationInbox.channelId + '"]').click();
                        }, 500);
                    }
                }
                data.loadMore ? $('#loadMore').show() : $('#loadMore').hide();

                if (showLoader)
                    hideBigLoader();
            }
        });

        CommunicationInbox.filterCount();
    },
    getSearchInbox: function (name) {
        CommunicationInbox.filter.setMessageClient(name)
        CommunicationInbox.filter.applyFilters(false)
    },
    typeWatch: function () {
        var timer = 0;
        return function (callback, ms) {
            clearTimeout(timer);
            timer = setTimeout(callback, ms);
        }
    }(),
    newCommunication: function (communication) {
        CommunicationInbox.updateInboxConversation(communication.id);
    },
    updateCommunication: function (communication) {
        CommunicationInbox.updateInboxConversation(communication.id);
    },
    updateInboxConversation: function (communicationId) {
        $.get({
            url: tractionWebRoot + '/communication/inboxConversation',
            data: {
                id: communicationId
            },
            success: function (html) {
                CommunicationInbox.updateConversationElement(html);
            }
        });
    },
    updateConversationElement: function (html) {
        var $newElem = $(html);
        var $oldElem = $('#conversation-list .conversation[data-client-id="' + $newElem.data('client-id') + '"]');
        if (!$oldElem) {
            $oldElem = $('#conversation-list .conversation[data-contact-id="' + $newElem.data('contact-id') + '"]');
        }
        var $oldElem2 = $('#conversation-list .conversation[data-channel-id="' + $newElem.data('channel-id') + '"]');
        if ($oldElem2 && !$oldElem) {
            $oldElem = $oldElem2
        }
        if (!$oldElem.data('timestamp') || $oldElem.data('timestamp') <= $newElem.data('timestamp') || $oldElem.data('is-important') !== $newElem.data('is-important')) {
            if ($oldElem.hasClass('selected')) {
                $newElem.addClass('selected');
            }
            $oldElem.remove();
            var inserted = false;
            $('#conversation-list .conversation').each(function () {
                if ($(this).data('timestamp') < $newElem.data('timestamp')) {
                    $(this).before($newElem);
                    inserted = true;
                    return false;
                }
            });
            if (!inserted) {
                $('#conversation-list').append($newElem);
            }
        }
    },
    updateFilterButtonAppearance: function () {
        const filterButton = $('.com-button-filter');
        const filterCount = $("#filter-count");
        const filterIcon = filterButton.find('i');

        if (filterCount.text().trim() !== '') {
            filterButton.addClass('btn-orange');
            filterIcon.removeClass('fa-regular').addClass('fa-solid');

        } else {
            filterButton.removeClass('btn-orange');
            filterIcon.removeClass('fa-solid').addClass('fa-regular');
        }
    },

    filterCount: function () {
        const count = CommunicationInbox.filter.getNumberFilters();

        const filterCount = $("#filter-count");
        count > 0 ? filterCount.html(count) : filterCount.html('');

        CommunicationInbox.updateFilterButtonAppearance();
    },

};
