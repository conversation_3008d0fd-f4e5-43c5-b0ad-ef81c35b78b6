$(document).ready(function () {
    if ($('.tractagWorkOrder').length != 0) {
        $('.tractagWorkOrder').typeahead2({
            minLength: 1,
            maxItem: 30,
            dynamic: true,
            delay: 500,
            filter:false,
            template: function (query, item) {
                return `
<div class="d-flex p-2">
    <div class="flex-fill f-14">
        <div class="d-flex">
            <div class="me-2">
                <i class="{{typeIcon}} d-inline-block"></i> {{roID}}<br>
            </div>
            <div>
                {{client}}
            </div>
        </div>
        <div>
            <small class="text-muted">({{dmsName}})</small>
        </div>
    </div>
    <div>
        <i class="mdi mdi-forum scale-hover-2 f-18" data-toggle="tooltip" title="${I18N.m('communications')}" onclick="event.preventDefault();TractionModal.show({
            url: '/communication/modal',
            bigLoader: true,
            data: {
                clientId: ${item.clientId}
            }
        });"></i>
        <i class="mdi mdi-clipboard-arrow-down scale-hover-2 f-18" data-toggle="tooltip" title="${I18N.m('main.workflow')}" onclick="event.preventDefault();TractionModal.show({
            url: '/workflowData/modalGoTo',
            bigLoader: true,
            data: {
                clientId: ${item.clientId},
                opportunityId: ${item.workflowDataOpportunityId ? item.workflowDataOpportunityId : 0}
            }
        });"></i>
        <i class="mdi mdi-calendar scale-hover-2 f-18" data-toggle="tooltip" title="${I18N.m('serviceschedule')}" onclick="event.preventDefault();TractionModal.show({
            url: '/serviceSchedule/modalGoTo',
            bigLoader: true,
            data: {
                workOrderId: ${item.id}
            }
        });"></i>
    </div>
</div>`;
            },
            emptyTemplate: I18N.m('no.result.for') + ' {{query}}',
            source: {
                cards: {
                    display: ['clientId', 'roID', 'client', 'typeIcon', 'dmsName'],
                    // Ajax Request
                    ajax: {
                        path: 'data',
                        url: tractionWebRoot + '/workOrder/search',
                        data: {search: '{{query}}'}
                    }
                }
            },
            callback: {
                onClickAfter: function (node, a, item) {
                    window['updateWorkOrderSearch' + this.container.find('input').data('uuid')](item, this.container.find('input'));
                },
                onShowLayout: function (node, query, result) {
                    if (!node.is(':focus')) {
                        this.hideLayout();
                    }
                }
            }
        });
    }
});
