//= require lib/fullcalendar-scheduler/main.min

class WorkSchedule {
    constructor({
                    id,
                    divId,
                    userId,
                    departmentId,
                    canEdit,
                    slotMinTime,
                    slotMaxTime,
                    weekends
    }) {
        var self = this;
        window[id] = self;
        self.id = id;
        self.userId = userId;
        self.departmentId = departmentId;
        self.canEdit = canEdit;
        self.divId = divId;
        self.lastAddedEvent = null;
        self.modalMoveInfo = null;
        self.modalEvent = null;

        var now = new Date();
        var aYearFromNow = new Date();
        aYearFromNow.setFullYear(aYearFromNow.getFullYear() + 1);

        $('#' + self.divId).after('<div class="modal" id="modal-event"></div><div class="modal" id="modal-move-event"></div>');

        self.calendar = new FullCalendar.Calendar(document.getElementById(self.divId), {
            initialView: 'timeGridWeek',
            slotMinTime: slotMinTime == '' ? '00:00:00' : slotMinTime,
            slotMaxTime: slotMaxTime == '' ? '24:00:00' : slotMaxTime,
            weekends: weekends,
            locale: $('html').attr('lang'),
            themeSystem: 'bootstrap',
            eventClassNames: self.canEdit ? 'mdi mdi-delete' : '',
            selectable: self.canEdit,
            height: 'auto',
            editable: self.canEdit,
            eventOverlap: false,
            allDaySlot: false,
            nowIndicator: true,
            headerToolbar: {
                right: (self.canEdit ? 'deleteButton copyButton ' : '') + 'today prev,next' //printButton
            },
            buttonText: {
                today: I18N.m('today')
            },
            customButtons: {
                printButton: {
                    text:  I18N.m('Print'),
                    click: function () {
                        //var $fc = $('#' + self.divId).find('.fc-scroller');
                        //var style = $fc.attr('style');
                        //$fc.attr('style', '');

                        $('#' + self.divId).printThis({
                            importCSS: true,
                            importStyle: true,
                            copyTagClasses: true,
                            removeInline: false,
                            base: true,
                            afterPrint: function () {
                                //$fc.attr('style', style);
                            }
                        });
                    }
                },
                deleteButton: {
                    text: I18N.messages['workschedule.delete'],
                    click: function () {
                        confirmDelete(I18N.messages['workschedule.delete.confirm'], function () {
                            $.post({
                                url: tractionWebRoot + '/workSchedule/clear',
                                data: {
                                    userId: self.userId,
                                    departmentId: self.departmentId
                                },
                                success: function (data) {
                                    if (data.success) {
                                        self.calendar.refetchEvents();
                                    }
                                    else {
                                        $.notify(data.message, 'error');
                                    }
                                },
                                beforeSend: showBigLoader,
                                complete: hideBigLoader
                            });
                        });
                    }
                },
                copyButton: {
                    text: I18N.m('workschedule.copy'),
                    click: function () {
                        if (self.departmentId) {
                            $.notify(I18N.m('workschedule.copy.department.warning'), 'warning');
                            return;
                        }
                        TractionModal.show({url: '/workSchedule/modalCopy', data: {userId: self.userId}});
                    }
                }
            },
            eventTimeFormat: {
                hour: '2-digit',
                minute: '2-digit'
            },
            validRange: {
                start: now,
                end: aYearFromNow
            },
            eventResize: self.canEdit ? function (info) {
                self.modalMove(info);
            } : null,
            eventDrop: self.canEdit ? function (info) {
                self.modalMove(info);
            } : null,
            select: self.canEdit ? function(selectionInfo) {
                console.log(selectionInfo);
                var start = selectionInfo.start;
                var end = selectionInfo.end;
                if (isOverlapping(start, end)) {
                    $.notify(I18N.messages['workschedule.overlay.error'], 'error');
                }
                else {
                    self.lastAddedEvent = self.calendar.addEvent(selectionInfo);
                    self.modal(self.lastAddedEvent);
                }
                self.calendar.unselect();
                function isOverlapping(dateStart, dateEnd) {
                    var arrCalEvents = self.calendar.getEvents();
                    console.log(arrCalEvents);
                    for (var i in arrCalEvents) {
                        var endDay = new Date(arrCalEvents[i].start);
                        endDay.setDate(endDay.getDate() + 1);
                        if ((dateEnd > arrCalEvents[i].start && dateStart < (arrCalEvents[i].end ? arrCalEvents[i].end : endDay))) {
                            //return true;
                        }
                    }
                    return false;
                }
            } : null,
            eventClick: self.canEdit ? function(info) {
                self.confirmAndDeleteId = info.event.id;
                if (info.event._def.extendedProps.ruleId) {
                    TractionModal.show({url: '/workSchedule/modalEdit', data: {onsave: self.id + '.confirmAndDelete', title: 'workschedulerule.delete'}});
                }
                else {
                    self.confirmAndDelete();
                }
            } : null,
            events: {
                url: tractionWebRoot + '/workSchedule/getEvents',
                extraParams: function() { // a function that returns an object
                    return {
                        userId: self.userId,
                        departmentId: self.departmentId,
                        dynamic_value: Math.random()
                    };
                }
            },
            eventsSet: function (events) {
                var $elem = $('#' + self.divId + ' .fc-toolbar-title .total');
                if ($elem.length == 0) {
                    $elem = $('#' + self.divId + ' .fc-toolbar-title').append('<small class="ms-2">(' + I18N.messages['workschedule.week.hours'] + ': <span class="total"></span>h)</small>').find('.total');
                }
                var hours = 0;
                for (var i in events) {
                    hours += events[i].extendedProps.hours ? events[i].extendedProps.hours : 0;
                }
                $elem.html(hours);
            },
            loading: function( isLoading, view ) {
                isLoading ? showLogoLoader() : hideLogoLoader();
            }
        });

        this.calendar.render();
    }

    setUserId (userId) {
        this.departmentId = null;
        this.userId = userId;
        this.calendar.refetchEvents();
    }

    setDepartmentId (departmentId) {
        console.log('departmentId', departmentId);
        this.userId = null;
        this.departmentId = departmentId;
        this.calendar.refetchEvents();
    }

    modal (event) {
        this.modalEvent = event;
        TractionModal.show({url: '/workSchedule/modalAdd', data: {id: this.id}});
    }

    modalMove (info) {
        $('#this').prop('checked', true);
        this.modalMoveInfo = info;
        console.log(this.modalMoveInfo);
        if (!this.modalMoveInfo.event.extendedProps.ruleId) {
            this.modalMoveSave();
        }
        else {
            TractionModal.show({url: '/workSchedule/modalEdit', data: {onsave: this.id + '.modalMoveSave'}});
        }
    }

    modalMoveSave (editType) {
        var self = this;
        console.log('editType', self.modalMoveInfo);
        $.get({
            url: tractionWebRoot + '/workSchedule/move',
            data: {
                id: self.modalMoveInfo.oldEvent.id,
                dateDiff: self.modalMoveInfo.event.start - self.modalMoveInfo.oldEvent.start,
                editType: editType,
                duration: (self.modalMoveInfo.event.end - self.modalMoveInfo.event.start)
            },
            success: function (data) {
                if (data.success) {
                    self.calendar.refetchEvents();
                }
                else {
                    $.notify(data.message, 'error');
                }
            },
            beforeSend: showBigLoader,
            complete: hideBigLoader
        });
    }

    modalSave () {
        var self = this;
        var ruleString = '';
        if ($('#recurrence').prop('checked')) {
            var options = {
                freq: rrule.RRule[$('#freq').val()],
                interval: $('#interval').val()
            };

            switch ($('[name=end]:checked').val()) {
                case 'count':
                    options.count =  $('#count').val();
                    break;
                case 'until':
                    options.until = $('#until').val();
                    break;
                case 'never':
                    break;
            }
            ruleString = rrule.RRule.optionsToString(options);
        };
        $.get({
            url: tractionWebRoot + '/workSchedule/add',
            data: {
                userId: self.userId,
                departmentId: self.departmentId,
                rrule: ruleString,
                date: self.modalEvent.start.getTime(),
                duration: (self.modalEvent.end - self.modalEvent.start)
            },
            success: function (data) {
                self.modalEvent.remove();
                if (data.success) {
                    TractionModal.hide();
                    self.calendar.refetchEvents();
                }
                else {
                    $.notify(data.message, 'error');
                }
            },
            complete: hideBigLoader,
            beforeSend: showBigLoader
        });
    }

    confirmAndDelete (editType) {
        var self = this;
        console.log('confirmAndDelete', editType, self.confirmAndDeleteId);
        confirmDelete(I18N.messages['workscheduleday.delete.confirm'], function () {
            $.post({
                url: tractionWebRoot + '/workSchedule/deleteEvent',
                data: {
                    editType: editType,
                    id: self.confirmAndDeleteId
                },
                success: function (data) {
                    if (data.success) {
                        self.calendar.refetchEvents();
                    }
                    else {
                        $.notify(data.message, 'error');
                    }
                },
                beforeSend: showBigLoader,
                complete: hideBigLoader
            });
        });
    }

}

