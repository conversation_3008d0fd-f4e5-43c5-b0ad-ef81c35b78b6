!function(){"use strict";var n,e,t,r,o,i,u,a,A=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e]},v=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,arguments))}},I=function(n){return function(){return n}},h=function(n){return n},l=function(i){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var u=new Array(arguments.length-1),t=1;t<arguments.length;t++)u[t-1]=arguments[t];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];var o=u.concat(t);return i.apply(null,o)}},S=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,arguments)}},c=function(n){return function(){throw new Error(n)}},s=function(n){return n()},f=I(!1),d=I(!0),m=function(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"===e&&Array.prototype.isPrototypeOf(n)?"array":"object"===e&&String.prototype.isPrototypeOf(n)?"string":e}(n)===e}},b=m("string"),g=m("object"),p=m("array"),y=m("boolean"),w=m("function"),x=m("number"),T=Object.prototype.hasOwnProperty,O=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)T.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},k=O(function(n,e){return g(n)&&g(e)?k(n,e):e}),C=O(function(n,e){return e}),E=f,D=d,M=function(){return B},B=(r={fold:function(n,e){return n()},is:E,isSome:E,isNone:D,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:t,orThunk:e,map:M,ap:M,each:function(){},bind:M,flatten:M,exists:E,forall:D,filter:M,equals:n=function(n){return n.isNone()},equals_:n,toArray:function(){return[]},toString:I("none()")},Object.freeze&&Object.freeze(r),r),R=function(t){var n=function(){return t},e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:D,isNone:E,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return R(n(t))},ap:function(n){return n.fold(M,function(n){return R(n(t))})},each:function(n){n(t)},bind:r,flatten:n,exists:r,forall:r,filter:function(n){return n(t)?o:B},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(E,function(n){return e(t,n)})},toArray:function(){return[t]},toString:function(){return"some("+t+")"}};return o},F={some:R,none:M,from:function(n){return null===n||n===undefined?B:R(n)}},N=Object.keys,V=function(n,e){for(var t=N(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i,n)}},H=function(n,r){return z(n,function(n,e,t){return{k:e,v:r(n,e,t)}})},z=function(r,o){var i={};return V(r,function(n,e){var t=o(n,e,r);i[t.k]=t.v}),i},j=function(n,t){var r=[];return V(n,function(n,e){r.push(t(n,e))}),r},L=I("touchstart"),U=I("touchmove"),P=I("touchend"),$=I("mousedown"),W=I("mousemove"),G=I("mouseup"),_=I("mouseover"),q=I("keydown"),Y=I("input"),K=I("change"),X=I("click"),J=I("transitionend"),Q=I("selectstart"),Z=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},nn=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return tn(r(1),r(2))},en=function(){return tn(0,0)},tn=function(n,e){return{major:n,minor:e}},rn={nu:tn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?en():nn(n,t)},unknown:en},on="Firefox",un=function(n,e){return function(){return e===n}},an=function(n){var e=n.current;return{current:e,version:n.version,isEdge:un("Edge",e),isChrome:un("Chrome",e),isIE:un("IE",e),isOpera:un("Opera",e),isFirefox:un(on,e),isSafari:un("Safari",e)}},cn={unknown:function(){return an({current:undefined,version:rn.unknown()})},nu:an,edge:I("Edge"),chrome:I("Chrome"),ie:I("IE"),opera:I("Opera"),firefox:I(on),safari:I("Safari")},sn="Windows",fn="Android",ln="Solaris",dn="FreeBSD",mn=function(n,e){return function(){return e===n}},gn=function(n){var e=n.current;return{current:e,version:n.version,isWindows:mn(sn,e),isiOS:mn("iOS",e),isAndroid:mn(fn,e),isOSX:mn("OSX",e),isLinux:mn("Linux",e),isSolaris:mn(ln,e),isFreeBSD:mn(dn,e)}},vn={unknown:function(){return gn({current:undefined,version:rn.unknown()})},nu:gn,windows:I(sn),ios:I("iOS"),android:I(fn),linux:I("Linux"),osx:I("OSX"),solaris:I(ln),freebsd:I(dn)},pn=(o=Array.prototype.indexOf)===undefined?function(n,e){return kn(n,e)}:function(n,e){return o.call(n,e)},hn=function(n,e){return-1<pn(n,e)},bn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o,n)}return r},yn=function(n,e){for(var t=0,r=n.length;t<r;t++)e(n[t],t,n)},wn=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r,n)&&t.push(i)}return t},xn=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--)e(n[t],t,n)}(n,function(n){t=e(t,n)}),t},Sn=function(n,e,t){return yn(n,function(n){t=e(t,n)}),t},Tn=function(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t,n))return F.some(o)}return F.none()},On=function(n,e){for(var t=0,r=n.length;t<r;t++)if(e(n[t],t,n))return F.some(t);return F.none()},kn=function(n,e){for(var t=0,r=n.length;t<r;++t)if(n[t]===e)return t;return-1},Cn=Array.prototype.push,En=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!Array.prototype.isPrototypeOf(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);Cn.apply(e,n[t])}return e},Dn=function(n,e){var t=bn(n,e);return En(t)},An=function(n,e){for(var t=0,r=n.length;t<r;++t)if(!0!==e(n[t],t,n))return!1;return!0},In=Array.prototype.slice,Mn=function(n){var e=In.call(n,0);return e.reverse(),e},Bn=function(n){return[n]},Rn=(w(Array.from)&&Array.from,function(n,e){var t=String(e).toLowerCase();return Tn(n,function(n){return n.search(t)})}),Fn=function(n,t){return Rn(n,t).map(function(n){var e=rn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Nn=function(n,t){return Rn(n,t).map(function(n){var e=rn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Vn=function(n,e){return-1!==n.indexOf(e)},Hn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,zn=function(e){return function(n){return Vn(n,e)}},jn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Vn(n,"edge/")&&Vn(n,"chrome")&&Vn(n,"safari")&&Vn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Hn],search:function(n){return Vn(n,"chrome")&&!Vn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Vn(n,"msie")||Vn(n,"trident")}},{name:"Opera",versionRegexes:[Hn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:zn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:zn("firefox")},{name:"Safari",versionRegexes:[Hn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Vn(n,"safari")||Vn(n,"mobile/"))&&Vn(n,"applewebkit")}}],Ln=[{name:"Windows",search:zn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Vn(n,"iphone")||Vn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:zn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:zn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:zn("linux"),versionRegexes:[]},{name:"Solaris",search:zn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:zn("freebsd"),versionRegexes:[]}],Un={browsers:I(jn),oses:I(Ln)},Pn=function(n){var e,t,r,o,i,u,a,c,s,f,l,d=Un.browsers(),m=Un.oses(),g=Fn(d,n).fold(cn.unknown,cn.nu),v=Nn(m,n).fold(vn.unknown,vn.nu);return{browser:g,os:v,deviceType:(t=g,r=n,o=(e=v).isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isAndroid()&&3===e.version.major,a=e.isAndroid()&&4===e.version.major,c=o||u||a&&!0===/mobile/i.test(r),s=e.isiOS()||e.isAndroid(),f=s&&!c,l=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),{isiPad:I(o),isiPhone:I(i),isTablet:I(c),isPhone:I(f),isTouch:I(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:I(l)})}},$n={detect:Z(function(){var n=navigator.userAgent;return Pn(n)})},Wn={tap:I("alloy.tap")},Gn=I("alloy.focus"),_n=I("alloy.blur.post"),qn=I("alloy.receive"),Yn=I("alloy.execute"),Kn=I("alloy.focus.item"),Xn=Wn.tap,Jn=$n.detect().deviceType.isTouch()?Wn.tap:X,Qn=I("alloy.longpress"),Zn=I("alloy.system.init"),ne=I("alloy.system.scroll"),ee=I("alloy.system.attached"),te=I("alloy.system.detached"),re=function(n,e){ae(n,n.element(),e,{})},oe=function(n,e,t){ae(n,n.element(),e,t)},ie=function(n){re(n,Yn())},ue=function(n,e,t){ae(n,e,t,{})},ae=function(n,e,t,r){var o=k({target:e},r);n.getSystem().triggerEvent(t,e,H(o,I))},ce=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:I(n)}},se={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),"HTML must have a single root node";return ce(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return ce(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return ce(t)},fromDom:ce,fromPoint:function(n,e,t){var r=n.dom();return F.from(r.elementFromPoint(e,t)).map(ce)}},fe=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE,Node.DOCUMENT_NODE),le=(Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE),de=Node.TEXT_NODE,me=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(n){return n.dom().nodeName.toLowerCase()}),ge=function(e){return function(n){return n.dom().nodeType===e}},ve=ge(le),pe=ge(de),he=function(n){var e=pe(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)},be=Z(function(){return ye(se.fromDom(document))}),ye=function(n){var e=n.dom().body;if(null===e||e===undefined)throw"Body is not available yet";return se.fromDom(e)},we=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return yn(e,function(n,e){r[n]=I(t[e])}),r}},xe=function(n){return n.slice(0).sort()},Se=function(n,e){throw new Error("All required keys ("+xe(n).join(", ")+") were not specified. Specified keys were: "+xe(e).join(", ")+".")},Te=function(n){throw new Error("Unsupported keys for object: "+xe(n).join(", "))},Oe=function(e,n){if(!p(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");yn(n,function(n){if(!b(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})},ke=function(n){var t=xe(n);Tn(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})},Ce=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Oe("required",o),Oe("optional",i),ke(u),function(e){var t=N(e);An(o,function(n){return hn(t,n)})||Se(o,t);var n=wn(t,function(n){return!hn(u,n)});0<n.length&&Te(n);var r={};return yn(o,function(n){r[n]=I(e[n])}),yn(i,function(n){r[n]=I(Object.prototype.hasOwnProperty.call(e,n)?F.some(e[n]):F.none())}),r}},Ee="undefined"!=typeof window?window:Function("return this;")(),De=function(n,e){return function(n,e){for(var t=e!==undefined&&null!==e?e:Ee,r=0;r<n.length&&t!==undefined&&null!==t;++r)t=t[n[r]];return t}(n.split("."),e)},Ae={getOrDie:function(n,e){var t=De(n,e);if(t===undefined||null===t)throw n+" not available on this browser";return t}},Ie=le,Me=fe,Be=function(n,e){var t=n.dom();if(t.nodeType!==Ie)return!1;if(t.matches!==undefined)return t.matches(e);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(e);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(e);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Re=function(n){return n.nodeType!==Ie&&n.nodeType!==Me||0===n.childElementCount},Fe=function(n,e){var t=e===undefined?document:e.dom();return Re(t)?[]:bn(t.querySelectorAll(n),se.fromDom)},Ne=function(n,e){var t=e===undefined?document:e.dom();return Re(t)?F.none():F.from(t.querySelector(n)).map(se.fromDom)},Ve=function(n,e){return n.dom()===e.dom()},He=($n.detect().browser.isIE(),function(n){return se.fromDom(n.dom().ownerDocument)}),ze=function(n){var e=n.dom();return F.from(e.parentNode).map(se.fromDom)},je=function(n){var e=n.dom();return bn(e.childNodes,se.fromDom)},Le=function(n){return e=0,t=n.dom().childNodes,F.from(t[e]).map(se.fromDom);var e,t},Ue=(we("element","offset"),function(e,t){Le(e).fold(function(){Pe(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}),Pe=function(n,e){n.dom().appendChild(e.dom())},$e=function(e,n){yn(n,function(n){Pe(e,n)})},We=function(n){n.dom().textContent="",yn(je(n),function(n){Ge(n)})},Ge=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},_e=function(n){re(n,te());var e=n.components();yn(e,_e)},qe=function(n){var e=n.components();yn(e,qe),re(n,ee())},Ye=function(n,e){Ke(n,e,Pe)},Ke=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),he(n.element())&&qe(e),n.syncComponents()},Xe=function(n){_e(n),Ge(n.element()),n.getSystem().removeFromWorld(n)},Je=function(e){var n=ze(e.element()).bind(function(n){return e.getSystem().getByDom(n).fold(F.none,F.some)});Xe(e),n.each(function(n){n.syncComponents()})},Qe=function(t){return{is:function(n){return t===n},isValue:d,isError:f,getOr:I(t),getOrThunk:I(t),getOrDie:I(t),or:function(n){return Qe(t)},orThunk:function(n){return Qe(t)},fold:function(n,e){return e(t)},map:function(n){return Qe(n(t))},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return F.some(t)}}},Ze=function(t){return{is:f,isValue:f,isError:d,getOr:h,getOrThunk:function(n){return n()},getOrDie:function(){return c(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Ze(t)},each:A,bind:function(n){return Ze(t)},exists:f,forall:d,toOption:F.none}},nt={value:Qe,error:Ze},et=function(u){if(!p(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],t={};return yn(u,function(n,r){var e=N(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");a.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=N(n);if(a.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+e.join(","));if(!An(a,function(n){return hn(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+a.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:a,constructor:o,params:t})}}}}),t},tt=et([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),rt=function(n){return tt.defaultedThunk(I(n))},ot=tt.strict,it=tt.asOption,ut=tt.defaultedThunk,at=tt.mergeWithThunk,ct=(et([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){var e=[],t=[];return yn(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}),st=function(n){return v(nt.error,En)(n)},ft=function(n,e){var t,r,o=ct(n);return 0<o.errors.length?st(o.errors):(t=o.values,r=e,nt.value(k.apply(undefined,[r].concat(t))))},lt=function(n){var e=ct(n);return 0<e.errors.length?st(e.errors):nt.value(e.values)},dt=function(e){return function(n){return n.hasOwnProperty(e)?F.from(n[e]):F.none()}},mt=function(n,e){return dt(e)(n)},gt=function(n,e){var t={};return t[n]=e,t},vt=function(n,e){return t=n,r={},yn(e,function(n){t[n]!==undefined&&t.hasOwnProperty(n)&&(r[n]=t[n])}),r;var t,r},pt=function(n,e){return t=e,r={},V(n,function(n,e){hn(t,e)||(r[e]=n)}),r;var t,r},ht=function(n){return dt(n)},bt=function(n,e){return t=n,r=e,function(n){return dt(t)(n).getOr(r)};var t,r},yt=function(n,e){return mt(n,e)},wt=function(n,e){return gt(n,e)},xt=function(n){return e={},yn(n,function(n){e[n.key]=n.value}),e;var e},St=function(n,e){return ft(n,e)},Tt=function(n,e){return r=e,(t=n).hasOwnProperty(r)&&t[r]!==undefined&&null!==t[r];var t,r},Ot=et([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),kt=et([{field:["name","presence","type"]},{state:["name"]}]),Ct=function(){return Ae.getOrDie("JSON")},Et=function(n,e,t){return Ct().stringify(n,e,t)},Dt=function(n){return g(n)&&100<N(n).length?" removed due to size":Et(n,null,2)},At=function(n,e){return nt.error([{path:n,getErrorInfo:e}])},It=et([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Mt=function(t,r,o){return mt(r,o).fold(function(){return n=o,e=r,At(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+Dt(e)});var n,e},nt.value)},Bt=function(n,e,t){var r=mt(n,e).fold(function(){return t(n)},h);return nt.value(r)},Rt=function(o,a,n,c){return n.fold(function(i,e,n,t){var r=function(n){return t.extract(o.concat([i]),c,n).map(function(n){return gt(e,c(n))})},u=function(n){return n.fold(function(){var n=gt(e,c(F.none()));return nt.value(n)},function(n){return t.extract(o.concat([i]),c,n).map(function(n){return gt(e,c(F.some(n)))})})};return n.fold(function(){return Mt(o,a,i).bind(r)},function(n){return Bt(a,i,n).bind(r)},function(){return(n=a,e=i,nt.value(mt(n,e))).bind(u);var n,e},function(n){return(e=a,t=i,r=n,o=mt(e,t).map(function(n){return!0===n?r(e):n}),nt.value(o)).bind(u);var e,t,r,o},function(n){var e=n(a);return Bt(a,i,I({})).map(function(n){return k(e,n)}).bind(r)})},function(n,e){var t=e(a);return nt.value(gt(n,c(t)))})},Ft=function(r){return{extract:function(t,n,e){return r(e,n).fold(function(n){return e=n,At(t,function(){return e});var e},nt.value)},toString:function(){return"val"},toDsl:function(){return Ot.itemOf(r)}}},Nt=function(n){var c=Vt(n),s=xn(n,function(e,n){return n.fold(function(n){return k(e,wt(n,!0))},I(e))},{});return{extract:function(n,e,t){var r,o,i,u=y(t)?[]:(o=N(r=t),wn(o,function(n){return Tt(r,n)})),a=wn(u,function(n){return!Tt(s,n)});return 0===a.length?c.extract(n,e,t):(i=a,At(n,function(){return"There are unsupported fields: ["+i.join(", ")+"] specified"}))},toString:c.toString,toDsl:c.toDsl}},Vt=function(a){return{extract:function(n,e,t){return r=n,o=t,i=e,u=bn(a,function(n){return Rt(r,o,n,i)}),ft(u,{});var r,o,i,u},toString:function(){return"obj{\n"+bn(a,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return Ot.objOf(bn(a,function(n){return n.fold(function(n,e,t,r){return kt.field(n,t,r)},function(n,e){return kt.state(n)})}))}}},Ht=function(t,i){var e=function(n,e){return(o=Ft(t),{extract:function(t,r,n){var e=bn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return lt(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return Ot.arrOf(o)}}).extract(n,h,e);var o};return{extract:function(t,r,o){var n=N(o);return e(t,n).bind(function(n){var e=bn(n,function(n){return It.field(n,n,ot(),i)});return Vt(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return Ot.setOf(t,i)}}},zt=I(Ft(nt.value)),jt=It.state,Lt=It.field,Ut=function(t,e,r,o,i){return yt(o,i).fold(function(){return n=o,e=i,At(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+Dt(n)});var n,e},function(n){return Vt(n).extract(t.concat(["branch: "+i]),e,r)})},Pt=function(o,i){return{extract:function(e,t,r){return yt(r,o).fold(function(){return n=o,At(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Ut(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+N(i)},toDsl:function(){return Ot.choiceOf(o,i)}}},$t=Ft(nt.value),Wt=function(n,e,t,r){return e.extract([n],t,r).fold(function(n){return nt.error({input:r,errors:n})},nt.value)},Gt=function(n,e,t){return Wt(n,e,I,t)},_t=function(n){return n.fold(function(n){throw new Error(Kt(n))},h)},qt=function(n,e,t){return _t(Wt(n,e,h,t))},Yt=function(n,e,t){return _t(Gt(n,e,t))},Kt=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,bn(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}))+"\n\nInput object: "+Dt(n.input);var e,t},Xt=function(n,e){return Pt(n,e)},Jt=I($t),Qt=(i=w,u="function",Ft(function(n){var e=typeof n;return i(n)?nt.value(n):nt.error("Expected type: "+u+" but got: "+e)})),Zt=function(n){return Lt(n,n,ot(),zt())},nr=function(n,e){return Lt(n,n,ot(),e)},er=function(n){return nr(n,Qt)},tr=function(n,e){return Lt(n,n,ot(),Vt(e))},rr=function(n){return Lt(n,n,it(),zt())},or=function(n,e){return Lt(n,n,it(),Vt(e))},ir=function(n,e){return Lt(n,n,it(),Nt(e))},ur=function(n,e){return Lt(n,n,rt(e),zt())},ar=function(n,e,t){return Lt(n,n,rt(e),t)},cr=function(n,e){return jt(n,e)},sr=function(n){if(!Tt(n,"can")&&!Tt(n,"abort")&&!Tt(n,"run"))throw new Error("EventHandler defined by: "+Et(n,null,2)+" does not have can, abort, or run!");return qt("Extracting event.handler",Nt([ur("can",I(!0)),ur("abort",I(!1)),ur("run",A)]),n)},fr=function(t){var e,r,o,i,n=(e=t,r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Sn(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return Sn(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return sr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];yn(t,function(n){n.run.apply(undefined,e)})}})},lr=function(n){return xt(n)},dr=function(n,e){return{key:n,value:sr({abort:e})}},mr=function(n,e){return{key:n,value:sr({run:e})}},gr=function(n,e,t){return{key:n,value:sr({run:function(n){e.apply(undefined,[n].concat(t))}})}},vr=function(n){return function(r){return{key:n,value:sr({run:function(n,e){var t;t=e,Ve(n.element(),t.event().target())&&r(n,e)}})}}},pr=function(n,e,t){var u,r,o=e.partUids()[t];return r=o,mr(u=n,function(n,i){n.getSystem().getByUid(r).each(function(n){var e,t,r,o;t=(e=n).element(),r=u,o=i,e.getSystem().triggerEvent(r,t,o.event())})})},hr=function(n){return mr(n,function(n,e){e.cut()})},br=vr(ee()),yr=vr(te()),wr=vr(Zn()),xr=(a=Yn(),function(n){return mr(a,n)}),Sr=function(n){return bn(n,function(n){return r=e="/*",o=(t=n).length-e.length,""!==r&&(t.length<r.length||t.substr(o,o+r.length)!==r)?n:n.substring(0,n.length-"/*".length);var e,t,r,o})},Tr=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Sr(i)}},n},Or=Ce(["tag"],["classes","attributes","styles","value","innerHtml","domChildren","defChildren"]),kr=function(n){return{tag:n.tag(),classes:n.classes().getOr([]),attributes:n.attributes().getOr({}),styles:n.styles().getOr({}),value:n.value().getOr("<none>"),innerHtml:n.innerHtml().getOr("<none>"),defChildren:n.defChildren().fold(function(){return"<none>"},function(n){return Et(n,null,2)}),domChildren:n.domChildren().fold(function(){return"<none>"},function(n){return 0===n.length?"0 children, but still specified":String(n.length)})}},Cr=Ce([],["classes","attributes","styles","value","innerHtml","defChildren","domChildren"]),Er=function(e,n,t){return n.fold(function(){return t.fold(function(){return{}},function(n){return wt(e,n)})},function(n){return t.fold(function(){return wt(e,n)},function(n){return wt(e,n)})})},Dr=function(t,r,o){return wr(function(n,e){o(n,t,r)})},Ar=function(n,e,t,r,o,i){var u,a,c=n,s=or(e,[(u="config",a=n,Lt(u,u,it(),a))]);return Br(c,s,e,t,r,o,i)},Ir=function(o,i,u){var n,e,t,r,a,c;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:I(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,a=t.indexOf("("),c=t.substring(a+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:Sr(c.slice(0,1).concat(c.slice(3)))}},n},Mr=function(n){return{key:n,value:undefined}},Br=function(t,n,r,o,e,i,u){var a=function(n){return Tt(n,r)?n[r]():F.none()},c=H(e,function(n,e){return Ir(r,n,e)}),s=H(i,function(n,e){return Tr(n,e)}),f=k(s,c,{revoke:l(Mr,r),config:function(n){var e=Yt(r+"-config",t,n);return{key:r,value:{config:e,me:f,configAsRaw:Z(function(){return qt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return a(n).bind(function(e){return yt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Cr({}))},name:function(){return r},handlers:function(n){return a(n).bind(function(e){return yt(o,"events").map(function(n){return n(e.config,e.state)})}).getOr({})}});return f},Rr=function(n,e){return Fr(n,e,{validate:w,label:"function"})},Fr=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return Oe("required",o),ke(o),function(e){var t=N(e);An(o,function(n){return hn(t,n)})||Se(o,t),r(o,t);var n=wn(o,function(n){return!i.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+xe(n).join(", ")+") were not.")}(n,i.label),e}},Nr=function(e,n){var t=wn(n,function(n){return!hn(e,n)});0<t.length&&Te(t)},Vr=A,Hr=function(n){return Rr(Nr,n)},zr={init:function(){return jr({readState:function(){return"No State required"}})}},jr=function(n){return Rr(Vr,["readState"])(n),n},Lr=function(n){return xt(n)},Ur=Nt([Zt("fields"),Zt("name"),ur("active",{}),ur("apis",{}),ur("state",zr),ur("extra",{})]),Pr=function(n){var e,t,r,o,i,u,a,c,s=qt("Creating behaviour: "+n.name,Ur,n);return e=s.fields,t=s.name,r=s.active,o=s.apis,i=s.extra,u=s.state,a=Nt(e),c=or(t,[ir("config",e)]),Br(a,c,t,r,o,i,u)},$r=Nt([Zt("branchKey"),Zt("branches"),Zt("name"),ur("active",{}),ur("apis",{}),ur("state",zr),ur("extra",{})]),Wr=I(undefined),Gr=function(n,e,t){if(!(b(t)||y(t)||x(t)))throw console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},_r=function(n,e,t){Gr(n.dom(),e,t)},qr=function(n,e){var t=n.dom();V(e,function(n,e){Gr(t,e,n)})},Yr=function(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t},Kr=function(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},Xr=function(n,e){n.dom().removeAttribute(e)},Jr=function(n,e){var t=Yr(n,e);return t===undefined||""===t?[]:t.split(" ")},Qr=function(n){return n.dom().classList!==undefined},Zr=function(n){return Jr(n,"class")},no=function(n,e){return o=e,i=Jr(t=n,r="class").concat([o]),_r(t,r,i.join(" ")),!0;var t,r,o,i},eo=function(n,e){return o=e,0<(i=wn(Jr(t=n,r="class"),function(n){return n!==o})).length?_r(t,r,i.join(" ")):Xr(t,r),!1;var t,r,o,i},to=function(n,e){Qr(n)?n.dom().classList.add(e):no(n,e)},ro=function(n,e){var t;Qr(n)?n.dom().classList.remove(e):eo(n,e),0===(Qr(t=n)?t.dom().classList:Zr(t)).length&&Xr(t,"class")},oo=function(n,e){return Qr(n)?n.dom().classList.toggle(e):(r=e,hn(Zr(t=n),r)?eo(t,r):no(t,r));var t,r},io=function(n,e){return Qr(n)&&n.dom().classList.contains(e)},uo=function(n,e,t){ro(n,t),to(n,e)},ao=Object.freeze({toAlpha:function(n,e,t){uo(n.element(),e.alpha(),e.omega())},toOmega:function(n,e,t){uo(n.element(),e.omega(),e.alpha())},isAlpha:function(n,e,t){return io(n.element(),e.alpha())},isOmega:function(n,e,t){return io(n.element(),e.omega())},clear:function(n,e,t){ro(n.element(),e.alpha()),ro(n.element(),e.omega())}}),co=[Zt("alpha"),Zt("omega")],so=Pr({fields:co,name:"swapping",apis:ao}),fo=function(n){var e=n,t=function(){return e};return{get:t,set:function(n){e=n},clone:function(){return fo(t())}}};function lo(n,e,t,r,o){return n(t,r)?F.some(t):w(o)&&o(t)?F.none():e(t,r,o)}var mo=function(n,e,t){for(var r=n.dom(),o=w(t)?t:I(!1);r.parentNode;){r=r.parentNode;var i=se.fromDom(r);if(e(i))return F.some(i);if(o(i))break}return F.none()},go=function(n,e,t){return lo(function(n){return e(n)},mo,n,e,t)},vo=function(n,r){var o=function(n){for(var e=0;e<n.childNodes.length;e++){if(r(se.fromDom(n.childNodes[e])))return F.some(se.fromDom(n.childNodes[e]));var t=o(n.childNodes[e]);if(t.isSome())return t}return F.none()};return o(n.dom())},po=function(n){n.dom().focus()},ho=function(n){n.dom().blur()},bo=function(n){var e=n!==undefined?n.dom():document;return F.from(e.activeElement).map(se.fromDom)},yo=function(e){return bo(He(e)).filter(function(n){return e.dom().contains(n.dom())})},wo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),xo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),So=function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)},To={formatChanged:I("formatChanged"),orientationChanged:I("orientationChanged"),dropupDismissed:I("dropupDismissed")},Oo=function(n){return n.dom().innerHTML},ko=function(n,e){var t,r,o=He(n).dom(),i=se.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||document).createElement("div")).innerHTML=t,je(se.fromDom(r)));$e(i,u),We(n),Pe(n,i)},Co=function(n){return e=n,t=!1,se.fromDom(e.dom().cloneNode(t));var e,t},Eo=function(n){var e,t,r,o=Co(n);return e=o,t=se.fromTag("div"),r=se.fromDom(e.dom().cloneNode(!0)),Pe(t,r),Oo(t)},Do=function(n){return Eo(n)},Ao=Object.freeze({events:function(a){return lr([mr(qn(),function(o,i){var n,e,u=a.channels(),t=N(u),r=(n=t,(e=i).universal()?n:wn(n,function(n){return hn(e.channels(),n)}));yn(r,function(n){var e=u[n](),t=e.schema(),r=Yt("channel["+n+"] data\nReceiver: "+Do(o.element()),t,i.data());e.onReceive()(o,r)})})])}}),Io=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Mo=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return F.none()},Bo="unknown",Ro=[],Fo=["alloy/data/Fields","alloy/debugging/Debugging"],No=function(){var n=new Error;if(n.stack!==undefined){var e=n.stack.split("\n");return Tn(e,function(e){return 0<e.indexOf("alloy")&&!On(Fo,function(n){return-1<e.indexOf(n)}).isSome()}).getOr(Bo)}return Bo},Vo={logEventCut:A,logEventStopped:A,logNoParent:A,logEventNoHandlers:A,logEventResponse:A,write:A},Ho=function(n,e,t){var r,o="*"===Ro||hn(Ro,n)?(r=[],{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){hn(["mousemove","mouseover","mouseout",Zn()],n)||console.log(n,{event:n,target:e.dom(),sequence:bn(r,function(n){return hn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Do(n.target)+")":n.outcome})})}}):Vo,i=t(o);return o.write(),i},zo=I([Zt("menu"),Zt("selectedMenu")]),jo=I([Zt("item"),Zt("selectedItem")]),Lo=(I(Nt(jo().concat(zo()))),I(Nt(jo()))),Uo=tr("initSize",[Zt("numColumns"),Zt("numRows")]),Po=function(n,e,t){var r;return No(),Lt(e,e,t,(r=function(t){return nt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})},Ft(function(n){return r(n)})))},$o=function(n){return Po(0,n,rt(A))},Wo=function(n){return Po(0,n,rt(F.none))},Go=function(n){return Po(0,n,ot())},_o=function(n){return Po(0,n,ot())},qo=function(n,e){return cr(n,I(e))},Yo=function(n){return cr(n,h)},Ko=I(Uo),Xo=[nr("channels",Ht(nt.value,Nt([Go("onReceive"),ur("schema",Jt())])))],Jo=Pr({fields:Xo,name:"receiving",active:Ao}),Qo=function(n,e){var t=ti(n,e),r=e.aria();r.update()(n,r,t)},Zo=function(n,e,t){oo(n.element(),e.toggleClass()),Qo(n,e)},ni=function(n,e,t){to(n.element(),e.toggleClass()),Qo(n,e)},ei=function(n,e,t){ro(n.element(),e.toggleClass()),Qo(n,e)},ti=function(n,e){return io(n.element(),e.toggleClass())},ri=function(n,e,t){(e.selected()?ni:ei)(n,e,t)},oi=Object.freeze({onLoad:ri,toggle:Zo,isOn:ti,on:ni,off:ei}),ii=Object.freeze({exhibit:function(n,e,t){return Cr({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=Zo,xr(function(n){o(n,t,r)})),u=Dr(n,e,ri);return lr(En([n.toggleOnExecute()?[i]:[],[u]]))}}),ui=function(n,e,t){_r(n.element(),"aria-expanded",t)},ai=[ur("selected",!1),Zt("toggleClass"),ur("toggleOnExecute",!0),ar("aria",{mode:"none"},Xt("mode",{pressed:[ur("syncWithExpanded",!1),qo("update",function(n,e,t){_r(n.element(),"aria-pressed",t),e.syncWithExpanded()&&ui(n,e,t)})],checked:[qo("update",function(n,e,t){_r(n.element(),"aria-checked",t)})],expanded:[qo("update",ui)],selected:[qo("update",function(n,e,t){_r(n.element(),"aria-selected",t)})],none:[qo("update",A)]}))],ci=Pr({fields:ai,name:"toggling",active:ii,apis:oi}),si=function(t,r){return Jo.config({channels:wt(To.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},fi=function(n){return Jo.config({channels:wt(To.orientationChanged(),{onReceive:n})})},li=function(n,e){return{key:n,value:{onReceive:e}}},di="tinymce-mobile",mi={resolve:function(n){return di+"-"+n},prefix:I(di)},gi=function(n,e){e.ignore()||(po(n.element()),e.onFocus()(n))},vi=Object.freeze({focus:gi,blur:function(n,e){e.ignore()||ho(n.element())},isFocused:function(n){return e=n.element(),t=He(e).dom(),e.dom()===t.activeElement;var e,t}}),pi=Object.freeze({exhibit:function(n,e){return e.ignore()?Cr({}):Cr({attributes:{tabindex:"-1"}})},events:function(t){return lr([mr(Gn(),function(n,e){gi(n,t),e.stop()})])}}),hi=[$o("onFocus"),ur("ignore",!1)],bi=Pr({fields:hi,name:"focusing",active:pi,apis:vi}),yi=function(n){return n.style!==undefined},wi=function(n,e,t){if(!b(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);yi(n)&&n.style.setProperty(e,t)},xi=function(n,e,t){var r=n.dom();wi(r,e,t)},Si=function(n,e){var t=n.dom();V(e,function(n,e){wi(t,e,n)})},Ti=function(n,e){var t=n.dom(),r=window.getComputedStyle(t).getPropertyValue(e),o=""!==r||he(n)?r:Oi(t,e);return null===o?undefined:o},Oi=function(n,e){return yi(n)?n.style.getPropertyValue(e):""},ki=function(n,e){var t=n.dom(),r=Oi(t,e);return F.from(r).filter(function(n){return 0<n.length})},Ci=function(n,e){var t,r,o=n.dom();r=e,yi(t=o)&&t.style.removeProperty(r),Kr(n,"style")&&""===Yr(n,"style").replace(/^\s+|\s+$/g,"")&&Xr(n,"style")},Ei=function(n){return n.dom().offsetWidth};function Di(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Ti(n,r);return parseFloat(t)||0}return e},i=function(o,n){return Sn(n,function(n,e){var t=Ti(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!x(e)&&!e.match(/^[0-9]+$/))throw r+".set accepts only positive integer values. Value was "+e;var t=n.dom();yi(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var Ai,Ii,Mi=Di("height",function(n){var e=n.dom();return he(n)?e.getBoundingClientRect().height:e.offsetHeight}),Bi=function(n){return Mi.get(n)},Ri=function(n,e,t){return wn(function(n,e){for(var t=w(e)?e:I(!1),r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=se.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},Fi=function(n,e){return wn(ze(t=n).map(je).map(function(n){return wn(n,function(n){return!Ve(t,n)})}).getOr([]),e);var t},Ni=function(n,e){return Fe(e,n)},Vi=function(n){return Ne(n)},Hi=function(n,e,t){return mo(n,function(n){return Be(n,e)},t)},zi=function(n,e){return Ne(e,n)},ji=function(n,e,t){return lo(Be,Hi,n,e,t)},Li=function(n,e,t){var r=Mn(n.slice(0,e)),o=Mn(n.slice(e+1));return Tn(r.concat(o),t)},Ui=function(n,e,t){var r=Mn(n.slice(0,e));return Tn(r,t)},Pi=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return Tn(o.concat(r),t)},$i=function(n,e,t){var r=n.slice(e+1);return Tn(r,t)},Wi=function(t){return function(n){var e=n.raw();return hn(t,e.which)}},Gi=function(n){return function(e){return An(n,function(n){return n(e)})}},_i=function(n){return!0===n.raw().shiftKey},qi=function(n){return!0===n.raw().ctrlKey},Yi=S(_i),Ki=function(n,e){return{matches:n,classification:e}},Xi=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},Ji=function(n,e,t){return n<=e?e:t<=n?t:n},Qi=function(e,t,n){var r=Ni(e.element(),"."+t.highlightClass());yn(r,function(n){ro(n,t.highlightClass()),e.getSystem().getByDom(n).each(function(n){t.onDehighlight()(e,n)})})},Zi=function(n,e,t,r){var o=nu(n,e,t,r);Qi(n,e),to(r.element(),e.highlightClass()),o||e.onHighlight()(n,r)},nu=function(n,e,t,r){return io(r.element(),e.highlightClass())},eu=function(n,e,t,r){var o=Ni(n.element(),"."+e.itemClass());return F.from(o[r]).fold(function(){return nt.error("No element found with index "+r)},n.getSystem().getByDom)},tu=function(e,n,t){return zi(e.element(),"."+n.itemClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ru=function(e,n,t){var r=Ni(e.element(),"."+n.itemClass());return(0<r.length?F.some(r[r.length-1]):F.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},ou=function(t,e,n,r){var o=Ni(t.element(),"."+e.itemClass());return On(o,function(n){return io(n,e.highlightClass())}).bind(function(n){var e=Xi(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})},iu=Object.freeze({dehighlightAll:Qi,dehighlight:function(n,e,t,r){var o=nu(n,e,t,r);ro(r.element(),e.highlightClass()),o&&e.onDehighlight()(n,r)},highlight:Zi,highlightFirst:function(e,t,r){tu(e,t,r).each(function(n){Zi(e,t,r,n)})},highlightLast:function(e,t,r){ru(e,t,r).each(function(n){Zi(e,t,r,n)})},highlightAt:function(e,t,r,n){eu(e,t,r,n).fold(function(n){throw new Error(n)},function(n){Zi(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Ni(e.element(),"."+t.itemClass()),i=Io(bn(o,function(n){return e.getSystem().getByDom(n).toOption()}));Tn(i,n).each(function(n){Zi(e,t,r,n)})},isHighlighted:nu,getHighlighted:function(e,n,t){return zi(e.element(),"."+n.highlightClass()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:tu,getLast:ru,getPrevious:function(n,e,t){return ou(n,e,0,-1)},getNext:function(n,e,t){return ou(n,e,0,1)}}),uu=[Zt("highlightClass"),Zt("itemClass"),$o("onHighlight"),$o("onDehighlight")],au=Pr({fields:uu,name:"highlighting",apis:iu}),cu=function(){return{get:function(n){return yo(n.element())},set:function(n,e){n.getSystem().triggerFocus(e,n.element())}}},su=function(n,e,a,t,r,i){var u=function(e,t,r,o){var n,i,u=a(e,t,r,o);return(n=u,i=t.event(),Tn(n,function(n){return n.matches(i)}).map(function(n){return n.classification})).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([ur("focusManager",cu()),qo("handler",o),qo("state",e)])},processKey:u,toEvents:function(r,o){var n=t(r,o),e=lr(i.map(function(t){return mr(Gn(),function(n,e){t(n,r,o,e),e.stop()})}).toArray().concat([mr(q(),function(n,e){u(n,e,r,o).each(function(n){e.stop()})})]));return k(n,e)},toApis:r};return o},fu=function(n){var e=[rr("onEscape"),rr("onEnter"),ur("selector",'[data-alloy-tabstop="true"]'),ur("firstTabstop",0),ur("useTabstopAt",I(!0)),rr("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector().bind(function(n){return ji(e,n)}).getOr(e);return 0<Bi(t)},a=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt()(t);var e,t}).fold(function(){return r.cyclic()?F.some(!0):F.none()},function(n){return r.focusManager().set(e,n),F.some(!0)})},i=function(e,n,t,r){var o,i,u=Ni(e.element(),t.selector());return(o=e,i=t,i.focusManager().get(o).bind(function(n){return ji(n,i.selector())})).bind(function(n){return On(u,l(Ve,n)).bind(function(n){return a(e,u,n,t,r)})})},t=I([Ki(Gi([_i,Wi([9])]),function(n,e,t,r){var o=t.cyclic()?Li:Ui;return i(n,0,t,o)}),Ki(Wi([9]),function(n,e,t,r){var o=t.cyclic()?Pi:$i;return i(n,0,t,o)}),Ki(Wi([27]),function(e,t,n,r){return n.onEscape().bind(function(n){return n(e,t)})}),Ki(Gi([Yi,Wi([13])]),function(e,t,n,r){return n.onEnter().bind(function(n){return n(e,t)})})]),r=I({}),o=I({});return su(e,zr.init,t,r,o,F.some(function(e,t){var n,r,o,i;(n=e,r=t,o=Ni(n.element(),r.selector()),i=wn(o,function(n){return u(r,n)}),F.from(i[r.firstTabstop()])).each(function(n){t.focusManager().set(e,n)})}))},lu=fu(cr("cyclic",I(!1))),du=fu(cr("cyclic",I(!0))),mu=function(n){return"input"===me(n)&&"radio"!==Yr(n,"type")||"textarea"===me(n)},gu=function(n,e,t){return mu(t)&&Wi([32])(e.event())?F.none():(ue(n,t,Yn()),F.some(!0))},vu=[ur("execute",gu),ur("useSpace",!1),ur("useEnter",!0),ur("useControlEnter",!1),ur("useDown",!1)],pu=function(n,e,t){return t.execute()(n,e,n.element())},hu=I({}),bu=I({}),yu=su(vu,zr.init,function(n,e,t,r){var o=t.useSpace()&&!mu(n.element())?[32]:[],i=t.useEnter()?[13]:[],u=t.useDown()?[40]:[],a=o.concat(i).concat(u);return[Ki(Wi(a),pu)].concat(t.useControlEnter()?[Ki(Gi([qi,Wi([13])]),pu)]:[])},hu,bu,F.none()),wu=function(n){var t=fo(F.none());return jr({readState:I({}),setGridSize:function(n,e){t.set(F.some({numRows:I(n),numColumns:I(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})},xu=Object.freeze({flatgrid:wu,init:function(n){return n.state()(n)}}),Su=function(e,t){return function(n){return"rtl"===Tu(n)?t:e}},Tu=function(n){return"rtl"===Ti(n,"direction")?"rtl":"ltr"},Ou=function(i){return function(n,e,t,r){var o=i(n.element());return Du(o,n,e,t,r)}},ku=function(n,e){var t=Su(n,e);return Ou(t)},Cu=function(n,e){var t=Su(e,n);return Ou(t)},Eu=function(o){return function(n,e,t,r){return Du(o,n,e,t,r)}},Du=function(e,t,n,r,o){return r.focusManager().get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager().set(t,n),!0})},Au=Eu,Iu=Eu,Mu=Eu,Bu=function(n){var e,t=n.dom();return!((e=t).offsetWidth<=0&&e.offsetHeight<=0)},Ru=Ce(["index","candidates"],[]),Fu=function(n,e,t){return Nu(n,e,t,Bu)},Nu=function(n,e,t,r){var o,i=l(Ve,e),u=Ni(n,t),a=wn(u,Bu);return On(o=a,i).map(function(n){return Ru({index:n,candidates:o})})},Vu=function(n,e){return On(n,function(n){return Ve(e,n)})},Hu=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?F.some(t[e]):F.none()})},zu=function(o,n,i,u,a){return Hu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=Xi(e,a,0,t-1);return F.some({row:I(n),column:I(r)})})},ju=function(i,n,u,a,c){return Hu(i,n,a,function(n,e){var t=Xi(n,c,0,u-1),r=t===u-1?i.length-t*a:a,o=Ji(e,0,r-1);return F.some({row:I(t),column:I(o)})})},Lu=[Zt("selector"),ur("execute",gu),Wo("onEscape"),ur("captureTab",!1),Ko()],Uu=function(o){return function(n,e,t,r){return Fu(n,e,t.selector()).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize().numRows()),r.getNumColumns().getOr(t.initSize().numColumns()))})}},Pu=function(n,e,t,r){return t.captureTab()?F.some(!0):F.none()},$u=Uu(function(n,e,t,r){return zu(n,e,t,r,-1)}),Wu=Uu(function(n,e,t,r){return zu(n,e,t,r,1)}),Gu=Uu(function(n,e,t,r){return ju(n,e,t,r,-1)}),_u=Uu(function(n,e,t,r){return ju(n,e,t,r,1)}),qu=I([Ki(Wi([37]),ku($u,Wu)),Ki(Wi([39]),Cu($u,Wu)),Ki(Wi([38]),Au(Gu)),Ki(Wi([40]),Iu(_u)),Ki(Gi([_i,Wi([9])]),Pu),Ki(Gi([Yi,Wi([9])]),Pu),Ki(Wi([27]),function(n,e,t,r){return t.onEscape()(n,e)}),Ki(Wi([32].concat([13])),function(e,t,r,n){return(o=e,i=r,i.focusManager().get(o).bind(function(n){return ji(n,i.selector())})).bind(function(n){return r.execute()(e,t,n)});var o,i})]),Yu=I({}),Ku=su(Lu,wu,qu,Yu,{},F.some(function(e,t,n){zi(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Xu=function(n,e,t,o){return Fu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates(),r=Xi(e,o,0,t.length-1);return F.from(t[r])})},Ju=[Zt("selector"),ur("getInitial",F.none),ur("execute",gu),ur("executeOnMove",!1),ur("allowVertical",!0)],Qu=function(e,t,r){return(n=e,o=r,o.focusManager().get(n).bind(function(n){return ji(n,o.selector())})).bind(function(n){return r.execute()(e,t,n)});var n,o},Zu=function(n,e,t){return Xu(n,t.selector(),e,-1)},na=function(n,e,t){return Xu(n,t.selector(),e,1)},ea=function(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove()?Qu(n,e,t):F.some(!0)})}},ta=I({}),ra=I({}),oa=su(Ju,zr.init,function(n,e,t,r){var o=[37].concat(t.allowVertical()?[38]:[]),i=[39].concat(t.allowVertical()?[40]:[]);return[Ki(Wi(o),ea(ku(Zu,na))),Ki(Wi(i),ea(Cu(Zu,na))),Ki(Wi([13]),Qu),Ki(Wi([32]),Qu)]},ta,ra,F.some(function(e,t){t.getInitial()(e).or(zi(e.element(),t.selector())).each(function(n){t.focusManager().set(e,n)})})),ia=Ce(["rowIndex","columnIndex","cell"],[]),ua=function(n,e,t){return F.from(n[e]).bind(function(n){return F.from(n[t]).map(function(n){return ia({rowIndex:e,columnIndex:t,cell:n})})})},aa=function(n,e,t,r){var o=n[e].length,i=Xi(t,r,0,o-1);return ua(n,e,i)},ca=function(n,e,t,r){var o=Xi(t,r,0,n.length-1),i=n[o].length,u=Ji(e,0,i-1);return ua(n,o,u)},sa=function(n,e,t,r){var o=n[e].length,i=Ji(t+r,0,o-1);return ua(n,e,i)},fa=function(n,e,t,r){var o=Ji(t+r,0,n.length-1),i=n[o].length,u=Ji(e,0,i-1);return ua(n,o,u)},la=[tr("selectors",[Zt("row"),Zt("cell")]),ur("cycles",!0),ur("previousSelector",F.none),ur("execute",gu)],da=function(n,e){return function(t,r,i){var u=i.cycles()?n:e;return ji(r,i.selectors().row()).bind(function(n){var e=Ni(n,i.selectors().cell());return Vu(e,r).bind(function(r){var o=Ni(t,i.selectors().row());return Vu(o,n).bind(function(n){var e,t=(e=i,bn(o,function(n){return Ni(n,e.selectors().cell())}));return u(t,n,r).map(function(n){return n.cell()})})})})}},ma=da(function(n,e,t){return aa(n,e,t,-1)},function(n,e,t){return sa(n,e,t,-1)}),ga=da(function(n,e,t){return aa(n,e,t,1)},function(n,e,t){return sa(n,e,t,1)}),va=da(function(n,e,t){return ca(n,t,e,-1)},function(n,e,t){return fa(n,t,e,-1)}),pa=da(function(n,e,t){return ca(n,t,e,1)},function(n,e,t){return fa(n,t,e,1)}),ha=I([Ki(Wi([37]),ku(ma,ga)),Ki(Wi([39]),Cu(ma,ga)),Ki(Wi([38]),Au(va)),Ki(Wi([40]),Iu(pa)),Ki(Wi([32].concat([13])),function(e,t,r){return yo(e.element()).bind(function(n){return r.execute()(e,t,n)})})]),ba=I({}),ya=I({}),wa=su(la,zr.init,ha,ba,ya,F.some(function(e,t){t.previousSelector()(e).orThunk(function(){var n=t.selectors();return zi(e.element(),n.cell())}).each(function(n){t.focusManager().set(e,n)})})),xa=[Zt("selector"),ur("execute",gu),ur("moveOnTab",!1)],Sa=function(e,t,r){return r.focusManager().get(e).bind(function(n){return r.execute()(e,t,n)})},Ta=function(n,e,t){return Xu(n,t.selector(),e,-1)},Oa=function(n,e,t){return Xu(n,t.selector(),e,1)},ka=I([Ki(Wi([38]),Mu(Ta)),Ki(Wi([40]),Mu(Oa)),Ki(Gi([_i,Wi([9])]),function(n,e,t){return t.moveOnTab()?Mu(Ta)(n,e,t):F.none()}),Ki(Gi([Yi,Wi([9])]),function(n,e,t){return t.moveOnTab()?Mu(Oa)(n,e,t):F.none()}),Ki(Wi([13]),Sa),Ki(Wi([32]),Sa)]),Ca=I({}),Ea=I({}),Da=su(xa,zr.init,ka,Ca,Ea,F.some(function(e,t){zi(e.element(),t.selector()).each(function(n){t.focusManager().set(e,n)})})),Aa=[Wo("onSpace"),Wo("onEnter"),Wo("onShiftEnter"),Wo("onLeft"),Wo("onRight"),Wo("onTab"),Wo("onShiftTab"),Wo("onUp"),Wo("onDown"),Wo("onEscape"),rr("focusIn")],Ia=su(Aa,zr.init,function(n,e,t){return[Ki(Wi([32]),t.onSpace()),Ki(Gi([Yi,Wi([13])]),t.onEnter()),Ki(Gi([_i,Wi([13])]),t.onShiftEnter()),Ki(Gi([_i,Wi([9])]),t.onShiftTab()),Ki(Gi([Yi,Wi([9])]),t.onTab()),Ki(Wi([38]),t.onUp()),Ki(Wi([40]),t.onDown()),Ki(Wi([37]),t.onLeft()),Ki(Wi([39]),t.onRight()),Ki(Wi([32]),t.onSpace()),Ki(Wi([27]),t.onEscape())]},function(){return{}},function(){return{}},F.some(function(e,t){return t.focusIn().bind(function(n){return n(e,t)})})),Ma=lu.schema(),Ba=du.schema(),Ra=oa.schema(),Fa=Ku.schema(),Na=wa.schema(),Va=yu.schema(),Ha=Da.schema(),za=Ia.schema(),ja=(Ii=qt("Creating behaviour: "+(Ai={branchKey:"mode",branches:Object.freeze({acyclic:Ma,cyclic:Ba,flow:Ra,flatgrid:Fa,matrix:Na,execution:Va,menu:Ha,special:za}),name:"keying",active:{events:function(n,e){return n.handler().toEvents(n,e)}},apis:{focusIn:function(n){n.getSystem().triggerFocus(n.element(),n.element())},setGridSize:function(n,e,t,r,o){Tt(t,"setGridSize")?t.setGridSize(r,o):console.error("Layout does not support setGridSize")}},state:xu}).name,$r,Ai),Ar(Xt(Ii.branchKey,Ii.branches),Ii.name,Ii.active,Ii.apis,Ii.extra,Ii.state)),La=function(r,n){return e=r,t={},o=bn(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,Lt(e,e,it(),Ft(function(n){return nt.error("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([cr("dump",h)]),Lt(e,e,rt(t),Vt(o));var e,t,o},Ua=function(n){return n.dump()},Pa="placeholder",$a=et([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Wa=function(n,e,t,r){return t.uiType===Pa?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?$a.single(!0,I(i)):yt(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+N(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+Et(i,null,2))},function(n){return n.replace()})):$a.single(!1,I(t));var o,i,u},Ga=function(i,u,a,c){return Wa(i,0,a,c).fold(function(n,e){var t=e(u,a.config,a.validated),r=yt(t,"components").getOr([]),o=Dn(r,function(n){return Ga(i,u,n,c)});return[k(t,{components:o})]},function(n,e){return e(u,a.config,a.validated)})},_a=function(e,t,n,r){var o,i,u,a=H(r,function(n,e){return r=n,o=!1,{name:I(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(!0===o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),c=(o=e,i=t,u=a,Dn(n,function(n){return Ga(o,i,n,u)}));return V(a,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+Et(t.components(),null,2))}),c},qa=$a.single,Ya=$a.multiple,Ka=I(Pa),Xa=0,Ja=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Xa+String(e)},Qa=et([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Za=ur("factory",{sketch:h}),nc=ur("schema",[]),ec=Zt("name"),tc=Lt("pname","pname",ut(function(n){return"<alloy."+Ja(n.name)+">"}),Jt()),rc=ur("defaults",I({})),oc=ur("overrides",I({})),ic=Vt([Za,nc,ec,tc,rc,oc]),uc=Vt([Za,nc,ec,tc,rc,oc]),ac=Vt([Za,nc,ec,Zt("unit"),tc,rc,oc]),cc=function(n){var e=function(n){return n.name()};return n.fold(e,e,e,e)},sc=function(t,r){return function(n){var e=Yt("Converting part type",r,n);return t(e)}},fc=sc(Qa.required,ic),lc=sc(Qa.optional,uc),dc=sc(Qa.group,ac),mc=I("entirety"),gc=function(n,e,t,r){var o=t;return k(e.defaults()(n,t,r),t,{uid:n.partUids()[e.name()]},e.overrides()(n,t,r),{"debug.sketcher":wt("part-"+e.name(),o)})},vc=function(o,n){var i={};return yn(n,function(n){var e;(e=n,e.fold(F.some,F.none,F.some,F.some)).each(function(t){var r=pc(o,t.pname());i[t.name()]=function(n){var e=qt("Part: "+t.name()+" in "+o,Vt(t.schema()),n);return k(r,{config:n,validated:e})}})}),i},pc=function(n,e){return{uiType:Ka(),owner:n,name:e}},hc=function(n,e,t){return r=e,i={},o={},yn(t,function(n){n.fold(function(r){i[r.pname()]=qa(!0,function(n,e,t){return r.factory().sketch(gc(n,r,e,t))})},function(n){var e=r.parts()[n.name()]();o[n.name()]=I(gc(r,n,e[mc()]()))},function(r){i[r.pname()]=qa(!1,function(n,e,t){return r.factory().sketch(gc(n,r,e,t))})},function(o){i[o.pname()]=Ya(!0,function(e,n,t){var r=e[o.name()]();return bn(r,function(n){return o.factory().sketch(k(o.defaults()(e,n),n,o.overrides()(e,n)))})})})}),{internals:I(i),externals:I(o)};var r,i,o},bc=function(n,e,t){return _a(F.some(n),e,e.components(),t)},yc=function(n,e,t){var r=e.partUids()[t];return n.getSystem().getByUid(r).toOption()},wc=function(n,e,t){return yc(n,e,t).getOrDie("Could not find part: "+t)},xc=function(e,n){var t=bn(n,cc);return xt(bn(t,function(n){return{key:n,value:e+"-"+n}}))},Sc=function(e){return Lt("partUids","partUids",at(function(n){return xc(n.uid,e)}),Jt())},Tc=Ja("alloy-premade"),Oc=Ja("api"),kc=function(n){return wt(Tc,n)},Cc=function(o){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];var r=n.config(Oc);return o.apply(undefined,[r].concat([n].concat(e)))},e=o.toString(),t=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Sr(i.slice(1))}},n;var n,e,t,r,i},Ec=I(Oc),Dc=I("alloy-id-"),Ac=I("data-alloy-id"),Ic=Dc(),Mc=Ac(),Bc=function(n){var e=ve(n)?Yr(n,Mc):null;return F.from(e)},Rc=function(n){return Ja(n)},Fc=function(n,e,t,r,o){var i,u,a=(u=o,(0<(i=r).length?[tr("parts",i)]:[]).concat([Zt("uid"),ur("dom",{}),ur("components",[]),Yo("originalSpec"),ur("debug.sketcher",{})]).concat(u));return Yt(n+" [SpecSchema]",Nt(a.concat(e)),t)},Nc=function(n,e,t,r,o){var i=Vc(o),u=Dn(t,function(n){return n.fold(F.none,F.some,F.none,F.none).map(function(n){return tr(n.name(),n.schema().concat([Yo(mc())]))}).toArray()}),a=Sc(t),c=Fc(n,e,i,u,[a]),s=hc(0,c,t),f=bc(n,c,s.internals());return k(r(c,f,i,s.externals()),{"debug.sketcher":wt(n,o)})},Vc=function(n){return k({uid:Rc("uid")},n)},Hc=Nt([Zt("name"),Zt("factory"),Zt("configFields"),ur("apis",{}),ur("extraApis",{})]),zc=Nt([Zt("name"),Zt("factory"),Zt("configFields"),Zt("partFields"),ur("apis",{}),ur("extraApis",{})]),jc=function(n){var a=qt("Sketcher for "+n.name,Hc,n),e=H(a.apis,Cc),t=H(a.extraApis,function(n,e){return Tr(n,e)});return k({name:I(a.name),partFields:I([]),configFields:I(a.configFields),sketch:function(n){return e=a.name,t=a.configFields,r=a.factory,i=Vc(o=n),u=Fc(e,t,i,[],[]),k(r(u,i),{"debug.sketcher":wt(e,o)});var e,t,r,o,i,u}},e,t)},Lc=function(n){var e=qt("Sketcher for "+n.name,zc,n),t=vc(e.name,e.partFields),r=H(e.apis,Cc),o=H(e.extraApis,function(n,e){return Tr(n,e)});return k({name:I(e.name),partFields:I(e.partFields),configFields:I(e.configFields),sketch:function(n){return Nc(e.name,e.configFields,e.partFields,e.factory,n)},parts:I(t)},r,o)},Uc=jc({name:"Button",factory:function(n){var e,t,r,o=(e=n.action(),t=function(n,e){e.stop(),ie(n)},r=$n.detect().deviceType.isTouch()?[mr(Xn(),t)]:[mr(X(),t),mr($(),function(n,e){e.cut()})],lr(En([e.map(function(t){return mr(Yn(),function(n,e){t(n),e.stop()})}).toArray(),r]))),i=yt(n.dom(),"attributes").bind(ht("type")),u=yt(n.dom(),"tag");return{uid:n.uid(),dom:n.dom(),components:n.components(),events:o,behaviours:k(Lr([bi.config({}),ja.config({mode:"execution",useSpace:!0,useEnter:!0})]),Ua(n.buttonBehaviours())),domModification:{attributes:k(i.fold(function(){return u.is("button")?{type:"button"}:{}},function(n){return{}}),{role:n.role().getOr("button")})},eventOrder:n.eventOrder()}},configFields:[ur("uid",undefined),Zt("dom"),ur("components",[]),La("buttonBehaviours",[bi,ja]),rr("action"),rr("role"),ur("eventOrder",{})]}),Pc=Pr({fields:[],name:"unselecting",active:Object.freeze({events:function(n){return lr([dr(Q(),I(!0))])},exhibit:function(n,e){return Cr({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),$c=function(n){var e,t,r,o=se.fromHtml(n),i=je(o),u=(t=(e=o).dom().attributes!==undefined?e.dom().attributes:[],Sn(t,function(n,e){return"class"===e.name?n:k(n,wt(e.name,e.value))},{})),a=(r=o,Array.prototype.slice.call(r.dom().classList,0)),c=0===i.length?{}:{innerHtml:Oo(o)};return k({tag:me(o),classes:a,attributes:u},c)},Wc=function(n){var e,o,t=(e=n,o={prefix:mi.prefix()},e.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"===t?r.toString():n}));return $c(t)},Gc=function(n){return{dom:Wc(n)}},_c=function(n){return Lr([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),si(n,function(n,e){(e?ci.on:ci.off)(n)})])},qc=function(n,e,t){return Uc.sketch({dom:Wc('<span class="${prefix}-toolbar-button ${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:e,buttonBehaviours:k(Lr([Pc.config({})]),t)})},Yc={forToolbar:qc,forToolbarCommand:function(n,e){return qc(e,function(){n.execCommand(e)},{})},forToolbarStateAction:function(n,e,t,r){var o=_c(t);return qc(e,r,o)},forToolbarStateCommand:function(n,e){var t=_c(e);return qc(e,function(){n.execCommand(e)},t)}},Kc=function(t,r){return{left:I(t),top:I(r),translate:function(n,e){return Kc(t+n,r+e)}}},Xc=Kc,Jc=function(n,e,t){return Math.max(e,Math.min(t,n))},Qc=function(n,e,t,r,o,i,u){var a=t-e;if(r<n.left)return e-1;if(r>n.right)return t+1;var c,s,f,l,d=Math.min(n.right,Math.max(r,n.left))-n.left,m=Jc(d/n.width*a+e,e-1,t+1),g=Math.round(m);return i&&e<=m&&m<=t?(c=m,s=e,f=t,l=o,u.fold(function(){var n=c-s,e=Math.round(n/l)*l;return Jc(s+e,s-1,f+1)},function(n){var e=(c-n)%l,t=Math.round(e/l),r=Math.floor((c-n)/l),o=Math.floor((f-n)/l),i=n+Math.min(o,r+t)*l;return Math.max(n,i)})):g},Zc="slider.change.value",ns=$n.detect().deviceType.isTouch(),es=function(n){return function(n){var e=n.event().raw();if(ns){var t=e;return t.touches!==undefined&&1===t.touches.length?F.some(t.touches[0]).map(function(n){return Xc(n.clientX,n.clientY)}):F.none()}var r=e;return r.clientX!==undefined?F.some(r).map(function(n){return Xc(n.clientX,n.clientY)}):F.none()}(n).map(function(n){return n.left()})},ts=function(n,e){oe(n,Zc,{value:e})},rs=function(i,u,a,n){return es(n).map(function(n){var e,t,r,o;return e=i,r=n,o=Qc(a,(t=u).min(),t.max(),r,t.stepSize(),t.snapToGrid(),t.snapStart()),ts(e,o),n})},os=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),t<r?t:o<t?o:t===r?r-1:Math.max(r,t-i));ts(n,u)},is=function(n,e){var t,r,o,i,u=(t=e.value().get(),r=e.min(),o=e.max(),i=e.stepSize(),o<t?t:t<r?r:t===o?o+1:Math.min(o,t+i));ts(n,u)},us=$n.detect().deviceType.isTouch(),as=function(n,r){return lc({name:n+"-edge",overrides:function(n){var e=lr([gr(L(),r,[n])]),t=lr([gr($(),r,[n]),gr(W(),function(n,e){e.mouseIsDown().get()&&r(n,e)},[n])]);return{events:us?e:t}}})},cs=[as("left",function(n,e){ts(n,e.min()-1)}),as("right",function(n,e){ts(n,e.max()+1)}),fc({name:"thumb",defaults:I({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:lr([pr(L(),n,"spectrum"),pr(U(),n,"spectrum"),pr(P(),n,"spectrum")])}}}),fc({schema:[cr("mouseIsDown",function(){return fo(!1)})],name:"spectrum",overrides:function(r){var t=function(n,e){var t=n.element().dom().getBoundingClientRect();rs(n,r,t,e)},n=lr([mr(L(),t),mr(U(),t)]),e=lr([mr($(),t),mr(W(),function(n,e){r.mouseIsDown().get()&&t(n,e)})]);return{behaviours:Lr(us?[]:[ja.config({mode:"special",onLeft:function(n){return os(n,r),F.some(!0)},onRight:function(n){return is(n,r),F.some(!0)}}),bi.config({})]),events:us?n:e}}})],ss=function(n,e,t){e.store().manager().onLoad(n,e,t)},fs=function(n,e,t){e.store().manager().onUnload(n,e,t)},ls=Object.freeze({onLoad:ss,onUnload:fs,setValue:function(n,e,t,r){e.store().manager().setValue(n,e,t,r)},getValue:function(n,e,t){return e.store().manager().getValue(n,e,t)}}),ds=Object.freeze({events:function(t,r){var n=t.resetOnDom()?[br(function(n,e){ss(n,t,r)}),yr(function(n,e){fs(n,t,r)})]:[Dr(t,r,ss)];return lr(n)}}),ms=function(){var n=fo(null);return jr({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},gs=function(){var n=fo({});return jr({readState:function(){return{mode:"dataset",dataset:n.get()}},set:n.set,get:n.get})},vs=Object.freeze({memory:ms,dataset:gs,manual:function(){return jr({readState:function(){}})},init:function(n){return n.store().manager().state(n)}}),ps=function(n,e,t,r){e.store().getDataKey(),t.set({}),e.store().setData()(n,r),e.onSetValue()(n,r)},hs=[rr("initialValue"),Zt("getFallbackEntry"),Zt("getDataKey"),Zt("setData"),qo("manager",{setValue:ps,getValue:function(n,e,t){var r=e.store().getDataKey()(n),o=t.get();return yt(o,r).fold(function(){return e.store().getFallbackEntry()(r)},function(n){return n})},onLoad:function(e,t,r){t.store().initialValue().each(function(n){ps(e,t,r,n)})},onUnload:function(n,e,t){t.set({})},state:gs})],bs=[Zt("getValue"),ur("setValue",A),rr("initialValue"),qo("manager",{setValue:function(n,e,t,r){e.store().setValue()(n,r),e.onSetValue()(n,r)},getValue:function(n,e,t){return e.store().getValue()(n)},onLoad:function(e,t,n){t.store().initialValue().each(function(n){t.store().setValue()(e,n)})},onUnload:A,state:zr.init})],ys=[rr("initialValue"),qo("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue()(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store().initialValue().each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:ms})],ws=[ar("store",{mode:"memory"},Xt("mode",{memory:ys,manual:bs,dataset:hs})),$o("onSetValue"),ur("resetOnDom",!1)],xs=Pr({fields:ws,name:"representing",active:ds,apis:ls,extra:{setValueFrom:function(n,e){var t=xs.getValue(e);xs.setValue(n,t)}},state:vs}),Ss=$n.detect().deviceType.isTouch(),Ts=[Zt("min"),Zt("max"),ur("stepSize",1),ur("onChange",A),ur("onInit",A),ur("onDragStart",A),ur("onDragEnd",A),ur("snapToGrid",!1),rr("snapStart"),Zt("getInitialValue"),La("sliderBehaviours",[ja,xs]),cr("value",function(n){return fo(n.min)})].concat(Ss?[]:[cr("mouseIsDown",function(){return fo(!1)})]),Os=Di("width",function(n){return n.dom().offsetWidth}),ks=function(n,e){Os.set(n,e)},Cs=function(n){return Os.get(n)},Es=$n.detect().deviceType.isTouch(),Ds=Lc({name:"Slider",configFields:Ts,partFields:cs,factory:function(c,n,e,t){var s=c.max()-c.min(),f=function(n){var e=n.element().dom().getBoundingClientRect();return(e.left+e.right)/2},o=function(n){return wc(n,c,"thumb")},i=function(n){var e,t,r,o,i=wc(n,c,"spectrum").element().dom().getBoundingClientRect(),u=n.element().dom().getBoundingClientRect(),a=(e=n,t=i,(o=(r=c).value().get())<r.min()?yc(e,r,"left-edge").fold(function(){return 0},function(n){return f(n)-t.left}):o>r.max()?yc(e,r,"right-edge").fold(function(){return t.width},function(n){return f(n)-t.left}):(r.value().get()-r.min())/s*t.width);return i.left-u.left+a},u=function(n){var e=i(n),t=o(n),r=Cs(t.element())/2;xi(t.element(),"left",e-r+"px")},r=function(n,e){var t=c.value().get(),r=o(n);return t!==e||ki(r.element(),"left").isNone()?(c.value().set(e),u(n),c.onChange()(n,r,e),F.some(!0)):F.none()},a=Es?[mr(L(),function(n,e){c.onDragStart()(n,o(n))}),mr(P(),function(n,e){c.onDragEnd()(n,o(n))})]:[mr($(),function(n,e){e.stop(),c.onDragStart()(n,o(n)),c.mouseIsDown().set(!0)}),mr(G(),function(n,e){c.onDragEnd()(n,o(n)),c.mouseIsDown().set(!1)})];return{uid:c.uid(),dom:c.dom(),components:n,behaviours:k(Lr(En([Es?[]:[ja.config({mode:"special",focusIn:function(n){return yc(n,c,"spectrum").map(ja.focusIn).map(I(!0))}})],[xs.config({store:{mode:"manual",getValue:function(n){return c.value().get()}}})]])),Ua(c.sliderBehaviours())),events:lr([mr(Zc,function(n,e){r(n,e.event().value())}),br(function(n,e){c.value().set(c.getInitialValue()());var t=o(n);u(n),c.onInit()(n,t,c.value().get())})].concat(a)),apis:{resetToMin:function(n){r(n,c.min())},resetToMax:function(n){r(n,c.max())},refresh:u},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),As=function(e,t,r){return Yc.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{})},Is=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Ds.sketch({dom:Wc('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Ds.parts()["left-edge"](Gc('<div class="${prefix}-hue-slider-black"></div>')),Ds.parts().spectrum({dom:Wc('<div class="${prefix}-slider-gradient-container"></div>'),components:[Gc('<div class="${prefix}-slider-gradient"></div>')],behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])}),Ds.parts()["right-edge"](Gc('<div class="${prefix}-hue-slider-white"></div>')),Ds.parts().thumb({dom:Wc('<div class="${prefix}-slider-thumb"></div>'),behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t);xi(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},onInit:function(n,e,t){var r=i(t);xi(e.element(),"background-color",r)},stepSize:10,min:0,max:360,getInitialValue:o.getInitialValue,sliderBehaviours:Lr([fi(Ds.refresh)])}))];var o,i},Ms=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return As(n,"color",function(){return Is(e)})},Bs=Nt([Zt("getInitialValue"),Zt("onChange"),Zt("category"),Zt("sizes")]),Rs=function(n){var o=qt("SizeSlider",Bs,n);return Ds.sketch({dom:{tag:"div",classes:[mi.resolve("slider-"+o.category+"-size-container"),mi.resolve("slider"),mi.resolve("slider-size-container")]},onChange:function(n,e,t){var r;0<=(r=t)&&r<o.sizes.length&&o.onChange(t)},onDragStart:function(n,e){ci.on(e)},onDragEnd:function(n,e){ci.off(e)},min:0,max:o.sizes.length-1,stepSize:1,getInitialValue:o.getInitialValue,snapToGrid:!0,sliderBehaviours:Lr([fi(Ds.refresh)]),components:[Ds.parts().spectrum({dom:Wc('<div class="${prefix}-slider-size-container"></div>'),components:[Gc('<div class="${prefix}-slider-size-line"></div>')]}),Ds.parts().thumb({dom:Wc('<div class="${prefix}-slider-thumb"></div>'),behaviours:Lr([ci.config({toggleClass:mi.resolve("thumb-active")})])})]})},Fs=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Ns=function(n){var e,t,r=n.selection.getStart(),o=se.fromDom(r),i=se.fromDom(n.getBody()),u=(e=function(n){return Ve(i,n)},(ve(t=o)?F.some(t):ze(t)).map(function(n){return go(n,function(n){return ki(n,"font-size").isSome()},e).bind(function(n){return ki(n,"font-size")}).getOrThunk(function(){return Ti(n,"font-size")})}).getOr(""));return Tn(Fs,function(n){return u===n}).getOr("medium")},Vs={candidates:I(Fs),get:function(n){var e,t=Ns(n);return(e=t,On(Fs,function(n){return n===e})).getOr(2)},apply:function(r,n){var e;(e=n,F.from(Fs[e])).each(function(n){var e,t;t=n,Ns(e=r)!==t&&e.execCommand("fontSize",!1,t)})}},Hs=Vs.candidates(),zs=function(n){return[Gc('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),(e=n,Rs({onChange:e.onChange,sizes:Hs,category:"font",getInitialValue:e.getInitialValue})),Gc('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},js=function(n){var e=n.uid!==undefined&&Tt(n,"uid")?n.uid:Rc("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).fold(F.none,F.some)},asSpec:function(){return k(n,{uid:e})}}};function Ls(n,e){return Ps(document.createElement("canvas"),n,e)}function Us(n){return n.getContext("2d")}function Ps(n,e,t){return n.width=e,n.height=t,n}var $s={create:Ls,clone:function(n){var e;return Us(e=Ls(n.width,n.height)).drawImage(n,0,0),e},resize:Ps,get2dContext:Us,get3dContext:function(n){var e=null;try{e=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(t){}return e||(e=null),e}},Ws={getWidth:function(n){return n.naturalWidth||n.width},getHeight:function(n){return n.naturalHeight||n.height}},Gs=window.Promise?window.Promise:function(){var n=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(n,r(o,this),r(u,this))},e=n.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){setTimeout(n,1)};function r(n,e){return function(){n.apply(e,arguments)}}var t=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)};function i(r){var o=this;null!==this._state?e(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function o(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void s(r(e,n),r(o,this),r(u,this))}this._state=!0,this._value=n,a.call(this)}catch(t){u.call(this,t)}}function u(n){this._state=!1,this._value=n,a.call(this)}function a(){for(var n=0,e=this._deferreds.length;n<e;n++)i.call(this,this._deferreds[n]);this._deferreds=null}function c(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function s(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}return n.prototype["catch"]=function(n){return this.then(null,n)},n.prototype.then=function(t,r){var o=this;return new n(function(n,e){i.call(o,new c(t,r,n,e))})},n.all=function(){var c=Array.prototype.slice.call(1===arguments.length&&t(arguments[0])?arguments[0]:arguments);return new n(function(o,i){if(0===c.length)return o([]);var u=c.length;function a(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){a(e,n)},i)}c[e]=n,0==--u&&o(c)}catch(r){i(r)}}for(var n=0;n<c.length;n++)a(n,c[n])})},n.resolve=function(e){return e&&"object"==typeof e&&e.constructor===n?e:new n(function(n){n(e)})},n.reject=function(t){return new n(function(n,e){e(t)})},n.race=function(o){return new n(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},n}();function _s(){return new(Ae.getOrDie("FileReader"))}var qs={atob:function(n){return Ae.getOrDie("atob")(n)},requestAnimationFrame:function(n){Ae.getOrDie("requestAnimationFrame")(n)}};function Ys(a){return new Gs(function(n,e){var t=URL.createObjectURL(a),r=new Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",u)};function i(){o(),n(r)}function u(){o(),e("Unable to load data of type "+a.type+": "+t)}r.addEventListener("load",i),r.addEventListener("error",u),r.src=t,r.complete&&i()})}function Ks(r){return new Gs(function(n,t){var e=new XMLHttpRequest;e.open("GET",r,!0),e.responseType="blob",e.onload=function(){200==this.status&&n(this.response)},e.onerror=function(){var n,e=this;t(0===this.status?((n=new Error("No access to download image")).code=18,n.name="SecurityError",n):new Error("Error "+e.status+" downloading image"))},e.send()})}function Xs(n){var e=n.split(","),t=/data:([^;]+)/.exec(e[0]);if(!t)return F.none();for(var r,o,i,u=t[1],a=e[1],c=qs.atob(a),s=c.length,f=Math.ceil(s/1024),l=new Array(f),d=0;d<f;++d){for(var m=1024*d,g=Math.min(m+1024,s),v=new Array(g-m),p=m,h=0;p<g;++h,++p)v[h]=c[p].charCodeAt(0);l[d]=(r=v,new(Ae.getOrDie("Uint8Array"))(r))}return F.some((o=l,i={type:u},new(Ae.getOrDie("Blob"))(o,i)))}function Js(t){return new Gs(function(n,e){Xs(t).fold(function(){e("uri is not base64: "+t)},n)})}function Qs(t){return new Gs(function(n){var e=_s();e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}var Zs,nf,ef,tf,rf,of,uf,af,cf={blobToImage:Ys,imageToBlob:function(n){var e=n.src;return 0===e.indexOf("data:")?Js(e):Ks(e)},blobToArrayBuffer:function(t){return new Gs(function(n){var e=_s();e.onloadend=function(){n(e.result)},e.readAsArrayBuffer(t)})},blobToDataUri:Qs,blobToBase64:function(n){return Qs(n).then(function(n){return n.split(",")[1]})},dataUriToBlobSync:Xs,canvasToBlob:function(n,t,r){return t=t||"image/png",HTMLCanvasElement.prototype.toBlob?new Gs(function(e){n.toBlob(function(n){e(n)},t,r)}):Js(n.toDataURL(t,r))},canvasToDataURL:function(n,e,t){return e=e||"image/png",n.then(function(n){return n.toDataURL(e,t)})},blobToCanvas:function(n){return Ys(n).then(function(n){var e,t;return e=n,URL.revokeObjectURL(e.src),t=$s.create(Ws.getWidth(n),Ws.getHeight(n)),$s.get2dContext(t).drawImage(n,0,0),t})},uriToBlob:function(n){return 0===n.indexOf("blob:")?Ks(n):0===n.indexOf("data:")?Js(n):null}},sf=function(n){return cf.blobToBase64(n)},ff=function(u){var e=js({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:lr([hr(X()),mr(K(),function(n,e){var t,r,o;(t=e,r=t.event(),o=r.raw().target.files||r.raw().dataTransfer.files,F.from(o[0])).each(function(n){var o,i;o=u,sf(i=n).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Ja("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})})})])});return Uc.sketch({dom:Wc('<span class="${prefix}-toolbar-button ${prefix}-icon-image ${prefix}-icon"></span>'),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})},lf=function(n){return n.dom().textContent},df=function(n){return 0<n.length},mf=function(n){return n===undefined||null===n?"":n},gf=function(e,t,n){return n.text.filter(df).fold(function(){return Yr(n=e,"href")===lf(n)?F.some(t):F.none();var n},F.some)},vf=function(n){var e=se.fromDom(n.selection.getStart());return ji(e,"a")},pf={getInfo:function(n){return vf(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:F.none()}},function(n){return t=lf(e=n),r=Yr(e,"href"),o=Yr(e,"title"),i=Yr(e,"target"),{url:mf(r),text:t!==r?mf(t):"",title:mf(o),target:mf(i),link:F.some(e)};var e,t,r,o,i})},applyInfo:function(o,i){i.url.filter(df).fold(function(){var e;e=o,i.link.bind(h).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.filter(df).each(function(n){t.title=n}),n.target.filter(df).each(function(n){t.target=n}),t);i.link.bind(h).fold(function(){var n=i.text.filter(df).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=gf(t,e,i);qr(t,r),n.each(function(n){var e;e=n,t.dom().textContent=e})})})},query:vf},hf=$n.detect(),bf=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},yf=function(n,e){(hf.os.isAndroid()?bf:s)(e,n)},wf=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=lr(e),Pr({fields:[Zt("enabled")],name:t,active:{events:I(r)}})),configAsRaw:I({}),initialConfig:{},state:zr}}},xf=Object.freeze({getCurrent:function(n,e,t){return e.find()(n)}}),Sf=[Zt("find")],Tf=Pr({fields:Sf,name:"composing",apis:xf}),Of=jc({name:"Container",factory:function(n){return{uid:n.uid(),dom:k({tag:"div",attributes:{role:"presentation"}},n.dom()),components:n.components(),behaviours:Ua(n.containerBehaviours()),events:n.events(),domModification:n.domModification(),eventOrder:n.eventOrder()}},configFields:[ur("components",[]),La("containerBehaviours",[]),ur("events",{}),ur("domModification",{}),ur("eventOrder",{})]}),kf=jc({name:"DataField",factory:function(t){return{uid:t.uid(),dom:t.dom(),behaviours:k(Lr([xs.config({store:{mode:"memory",initialValue:t.getInitialValue()()}}),Tf.config({find:F.some})]),Ua(t.dataBehaviours())),events:lr([br(function(n,e){xs.setValue(n,t.getInitialValue()())})])}},configFields:[Zt("uid"),Zt("dom"),Zt("getInitialValue"),La("dataBehaviours",[xs,Tf])]}),Cf=function(n){return n.dom().value},Ef=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e},Df=I([rr("data"),ur("inputAttributes",{}),ur("inputStyles",{}),ur("type","input"),ur("tag","input"),ur("inputClasses",[]),$o("onSetValue"),ur("styles",{}),rr("placeholder"),ur("eventOrder",{}),La("inputBehaviours",[xs,bi]),ur("selectOnFocus",!0)]),Af=function(n){return k(Lr([xs.config({store:{mode:"manual",initialValue:n.data().getOr(undefined),getValue:function(n){return Cf(n.element())},setValue:function(n,e){Cf(n.element())!==e&&Ef(n.element(),e)}},onSetValue:n.onSetValue()})]),(e=n,Lr([bi.config({onFocus:!1===e.selectOnFocus()?A:function(n){var e=n.element(),t=Cf(e);e.dom().setSelectionRange(0,t.length)}})])),Ua(n.inputBehaviours()));var e},If=jc({name:"Input",configFields:Df(),factory:function(n,e){return{uid:n.uid(),dom:(t=n,{tag:t.tag(),attributes:k(xt([{key:"type",value:t.type()}].concat(t.placeholder().map(function(n){return{key:"placeholder",value:n}}).toArray())),t.inputAttributes()),styles:t.inputStyles(),classes:t.inputClasses()}),components:[],behaviours:Af(n),eventOrder:n.eventOrder()};var t}}),Mf=Object.freeze({exhibit:function(n,e){return Cr({attributes:xt([{key:e.tabAttr(),value:"true"}])})}}),Bf=[ur("tabAttr","data-alloy-tabstop")],Rf=Pr({fields:Bf,name:"tabstopping",active:Mf}),Ff=function(n,e){var t=js(If.sketch({placeholder:e,onSetValue:function(n,e){re(n,Y())},inputBehaviours:Lr([Tf.config({find:F.some}),Rf.config({}),ja.config({mode:"execution"})]),selectOnFocus:!1})),r=js(Uc.sketch({dom:Wc('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);xs.setValue(e,"")}}));return{name:n,spec:Of.sketch({dom:Wc('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("input-container-empty")}),Tf.config({find:function(n){return F.some(t.get(n))}}),wf("input-clearing",[mr(Y(),function(n){var e=t.get(n);(0<xs.getValue(e).length?ci.off:ci.on)(n)})])])})}},Nf=["input","button","textarea"],Vf=function(n,e,t){e.disabled()&&Pf(n,e,t)},Hf=function(n){return hn(Nf,me(n.element()))},zf=function(n){_r(n.element(),"disabled","disabled")},jf=function(n){Xr(n.element(),"disabled")},Lf=function(n){_r(n.element(),"aria-disabled","true")},Uf=function(n){_r(n.element(),"aria-disabled","false")},Pf=function(e,n,t){n.disableClass().each(function(n){to(e.element(),n)}),(Hf(e)?zf:Lf)(e)},$f=function(n){return Hf(n)?Kr(n.element(),"disabled"):"true"===Yr(n.element(),"aria-disabled")},Wf=Object.freeze({enable:function(e,n,t){n.disableClass().each(function(n){ro(e.element(),n)}),(Hf(e)?jf:Uf)(e)},disable:Pf,isDisabled:$f,onLoad:Vf}),Gf=Object.freeze({exhibit:function(n,e,t){return Cr({classes:e.disabled()?e.disableClass().map(Bn).getOr([]):[]})},events:function(n,e){return lr([dr(Yn(),function(n,e){return $f(n)}),Dr(n,e,Vf)])}}),_f=[ur("disabled",!1),rr("disableClass")],qf=Pr({fields:_f,name:"disabling",active:Gf,apis:Wf}),Yf=[La("formBehaviours",[xs])],Kf=function(n){return"<alloy.field."+n+">"},Xf=function(o,n,e){return k({"debug.sketcher":{Form:e},uid:o.uid(),dom:o.dom(),components:n,behaviours:k(Lr([xs.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),H(e.partUids(),function(n,e){return I(t.getByUid(n))}));return H(r,function(n,e){return n().bind(Tf.getCurrent).map(xs.getValue)})},setValue:function(t,n){V(n,function(e,n){yc(t,o,n).each(function(n){Tf.getCurrent(n).each(function(n){xs.setValue(n,e)})})})}}})]),Ua(o.formBehaviours())),apis:{getField:function(n,e){return yc(n,o,e).bind(Tf.getCurrent)}}})},Jf=(Cc(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Kf(n),o=e,{uiType:Ka(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=bn(r,function(n){return fc({name:n,pname:Kf(n)})});return Nc("form",Yf,o,Xf,t)}),Qf=function(){var e=fo(F.none()),t=function(){e.get().each(function(n){n.destroy()})};return{clear:function(){t(),e.set(F.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(F.some(n))},run:function(n){e.get().each(n)}}},Zf=function(){var e=fo(F.none());return{clear:function(){e.set(F.none())},set:function(n){e.set(F.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}},nl=function(n){return{xValue:n,points:[]}},el=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},tl=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},rl=function(n){var r="navigateEvent",e=Vt([Zt("fields"),ur("maxFieldIndex",n.fields.length-1),Zt("onExecute"),Zt("getInitialValue"),cr("state",function(){return{dialogSwipeState:Zf(),currentScreen:fo(0)}})]),u=qt("SerialisedDialog",e,n),o=function(e,n,t){return Uc.sketch({dom:Wc('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){oe(n,r,{direction:e})},buttonBehaviours:Lr([qf.config({disableClass:mi.resolve("toolbar-navigation-disabled"),disabled:!t})])})},i=function(n,o){var i=Ni(n.element(),"."+mi.resolve("serialised-dialog-screen"));zi(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(ki(r,"left").each(function(n){var e=parseInt(n,10),t=Cs(i[0]);xi(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},a=function(r){var n=Ni(r.element(),"input");F.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element(),e.getSystem().triggerFocus(t,e.element())})});var e=s.get(r);au.highlightAt(e,u.state.currentScreen.get())},c=js(Jf(function(t){return{dom:Wc('<div class="${prefix}-serialised-dialog"></div>'),components:[Of.sketch({dom:Wc('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:bn(u.fields,function(n,e){return e<=u.maxFieldIndex?Of.sketch({dom:Wc('<div class="${prefix}-serialised-dialog-screen"></div>'),components:En([[o(-1,"previous",0<e)],[t.field(n.name,n.spec)],[o(1,"next",e<u.maxFieldIndex)]])}):t.field(n.name,n.spec)})})],formBehaviours:Lr([fi(function(n,e){var t;t=e,zi(n.element(),"."+mi.resolve("serialised-dialog-chain")).each(function(n){xi(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),ja.config({mode:"special",focusIn:function(n){a(n)},onTab:function(n){return i(n,1),F.some(!0)},onShiftTab:function(n){return i(n,-1),F.some(!0)}}),wf("form-events",[br(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=s.get(e);au.highlightFirst(t),u.getInitialValue(e).each(function(n){xs.setValue(e,n)})}),xr(u.onExecute),mr(J(),function(n,e){"left"===e.event().raw().propertyName&&a(n)}),mr(r,function(n,e){var t=e.event().direction();i(n,t)})])])}})),s=js({dom:Wc('<div class="${prefix}-dot-container"></div>'),behaviours:Lr([au.config({highlightClass:mi.resolve("dot-active"),itemClass:mi.resolve("dot-item")})]),components:Dn(u.fields,function(n,e){return e<=u.maxFieldIndex?[Gc('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Wc('<div class="${prefix}-serializer-wrapper"></div>'),components:[c.asSpec(),s.asSpec()],behaviours:Lr([ja.config({mode:"special",focusIn:function(n){var e=c.get(n);ja.focusIn(e)}}),wf("serializer-wrapper-events",[mr(L(),function(n,e){var t=e.event();u.state.dialogSwipeState.set(nl(t.touches[0].clientX))}),mr(U(),function(n,e){var t=e.event();u.state.dialogSwipeState.on(function(n){e.event().prevent(),u.state.dialogSwipeState.set(el(n,t.raw().touches[0].clientX))})}),mr(P(),function(r){u.state.dialogSwipeState.on(function(n){var e=c.get(r),t=-1*tl(n);i(e,t)})})])])}},ol=Z(function(t,r){return[{label:"the link group",items:[rl({fields:[Ff("url","Type or paste URL"),Ff("text","Link text"),Ff("title","Link title"),Ff("target","Link target"),(n="link",{name:n,spec:kf.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return F.none()}})})],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return F.some(pf.getInfo(r))},onExecute:function(n){var e=xs.getValue(n);pf.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}];var n}),il=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],ul=lr([(Zs=Gn(),nf=function(n,e){var t,r,o=e.event().originator(),i=e.event().target();return r=i,!(Ve(t=o,n.element())&&!Ve(t,r)&&(console.warn(Gn()+" did not get interpreted by the desired target. \nOriginator: "+Do(o)+"\nTarget: "+Do(i)+"\nCheck the "+Gn()+" event handlers"),1))},{key:Zs,value:sr({can:nf})})]),al=Object.freeze({events:ul}),cl=h,sl=Hr(["debugInfo","triggerFocus","triggerEvent","triggerEscape","addToWorld","removeFromWorld","addToGui","removeFromGui","build","getByUid","getByDom","broadcast","broadcastOn","isConnected"]),fl=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Do(e().element())+" is not in context.")}};return sl({debugInfo:I("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),isConnected:I(!1)})},ll=function(n,o){var i={};return V(n,function(n,r){V(n,function(n,e){var t=bt(e,[])(i);i[e]=t.concat([o(r,n)])})}),i},dl=function(n,e){return 1<n.length?nt.error('Multiple behaviours have tried to change DOM "'+e+'". The guilty behaviours are: '+Et(bn(n,function(n){return n.name()}))+". At this stage, this is not supported. Future releases might provide strategies for resolving this."):0===n.length?nt.value({}):nt.value(n[0].modification().fold(function(){return{}},function(n){return wt(e,n)}))},ml=function(u,a){return Sn(u,function(n,e){var t=e.modification().getOr({});return n.bind(function(i){var n=j(t,function(n,e){return i[e]!==undefined?(t=a,r=e,o=u,nt.error("Mulitple behaviours have tried to change the _"+r+'_ "'+t+'". The guilty behaviours are: '+Et(Dn(o,function(n){return n.modification().getOr({})[r]!==undefined?[n.name()]:[]}),null,2)+". This is not currently supported.")):nt.value(wt(e,n));var t,r,o});return St(n,i)})},nt.value({})).map(function(n){return wt(a,n)})},gl={classes:function(n,e){var t=Dn(n,function(n){return n.modification().getOr([])});return nt.value(wt(e,t))},attributes:ml,styles:ml,domChildren:dl,defChildren:dl,innerHtml:dl,value:dl},vl=function(n,e){return t=l.apply(undefined,[n.handler].concat(e)),r=n.purpose(),{cHandler:t,purpose:I(r)};var t,r},pl=function(n){return n.cHandler},hl=function(n,e){return{name:I(n),handler:I(e)}},bl=function(n,e,t){var r,o,i=k(t,(r=n,o={},yn(e,function(n){o[n.name()]=n.handlers(r)}),o));return ll(i,hl)},yl=function(n){var e,i=w(e=n)?{can:I(!0),abort:I(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},wl=function(n,e,t){var r,o,i=e[t];return i?function(u,a,n,c){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[a](),r=e[a](),o=c.indexOf(t),i=c.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+Et(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+Et(c,null,2));return o<i?-1:i<o?1:0});return nt.value(t)}catch(r){return nt.error([r])}}("Event: "+t,"name",n,i).map(function(n){var e=bn(n,function(n){return n.handler()});return fr(e)}):(r=t,o=n,nt.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+Et(bn(o,function(n){return n.name()}),null,2)]))},xl=function(n,i){var e=j(n,function(r,o){return(1===r.length?nt.value(r[0].handler()):wl(r,i,o)).map(function(n){var e=yl(n),t=1<r.length?wn(i,function(e){return hn(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return wt(o,{handler:e,purpose:I(t)})})});return St(e,{})},Sl=function(n){return Gt("custom.definition",Nt([Lt("dom","dom",ot(),Nt([Zt("tag"),ur("styles",{}),ur("classes",[]),ur("attributes",{}),rr("value"),rr("innerHtml")])),Zt("components"),Zt("uid"),ur("events",{}),ur("apis",I({})),Lt("eventOrder","eventOrder",(e={"alloy.execute":["disabling","alloy.base.behaviour","toggling"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing"]},tt.mergeWithThunk(I(e))),Jt()),rr("domModification"),Yo("originalSpec"),ur("debug.sketcher","unknown")]),n);var e},Tl=function(n){var e,t={tag:n.dom().tag(),classes:n.dom().classes(),attributes:k((e=n,wt(Ac(),e.uid())),n.dom().attributes()),styles:n.dom().styles(),domChildren:bn(n.components(),function(n){return n.element()})};return Or(k(t,n.dom().innerHtml().map(function(n){return wt("innerHtml",n)}).getOr({}),n.dom().value().map(function(n){return wt("value",n)}).getOr({})))},Ol=function(e,n){yn(n,function(n){to(e,n)})},kl=function(e,n){yn(n,function(n){ro(e,n)})},Cl=function(e){if(e.domChildren().isSome()&&e.defChildren().isSome())throw new Error("Cannot specify children and child specs! Must be one or the other.\nDef: "+(n=kr(e),Et(n,null,2)));return e.domChildren().fold(function(){var n=e.defChildren().getOr([]);return bn(n,Dl)},function(n){return n});var n},El=function(n){var e=se.fromTag(n.tag());qr(e,n.attributes().getOr({})),Ol(e,n.classes().getOr([])),Si(e,n.styles().getOr({})),ko(e,n.innerHtml().getOr(""));var t=Cl(n);return $e(e,t),n.value().each(function(n){Ef(e,n)}),e},Dl=function(n){var e=Or(n);return El(e)},Al=function(n,e){return t=n,o=bn(r=e,function(n){return or(n.name(),[Zt("config"),ur("state",zr)])}),i=Gt("component.behaviours",Vt(o),t.behaviours).fold(function(n){throw new Error(Kt(n)+"\nComplete spec:\n"+Et(t,null,2))},function(n){return n}),{list:r,data:H(i,function(n){var e=n().map(function(n){return{config:n.config(),state:n.state().init(n.config())}});return function(){return e}})};var t,r,o,i},Il=function(n){var e,t,r=(e=yt(n,"behaviours").getOr({}),t=wn(N(e),function(n){return e[n]!==undefined}),bn(t,function(n){return e[n].me}));return Al(n,r)},Ml=Hr(["getSystem","config","hasConfigured","spec","connect","disconnect","element","syncComponents","readState","components","events"]),Bl=function(n,e,t){var r,o,i,u,a=Tl(n),c=function(e,n,t,r){var o=k({},n);yn(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=ll(o,function(n,e){return{name:function(){return n},modification:e}}),u=H(i,function(n,e){return Dn(n,function(e){return e.modification().fold(function(){return[]},function(n){return[e]})})}),a=j(u,function(e,t){return yt(gl,t).fold(function(){return nt.error("Unknown field type: "+t)},function(n){return n(e,t)})});return St(a,{}).map(Cr)}(t,{"alloy.base.modification":(r=n,r.domModification().fold(function(){return Cr({})},Cr))},e,a).getOrDie();return i=c,u=k({tag:(o=a).tag(),classes:i.classes().getOr([]).concat(o.classes().getOr([])),attributes:C(o.attributes().getOr({}),i.attributes().getOr({})),styles:C(o.styles().getOr({}),i.styles().getOr({}))},i.innerHtml().or(o.innerHtml()).map(function(n){return wt("innerHtml",n)}).getOr({}),Er("domChildren",i.domChildren(),o.domChildren()),Er("defChildren",i.defChildren(),o.defChildren()),i.value().or(o.value()).map(function(n){return wt("value",n)}).getOr({})),Or(u)},Rl=function(n,e,t){var r,o,i,u,a,c,s={"alloy.base.behaviour":(r=n,r.events())};return(o=t,i=n.eventOrder(),u=e,a=s,c=bl(o,u,a),xl(c,i)).getOrDie()},Fl=function(n){var e,t,r,o,i,u,a,c,s,f,l,d,m,g,v=cl(n),p=(e=v,t=bt("components",[])(e),bn(t,Hl)),h=k(al,v,wt("components",p));return nt.value((r=h,i=fo(fl(o=function(){return g})),u=_t(Sl(k(r,{behaviours:undefined}))),a=Il(r),c=a.list,s=a.data,f=Bl(u,c,s),l=El(f),d=Rl(u,c,s),m=fo(u.components()),g=Ml({getSystem:i.get,config:function(n){if(n===Ec())return u.apis();if(b(n))throw new Error("Invalid input: only API constant is allowed");var e=s;return(w(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+Et(r,null,2))})()},hasConfigured:function(n){return w(s[n.name()])},spec:I(r),readState:function(n){return s[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},connect:function(n){i.set(n)},disconnect:function(){i.set(fl(o))},element:I(l),syncComponents:function(){var n=je(l),e=Dn(n,function(n){return i.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});m.set(e)},components:m.get,events:I(d)})))},Nl=function(n){var e=se.fromText(n);return Vl({element:e})},Vl=function(n){var t=Yt("external.component",Nt([Zt("element"),rr("uid")]),n),e=fo(fl());t.uid().each(function(n){var e;e=t.element(),_r(e,Mc,n)});var r=Ml({getSystem:e.get,config:F.none,hasConfigured:I(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(fl(function(){return r}))},element:I(t.element()),spec:I(n),readState:I("No state"),syncComponents:A,components:I([]),events:I({})});return kc(r)},Hl=function(e){return(n=e,yt(n,Tc)).fold(function(){var n=k({uid:Rc("")},e);return Fl(n).getOrDie()},function(n){return n});var n},zl=kc,jl="alloy.item-hover",Ll="alloy.item-focus",Ul=function(n){(yo(n.element()).isNone()||bi.isFocused(n))&&(bi.isFocused(n)||bi.focus(n),oe(n,jl,{item:n}))},Pl=function(n){oe(n,Ll,{item:n})},$l=I(jl),Wl=I(Ll),Gl=[Zt("data"),Zt("components"),Zt("dom"),rr("toggling"),ur("itemBehaviours",{}),ur("ignoreFocus",!1),ur("domModification",{}),qo("builder",function(n){return{dom:k(n.dom(),{attributes:{role:n.toggling().isSome()?"menuitemcheckbox":"menuitem"}}),behaviours:k(Lr([n.toggling().fold(ci.revoke,function(n){return ci.config(k({aria:{mode:"checked"}},n))}),bi.config({ignore:n.ignoreFocus(),onFocus:function(n){Pl(n)}}),ja.config({mode:"execution"}),xs.config({store:{mode:"memory",initialValue:n.data()}})]),n.itemBehaviours()),events:lr([(e=Jn(),r=ie,mr(e,function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){r(e,n,t)})})),hr($()),mr(_(),Ul),mr(Kn(),bi.focus)]),components:n.components(),domModification:n.domModification(),eventOrder:n.eventOrder()};var e,r}),ur("eventOrder",{})],_l=[Zt("dom"),Zt("components"),qo("builder",function(n){return{dom:n.dom(),components:n.components(),events:lr([(e=Kn(),mr(e,function(n,e){e.stop()}))])};var e})],ql=I([fc({name:"widget",overrides:function(e){return{behaviours:Lr([xs.config({store:{mode:"manual",getValue:function(n){return e.data()},setValue:function(){}}})])}}})]),Yl=[Zt("uid"),Zt("data"),Zt("components"),Zt("dom"),ur("autofocus",!1),ur("domModification",{}),Sc(ql()),qo("builder",function(t){var n=hc(0,t,ql()),e=bc("item-widget",t,n.internals()),r=function(n){return yc(n,t,"widget").map(function(n){return ja.focusIn(n),n})},o=function(n,e){return mu(e.event().target())||t.autofocus()&&e.setSource(n.element()),F.none()};return k({dom:t.dom(),components:e,domModification:t.domModification(),events:lr([xr(function(n,e){r(n).each(function(n){e.stop()})}),mr(_(),Ul),mr(Kn(),function(n,e){t.autofocus()?r(n):bi.focus(n)})]),behaviours:Lr([xs.config({store:{mode:"memory",initialValue:t.data()}}),bi.config({onFocus:function(n){Pl(n)}}),ja.config({mode:"special",focusIn:t.autofocus()?function(n){r(n)}:Wr(),onLeft:o,onRight:o,onEscape:function(n,e){return bi.isFocused(n)||t.autofocus()?(t.autofocus()&&e.setSource(n.element()),F.none()):(bi.focus(n),F.some(!0))}})])})})],Kl=Xt("type",{widget:Yl,item:Gl,separator:_l}),Xl=I([dc({factory:{sketch:function(n){var e=Yt("menu.spec item",Kl,n);return e.builder()(e)}},name:"items",unit:"item",defaults:function(n,e){var t=Rc("");return k({uid:t},e)},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus(),domModification:{classes:[n.markers().item()]}}}})]),Jl=I([Zt("value"),Zt("items"),Zt("dom"),Zt("components"),ur("eventOrder",{}),La("menuBehaviours",[au,xs,Tf,ja]),ar("movement",{mode:"menu",moveOnTab:!0},Xt("mode",{grid:[Ko(),qo("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers().item(),initSize:{numColumns:e.initSize().numColumns(),numRows:e.initSize().numRows()},focusManager:n.focusManager()}})],menu:[ur("moveOnTab",!0),qo("config",function(n,e){return{mode:"menu",selector:"."+n.markers().item(),moveOnTab:e.moveOnTab(),focusManager:n.focusManager()}})]})),nr("markers",Lo()),ur("fakeFocus",!1),ur("focusManager",cu()),$o("onHighlight")]),Ql=I("alloy.menu-focus"),Zl=Lc({name:"Menu",configFields:Jl(),partFields:Xl(),factory:function(n,e,t,r){return k({dom:k(n.dom(),{attributes:{role:"menu"}}),uid:n.uid(),behaviours:k(Lr([au.config({highlightClass:n.markers().selectedItem(),itemClass:n.markers().item(),onHighlight:n.onHighlight()}),xs.config({store:{mode:"memory",initialValue:n.value()}}),Tf.config({find:F.some}),ja.config(n.movement().config()(n,n.movement()))]),Ua(n.menuBehaviours())),events:lr([mr(Wl(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){au.highlight(e,n),t.stop(),oe(e,Ql(),{menu:e,item:n})})}),mr($l(),function(n,e){var t=e.event().item();au.highlight(n,t)})]),components:e,eventOrder:n.eventOrder()})}}),nd=function(n,e,t,r){var o=n.getSystem().build(r);Ke(n,o,t)},ed=function(n,e){return n.components()},td=Pr({fields:[],name:"replacing",apis:Object.freeze({append:function(n,e,t,r){nd(n,0,Pe,r)},prepend:function(n,e,t,r){nd(n,0,Ue,r)},remove:function(n,e,t,r){var o=ed(n,e);Tn(o,function(n){return Ve(r.element(),n.element())}).each(Je)},set:function(e,n,t,r){var o,i,u,a,c,s;i=(o=e).components(),yn(i,Xe),We(o.element()),o.syncComponents(),u=function(){var n=bn(r,e.getSystem().build);yn(n,function(n){Ye(e,n)})},a=e.element(),c=He(a),s=bo(c).bind(function(e){var n=function(n){return Ve(e,n)};return n(a)?F.some(a):vo(a,n)}),u(a),s.each(function(e){bo(c).filter(function(n){return Ve(n,e)}).fold(function(){po(e)},A)})},contents:ed})}),rd=function(t,r,o,n){return yt(o,n).bind(function(n){return yt(t,n).bind(function(n){var e=rd(t,r,o,n);return F.some([n].concat(e))})}).getOr([])},od=function(n,e){var t={};V(n,function(n,e){yn(n,function(n){t[n]=e})});var r=e,o=z(e,function(n,e){return{k:n,v:e}}),i=H(o,function(n,e){return[e].concat(rd(t,r,o,e))});return H(t,function(n){return yt(i,n).getOr([n])})},id=function(){var i=fo({}),u=fo({}),a=fo({}),c=fo(F.none()),s=fo({}),n=function(n){return yt(u.get(),n)};return{setContents:function(n,e,t,r){c.set(F.some(n)),i.set(t),u.set(e),s.set(r);var o=od(r,t);a.set(o)},expand:function(t){return yt(i.get(),t).map(function(n){var e=yt(a.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return yt(a.get(),n)},collapse:function(n){return yt(a.get(),n).bind(function(n){return 1<n.length?F.some(n.slice(1)):F.none()})},lookupMenu:n,otherMenus:function(n){var e,t,r=s.get();return e=N(r),t=n,wn(e,function(n){return!hn(t,n)})},getPrimary:function(){return c.get().bind(n)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(F.none())},isClear:function(){return c.get().isNone()}}},ud=I("collapse-item"),ad=jc({name:"TieredMenu",configFields:[_o("onExecute"),_o("onEscape"),Go("onOpenMenu"),Go("onOpenSubmenu"),$o("onCollapseMenu"),ur("openImmediately",!0),tr("data",[Zt("primary"),Zt("menus"),Zt("expansions")]),ur("fakeFocus",!1),$o("onHighlight"),$o("onHover"),tr("markers",[Zt("backgroundMenu")].concat(zo()).concat(jo())),Zt("dom"),ur("navigateOnHover",!0),ur("stayInDom",!1),La("tmenuBehaviours",[ja,au,Tf,td]),ur("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)}},factory:function(u,o){var i=function(r,n){return H(n,function(n,e){var t=Zl.sketch(k(n,{value:e,items:n.items,markers:vt(o.markers,["item","selectedItem"]),fakeFocus:u.fakeFocus(),onHighlight:u.onHighlight(),focusManager:u.fakeFocus()?{get:function(n){return au.getHighlighted(n).map(function(n){return n.element()})},set:function(e,n){e.getSystem().getByDom(n).fold(A,function(n){au.highlight(e,n)})}}:cu()}));return r.getSystem().build(t)})},a=id(),c=function(n){return xs.getValue(n).value},s=function(n){return H(u.data().menus(),function(n,e){return Dn(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},f=function(e,n){au.highlight(e,n),au.getHighlighted(n).orThunk(function(){return au.getFirst(n)}).each(function(n){ue(e,n.element(),Kn())})},l=function(n,e){return Io(bn(e,n.lookupMenu))},d=function(r,o,i){return F.from(i[0]).bind(o.lookupMenu).map(function(n){var e=l(o,i.slice(1));yn(e,function(n){to(n.element(),u.markers().backgroundMenu())}),he(n.element())||td.append(r,zl(n)),kl(n.element(),[u.markers().backgroundMenu()]),f(r,n);var t=l(o,o.otherMenus(i));return yn(t,function(n){kl(n.element(),[u.markers().backgroundMenu()]),u.stayInDom()||td.remove(r,n)}),n})},m=function(e,t){var n=c(t);return a.expand(n).bind(function(n){return F.from(n[0]).bind(a.lookupMenu).each(function(n){he(n.element())||td.append(e,zl(n)),u.onOpenSubmenu()(e,t,n),au.highlightFirst(n)}),d(e,a,n)})},r=function(e,t){var n=c(t);return a.collapse(n).bind(function(n){return d(e,a,n).map(function(n){return u.onCollapseMenu()(e,t,n),n})})},n=function(t){return function(e,n){return ji(n.getSource(),"."+u.markers().item()).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}},e=lr([mr(Ql(),function(n,e){var t=e.event().menu();au.highlight(n,t)}),xr(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===c(n).indexOf("collapse-item")&&r(e,n),m(e,n).fold(function(){u.onExecute()(e,n)},function(){})})}),br(function(e,n){var t,r,o;(t=e,r=i(t,u.data().menus()),o=s(t),a.setContents(u.data().primary(),r,u.data().expansions(),o),a.getPrimary()).each(function(n){td.append(e,zl(n)),u.openImmediately()&&(f(e,n),u.onOpenMenu()(e,n))})})].concat(u.navigateOnHover()?[mr($l(),function(n,e){var t,r,o=e.event().item();t=n,r=c(o),a.refresh(r).bind(function(n){return d(t,a,n)}),m(n,o),u.onHover()(n,o)})]:[]));return{uid:u.uid(),dom:u.dom(),behaviours:k(Lr([ja.config({mode:"special",onRight:n(function(n,e){return mu(e.element())?F.none():m(n,e)}),onLeft:n(function(n,e){return mu(e.element())?F.none():r(n,e)}),onEscape:n(function(n,e){return r(n,e).orThunk(function(){return u.onEscape()(n,e).map(function(){return n})})}),focusIn:function(e,n){a.getPrimary().each(function(n){ue(e,n.element(),Kn())})}}),au.config({highlightClass:u.markers().selectedMenu(),itemClass:u.markers().menu()}),Tf.config({find:function(n){return au.getHighlighted(n)}}),td.config({})]),Ua(u.tmenuBehaviours())),eventOrder:u.eventOrder(),apis:{collapseMenu:function(e){au.getHighlighted(e).each(function(n){au.getHighlighted(n).each(function(n){r(e,n)})})}},events:e}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:wt(n,e),expansions:{}}},collapseItem:function(n){return{value:Ja(ud()),text:n}}}}),cd=function(n,e,t,r){return yt(e.routes(),r.start()).map(s).bind(function(n){return yt(n,r.destination()).map(s)})},sd=function(n,e,t,r){return cd(0,e,0,r).bind(function(e){return e.transition().map(function(n){return{transition:I(n),route:I(e)}})})},fd=function(t,r,n){var e,o,i;(e=t,o=r,i=n,ld(e,o,i).bind(function(n){return sd(e,o,i,n)})).each(function(n){var e=n.transition();ro(t.element(),e.transitionClass()),Xr(t.element(),r.destinationAttr())})},ld=function(n,e,t){var r=n.element();return Kr(r,e.destinationAttr())?F.some({start:I(Yr(n.element(),e.stateAttr())),destination:I(Yr(n.element(),e.destinationAttr()))}):F.none()},dd=function(n,e,t,r){fd(n,e,t),Kr(n.element(),e.stateAttr())&&Yr(n.element(),e.stateAttr())!==r&&e.onFinish()(n,r),_r(n.element(),e.stateAttr(),r)},md=Object.freeze({findRoute:cd,disableTransition:fd,getCurrentRoute:ld,jumpTo:dd,progressTo:function(t,r,o,i){var n,e;e=r,Kr((n=t).element(),e.destinationAttr())&&(_r(n.element(),e.stateAttr(),Yr(n.element(),e.destinationAttr())),Xr(n.element(),e.destinationAttr()));var u,a,c=(u=r,a=i,{start:I(Yr(t.element(),u.stateAttr())),destination:I(a)});sd(t,r,o,c).fold(function(){dd(t,r,o,i)},function(n){fd(t,r,o);var e=n.transition();to(t.element(),e.transitionClass()),_r(t.element(),r.destinationAttr(),i)})},getState:function(n,e,t){var r=n.element();return Kr(r,e.stateAttr())?F.some(Yr(r,e.stateAttr())):F.none()}}),gd=Object.freeze({events:function(o,i){return lr([mr(J(),function(t,n){var r=n.event().raw();ld(t,o,i).each(function(e){cd(0,o,0,e).each(function(n){n.transition().each(function(n){r.propertyName===n.property()&&(dd(t,o,i,e.destination()),o.onTransition()(t,e))})})})}),br(function(n,e){dd(n,o,i,o.initialState())})])}}),vd=[ur("destinationAttr","data-transitioning-destination"),ur("stateAttr","data-transitioning-state"),Zt("initialState"),$o("onTransition"),$o("onFinish"),nr("routes",Ht(nt.value,Ht(nt.value,Nt([ir("transition",[Zt("property"),Zt("transitionClass")])]))))],pd=Pr({fields:vd,name:"transitioning",active:gd,apis:md,extra:{createRoutes:function(n){var r={};return V(n,function(n,e){var t=e.split("<->");r[t[0]]=wt(t[1],n),r[t[1]]=wt(t[0],n)}),r},createBistate:function(n,e,t){return xt([{key:n,value:wt(e,t)},{key:e,value:wt(n,t)}])},createTristate:function(n,e,t,r){return xt([{key:n,value:xt([{key:e,value:r},{key:t,value:r}])},{key:e,value:xt([{key:n,value:r},{key:t,value:r}])},{key:t,value:xt([{key:n,value:r},{key:e,value:r}])}])}}}),hd=mi.resolve("scrollable"),bd={register:function(n){to(n,hd)},deregister:function(n){ro(n,hd)},scrollable:I(hd)},yd=function(n){return yt(n,"format").getOr(n.title)},wd=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[mi.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:mi.resolve("format-matches"),selected:t},itemBehaviours:Lr(o?[]:[si(n,function(n,e){(e?ci.on:ci.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},xd=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Uc.sketch({dom:{tag:"div",classes:[mi.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[mi.resolve("styles-collapse-icon")]}},Nl(n)]:[Nl(n)],action:function(n){if(r){var e=t().get(n);ad.collapseMenu(e)}}}),{dom:{tag:"div",classes:[mi.resolve("styles-menu-items-container")]},components:[Zl.parts().items({})],behaviours:Lr([wf("adhoc-scrollable-menu",[br(function(n,e){xi(n.element(),"overflow-y","auto"),xi(n.element(),"-webkit-overflow-scrolling","touch"),bd.register(n.element())}),yr(function(n){Ci(n.element(),"overflow-y"),Ci(n.element(),"-webkit-overflow-scrolling"),bd.deregister(n.element())})])])}],items:e,menuBehaviours:Lr([pd.config({initialState:"after",routes:pd.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},Sd=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return a},n=xd("Styles",[].concat(bn(o.items,function(n){return wd(yd(n),n.title,n.isSelected(),n.getPreview(),Tt(o.expansions,yd(n)))})),i,!1),e=H(o.menus,function(n,e){var t=bn(n,function(n){return wd(yd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",Tt(o.expansions,yd(n)))});return xd(e,t,i,!0)}),t=k(e,wt("styles",n)),{tmenu:ad.tieredData("styles",t,o.expansions)}),a=js(ad.sketch({dom:{tag:"div",classes:[mi.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=xs.getValue(e);return r.handle(e,t.value),F.none()},onEscape:function(){return F.none()},onOpenMenu:function(n,e){var t=Cs(n.element());ks(e.element(),t),pd.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=Cs(n.element()),o=Hi(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();ks(t.element(),r),pd.progressTo(i,"before"),pd.jumpTo(t,"after"),pd.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Hi(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();pd.progressTo(o,"after"),pd.progressTo(t,"current")},navigateOnHover:!1,openImmediately:!0,data:u.tmenu,markers:{backgroundMenu:mi.resolve("styles-background-menu"),menu:mi.resolve("styles-menu"),selectedMenu:mi.resolve("styles-selected-menu"),item:mi.resolve("styles-item"),selectedItem:mi.resolve("styles-selected-item")}}));return a.asSpec()},Td=function(n){return Tt(n,"items")?(t=k(pt(e=n,["items"]),{menu:!0}),r=Od(e.items),{item:t,menus:k(r.menus,wt(e.title,r.items)),expansions:k(r.expansions,wt(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},Od=function(n){return xn(n,function(n,e){var t=Td(e);return{menus:k(n.menus,t.menus),items:[t.item].concat(n.items),expansions:k(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},kd={expand:Od},Cd=function(u,n){var a=function(n){return function(){return u.formatter.match(n)}},c=function(n){return function(){return u.formatter.getCssText(n)}},e=yt(n,"style_formats").getOr(il),s=function(n){return bn(n,function(n){if(Tt(n,"items")){var e=s(n.items);return k(k(n,{isSelected:I(!1),getPreview:I("")}),{items:e})}return Tt(n,"format")?k(i=n,{isSelected:a(i.format),getPreview:c(i.format)}):(r=Ja((t=n).title),o=k(t,{format:r,isSelected:a(r),getPreview:c(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return s(e)},Ed=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return Dn(n,function(n){return n.items!==undefined?0<o(n.items).length?[n]:[]:!Tt(n,"format")||e.formatter.canApply(n.format)?[n]:[]})})(n),kd.expand(i));return Sd({formats:u,handle:function(n,e){t.undoManager.transact(function(){ci.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},Dd=["undo","bold","italic","link","image","bullist","styleselect"],Ad=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},Id=function(n){return Dn(n,function(n){return p(n)?Id(n):Ad(n)})},Md=function(n){var e=n.toolbar!==undefined?n.toolbar:Dd;return p(e)?Id(e):Ad(e)},Bd=function(r,o){var n=function(n){return function(){return Yc.forToolbarCommand(o,n)}},e=function(n){return function(){return Yc.forToolbarStateCommand(o,n)}},t=function(n,e,t){return function(){return Yc.forToolbarStateAction(o,n,e,t)}},i=n("undo"),u=n("redo"),a=e("bold"),c=e("italic"),s=e("underline"),f=n("removeformat"),l=t("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=t("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=t("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=Cd(o,o.settings),v=function(){return Ed(o,g,function(){o.fire("scrollIntoView")})},p=function(n,e){return{isSupported:function(){return n.forall(function(n){return Tt(o.buttons,n)})},sketch:e}};return{undo:p(F.none(),i),redo:p(F.none(),u),bold:p(F.none(),a),italic:p(F.none(),c),underline:p(F.none(),s),removeformat:p(F.none(),f),link:p(F.none(),function(){return e=r,t=o,Yc.forToolbarStateAction(t,"link","link",function(){var n=ol(e,t);e.setContextToolbar(n),yf(t,function(){e.focusToolbar()}),pf.query(t).each(function(n){t.selection.select(n.dom())})});var e,t}),unlink:p(F.none(),l),image:p(F.none(),function(){return ff(o)}),bullist:p(F.some("bullist"),d),numlist:p(F.some("numlist"),m),fontsizeselect:p(F.none(),function(){return e=o,n={onChange:function(n){Vs.apply(e,n)},getInitialValue:function(){return Vs.get(e)}},As(r,"font-size",function(){return zs(n)});var e,n}),forecolor:p(F.none(),function(){return Ms(r,o)}),styleselect:p(F.none(),function(){return Yc.forToolbar("style-formats",function(n){o.fire("toReading"),r.dropup().appear(v,ci.on,n)},Lr([ci.config({toggleClass:mi.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Jo.config({channels:xt([li(To.orientationChanged(),ci.off),li(To.dropupDismissed(),ci.off)])})]))})}},Rd=function(n,t){var e=Md(n),r={};return Dn(e,function(n){var e=!Tt(r,n)&&Tt(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Fd=function(m,g){return function(n){if(m(n)){var e,t,r,o,i,u,a,c=se.fromDom(n.target),s=function(){n.stopPropagation()},f=function(){n.preventDefault()},l=v(f,s),d=(e=c,t=n.clientX,r=n.clientY,o=s,i=f,u=l,a=n,{target:I(e),x:I(t),y:I(r),stop:o,prevent:i,kill:u,raw:I(a)});g(d)}}},Nd=function(n,e,t,r,o){var i=Fd(t,r);return n.dom().addEventListener(e,i,o),{unbind:l(Vd,n,e,i,o)}},Vd=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Hd=I(!0),zd=function(n,e,t){return Nd(n,e,Hd,t,!1)},jd=function(n,e,t){return Nd(n,e,Hd,t,!0)},Ld=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:I(e)}},Ud=Ld,Pd=function(r,e){var n=se.fromDom(r),o=null,t=zd(n,"orientationchange",function(){clearInterval(o);var n=Ld(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){clearInterval(o);var e=r.innerHeight,t=0;o=setInterval(function(){e!==r.innerHeight?(clearInterval(o),n(F.some(r.innerHeight))):20<t&&(clearInterval(o),n(F.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},$d=function(n){var e=$n.detect().os.isiOS(),t=Ld(n).isPortrait();return e&&!t?n.screen.height:n.screen.width},Wd=function(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?F.none():F.some(e.touches[0])},Gd=function(t){var r,o,i,u=fo(F.none()),a=(r=function(n){u.set(F.none()),t.triggerEvent(Qn(),n)},o=400,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i=setTimeout(function(){r.apply(null,n),i=null},o)}}),c=xt([{key:L(),value:function(t){return Wd(t).each(function(n){a.cancel();var e={x:I(n.clientX),y:I(n.clientY),target:t.target};a.schedule(t),u.set(F.some(e))}),F.none()}},{key:U(),value:function(n){return a.cancel(),Wd(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x()),o=Math.abs(e.clientY-t.y()),(5<r||5<o)&&u.set(F.none())})}),F.none()}},{key:P(),value:function(e){return a.cancel(),u.get().filter(function(n){return Ve(n.target(),e.target())}).map(function(n){return t.triggerEvent(Xn(),e)})}}]);return{fireIfReady:function(e,n){return yt(c,n).bind(function(n){return n(e)})}}},_d=function(t){var e=Gd({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return zd(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return zd(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},qd=6<=$n.detect().os.version.major,Yd=function(r,e,t){var o=_d(r),i=He(e),u=function(n){return!Ve(n.start(),n.finish())||n.soffset()!==n.foffset()},n=function(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||bo(i).filter(function(n){return"input"===me(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?ci.on:ci.off)},a=[zd(r.body(),"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),zd(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){ho(r.body())}),r.onToEditing(A),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!==t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0===qd?[]:[zd(se.fromDom(r.win()),"blur",function(){t.getByDom(e).each(ci.off)}),zd(i,"select",n),zd(r.doc(),"selectionchange",n)]);return{destroy:function(){yn(a,function(n){n.unbind()})}}},Kd=function(n,e){var t=parseInt(Yr(n,e),10);return isNaN(t)?0:t},Xd=(ef=pe,tf="text",rf=function(n){return ef(n)?F.from(n.dom().nodeValue):F.none()},of=$n.detect().browser,{get:function(n){if(!ef(n))throw new Error("Can only get "+tf+" value of a "+tf+" node");return uf(n).getOr("")},getOption:uf=of.isIE()&&10===of.version.major?function(n){try{return rf(n)}catch(e){return F.none()}}:rf,set:function(n,e){if(!ef(n))throw new Error("Can only set raw "+tf+" value of a "+tf+" node");n.dom().nodeValue=e}}),Jd=function(n){return Xd.getOption(n)},Qd=et([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Zd={before:Qd.before,on:Qd.on,after:Qd.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(h,h,h)}},nm=et([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),em=we("start","soffset","finish","foffset"),tm=nm.relative,rm=nm.exact,om=function(n,e,t,r){var o,i,u,a,c,s=(i=e,u=t,a=r,(c=He(o=n).dom().createRange()).setStart(o.dom(),i),c.setEnd(u.dom(),a),c),f=Ve(n,t)&&e===r;return s.collapsed&&!f},im=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom())},function(n,e){r.setStart(n.dom(),e)},function(n){r.setStartAfter(n.dom())}),o=i,t.fold(function(n){o.setEndBefore(n.dom())},function(n,e){o.setEnd(n.dom(),e)},function(n){o.setEndAfter(n.dom())}),i},um=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i},am=function(n){return{left:I(n.left),top:I(n.top),right:I(n.right),bottom:I(n.bottom),width:I(n.width),height:I(n.height)}},cm=et([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),sm=function(n,e,t){return e(se.fromDom(t.startContainer),t.startOffset,se.fromDom(t.endContainer),t.endOffset)},fm=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:I(n),rtl:F.none}},relative:function(n,e){return{ltr:Z(function(){return im(o,n,e)}),rtl:Z(function(){return F.some(im(o,e,n))})}},exact:function(n,e,t,r){return{ltr:Z(function(){return um(o,n,e,t,r)}),rtl:Z(function(){return F.some(um(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return cm.rtl(se.fromDom(n.endContainer),n.endOffset,se.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return sm(0,cm.ltr,r)}):sm(0,cm.ltr,r)},lm=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n,e){var t=me(n);return"input"===t?Zd.after(n):hn(["br","img"],t)?0===e?Zd.before(n):Zd.after(n):Zd.on(n,e)}),dm=function(n,e,t,r,o){var i,u,a=um(n,e,t,r,o);i=n,u=a,F.from(i.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(u)})},mm=function(n,e,t,r,o){var i,u,a,c,l,s=(i=r,u=o,a=lm(e,t),c=lm(i,u),tm(a,c));fm(l=n,s).match({ltr:function(n,e,t,r){dm(l,n,e,t,r)},rtl:function(n,e,t,r){var o,i,u,a,c,s=l.getSelection();if(s.setBaseAndExtent)s.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(s.extend)try{i=n,u=e,a=t,c=r,(o=s).collapse(i.dom(),u),o.extend(a.dom(),c)}catch(f){dm(l,t,r,n,e)}else dm(l,t,r,n,e)}})},gm=function(n){var e=se.fromDom(n.anchorNode),t=se.fromDom(n.focusNode);return om(e,n.anchorOffset,t,n.focusOffset)?F.some(em(se.fromDom(n.anchorNode),n.anchorOffset,se.fromDom(n.focusNode),n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return F.some(em(se.fromDom(e.startContainer),e.startOffset,se.fromDom(t.endContainer),t.endOffset))}return F.none()}(n)},vm=function(n){return F.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(gm)},pm=function(n,e){var i,t,r,o,u=fm(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?F.some(o).map(am):F.none()},hm=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:I(2),height:n.height}},bm=function(n){return{left:I(n.left),top:I(n.top),right:I(n.right),bottom:I(n.bottom),width:I(n.width),height:I(n.height)}},ym=function(r){if(r.collapsed){var o=se.fromDom(r.startContainer);return ze(o).bind(function(n){var e,t=rm(o,r.startOffset,n,"img"===me(e=n)?1:Jd(e).fold(function(){return je(e).length},function(n){return n.length}));return pm(r.startContainer.ownerDocument.defaultView,t).map(hm).map(Bn)}).getOr([])}return bn(r.getClientRects(),bm)},wm=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?ym(e.getRangeAt(0)):[]},xm=function(n){n.focus();var e=se.fromDom(n.document.body);(bo().exists(function(n){return hn(["input","textarea"],me(n))})?function(n){setTimeout(function(){n()},0)}:s)(function(){bo().each(ho),po(e)})},Sm="data-"+mi.resolve("last-outer-height"),Tm=function(n,e){_r(n,Sm,e)},Om=function(n){return{top:I(n.top()),bottom:I(n.top()+n.height())}},km=function(n,e){var t=Kd(e,Sm),r=n.innerHeight;return r<t?F.some(t-r):F.none()},Cm=function(n,u){var e=se.fromDom(u.document.body),t=zd(se.fromDom(n),"resize",function(){km(n,e).each(function(i){var n,e;(n=u,e=wm(n),0<e.length?F.some(e[0]).map(Om):F.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top()>e.innerHeight||t.bottom()>e.innerHeight?Math.min(r,t.bottom()-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),Tm(e,n.innerHeight)});return Tm(e,n.innerHeight),{toEditing:function(){xm(u)},destroy:function(){t.unbind()}}},Em=function(n){return F.some(se.fromDom(n.dom().contentWindow.document.body))},Dm=function(n){return F.some(se.fromDom(n.dom().contentWindow.document))},Am=function(n){return F.from(n.dom().contentWindow)},Im=function(n){return Am(n).bind(vm)},Mm=function(n){return n.getFrame()},Bm=function(n,t){return function(e){return e[n].getOrThunk(function(){var n=Mm(e);return function(){return t(n)}})()}},Rm=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return zd(e,r,n)}})},Fm=function(n){return{left:I(n.left),top:I(n.top),right:I(n.right),bottom:I(n.bottom),width:I(n.width),height:I(n.height)}},Nm={getBody:Bm("getBody",Em),getDoc:Bm("getDoc",Dm),getWin:Bm("getWin",Am),getSelection:Bm("getSelection",Im),getFrame:Mm,getActiveApi:function(a){var c=Mm(a);return Em(c).bind(function(u){return Dm(c).bind(function(i){return Am(c).map(function(o){var n=se.fromDom(i.dom().documentElement),e=a.getCursorBox.getOrThunk(function(){return function(){return(n=o,vm(n).map(function(n){return rm(n.start(),n.soffset(),n.finish(),n.foffset())})).bind(function(n){return pm(o,n).orThunk(function(){return vm(o).filter(function(n){return Ve(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?F.some(e).map(Fm):F.none()})})});var n}}),t=a.setSelection.getOrThunk(function(){return function(n,e,t,r){mm(o,n,e,t,r)}}),r=a.clearSelection.getOrThunk(function(){return function(){o.getSelection().removeAllRanges()}});return{body:I(u),doc:I(i),win:I(o),html:I(n),getSelection:l(Im,c),setSelection:t,clearSelection:r,frame:I(c),onKeyup:Rm(a,i,"onKeyup","keyup"),onNodeChanged:Rm(a,i,"onNodeChanged","selectionchange"),onDomChanged:a.onDomChanged,onScrollToCursor:a.onScrollToCursor,onScrollToElement:a.onScrollToElement,onToReading:a.onToReading,onToEditing:a.onToEditing,onToolbarScrollStart:a.onToolbarScrollStart,onTouchContent:a.onTouchContent,onTapContent:a.onTapContent,onTouchToolstrip:a.onTouchToolstrip,getCursorBox:e}})})})}},Vm="data-ephox-mobile-fullscreen-style",Hm="position:absolute!important;",zm="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;",jm=$n.detect().os.isAndroid(),Lm=function(n,e){var t,r,o,i=function(r){return function(n){var e=Yr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(_r(n,Vm,t),_r(n,"style",r))}},u=(t="*",Ri(n,function(n){return Be(n,t)},r)),a=Dn(u,function(n){var e;return e="*",Fi(n,function(n){return Be(n,e)})}),c=(o=Ti(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";yn(a,i("display:none!important;")),yn(u,i(Hm+zm+c)),i((!0===jm?"":Hm)+zm+c)(n)},Um=function(){var n=Fe("["+Vm+"]");yn(n,function(n){var e=Yr(n,Vm);"no-styles"!==e?_r(n,"style",e):Xr(n,"style"),Xr(n,Vm)})},Pm=function(){var e=Vi("head").getOrDie(),n=Vi('meta[name="viewport"]').getOrThunk(function(){var n=se.fromTag("meta");return _r(n,"name","viewport"),Pe(e,n),n}),t=Yr(n,"content");return{maximize:function(){_r(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?_r(n,"content",t):_r(n,"content","user-scalable=yes")}}},$m=function(e,n){var t=Pm(),r=Qf(),o=Qf();return{enter:function(){n.hide(),to(e.container,mi.resolve("fullscreen-maximized")),to(e.container,mi.resolve("android-maximized")),t.maximize(),to(e.body,mi.resolve("android-scroll-reload")),r.set(Cm(e.win,Nm.getWin(e.editor).getOrDie("no"))),Nm.getActiveApi(e.editor).each(function(n){Lm(e.container,n.body()),o.set(Yd(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),ro(e.container,mi.resolve("fullscreen-maximized")),ro(e.container,mi.resolve("android-maximized")),Um(),ro(e.body,mi.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Wm=function(t,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&clearTimeout(o),o=setTimeout(function(){t.apply(null,n),o=null},r)}}},Gm=function(n,e){var t,r,o,i=js(Of.sketch({dom:Wc('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=setTimeout(function(){t.apply(null,n),o=null},r))}});return Of.sketch({dom:Wc('<div class="${prefix}-disabled-mask"></div>'),components:[Of.sketch({dom:Wc('<div class="${prefix}-content-container"></div>'),components:[Uc.sketch({dom:Wc('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:Lr([ci.config({toggleClass:mi.resolve("mask-tap-icon-selected")})])})]})]})},_m=Vt([tr("editor",[Zt("getFrame"),rr("getBody"),rr("getDoc"),rr("getWin"),rr("getSelection"),rr("setSelection"),rr("clearSelection"),rr("cursorSaver"),rr("onKeyup"),rr("onNodeChanged"),rr("getCursorBox"),Zt("onDomChanged"),ur("onTouchContent",A),ur("onTapContent",A),ur("onTouchToolstrip",A),ur("onScrollToCursor",I({unbind:A})),ur("onScrollToElement",I({unbind:A})),ur("onToEditing",I({unbind:A})),ur("onToReading",I({unbind:A})),ur("onToolbarScrollStart",h)]),Zt("socket"),Zt("toolstrip"),Zt("dropup"),Zt("toolbar"),Zt("container"),Zt("alloy"),cr("win",function(n){return He(n.socket).dom().defaultView}),cr("body",function(n){return se.fromDom(n.socket.dom().ownerDocument.body)}),ur("translate",h),ur("setReadOnly",A),ur("readOnlyOnInit",I(!0))]),qm=function(n){var e=qt("Getting AndroidWebapp schema",_m,n);xi(e.toolstrip,"width","100%");var t=Hl(Gm(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Pe(e.container,t.element());var o=$m(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:A,enter:o.enter,exit:o.exit,destroy:A}},Ym=I([ur("shell",!0),La("toolbarBehaviours",[td])]),Km=I([lc({name:"groups",overrides:function(n){return{behaviours:Lr([td.config({})])}}})]),Xm=Lc({name:"Toolbar",configFields:Ym(),partFields:Km(),factory:function(e,n,t,r){var o=function(n){return e.shell()?F.some(n):yc(n,e,"groups")},i=e.shell()?{behaviours:[td.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid(),dom:e.dom(),components:i.components,behaviours:k(Lr(i.behaviours),Ua(e.toolbarBehaviours())),apis:{setGroups:function(n,e){o(n).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){td.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Jm=I([Zt("items"),(af=["itemClass"],tr("markers",bn(af,Zt))),La("tgroupBehaviours",[ja])]),Qm=I([dc({name:"items",unit:"item",overrides:function(n){return{domModification:{classes:[n.markers().itemClass()]}}}})]),Zm=Lc({name:"ToolbarGroup",configFields:Jm(),partFields:Qm(),factory:function(n,e,t,r){return k({dom:{attributes:{role:"toolbar"}}},{uid:n.uid(),dom:n.dom(),components:e,behaviours:k(Lr([ja.config({mode:"flow",selector:"."+n.markers().itemClass()})]),Ua(n.tgroupBehaviours())),"debug.sketcher":t["debug.sketcher"]})}}),ng="data-"+mi.resolve("horizontal-scroll"),eg=function(n){return"true"===Yr(n,ng)?0<(t=n).dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(t):0<(e=n).dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(e);var e,t},tg={exclusive:function(n,e){return zd(n,"touchmove",function(n){ji(n.target(),e).filter(eg).fold(function(){n.raw().preventDefault()},A)})},markAsHorizontal:function(n){_r(n,ng,"true")}};function rg(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Wc('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Lr([wf("adhoc-scrollable-toolbar",!0===n.scrollable?[wr(function(n,e){xi(n.element(),"overflow-x","auto"),tg.markAsHorizontal(n.element()),bd.register(n.element())})]:[])]),components:[Of.sketch({components:[Zm.parts().items({})]})],markers:{itemClass:mi.resolve("toolbar-group-item")},items:n.items}},t=Hl(Xm.sketch({dom:Wc('<div class="${prefix}-toolbar"></div>'),components:[Xm.parts().groups({})],toolbarBehaviours:Lr([ci.config({toggleClass:mi.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),ja.config({mode:"cyclic"})]),shell:!0})),n=Hl(Of.sketch({dom:{classes:[mi.resolve("toolstrip")]},components:[zl(t)],containerBehaviours:Lr([ci.config({toggleClass:mi.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Xm.setGroups(t,o.get()),ci.off(t)},o=fo([]);return{wrapper:I(n),toolbar:I(t),createGroups:function(n){return bn(n,v(Zm.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){ci.on(t),Xm.setGroups(t,n)},restoreToolbar:function(){ci.isOn(t)&&r()},refresh:function(){},focus:function(){ja.focusIn(t)}}}var og=function(n,e){td.append(n,zl(e))},ig=function(n,e){td.remove(n,e)},ug=function(n){return Hl(Uc.sketch({dom:Wc('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},ag=function(){return Hl(Of.sketch({dom:Wc('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Lr([td.config({})])}))},cg=function(n,e,t,r){(!0===t?so.toAlpha:so.toOmega)(r),(t?og:ig)(n,e)},sg=function(e,n){return n.getAnimationRoot().fold(function(){return e.element()},function(n){return n(e)})},fg=function(n){return n.dimension().property()},lg=function(n,e){return n.dimension().getDimension()(e)},dg=function(n,e){var t=sg(n,e);kl(t,[e.shrinkingClass(),e.growingClass()])},mg=function(n,e){ro(n.element(),e.openClass()),to(n.element(),e.closedClass()),xi(n.element(),fg(e),"0px"),Ei(n.element())},gg=function(n,e){ro(n.element(),e.closedClass()),to(n.element(),e.openClass()),Ci(n.element(),fg(e))},vg=function(n,e,t){t.setCollapsed(),xi(n.element(),fg(e),lg(e,n.element())),Ei(n.element());var r=sg(n,e);to(r,e.shrinkingClass()),mg(n,e),e.onStartShrink()(n)},pg=function(n,e,t){var r=function(n,e){gg(n,e);var t=lg(e,n.element());return mg(n,e),t}(n,e),o=sg(n,e);to(o,e.growingClass()),gg(n,e),xi(n.element(),fg(e),r),t.setExpanded(),e.onStartGrow()(n)},hg=function(n,e,t){var r=sg(n,e);return!0===io(r,e.growingClass())},bg=function(n,e,t){var r=sg(n,e);return!0===io(r,e.shrinkingClass())},yg=Object.freeze({grow:function(n,e,t){t.isExpanded()||pg(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&vg(n,e,t)},immediateShrink:function(n,e,t){var r,o;t.isExpanded()&&(r=n,o=e,t.setCollapsed(),xi(r.element(),fg(o),lg(o,r.element())),Ei(r.element()),dg(r,o),mg(r,o),o.onStartShrink()(r),o.onShrunk()(r))},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:hg,isShrinking:bg,isTransitioning:function(n,e,t){return!0===hg(n,e)||!0===bg(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?vg:pg)(n,e,t)},disableTransitions:dg}),wg=Object.freeze({exhibit:function(n,e){var t=e.expanded();return Cr(t?{classes:[e.openClass()],styles:{}}:{classes:[e.closedClass()],styles:wt(e.dimension().property(),"0px")})},events:function(t,r){return lr([mr(J(),function(n,e){e.event().raw().propertyName===t.dimension().property()&&(dg(n,t),r.isExpanded()&&Ci(n.element(),t.dimension().property()),(r.isExpanded()?t.onGrown():t.onShrunk())(n))})])}}),xg=[Zt("closedClass"),Zt("openClass"),Zt("shrinkingClass"),Zt("growingClass"),rr("getAnimationRoot"),$o("onShrunk"),$o("onStartShrink"),$o("onGrown"),$o("onStartGrow"),ur("expanded",!1),nr("dimension",Xt("property",{width:[qo("property","width"),qo("getDimension",function(n){return Cs(n)+"px"})],height:[qo("property","height"),qo("getDimension",function(n){return Bi(n)+"px"})]}))],Sg=Pr({fields:xg,name:"sliding",active:wg,apis:yg,state:Object.freeze({init:function(n){var e=fo(n.expanded());return jr({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:l(e.set,!1),setExpanded:l(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),Tg=function(e,t){var r=Hl(Of.sketch({dom:{tag:"div",classes:[mi.resolve("dropup")]},components:[],containerBehaviours:Lr([td.config({}),Sg.config({closedClass:mi.resolve("dropup-closed"),openClass:mi.resolve("dropup-open"),shrinkingClass:mi.resolve("dropup-shrinking"),growingClass:mi.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),td.set(n,[])},onGrown:function(n){e(),t()}}),fi(function(n,e){o(A)})])})),o=function(n){window.requestAnimationFrame(function(){n(),Sg.shrink(r)})};return{appear:function(n,e,t){!0===Sg.hasShrunk(r)&&!1===Sg.isTransitioning(r)&&window.requestAnimationFrame(function(){e(t),td.set(r,[n()]),Sg.grow(r)})},disappear:o,component:I(r),element:r.element}},Og=$n.detect().browser.isFirefox(),kg=Nt([er("triggerEvent"),er("broadcastEvent"),ur("stopBackspace",!0)]),Cg=function(e,n){var t,r,o,i,u,a=qt("Getting GUI events settings",kg,n),c=$n.detect().deviceType.isTouch()?["touchstart","touchmove","touchend","gesturestart"]:["mousedown","mouseup","mouseover","mousemove","mouseout","click"],s=Gd(a),f=bn(c.concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop"]),function(n){return zd(e,n,function(e){s.fireIfReady(e,n).each(function(n){n&&e.kill()}),a.triggerEvent(n,e)&&e.kill()})}),l=zd(e,"keydown",function(n){var e;a.triggerEvent("keydown",n)?n.kill():!0!==a.stopBackspace||8!==(e=n).raw().which||hn(["input","textarea"],me(e.target()))||n.prevent()}),d=(t=e,r=function(n){a.triggerEvent("focusin",n)&&n.kill()},Og?jd(t,"focus",r):zd(t,"focusin",r)),m=(o=e,i=function(n){a.triggerEvent("focusout",n)&&n.kill(),setTimeout(function(){a.triggerEvent(_n(),n)},0)},Og?jd(o,"blur",i):zd(o,"focusout",i)),g=(u=e.dom().ownerDocument.defaultView,se.fromDom(u)),v=zd(g,"scroll",function(n){a.broadcastEvent(ne(),n)&&n.kill()});return{unbind:function(){yn(f,function(n){n.unbind()}),l.unbind(),d.unbind(),m.unbind(),v.unbind()}}},Eg=function(n,e){var t=yt(n,"target").map(function(n){return n()}).getOr(e);return fo(t)},Dg=et([{stopped:[]},{resume:["element"]},{complete:[]}]),Ag=function(n,r,e,t,o,i){var u,a,c,s,f=n(r,t),l=(u=e,a=o,c=fo(!1),s=fo(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:I(u),setSource:a.set,getSource:a.get});return f.fold(function(){return i.logEventNoHandlers(r,t),Dg.complete()},function(e){var t=e.descHandler();return pl(t)(l),l.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),Dg.stopped()):l.isCut()?(i.logEventCut(r,e.element(),t.purpose()),Dg.complete()):ze(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),Dg.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),Dg.resume(n)})})},Ig=function(e,t,r,n,o,i){return Ag(e,t,r,n,o,i).fold(function(){return!0},function(n){return Ig(e,t,r,n,o,i)},function(){return!1})},Mg=function(n,e,t){var r,o,i=(r=e,o=fo(!1),{stop:function(){o.set(!0)},cut:A,isStopped:o.get,isCut:I(!1),event:I(r),setSource:c("Cannot set source of a broadcasted event"),getSource:c("Cannot get source of a broadcasted event")});return yn(n,function(n){var e=n.descHandler();pl(e)(i)}),i.isStopped()},Bg=function(n,e,t,r,o){var i=Eg(t,r);return Ig(n,e,t,r,i,o)},Rg=function(n,e,t){return go(n,function(n){return e(n).isSome()},t).bind(e)},Fg=we("element","descHandler"),Ng=function(n,e){return{id:I(n),descHandler:I(e)}};function Vg(){var i={};return{registerId:function(r,o,n){V(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=vl(n,r),i[e]=t})},unregisterId:function(t){V(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return yt(i,n).map(function(n){return j(n,function(n,e){return Ng(e,n)})}).getOr([])},find:function(n,e,t){var o=ht(e)(i);return Rg(t,function(n){return t=o,Bc(r=n).fold(function(){return F.none()},function(n){var e=ht(n);return t.bind(e).map(function(n){return Fg(r,n)})});var t,r},n)}}}function Hg(){var r=Vg(),o={},i=function(r){var n=r.element();return Bc(n).fold(function(){return n="uid-",e=r.element(),t=Ja(Ic+n),_r(e,Mc,t),t;var n,e,t},function(n){return n})},u=function(n){Bc(n.element()).each(function(n){o[n]=undefined,r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);Tt(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+Do(t.element())+"\nCannot use it for: "+Do(n.element())+"\nThe conflicting element is"+(he(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events()),o[e]=n},unregister:u,getById:function(n){return ht(n)(o)}}}var zg=function(t){var r=function(e){return ze(t.element()).fold(function(){return!0},function(n){return Ve(e,n)})},o=Hg(),s=function(n,e){return o.find(r,n,e)},n=Cg(t.element(),{triggerEvent:function(u,a){return Ho(u,a.target(),function(n){return e=s,t=u,o=n,i=(r=a).target(),Bg(e,t,r,i,o);var e,t,r,o,i})},broadcastEvent:function(n,e){var t=o.filter(n);return Mg(t,e)}}),i=sl({debugInfo:I("real"),triggerEvent:function(e,t,r){Ho(e,t,function(n){Bg(s,e,r,t,n)})},triggerFocus:function(a,c){Bc(a).fold(function(){po(a)},function(n){Ho(Gn(),a,function(n){var e,t,r,o,i,u;e=s,t=Gn(),r={originator:I(c),kill:A,prevent:A,target:I(a)},i=n,u=Eg(r,o=a),Ag(e,t,r,o,u,i)})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return m(n)},getByDom:function(n){return g(n)},build:Hl,addToGui:function(n){a(n)},removeFromGui:function(n){c(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},isConnected:I(!0)}),e=function(n){n.connect(i),pe(n.element())||(o.register(n),yn(n.components(),e),i.triggerEvent(Zn(),n.element(),{target:I(n.element())}))},u=function(n){pe(n.element())||(yn(n.components(),u),o.unregister(n)),n.disconnect()},a=function(n){Ye(t,n)},c=function(n){Je(n)},f=function(t){var n=o.filter(qn());yn(n,function(n){var e=n.descHandler();pl(e)(t)})},l=function(n){f({universal:I(!0),data:I(n)})},d=function(n,e){f({universal:I(!1),channels:I(n),data:I(e)})},m=function(n){return o.getById(n).fold(function(){return nt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},nt.value)},g=function(n){var e=Bc(n).getOr("not found");return m(e)};return e(t),{root:I(t),element:t.element,destroy:function(){n.unbind(),Ge(t.element())},add:a,remove:c,getByUid:m,getByDom:g,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d}},jg=I(mi.resolve("readonly-mode")),Lg=I(mi.resolve("edit-mode"));function Ug(n){var e=Hl(Of.sketch({dom:{classes:[mi.resolve("outer-container")].concat(n.classes)},containerBehaviours:Lr([so.config({alpha:jg(),omega:Lg()})])}));return zg(e)}var Pg=function(n,e){var t=se.fromTag("input");Si(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Pe(n,t),po(t),e(t),Ge(t)},$g=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Wg=function(n,e){bo().each(function(n){Ve(n,e)||ho(n)}),n.focus(),po(se.fromDom(n.document.body)),$g(n)},Gg={stubborn:function(n,e,t,r){var o=function(){Wg(e,r)},i=zd(t,"keydown",function(n){hn(["input","textarea"],me(n.target()))||o()});return{toReading:function(){Pg(n,ho)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){var o=function(){ho(r)};return{toReading:function(){o()},toEditing:function(){Wg(e,r)},onToolbarTouch:function(){o()},destroy:A}}},_g=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},a=function(){r.run(function(n){n.clearSelection()})},c=function(){t.getCursorBox().each(function(n){e(n.top(),n.height())}),r.run(function(n){n.syncHeight()})},s=_d(t),f=Wm(c,300),l=[t.onKeyup(function(){a(),f.throttle()}),t.onNodeChanged(u),t.onDomChanged(f.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),f.throttle()}),t.onScrollToElement(function(n){n.element(),e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),zd(t.doc(),"touchend",function(n){Ve(t.html(),n.target())||Ve(t.body(),n.target())}),zd(o,"transitionend",function(n){var e;"height"===n.raw().propertyName&&(e=Bi(o),r.run(function(n){n.setViewportOffset(e)}),u(),c())}),jd(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),zd(t.body(),"touchstart",function(n){a(),t.onTouchContent(),s.fireTouchstart(n)}),s.onTouchmove(),s.onTouchend(),zd(t.body(),"click",function(n){n.kill()}),zd(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){yn(l,function(n){n.unbind()})}}},qg=function(n){var t=F.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){yn(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){t=F.some(n),i(e),e=[]}),{get:r,map:function(t){return qg(function(e){r(function(n){e(t(n))})})},isReady:o}},Yg={nu:qg,pure:function(e){return qg(function(n){n(e)})}},Kg=function(e){var n=function(n){var r;e((r=n,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=this;setTimeout(function(){r.apply(t,n)},0)}))},t=function(){return Yg.nu(n)};return{map:function(r){return Kg(function(t){n(function(n){var e=r(n);t(e)})})},bind:function(t){return Kg(function(e){n(function(n){t(n).get(e)})})},anonBind:function(t){return Kg(function(e){n(function(n){t.get(e)})})},toLazy:t,toCached:function(){var e=null;return Kg(function(n){null===e&&(e=t()),e.get(n)})},get:n}},Xg={nu:Kg,pure:function(e){return Kg(function(n){n(e)})}},Jg=function(n,e,t){return Math.abs(n-e)<=t?F.none():n<e?F.some(n+t):F.some(n-t)},Qg=function(){var s=null;return{animate:function(r,o,n,i,e,t){var u=!1,a=function(n){u=!0,e(n)};clearInterval(s);var c=function(n){clearInterval(s),a(n)};s=setInterval(function(){var t=r();Jg(t,o,n).fold(function(){clearInterval(s),a(o)},function(n){if(i(n,c),!u){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(clearInterval(s),a(o))}})},t)}}},Zg=function(e,t){return Mo([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e<=n.width&&t<=n.height?F.some(n.keyboard):F.none()}).getOr({portrait:t/5,landscape:e/4})},nv=function(n){var e,t=Ud(n).isPortrait(),r=Zg((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},ev=function(n,e){var t=He(n).dom().defaultView;return Bi(n)+Bi(e)-nv(t)},tv=ev,rv=function(n,e,t){var r=ev(e,t),o=Bi(e)+Bi(t)-r;xi(n,"padding-bottom",o+"px")},ov=et([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),iv="data-"+mi.resolve("position-y-fixed"),uv="data-"+mi.resolve("y-property"),av="data-"+mi.resolve("scrolling"),cv="data-"+mi.resolve("last-window-height"),sv=function(n){return Kd(n,iv)},fv=function(n,e){var t=Yr(n,uv);return ov.fixed(n,t,e)},lv=function(n,e){return ov.scroller(n,e)},dv=function(n){var e=sv(n);return("true"===Yr(n,av)?lv:fv)(n,e)},mv=function(n,e,t){var r=He(n).dom().defaultView.innerHeight;return _r(n,cv,r+"px"),r-e-t},gv=function(n){var e=Ni(n,"["+iv+"]");return bn(e,dv)},vv=function(r,o,i,u){var n,e,t,a,c,s,f,l,d=He(r).dom().defaultView,m=(l=Yr(f=i,"style"),Si(f,{position:"absolute",top:"0px"}),_r(f,iv,"0px"),_r(f,uv,"top"),{restore:function(){_r(f,"style",l||""),Xr(f,iv),Xr(f,uv)}}),g=Bi(i),v=Bi(u),p=mv(r,g,v),h=(t=g,a=p,s=Yr(c=r,"style"),bd.register(c),Si(c,{position:"absolute",height:a+"px",width:"100%",top:t+"px"}),_r(c,iv,t+"px"),_r(c,av,"true"),_r(c,uv,"top"),{restore:function(){bd.deregister(c),_r(c,"style",s||""),Xr(c,iv),Xr(c,av),Xr(c,uv)}}),b=(e=Yr(n=u,"style"),Si(n,{position:"absolute",bottom:"0px"}),_r(n,iv,"0px"),_r(n,uv,"bottom"),{restore:function(){_r(n,"style",e||""),Xr(n,iv),Xr(n,uv)}}),y=!0,w=function(){var n=d.innerHeight;return Kd(r,cv)<n},x=function(){if(y){var n=Bi(i),e=Bi(u),t=mv(r,n,e);_r(r,iv,n+"px"),xi(r,"height",t+"px"),xi(u,"bottom",-(n+t+e)+"px"),rv(o,r,u)}};return rv(o,r,u),{setViewportOffset:function(n){_r(r,iv,n+"px"),x()},isExpanding:w,isShrinking:S(w),refresh:x,restore:function(){y=!1,m.restore(),h.restore(),b.restore()}}},pv=sv,hv=Qg(),bv="data-"+mi.resolve("last-scroll-top"),yv=function(n){var e=ki(n,"top").getOr("0");return parseInt(e,10)},wv=function(n){return parseInt(n.dom().scrollTop,10)},xv=function(n,e){var t=e+pv(n)+"px";xi(n,"top",t)},Sv=function(t,r,o){return Xg.nu(function(n){var e=l(wv,t);hv.animate(e,r,15,function(n){t.dom().scrollTop=n,xi(t,"top",yv(t)+15+"px")},function(){t.dom().scrollTop=r,xi(t,"top",o+"px"),n(r)},10)})},Tv=function(o,i){return Xg.nu(function(n){var e=l(wv,o);_r(o,bv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);hv.animate(e,i,r,function(n,e){Kd(o,bv)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,_r(o,bv,n))},function(){o.dom().scrollTop=i,_r(o,bv,i),n(i)},10)})},Ov=function(i,u){return Xg.nu(function(n){var e=l(yv,i),t=function(n){xi(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);hv.animate(e,u,o,t,function(){t(u),n(u)},10)})},kv=function(e,t,r){var o=He(e).dom().defaultView;return Xg.nu(function(n){xv(e,r),xv(t,r),o.scrollTo(0,r),n(r)})},Cv=function(n,e,t,r,o){var i=tv(e,t),u=l($g,n);i<r||i<o?Tv(e,e.dom().scrollTop-i+o).get(u):r<0&&Tv(e,e.dom().scrollTop+r).get(u)},Ev=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):yn(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},Dv=function(n,c){return n.fold(function(n,e,t){return xi(n,e,c+(r=t)+"px"),Xg.pure(r);var r},function(n,e){return o=c+(r=e),i=ki(t=n,"top").getOr(r),u=o-parseInt(i,10),a=t.dom().scrollTop+u,Sv(t,a,o);var t,r,o,i,u,a})},Av=function(n,e){var t=gv(n),r=bn(t,function(n){return Dv(n,e)});return Ev(r,Xg.nu)},Iv=function(e,t,n,r,o,i){var u,a,c=(u=function(n){return kv(e,t,n)},a=fo(Yg.pure({})),{start:function(e){var n=Yg.nu(function(n){return u(e).get(n)});a.set(n)},idle:function(n){a.get().get(function(){n()})}}),s=Wm(function(){c.idle(function(){Av(n,r.pageYOffset).get(function(){var n;(n=wm(i),F.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?F.some({top:I(e),bottom:I(e+n.height())}):F.none()})).each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),c.start(0),o.refresh()})})},1e3),f=zd(se.fromDom(r),"scroll",function(){r.pageYOffset<0||s.throttle()});return Av(n,r.pageYOffset).get(h),{unbind:f.unbind}},Mv=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),a=n.keyboardType(),c=n.outerWindow(),s=n.dropup(),f=vv(r,e,o,s),l=a(n.outerBody(),t,be(),u,o,i),d=Pd(c,{onChange:A,onReady:f.refresh});d.onAdjustment(function(){f.refresh()});var m=zd(se.fromDom(c),"resize",function(){f.isExpanding()&&f.refresh()}),g=Iv(o,r,n.outerBody(),c,f,t),v=function(t,e){var n=t.document,r=se.fromTag("div");to(r,mi.resolve("unfocused-selections")),Pe(se.fromDom(n.documentElement),r);var o=zd(r,"touchstart",function(n){n.prevent(),Wg(t,e),u()}),i=function(n){var e=se.fromTag("span");return Ol(e,[mi.resolve("layer-editor"),mi.resolve("unfocused-selection")]),Si(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e},u=function(){We(r)};return{update:function(){u();var n=wm(t),e=bn(n,i);$e(r,e)},isActive:function(){return 0<je(r).length},destroy:function(){o.unbind(),Ge(r)},clear:u}}(t,u),p=function(){v.clear()};return{toEditing:function(){l.toEditing(),p()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){v.isActive()&&v.update()},clearSelection:p,highlightSelection:function(){v.update()},scrollIntoView:function(n,e){Cv(t,r,s,n,e)},updateToolbarPadding:A,setViewportOffset:function(n){f.setViewportOffset(n),Ov(r,n).get(h)},syncHeight:function(){xi(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:f.refresh,destroy:function(){f.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),v.destroy(),Pg(be(),ho)}}},Bv=function(r,n){var o=Pm(),i=Zf(),u=Zf(),a=Qf(),c=Qf();return{enter:function(){n.hide();var t=se.fromDom(document);Nm.getActiveApi(r.editor).each(function(n){i.set({socketHeight:ki(r.socket,"height"),iframeHeight:ki(n.frame(),"height"),outerScroll:document.body.scrollTop}),u.set({exclusives:tg.exclusive(t,"."+bd.scrollable())}),to(r.container,mi.resolve("fullscreen-maximized")),Lm(r.container,n.body()),o.maximize(),xi(r.socket,"overflow","scroll"),xi(r.socket,"-webkit-overflow-scrolling","touch"),po(n.body());var e=Ce(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);a.set(Mv(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:A,outerBody:r.body,outerWindow:r.win,keyboardType:Gg.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),a.run(function(n){n.syncHeight()}),c.set(_g(n,a,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){a.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),c.clear(),a.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){xi(r.socket,"height",n)}),n.iframeHeight.each(function(n){xi(r.editor.getFrame(),"height",n)}),document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),ro(r.container,mi.resolve("fullscreen-maximized")),Um(),bd.deregister(r.toolbar),Ci(r.socket,"overflow"),Ci(r.socket,"-webkit-overflow-scrolling"),ho(r.editor.getFrame()),Nm.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Rv=function(n){var e=qt("Getting IosWebapp schema",_m,n);xi(e.toolstrip,"width","100%"),xi(e.container,"position","relative");var t=Hl(Gm(function(){e.setReadOnly(e.readOnlyOnInit()),r.enter()},e.translate));e.alloy.add(t);var r=Bv(e,{show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}});return{setReadOnly:e.setReadOnly,refreshStructure:r.refreshStructure,enter:r.enter,exit:r.exit,destroy:A}},Fv=tinymce.util.Tools.resolve("tinymce.EditorManager"),Nv=function(n){var e=yt(n.settings,"skin_url").fold(function(){return Fv.baseURL+"/skins/lightgray"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},Vv=function(n,e,t){n.system().broadcastOn([To.formatChanged()],{command:e,state:t})},Hv=function(r,n){var e=N(n.formatter.get());yn(e,function(e){n.formatter.formatChanged(e,function(n){Vv(r,e,n)})}),yn(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Vv(r,t,n)})})},zv=(I(["x-small","small","medium","large","x-large"]),function(n){var e=function(){n._skinLoaded=!0,n.fire("SkinLoaded")};return function(){n.initialized?e():n.on("init",e)}}),jv=I("toReading"),Lv=I("toEditing");xo.add("mobile",function(D){return{getNotificationManagerImpl:function(){return{open:h,close:A,reposition:A,getArgs:h}},renderUI:function(n){var e=Nv(D);0==(!1===D.settings.skin)?(D.contentCSS.push(e.content),wo.DOM.styleSheetLoader.load(e.ui,zv(D))):zv(D)();var t,r,o,i,u,a,c,s,f,l,d,m,g,v,p,h,b,y=function(){D.fire("scrollIntoView")},w=se.fromTag("div"),x=$n.detect().os.isAndroid()?(s=y,f=Ug({classes:[mi.resolve("android-container")]}),l=rg(),d=Qf(),m=ug(d),g=ag(),v=Tg(A,s),f.add(l.wrapper()),f.add(g),f.add(v.component()),{system:I(f),element:f.element,init:function(n){d.set(qm(n))},exit:function(){d.run(function(n){n.exit(),td.remove(g,m)})},setToolbarGroups:function(n){var e=l.createGroups(n);l.setGroups(e)},setContextToolbar:function(n){var e=l.createGroups(n);l.setContextToolbar(e)},focusToolbar:function(){l.focus()},restoreToolbar:function(){l.restoreToolbar()},updateMode:function(n){cg(g,m,n,f.root())},socket:I(g),dropup:I(v)}):(t=y,r=Ug({classes:[mi.resolve("ios-container")]}),o=rg(),i=Qf(),u=ug(i),a=ag(),c=Tg(function(){i.run(function(n){n.refreshStructure()})},t),r.add(o.wrapper()),r.add(a),r.add(c.component()),{system:I(r),element:r.element,init:function(n){i.set(Rv(n))},exit:function(){i.run(function(n){td.remove(a,u),n.exit()})},setToolbarGroups:function(n){var e=o.createGroups(n);o.setGroups(e)},setContextToolbar:function(n){var e=o.createGroups(n);o.setContextToolbar(e)},focusToolbar:function(){o.focus()},restoreToolbar:function(){o.restoreToolbar()},updateMode:function(n){cg(a,u,n,r.root())},socket:I(a),dropup:I(c)}),S=se.fromDom(n.targetNode);we("element","offset"),h=w,(b=(p=S).dom(),F.from(b.nextSibling).map(se.fromDom)).fold(function(){ze(p).each(function(n){Pe(n,h)})},function(n){var e,t;t=h,ze(e=n).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}),function(n,e){Pe(n,e.element());var t=je(e.element());yn(t,function(n){e.getByDom(n).each(qe)})}(w,x.system());var T=n.targetNode.ownerDocument.defaultView,O=Pd(T,{onChange:function(){x.system().broadcastOn([To.orientationChanged()],{width:$d(T)})},onReady:A}),k=function(n,e,t,r){!1===r&&D.selection.collapse();var o=C(n,e,t);x.setToolbarGroups(!0===r?o.readOnly:o.main),D.setMode(!0===r?"readonly":"design"),D.fire(!0===r?jv():Lv()),x.updateMode(r)},C=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},E=function(n,e){return D.on(n,e),{unbind:function(){D.off(n)}}};return D.on("init",function(){x.init({editor:{getFrame:function(){return se.fromDom(D.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:A}},onToReading:function(n){return E(jv(),n)},onToEditing:function(n){return E(Lv(),n)},onScrollToCursor:function(e){return D.on("scrollIntoView",function(n){e(n)}),{unbind:function(){D.off("scrollIntoView"),O.destroy()}}},onTouchToolstrip:function(){t()},onTouchContent:function(){var n,e=se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolbar")));(n=e,yo(n).bind(function(n){return x.system().getByDom(n).toOption()})).each(ie),x.restoreToolbar(),t()},onTapContent:function(n){var e=n.target();"img"===me(e)?(D.selection.select(e.dom()),n.kill()):"a"===me(e)&&x.system().getByDom(se.fromDom(D.editorContainer)).each(function(n){so.isAlpha(n)&&So(e.dom())})}},container:se.fromDom(D.editorContainer),socket:se.fromDom(D.contentAreaContainer),toolstrip:se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolstrip"))),toolbar:se.fromDom(D.editorContainer.querySelector("."+mi.resolve("toolbar"))),dropup:x.dropup(),alloy:x.system(),translate:A,setReadOnly:function(n){k(c,a,u,n)},readOnlyOnInit:function(){return!1}});var t=function(){x.dropup().disappear(function(){x.system().broadcastOn([To.dropupDismissed()],{})})},n={label:"The first group",scrollable:!1,items:[Yc.forToolbar("back",function(){D.selection.collapse(),x.exit()},{})]},e={label:"Back to read only",scrollable:!1,items:[Yc.forToolbar("readonly-back",function(){k(c,a,u,!0)},{})]},r=Bd(x,D),o=Rd(D.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=fo([{label:"the action group",scrollable:!0,items:o},i]),a=fo([{label:"The read only mode group",scrollable:!0,items:[]},i]),c=fo({backToMask:[n],backToReadOnly:[e]});Hv(x,D)}),{iframeContainer:x.socket().element().dom(),editorContainer:x.element().dom()}}}})}();