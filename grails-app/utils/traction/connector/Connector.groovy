package traction.connector

import groovy.util.logging.Slf4j
import org.w3c.dom.Document
import org.w3c.dom.Element
import org.w3c.dom.Node
import org.w3c.dom.NodeList
import org.xml.sax.SAXException
import product.ManagerProductTask
import traction.connectors.BuildPrd
import traction.connectors.DifferentialBuildPrd
import traction.connectors.Dms
import traction.connectors.DownloadZII
import traction.connectors.MagentoPartApi
import traction.connectors.ValidateImport
import traction.connectors.MagentoPartApi_DELETE
import traction.vehicle.VehicleFeeds

import javax.xml.parsers.DocumentBuilder
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException
import javax.xml.transform.TransformerException

@Slf4j
public abstract class Connector {

    public static final String path_liste_produits = "liste_produits.xml";

    public abstract ArrayList<LinkedHashMap<String,String>> convert(int invcount, VehicleFeeds feeds, ManagerProductTask task, def products, String iprofile) throws IOException, ParserConfigurationException, TransformerException;

    public abstract String getSectionName();

    public static Connector getConnector(String name){
        System.out.println("name="+name)
        switch(name.toLowerCase()){
            case "productmanager.task.dms": return new Dms();
            case "productmanager.task.build": return new BuildPrd();
            case "productmanager.task.diffbuild": return new DifferentialBuildPrd();
            case "productmanager.task.validateimport": return new ValidateImport();
            case "productmanager.task.magentopartapi": return new MagentoPartApi();
            case "productmanager.task.magentopartapi_delete": return new MagentoPartApi_DELETE();
            case "category.feed.type.downloadzii": return new DownloadZII();
        }
    }

    public static ArrayList<LinkedHashMap<String,String>> read_products(ArrayList<LinkedHashMap<String,String>> products, ManagerProductTask task, String url_products) throws IOException, SAXException, ParserConfigurationException {
        System.out.println("Read product Connector")
        def file = new File("/big_data/client/Lautopak.xml")

        if(file.exists())
        {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(file);

            doc.getDocumentElement().normalize();

            NodeList nListProducts = doc.getElementsByTagName("product");

            for(int temp = 0; temp < nListProducts.getLength(); temp++)
            {
                LinkedHashMap<String,String> product = new LinkedHashMap<String, String>();

                Node nNode = nListProducts.item(temp);
                NodeList productNodes = nNode.getChildNodes();

                for(int i = 0 ; i < productNodes.getLength();i++) {
                    if(productNodes.item(i).getNodeType() == Node.ELEMENT_NODE)
                    {
                        Element eElement = (Element) productNodes.item(i);
                        product.put(eElement.getTagName(),eElement.getTextContent());
                    }
                }
                products.add(product);
            }

        }
        return products
    }
}

