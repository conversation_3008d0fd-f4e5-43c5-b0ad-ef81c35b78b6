package traction.client

import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j

@Slf4j
class AttachmentOptions {
    boolean multiple = true
    boolean copy = false
    String addCallback = ""

    AttachmentOptions(GrailsParameterMap params) {
        this.multiple = params.boolean('options[multiple]', true)
        this.copy = params.boolean('options[copy]', false)
        addCallback = params['options[addCallback]'] ?: ""
    }

    AttachmentOptions() {
        this.multiple = true
        this.copy = false
        addCallback = ""
    }
}
