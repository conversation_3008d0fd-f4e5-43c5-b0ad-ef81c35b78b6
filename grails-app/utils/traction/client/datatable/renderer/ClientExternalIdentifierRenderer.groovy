package traction.client.datatable.renderer

import groovy.sql.GroovyRowResult
import groovy.sql.Sql

class ClientExternalIdentifierRenderer {

    private Sql sql
    List<Long> idsList
    private HashMap<Long, Map<String, String>> map = [:]

    ClientExternalIdentifierRenderer(Sql sql, List<Long> idsList) {
        this.sql = sql
        this.idsList = idsList
        initMap()
    }

    Map<String, String> getClientExternalIdentifier(Long clientId) {
        return map[clientId]
    }

    private void initMap() {
        map = [:]
        getClientExternalIdentifierRowList().each {
            addRowToMap(it)
        }
    }

    private void addRowToMap(GroovyRowResult row) {1
        if (!map[row.client_id]) {
            map[row.client_id] = [:]
        }
        map[row.client_id][row.ext_id] = row.name
    }

    private List<GroovyRowResult> getClientExternalIdentifierRowList() {
        return idsList ? sql.rows(getSqlClientExternalIdentifierRowList()) : []
    }

    private String getSqlClientExternalIdentifierRowList() {
        return """
                SELECT
                    external_identifier.client_id,
                    external_identifier.ext_id,
                    dms.name
                FROM 
                    external_identifier
                INNER JOIN
                    dms ON external_identifier.dms_id=dms.id
                WHERE 
                    external_identifier.client_id IN (${idsList.join(",")})
                GROUP BY
                    external_identifier.client_id,
                    external_identifier.ext_id,
                    dms.name
                """
    }
}
