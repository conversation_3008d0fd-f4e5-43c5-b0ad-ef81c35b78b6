package traction.client

import grails.converters.JSON
import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import traction.CardTagLib
import traction.DateUtils
import traction.I18N
import traction.communication.CommunicationTagLib
import traction.datatable.DataTableColumnFilters
import traction.datatable.DataTableOrdering
import traction.datatable.DataTablePaging
import traction.opportunity.Opportunity
import traction.vehicle.Vehicle

class ClientServiceDataTable {

    DataTablePaging dataTablePaging
    DataTableOrdering dataTableOrdering
    DataTableColumnFilters filters
    Client client
    Vehicle vehicle
    String search
    private CardTagLib cardTagLib
    private CommunicationTagLib communicationTagLib
    String draw

    ClientServiceDataTable(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.dataTableOrdering = new DataTableOrdering(params)
        this.filters = new DataTableColumnFilters(params.filter)
        this.search = params["search[value]"]
        this.draw = params.draw
        this.client = Client.get(params.long("clientId", 0))
        this.vehicle = Vehicle.get(params.long("vehicleId", 0))
        this.cardTagLib = Holders.grailsApplication.mainContext.getBean("traction.CardTagLib")
        this.communicationTagLib = Holders.grailsApplication.mainContext.getBean("traction.communication.CommunicationTagLib")
    }

    JSON render() {
        List<Opportunity> opportunities = get()
        Map json = [
                draw           : draw,
                recordsTotal   : countTotalRecords(),
                recordsFiltered: countTotalRecords(),
                data           : opportunities.collect { format(it) }
        ]

        return json as JSON
    }

    private Map format(Opportunity opportunity) {

        return [
                opportunity: opportunity,
                cardDatatable : cardTagLib.card2(opportunity: opportunity),
        ]
    }

    private List<Opportunity> get() {
        if(this.client){
            List<Opportunity> opportunities = Opportunity.createCriteria().list(max: this.dataTablePaging.max, offset: this.dataTablePaging.offset){
                eq("client", this.client)
                eq("type", Opportunity.Type.SERVICE)
                dataTableOrdering.addToCriteriaBuilder(delegate, [opportunity: "name"])
            } as List<Opportunity>

            return opportunities? opportunities : []
        }

        return []
    }

    int countTotalRecords() {
        return  Opportunity.createCriteria().count(){
            if (this.client) eq("client", this.client)
            if(this.vehicle) eq("vehicleInstance", this.vehicle)
            eq("type", Opportunity.Type.SERVICE)
        }
    }
}