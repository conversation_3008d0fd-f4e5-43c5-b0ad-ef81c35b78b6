package traction.client

@Deprecated
class ClientAttributes {
    def static BIRTHDAY = [message: 'client.birthday', icon: '']
    def static DRIVER_LICENCE = [message: 'client.driverlicence', icon: '']
    def static FAX = [message: 'client.fax', icon: '']
    def static HOME_ADDRESS = [message: 'client.homeaddress', icon: '']
    def static HOME_CITY = [message: 'client.homecity', icon: '']
    def static HOME_PROVINCE = [message: 'client.homeprovince', icon: '']
    def static HOME_POSTAL = [message: 'client.homepostal', icon: '']
    def static HOME_COUNTRY = [message: 'client.homecountry', icon: '']
    def static WORK_CITY = [message: 'client.workcity', icon: '']
    def static WORK_PROVINCE = [message: 'client.workprovince', icon: '']
    def static WORK_ADDRESS = [message: 'client.workaddress', icon: '']
    def static WORK_POSTAL = [message: 'client.workpostal', icon: '']
    def static WORK_COUNTRY = [message: 'client.workcountry', icon: '']
    def static CONSENT = [message: 'client.consent', icon: '']
    def static EMERGENCYNAME = [message: 'client.emergencyname', icon: '']
    def static EMERGENCYPHONE = [message: 'client.emergencyphone', icon: '']

    public static List getAll() {
        def ret = []
        ret.add(BIRTHDAY)
        ret.add(FAX)
        ret.add(HOME_ADDRESS)
        ret.add(HOME_CITY)
        ret.add(HOME_PROVINCE)
        ret.add(HOME_POSTAL)
        ret.add(HOME_COUNTRY)
        ret.add(WORK_CITY)
        ret.add(WORK_PROVINCE)
        ret.add(WORK_ADDRESS)
        ret.add(WORK_POSTAL)
        ret.add(WORK_COUNTRY)
        ret.add(CONSENT)
        ret.add(EMERGENCYNAME)
        ret.add(EMERGENCYPHONE)
        ret.add(DRIVER_LICENCE)
        return ret
    }
}

