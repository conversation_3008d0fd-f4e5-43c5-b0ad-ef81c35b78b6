package traction.datatable

import grails.gorm.PagedResultList
import grails.web.servlet.mvc.GrailsParameterMap
import traction.dataTable.DataTableCookie

class DataTableViewSearch {
    DataTablePaging dataTablePaging
    DataTableOrdering dataTableOrdering

    String search
    List<Long> ids = []

    String name
    String cookieName
    String state

    Boolean isPublic

    DataTableViewSearch(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.dataTableOrdering = new DataTableOrdering(params)
        if (!this.dataTableOrdering.properties) {
            this.dataTableOrdering.properties.add(new OrderingProperty([property: "date", direction: "asc"]))
        }
        this.ids = params.viewIds ? params.viewIds.split(",").collect { Long.valueOf(it) } : null
        this.search = params["search[value]"]
        DataTableColumnFilters filter = new DataTableColumnFilters(params.filter)
        this.name = filter.getString("name")
        this.state = filter.getString("state")
        this.cookieName = filter.getString("cookieName")
        this.isPublic = params.getBoolean("isPublic")
    }

    PagedResultList<DataTableCookie> list() {
        return this.get(false)
    }

    int count() {
        return this.get(true)
    }

    private def get(boolean count) {
        def res = DataTableCookie.createCriteria().list(dataTablePaging.getListParams()) {
            if (ids) 'in'("id", ids)
            if (search) {
                or {
                    if (search.trim().isInteger()) {
                        eq("id", Long.valueOf(search.trim()))
                    }
                    String s = "%" + search.replace(" ", "%") + "%"
                    and { ilike("text", s) }
                    and { ilike("state", s) }
                    and { ilike("cookieName", s) }
                }
            }
            if (name) ilike("name", "%${name.replace(" ", "%")}%")
            if (state) ilike("state", "%${state.replace(" ", "%")}%")
            if (cookieName) ilike("cookieName", "%${cookieName.replace(" ", "%")}%")
            if (isPublic != null) eq("isPublic",isPublic)
            if (count) {
                projections {
                    rowCount()
                }
            }
            eq("deleted", false)
        }
        return count ? res[0] : res
    }
}
