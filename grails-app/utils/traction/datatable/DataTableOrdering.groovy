package traction.datatable

import grails.orm.HibernateCriteriaBuilder
import grails.web.servlet.mvc.GrailsParameterMap

class DataTableOrdering {

    List<OrderingProperty> properties = []

    DataTableOrdering(GrailsParameterMap params) {
        for (int i = 0; ; i++) {
            String orderColumn = params["order[$i][column]"]
            if (!orderColumn) break
            this.properties.add(new OrderingProperty([
                    property : params["columns[$orderColumn][data]"],
                    direction: params["order[$i][dir]"]
            ]))
        }
    }

    void addToCriteriaBuilder(HibernateCriteriaBuilder builder, Map propertyMapping = [:], List<String> reversedDirectionProperties = []) {
        properties.each {
            builder.order(propertyMapping[it.property] ?: it.property, reversedDirectionProperties.contains(it.property) ? getReverseDirection(it.direction) : it.direction)
        }
    }

    boolean hasOrderingProperty(String property) {
        return properties.any { it.property == property }
    }

    String toString() {
        return "DataTableOrdering(properties: $properties)"
    }

    private String getReverseDirection(String dir) {
        return dir.equalsIgnoreCase("asc") ? "desc" : "asc"
    }
}