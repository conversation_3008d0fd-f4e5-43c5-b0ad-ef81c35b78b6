package traction.chat

import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.I18N

class ChatQueryBuilder {

    String condition

    String id
    String field
    String type
    String operator
    Object value

    List<ChatQueryBuilder> rules = []

    ChatQueryBuilder(JSONObject json) {
        if (!json) return
        if (json.containsKey("condition") && json.containsKey("rules")) {
            this.condition = json.getString("condition").toLowerCase()
            if (this.condition != "and" && this.condition != "or") {
                throw new Exception("Unknown ChatQueryBuilder.condition : " + this.condition)
            }
            JSONArray rules = json.getJSONArray("rules")
            for (int i = 0; i < rules.length(); i++) {
                JSONObject rule = rules.getJSONObject(i)
                this.rules.add(new ChatQueryBuilder(rule))
            }
        } else if (json.containsKey("id")) {
            this.id = json.getString("id")
            this.field = json.getString("field")
            this.type = json.getString("type")
            this.value = json.get("value")
            this.operator = json.getString("operator")
        }
    }

    String getDescription() {
        return getFilteredFields().sort().join(", ")
    }

    HashSet<String> getFilteredFields() {
        if (condition) {
            if (!rules) return []
            return rules.collect { it.getFilteredFields() }.sum()
        }
        return [I18N.m("chatquerybuilder.${field}")]

    }

    boolean fits(List<WebVisit> webVisits) {
        if (!webVisits) return false
        if (condition) {
            if (!rules) return true
            List<Boolean> results = rules.collect { it.fits(webVisits) }
            return condition == "or" ? results.any() : results.every()
        }
        switch (field) {
            case "currentPageUrl":
                return evalCurrentPageUrl(webVisits)
            case "clientBrowsingTime":
                return evalClientBrowsingTime(webVisits)
            case "clientViewedPages":
                return evalClientViewedPages(webVisits)
            case "referringWebsiteAddress":
                return evalReferringWebsiteAddress(webVisits)
            case "anyPageUrl":
                return evalAnyPageUrl(webVisits)
            case "userConnectedCount":
                return evalConnectedUserCount()
            default:
                throw new Exception("Unknown ChatQueryBuilder.field : " + this.field)
        }
    }

    private boolean evalCurrentPageUrl(List<WebVisit> webVisits) {
        return evalString(webVisits.last().pageUrl)
    }

    private boolean evalClientBrowsingTime(List<WebVisit> webVisits) {
        int browsingTime = webVisits.collect { it.getDurationSeconds() }.sum()
        return evalInteger(browsingTime)
    }

    private boolean evalClientViewedPages(List<WebVisit> webVisits) {
        return evalInteger(webVisits.size())
    }

    private boolean evalConnectedUserCount() {
        List<Long> userList = []
        ChatWidgetGroup.getAll().each {
            userList.addAll(it.availableUserIds)
        }
        userList.unique()
        return evalInteger(userList.size())
    }

    private boolean evalReferringWebsiteAddress(List<WebVisit> webVisits) {
        return webVisits.any {
            evalString(it.referringUrl)
        }
    }

    private boolean evalAnyPageUrl(List<WebVisit> webVisits) {
        return webVisits.any {
            evalString(it.pageUrl)
        }
    }

    private boolean evalString(String s) {
        String stringValue = value.toString().toLowerCase()
        s = s.toLowerCase()
        switch (operator) {
            case "equal":
                return s == stringValue
            case "not_equal":
                return s != stringValue
            case "contains":
                return s.contains(stringValue)
            case "not_contains":
                return !s.contains(stringValue)
            default:
                throw new Exception("Unknown ChatQueryBuilder.evalString operator : " + this.operator)
        }
    }

    private boolean evalInteger(int i) {
        int integerValue = value as int
        switch (operator) {
            case "greater":
                return i > integerValue
            case "equal":
                return i == integerValue
            default:
                throw new Exception("Unknown ChatQueryBuilder.evalInteger operator : " + this.operator)
        }
    }

}
