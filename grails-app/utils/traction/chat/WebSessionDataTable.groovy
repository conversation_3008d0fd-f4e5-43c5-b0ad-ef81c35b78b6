package traction.chat

import grails.converters.JSON
import grails.gorm.PagedResultList
import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import traction.I18N
import traction.communication.CommunicationChannel
import traction.communication.CommunicationService
import traction.communication.CommunicationTagLib

class WebSessionDataTable {

    private String draw

    private WebSessionSearch webSessionSearch
    private CommunicationService communicationService
    private final static CommunicationTagLib communicationTagLib

    static {
        communicationTagLib = (CommunicationTagLib) Holders.grailsApplication.mainContext.getBean("traction.communication.CommunicationTagLib")
    }

    WebSessionDataTable(GrailsParameterMap params, CommunicationService communicationService) {
        this.draw = params.draw
        this.communicationService = communicationService
        this.webSessionSearch = new WebSessionSearch(params)
    }

    JSON render() {
        PagedResultList<WebSession> list = webSessionSearch.list()
        return [
                draw           : draw,
                recordsTotal   : webSessionSearch.getTotalCount(),
                recordsFiltered: list.getTotalCount(),
                data           : list.collect { renderWebSessionData(it) }
        ]
    }

    private Map renderWebSessionData(WebSession webSession) {
        CommunicationChannel channel = CommunicationChannel.createCriteria().get {
            eq("webSession", webSession)
            order("date", "desc")
            maxResults(1)
        }
        List lastWebVisit = webSession.getLastVisit().collect {
            [
                    dateOnline  : it.dateOnline.format(I18N.m("dateFormatWithTime")),
                    dateOffline : it.dateOffline?.format(I18N.m("dateFormatWithTime")),
                    isOnline    : it.isOnline(),
                    duration    : it.durationSeconds,
                    pageUrl     : it.pageUrl,
                    pageTitle   : it.pageTitle,
                    referringUrl: it.referringUrl
            ]
        }
        return [
                id             : webSession.id,
                client         : [
                        name  : webSession.webVisitor?.client?.toString(),
                        avatar: communicationTagLib.avatar(client: webSession.webVisitor?.client),
                ],
                actions        : "",
                status         : [
                        message: webSession.status.message,
                        color  : webSession.status.color
                ],
                chattingWith   : webSession.getChattingWith()?.fullName,
                date           : webSession.date?.format(I18N.m("dateFormatWithTime")),
                lastWebVisit   : lastWebVisit ? lastWebVisit.first() : null,
                ip             : webSession.ip,
                device         : webSession.device,
                operatingSystem: webSession.operatingSystem,
                browser        : webSession.browser,
                totalTime      : webSession.getTotalTime(),
                numberOfSession: webSession.webVisitor.getNumberOfSessions(),
                pageViewed     : webSession.getSessionVisits().size(),
                lastGreeting   : webSession.chatWidgetGreeting ? [
                        name: webSession.chatWidgetGreeting?.name,
                        text: webSession.chatWidgetGreeting?.text,
                ] : null,
                channel        : [
                        id          : channel?.id,
                        completed   : channel?.dateCompleted != null,
                        communicated: channel?.lastCommunication != null,
                        user        : channel?.user,
                        chatbox     : channel?.chatBox,
                ],
        ]
    }

}
