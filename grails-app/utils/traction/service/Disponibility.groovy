package traction.service

import org.hibernate.sql.JoinType

class Disponibility {

    List<DateRange> dateRanges = []

    Disponibility(WorkScheduleDay day, ServiceScheduleEvent event, ServiceScheduleEditSettings settings) {
        this.dateRanges = [new DateRange(day ? day.dateStart : new Date(0), day ? day.dateEnd : new Date(Long.MAX_VALUE))]
        List<ServiceScheduleEvent> fixedEvents = ServiceScheduleEvent.createCriteria().list {
            createAlias("schedule", "_schedule")
            createAlias("_schedule.labor", "_labor", JoinType.LEFT_OUTER_JOIN)
            createAlias("_labor.job", "_job", JoinType.LEFT_OUTER_JOIN)
            if (event.scheduleId) ne("_schedule.id", event.scheduleId)
            'in'("_schedule.status", ServiceSchedule.Status.getFixedList())
            eq("_schedule.resource", event.schedule.resource)
            or {
                isNull("_job.id")
                eq("_job.closed", false)
            }
            if (day) {
                or {
                    and {
                        ge("start", day.dateStart)
                        le("start", day.dateEnd)
                    }
                    and {
                        ge("end", day.dateStart)
                        le("end", day.dateEnd)
                    }
                    and {
                        lt("start", day.dateStart)
                        gt("end", day.dateEnd)
                    }
                }
            }
            else {
                gt("end", event.start - 100)
                lt("start", event.end + 100)
            }
        }
        fixedEvents.each {
            this.excludeRange(new DateRange(it.start, it.end))
        }
        if (settings.pushUpStart && settings.pushUpEnd) {
            this.excludeRange(new DateRange(settings.pushUpStart, settings.pushUpEnd))
        }
        if (settings.pushDownStart && settings.pushDownEnd) {
            this.excludeRange(new DateRange(settings.pushDownStart, settings.pushDownEnd))
        }
    }

    def excludeRange(DateRange toExclude) {
        List<DateRange> listCopy = new ArrayList<>(this.dateRanges)
        this.dateRanges.clear()
        listCopy.each { DateRange range ->
            if (range.includes(toExclude.start) && range.includes(toExclude.end)) {
                this.dateRanges.add(new DateRange(range.start, toExclude.start))
                this.dateRanges.add(new DateRange(toExclude.end, range.end))
            }
            else if (range.includes(toExclude.start)) {
                this.dateRanges.add(new DateRange(range.start, toExclude.start))
            }
            else if (range.includes(toExclude.end)) {
                this.dateRanges.add(new DateRange(toExclude.end, range.end))
            }
            else if (range.start.after(toExclude.start) && range.end.before(toExclude.end)) {
            }
            else {
                this.dateRanges.add(range)
            }
        }
        this.dateRanges.removeAll { it.getDuration() == 0 }
    }

    boolean includes(Date date) {
        return this.dateRanges.any { it.includes(date) }
    }
    
    Date getStartDate(Date end, int duration, WorkScheduleDay day, ServiceScheduleEvent event, ServiceScheduleEditSettings settings) {
        int timeToPlaceBefore = duration
        Disponibility currentDisponibility = this
        WorkScheduleDay currentDay = day
        while (timeToPlaceBefore) {
            for (DateRange dispo : currentDisponibility.dateRanges.sort {it.end }.reverse()) {
                long durationBefore = dispo.getDurationIncludedBefore(end)
                if (durationBefore >= timeToPlaceBefore) {
                    long endTime = Math.min(dispo.end.getTime(), end.getTime())
                    return new Date(endTime - timeToPlaceBefore)
                }
                timeToPlaceBefore -= durationBefore
            }
            currentDay = currentDay?.getPrevious()
            currentDisponibility = new Disponibility(currentDay, event, settings)
        }
        return end
    }

}
