package traction.service

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonToken

class LautopakFacturePiece {
    long PKMagasin
    int PKTypeVente
    long PKNoFacture
    long FKNoClient
    long FKNoCommis
    long FKNoEmploye
    double MtTotalPiece
    String Statut
    Date DateOuverture
    Date DateFermeture
    String NoSerie

    LautopakFacturePiece(JsonParser jsonParser) {
        while (jsonParser.nextToken() != JsonToken.END_OBJECT) {
            jsonParser.nextToken()
            switch (jsonParser.getCurrentName()) {
                case "PKMagasin":
                    this.PKMagasin = jsonParser.getLongValue()
                    break
                case "PKTypeVente":
                    this.PKTypeVente = jsonParser.getIntValue()
                    break
                case "PKNoFacture":
                    this.PKNoFacture = jsonParser.getLongValue()
                    break
                case "FKNoClient":
                    this.FKNoClient = jsonParser.getLongValue()
                    break
                case "FKNoCommis":
                    this.FKNoCommis = jsonParser.getLongValue()
                    break
                case "FKNoEmploye":
                    this.FKNoEmploye = jsonParser.getLongValue()
                    break
                case "MtTotalPiece":
                    this.MtTotalPiece = jsonParser.getDoubleValue()
                    break
                case "Statut":
                    this.Statut = jsonParser.getText()
                    break
                case "NoSerie":
                    this.NoSerie = jsonParser.getText()
                    break
                case "DateOuverture":
                    this.DateOuverture = LautopakJob.parseJsonDate(jsonParser.getText())
                    break
                case "DateFermeture":
                    this.DateFermeture = LautopakJob.parseJsonDate(jsonParser.getText())
                    break
            }
        }
    }
}
