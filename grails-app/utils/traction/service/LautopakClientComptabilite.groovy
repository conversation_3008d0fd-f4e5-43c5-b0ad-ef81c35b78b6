package traction.service

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonToken

class LautopakClientComptabilite {
    long PKCompagnie
    /** Numero du client */
    long PKNum
    double SoldeARecevoir

    LautopakClientComptabilite(JsonParser jsonParser) {
        while (jsonParser.nextToken() != JsonToken.END_OBJECT) {
            jsonParser.nextToken()
            switch (jsonParser.getCurrentName()) {
                case "PKCompagnie":
                    this.PKCompagnie = jsonParser.getLongValue()
                    break
                case "PKNum":
                    this.PKNum = jsonParser.getLongValue()
                    break
                case "SoldeARecevoir":
                    this.SoldeARecevoir = jsonParser.getDoubleValue()
                    break
            }
        }
    }
}
