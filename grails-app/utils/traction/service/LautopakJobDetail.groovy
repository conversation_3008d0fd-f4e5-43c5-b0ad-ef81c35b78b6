package traction.service

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonToken

class LautopakJobDetail {
    long PKFactureDetail
    long PKCompagnie
    long PKMagasin
    long PKNoFacture
    int PKNoJob
    int PKTypeVente
    long PKNoSequence
    double NBHeureVendant
    String CodeMenu
    String CodeMD
    String Description
    String Remarque
    String RemarqueCause
    String RemarqueCorrection
    Date LastUsrDate

    LautopakJobDetail(JsonParser jsonParser) {
        while (jsonParser.nextToken() != JsonToken.END_OBJECT) {
            jsonParser.nextToken()
            switch (jsonParser.getCurrentName()) {
                case "PKFactureDetail":
                    this.PKFactureDetail = jsonParser.getLongValue()
                    break
                case "PKCompagnie":
                    this.PKCompagnie = jsonParser.getLongValue()
                    break
                case "PKMagasin":
                    this.PKMagasin = jsonParser.getLongValue()
                    break
                case "PKNoSequence":
                    this.PKNoSequence = jsonParser.getLongValue()
                    break
                case "NBHeureVendant":
                    this.NBHeureVendant = jsonParser.getDoubleValue()
                    break
                case "PKNoFacture":
                    this.PKNoFacture = jsonParser.getLongValue()
                    break
                case "PKNoJob":
                    this.PKNoJob = jsonParser.getLongValue()
                    break
                case "PKTypeVente":
                    this.PKTypeVente = jsonParser.getLongValue()
                    break
                case "CodeMenu":
                    this.CodeMenu = jsonParser.getText()
                    break
                case "CodeMD":
                    this.CodeMD = jsonParser.getText()
                    break
                case "Description":
                    this.Description = jsonParser.getText()
                    break
                case "Remarque":
                    this.Remarque = jsonParser.getText()
                    break
                case "RemarqueCause":
                    this.RemarqueCause = jsonParser.getText()
                    break
                case "RemarqueCorrection":
                    this.RemarqueCorrection = jsonParser.getText()
                    break
                case "LastUsrDate":
                    this.LastUsrDate = LautopakJob.parseJsonDate(jsonParser.getText())
                    break
            }
        }
    }
}
