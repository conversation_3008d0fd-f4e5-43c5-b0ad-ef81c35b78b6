package traction.service

import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j

@Slf4j
class WorkOrderJobScheduleSearch {
    List<Map> orderList // Map=[col: "date", dir: "asc"]

    // WorkOrderJob.id
    Long jobId

    WorkOrderJobScheduleSearch(GrailsParameterMap params) {
        println "WorkOrderJobScheduleSearch(${params})"
        if (params.jobId) {
            this.jobId = params.long("jobId")
        }
        this.orderList = []
        for (int i = 0;; i++) {
            if (!params["order[${i}][column]"]) break
            String orderCol = params["columns[" + params["order[${i}][column]"] + "][data]"]
            String orderDir = params["order[${i}][dir]"]
            this.orderList.add([
                    col: orderCol,
                    dir: orderDir
            ])
        }
    }

    List<ServiceSchedule> list() {
        return ServiceSchedule.createCriteria().list {
            labor {
                job {
                    eq("id", this.jobId)
                }
            }
            if (this.orderList) {
                this.orderList.each { o ->
                    order(o.col, o.dir)
                }
            }
        }
    }
}