package traction

import grails.gorm.PagedResultList
import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j
import traction.client.Client
import traction.vehicle.Vehicle

@Slf4j
class ClientVehicleSearch {
    int max = 10
    int offset = 0
    String search
    Client client

    ClientVehicleSearch(GrailsParameterMap params) {
        log.debug("ClientVehicleSearch(${params})")
        this.client = Client.get(params.long("clientId", 0))
        this.search = params.search
        if (params.length) {
            this.max = params.length as int
        }
        if (params.start) {
            this.offset = params.start as int
        }
    }

    PagedResultList<Vehicle> list() {
        return Vehicle.createCriteria().list(max: this.max, offset: this.offset) { criteria ->
            eq("client", client)
            if (this.search) {
                String[] words
                try {
                    words = this.search.split("\\s+")
                }
                catch (Exception e) {
                    words = this.search
                }
                words.each { String word ->
                    println "word:$word"
                    String s = "%$word%"
                    Integer integerValue
                    try {
                        integerValue = Integer.valueOf(word)
                    } catch (Exception e) {
                    }
                    or {
                        if (integerValue) {
                            and { eq("year", integerValue) }
                        }
                        and { ilike("stockNumber", s) }
                        and { ilike("make", s) }
                        and { ilike("model", s) }
                        and { ilike("exteriorColor", s) }
                        and { ilike("modelCode", s) }
                        and { ilike("serialNumber", s) }
                    }
                }
            }
        } as PagedResultList<Vehicle>
    }

}
