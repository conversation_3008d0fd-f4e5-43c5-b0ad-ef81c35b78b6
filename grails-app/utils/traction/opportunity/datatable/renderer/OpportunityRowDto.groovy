package traction.opportunity.datatable.renderer

import grails.util.Holders
import groovy.time.TimeCategory
import groovy.time.TimeDuration
import traction.CardTagLib
import traction.DateUtils
import traction.I18N
import java.sql.ResultSet
import java.sql.Timestamp
import java.time.ZoneId
import java.time.ZonedDateTime

class OpportunityRowDto {

    private final static CardTagLib cardTagLib
    static {
        cardTagLib = (CardTagLib) Holders.grailsApplication.mainContext.getBean(CardTagLib.class.name)
    }

    Long id

    String name
    Integer status
    Integer category
    Long originId

    Long userSales
    Long userBdc
    Long userSales2
    Long userFni
    Long userParts
    Long userService
    Long userShipping
    Long userShop
    Long userTech

    Long clientId
    String clientFullName
    String clientEmail
    String clientPhone
    String clientPhonecell

    Integer communicationStatus
    Long vehicleId
    String vehicleFirstPictureUrl
    String vehicleMake
    String vehicleCategory
    String vehicleSubCategory
    String vehicleModel
    Integer vehicleYear
    String vehicleModelCode
    Boolean vehicleIsUsed
    Date vehicleDateReceipted

    String stocknumber
    String vehiculeIdentification
    private Date date
    private Date actDate
    private Date dateMOD
    private Date dateSOLD
    Integer activitiesSize
    Integer media
    Long origin
    Long department
    private Date userSalesNextTaskDate // nextTaskUserSales
    private Date userBdcNextTaskDate // nextTaskUserBdc
    Date lastTakeOverDate
    Date lastBriefingDate
    Date lastBumpDate
    Date dateStopCompileData
    String firstMeetingCategory
    String secondMeetingCategory
    Boolean archived
    Boolean excluded
    String rowData

    Long workflowData_workflowMaster
    Long workflowData_workflowBoard
    String workflowData_workOrder_jobs
    private Date workflowData_date
    private Date workflowData_dateStart
    private Date workflowData_datePreparation
    private Date workflowData_dateEnd
    private Date workflowData_dateParts
    private Date workflowData_lastBoardChange
    Integer workflowData_classOfService
    Integer workflowData_classOfSale
    Integer workflowData_classOfParts
    Integer workflowData_classOfShipping
    Integer workflowData_workflowTypeOfCard
    Integer workflowData_priority
    String workflowData_comments
    Long responder

    OpportunityRowDto(ResultSet rs) {
        int i = 1
        this.id = rs.getLong(i++)
        this.clientId = rs.getLong(i++)
        this.name = rs.getString(i++)
        this.category = rs.getInt(i++)
        this.status = rs.getInt(i++)
        this.originId = rs.getLong(i++)
        this.userSales = rs.getLong(i++)
        this.userBdc = rs.getLong(i++)

        this.userSales2 = rs.getLong(i++)
        this.userFni = rs.getLong(i++)
        this.userParts = rs.getLong(i++)
        this.userService = rs.getLong(i++)
        this.userShipping = rs.getLong(i++)
        this.userShop = rs.getLong(i++)
        this.userTech = rs.getLong(i++)

        this.communicationStatus = rs.getInt(i++)
        this.vehicleId = rs.getLong(i++)

        this.vehicleFirstPictureUrl = rs.getString(i++)
        this.vehicleMake = rs.getString(i++)
        this.vehicleCategory = rs.getString(i++)
        this.vehicleSubCategory = rs.getString(i++)
        this.vehicleModel = rs.getString(i++)
        this.vehicleYear = rs.getInt(i++)
        if (rs.wasNull()) {
            this.vehicleYear = null
        }
        this.vehicleModelCode = rs.getString(i++)
        this.vehicleIsUsed = rs.getBoolean(i++)
        if (rs.wasNull()) {
            this.vehicleIsUsed = null
        }
        this.vehicleDateReceipted = parseUTCDateToMtl(rs.getTimestamp(i++))

        this.stocknumber = rs.getString(i++)
        this.vehiculeIdentification = rs.getString(i++)
        this.date = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.actDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.dateMOD = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.dateSOLD = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.activitiesSize = rs.getInt(i++)
        this.media = rs.getInt(i++)
        this.origin = rs.getLong(i++)
        this.department = rs.getLong(i++)

        this.userSalesNextTaskDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.userBdcNextTaskDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.lastTakeOverDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.lastBriefingDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.lastBumpDate = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.dateStopCompileData = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.firstMeetingCategory = rs.getString(i++)
        this.secondMeetingCategory = rs.getString(i++)

        this.clientFullName = rs.getString(i++)
        this.clientEmail = rs.getString(i++)
        this.clientPhone = rs.getString(i++)
        this.clientPhonecell = rs.getString(i++)

        this.rowData = rs.getString(i++)

        this.archived = rs.getBoolean(i++)
        this.excluded = rs.getBoolean(i++)

        this.workflowData_workflowMaster = rs.getLong(i++)
        this.workflowData_workflowBoard = rs.getLong(i++)
        this.workflowData_workOrder_jobs = ""
        this.workflowData_date = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_dateStart = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_datePreparation = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_dateEnd = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_dateParts = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_lastBoardChange = parseUTCDateToMtl(rs.getTimestamp(i++))
        this.workflowData_classOfService = rs.getInt(i++)
        this.workflowData_classOfSale = rs.getInt(i++)
        this.workflowData_classOfParts = rs.getInt(i++)
        this.workflowData_classOfShipping= rs.getInt(i++)
        this.workflowData_workflowTypeOfCard = rs.getInt(i++)
        this.workflowData_priority = rs.getInt(i++)
        this.workflowData_comments = rs.getString(i++)
        this.responder = rs.getLong(i++)
    }

    Integer getVehicleDaysInStock() {
        if (!this.vehicleDateReceipted) return null
        TimeDuration duration
        use(TimeCategory) {
            duration = new Date() - this.vehicleDateReceipted
        }
        return duration.days
    }

    private Date parseUTCDateToMtl(Timestamp ts) {
        if (!ts) return null
        ZonedDateTime montrealZoned = ts.toLocalDateTime()
                .atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of("America/Montreal"))
        return Date.from(montrealZoned.toInstant())
    }

    Long getDT_RowId() {
        return id
    }

    String getClientFullName() {
        return clientFullName ?: clientEmail
    }

    String getExceedDateStopCompileData() {
        return dateStopCompileData && dateStopCompileData < new Date() ? I18N.m("opportunity.exceedDateStopCompileData", [dateStopCompileData.format("yyyy-MM-dd")] as String[]) : null
    }

    String getUserSalesNextTaskDate() { userSalesNextTaskDate?.format(I18N.m("dateFormatWithTime")) }
    String getUserSalesNextTaskDateIsLate() { userSalesNextTaskDate < new Date() }
    String getUserBdcNextTaskDate() { userBdcNextTaskDate?.format(I18N.m("dateFormatWithTime")) }
    String getUserBdcNextTaskDateIsLate() { userBdcNextTaskDate < new Date() }

    String getLastTakeOverDelai() { DateUtils.getShortDurationString(lastTakeOverDate) }
    String getLastBriefingDelai() { DateUtils.getShortDurationString(lastBriefingDate) }
    String getLastBumpDelai() { DateUtils.getShortDurationString(lastBumpDate) }

    String getDate() { date?.format(I18N.m("dateFormatWithTime")) }
    String getDateDelai() { DateUtils.getShortDurationString(date) }

    String getActDate() { actDate?.format(I18N.m("dateFormatWithTime")) }
    String getActDateDelai() { DateUtils.getShortDurationString(actDate) }

    String getDateMOD() { dateMOD?.format(I18N.m("dateFormatWithTime")) }
    String getDateMODDelai() { DateUtils.getShortDurationString(dateMOD) }

    String getDateSOLD() { dateSOLD?.format(I18N.m("dateFormatWithTime")) }
    String getDateSOLDDelai() { DateUtils.getShortDurationString(dateSOLD) }

    String getWorkflowData_date() { workflowData_date?.format(I18N.m("dateFormatWithTime")) }
    String getWorkflowData_dateDelai() { DateUtils.getShortDurationString(workflowData_date) }

    String getWorkflowData_lastBoardChange() { workflowData_lastBoardChange?.format(I18N.m("dateFormatWithTime")) }
    String getWorkflowData_lastBoardChangeDelai() { DateUtils.getShortDurationString(workflowData_lastBoardChange) }

    String getWorkflowData_dateStart() { workflowData_dateStart?.format(I18N.m("dateFormatWithTime")) }

    String getWorkflowData_datePreparation() { workflowData_datePreparation?.format(I18N.m("dateFormatWithTime")) }

    String getWorkflowData_dateEnd() { workflowData_dateEnd?.format(I18N.m("dateFormatWithTime")) }

    String getWorkflowData_dateParts() { workflowData_dateParts?.format(I18N.m("dateFormatWithTime")) }

    String getUrlWorkflow() {
        return workflowData_workflowMaster ? "/traction/workflow/index/${workflowData_workflowMaster}?opportunityId=${id}" : null
    }

    String getOpportunitycard() {
        return cardTagLib.cardFromId(id: id)
    }

    String getClientUrl() {
        return "/traction/client/index/${clientId}#sales"
    }

    static String getSelectSql(String idsString) {
        return """
SELECT 
    opp.id,
    opp.client_id, 
    opp.name,
    opp.category,
    opp.status,
    opp.origin_id,
    opp.user_sales_id,
    opp.user_bdc_id,

    _compiledData.user_sales2id,
    _compiledData.user_fni_id,
    _compiledData.user_parts_id,
    _compiledData.user_service_id,
    _compiledData.user_shipping_id,
    _compiledData.user_shop_id,
    _compiledData.user_tech_id,

    _compiledData.communication_status,
    opp.vehicle_instance_id AS vehicle_id,
    _compiledData.vehicle_first_picture_url,
    _compiledData.vehicle_make,
    _compiledData.vehicle_category,
    _compiledData.vehicle_sub_category,
    _compiledData.vehicle_model,
    _compiledData.vehicle_year,
    _compiledData.vehicle_model_code,
    _compiledData.vehicle_is_used,
    _compiledData.vehicle_date_receipted,

    opp.stocknumber,
    opp.vehicule_identification,
    opp.date,
    opp.act_date,
    opp.dateMOD,
    opp.dateSOLD,
    opp.activities_size,
    opp.media,
    opp.origin_id,
    opp.department_id,

    _compiledData.user_sales_next_task_date,
    _compiledData.user_bdc_next_task_date,
    _compiledData.last_take_over_date,
    _compiledData.last_briefing_date,
    _compiledData.last_bump_date,
    _compiledData.date_stop_compile_data,
    _compiledData.first_meeting_category,
    _compiledData.second_meeting_category,

    -- Nouveaux champs client
    _compiledData.client_full_name,
    _compiledData.client_email,
    _compiledData.client_phone,
    _compiledData.client_phonecell,

    _compiledData.row_data, 

    opp.archived,
    opp.excluded,

    _workflow_board.workflow_master_id,
    _workflow_data.workflow_board_id,
    _workflow_data.date,
    _workflow_data.date_start,
    _workflow_data.date_preparation,
    _workflow_data.date_end,
    _workflow_data.date_parts,
    _workflow_data.last_board_change,
    _workflow_data.class_of_service,
    _workflow_data.class_of_sale,
    _workflow_data.class_of_parts,
    _workflow_data.class_of_shipping,
    _workflow_data.type_of_card,
    _workflow_data.priority,
    _workflow_data.comments,
    opp.responder_id
FROM 
    opportunity opp
LEFT JOIN
    opportunity_compiled_data _compiledData ON opp.compiled_data_id = _compiledData.id 
LEFT JOIN
    workflow_data _workflow_data ON opp.workflow_data_id = _workflow_data.id 
LEFT JOIN
    workflow_board _workflow_board ON _workflow_data.workflow_board_id = _workflow_board.id 
WHERE 
    opp.id IN (${idsString})
"""
    }
}
