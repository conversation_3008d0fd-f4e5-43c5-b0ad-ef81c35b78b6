package traction.opportunity.datatable.renderer

import traction.communication.*
import traction.communication.email.EmailUtils
import traction.opportunity.Opportunity

class OpportunityContactItems {

    List<OpportunityContactItem> salesOutbound
    List<OpportunityContactItem> salesInbound
    List<OpportunityContactItem> bdcOutbound
    List<OpportunityContactItem> bdcInbound

    OpportunityContactItems(Opportunity opportunity) {
        List<String> availableContactMethods = [CommunicationMeeting.class.name]
        if (EmailUtils.isValid(opportunity.client.email)) {
            availableContactMethods.add(MailMessage.class.name)
        }
        if (opportunity.client.asIndividualClient()?.phonecell) {
            availableContactMethods.add(SmsMessage.class.name)
            availableContactMethods.add(CallData.class.name)
        } else if (opportunity.client.phone) {
            availableContactMethods.add(CallData.class.name)
        }
        if (opportunity.client.facebookClients) {
            availableContactMethods.add(FacebookMessage.class.name)
        }
        Date minDate = opportunity.getCommunicationsMinDate()
        Date maxDate = opportunity.compiledData.dateStopCompileData
        List<List> communicationData = Communication.createCriteria().list {
            eq("completed", true)
            eq("client", opportunity.client)
            'in'("user", [opportunity.userSales, opportunity.userBdc])
            if (minDate) ge("date", minDate)
            if (maxDate) le("date", maxDate)
            projections {
                groupProperty("outbound")
                groupProperty("user.id")
                groupProperty("class")
                max("date")
                rowCount()
            }
        }

        this.salesOutbound = createOpportunityContactItemList(communicationData, availableContactMethods, opportunity.userSalesId, true)
        this.salesInbound = createOpportunityContactItemList(communicationData, availableContactMethods, opportunity.userSalesId, false)

        this.bdcOutbound = createOpportunityContactItemList(communicationData, availableContactMethods, opportunity.userBdcId, false)
        this.bdcInbound = createOpportunityContactItemList(communicationData, availableContactMethods, opportunity.userBdcId, false)

        updateLastCommunicationDate(salesInbound + salesOutbound)
        updateLastCommunicationDate(bdcInbound + bdcOutbound)
    }

    private List<OpportunityContactItem> createOpportunityContactItemList(List<List> communicationData, List<String> availableContactMethods, Long userId, boolean outbound) {
        List<OpportunityContactItem> items = []
        Communication.getContactSubTypes().each { String className ->
            List data = communicationData.find {
                it[0] == outbound && it[1] == userId && it[2] == className
            }
            items.add(
                    new OpportunityContactItem(
                            className: className,
                            available: availableContactMethods.contains(className),
                            lastCommunicationDate: data ? data[3] : null,
                            count: data ? data[4] : 0
                    )
            )
        }
        return items
    }

    private void updateLastCommunicationDate(List<OpportunityContactItem> items) {
        Date latestCommunicationDate = items.lastCommunicationDate.max()
        boolean latestFound = false
        items.each {
            if (latestFound || it.lastCommunicationDate != latestCommunicationDate) {
                it.lastCommunicationDate = null
            } else {
                latestFound = true
            }
        }
    }
}
