package traction.opportunity.datatable.renderer

import grails.converters.JSON
import traction.ExecutionTime
import traction.communication.Communication
import traction.opportunity.Opportunity

class OpportunityRowDisplayData {

    Map<String, Integer> clientSalesOpportunities = [:] // key=Opportunity.Category.name(), value=count
    Map<String, Integer> vehicleOpportunities = [
            sold: 0,
            active: 0,
            other: 0
    ]

    List<OpportunityContactItem> salesOutbound = []
    List<OpportunityContactItem> salesInbound = []

    List<OpportunityContactItem> bdcOutbound = []
    List<OpportunityContactItem> bdcInbound = []

    String toJsonString() {
        return (this as JSON).toString()
    }

    void addToVehicleOpportunities(Opportunity.Status oppStatus, Long count) {
        if (!oppStatus || !count) return
        switch (oppStatus.category) {
            case Opportunity.Category.SOLD:
                if (oppStatus.isLostSold()) {
                    vehicleOpportunities.other += count
                } else {
                    vehicleOpportunities.sold += count
                }
                break
            case Opportunity.Category.ACTIVE:
                vehicleOpportunities.active += count
                break
            default:
                vehicleOpportunities.other += count
        }
    }

    static create(Opportunity opportunity) {
        List<Opportunity.Category> clientSalesOpportunitiesList = Opportunity.createCriteria().list {
            eq("type", Opportunity.Type.SALE)
            eq("client", opportunity.client)
            projections {
                property("category")
            }
        }
        OpportunityContactItems items = new OpportunityContactItems(opportunity)
        OpportunityRowDisplayData opportunityRowDisplayData = new OpportunityRowDisplayData(
                clientSalesOpportunities: clientSalesOpportunitiesList.countBy { it.message },
                salesOutbound: items.salesOutbound,
                salesInbound: items.salesInbound,
                bdcOutbound: items.bdcOutbound,
                bdcInbound: items.bdcInbound
        )
        List<Opportunity.Status> vehicleOpportunities = opportunity.stocknumber ? Opportunity.createCriteria().list {
            eq("type", Opportunity.Type.SALE)
            eq("stocknumber", opportunity.stocknumber)
            projections {
                property("status")
            }
        } : []
        vehicleOpportunities.each {
            opportunityRowDisplayData.addToVehicleOpportunities(it, 1)
        }
        return opportunityRowDisplayData
    }
}
