package traction.opportunity.datatable

import grails.converters.JSON
import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import traction.CardTagLib
import traction.communication.CommunicationTagLib
import traction.datatable.DataTableColumnFilters
import traction.datatable.DataTablePaging
import traction.opportunity.Opportunity
import traction.client.Client
import traction.vehicle.Vehicle

class OpportunityClientVehicleDataTable {

    DataTablePaging dataTablePaging
    DataTableColumnFilters filters
    Client client
    Vehicle vehicle
    String search
    private CardTagLib cardTagLib
    private CommunicationTagLib communicationTagLib
    String draw


    OpportunityClientVehicleDataTable(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.filters = new DataTableColumnFilters(params.filter)
        this.search = params["search[value]"]
        this.draw = params.draw
        this.client = Client.get(params.long("clientId", 0))
        this.vehicle = Vehicle.get(params.long("vehicleId", 0))
        this.cardTagLib = Holders.grailsApplication.mainContext.getBean("traction.CardTagLib")
        this.communicationTagLib = Holders.grailsApplication.mainContext.getBean("traction.communication.CommunicationTagLib")
    }

    JSON render() {
        List<Opportunity> opportunities = get()
        Map json = [
                draw           : draw,
                recordsTotal   : countTotalRecords(),
                recordsFiltered: countTotalRecords(),
                data           : opportunities.collect { format(it) }
        ]
        return json as JSON
    }

    private Map format(Opportunity opportunity) {
        return [
                opportunity  : opportunity,
                cardDatatable: cardTagLib.card2(opportunity: opportunity),
        ]
    }


    private List<Opportunity> get() {
        if (this.client || this.vehicle) {
            return Opportunity.createCriteria().list(max: this.dataTablePaging.max, offset: this.dataTablePaging.offset) {
                if (this.client) eq("client", this.client)
                if (this.vehicle) eq("vehicleInstance", this.vehicle)
                eq("type", Opportunity.Type.SALE)
                order("dateMOD", "desc")
                if (filters.getMinMaxDate("date")) {
                    filters.getMinMaxDate("date").addToCriteriaBuilder(delegate)
                }
                or {
                    if (filters.getBoolean("opportunity")) {
                        isEmpty("cotationElements")
                    }
                    if (filters.getBoolean("cotation")) {
                        isNotEmpty("cotationElements")
                    }
                }
                or {
                    if (filters.getList("category").size() > 0) {

                        'in'('category', filters.getList("category").collect{Opportunity.Category.getById(Integer.parseInt(it.toString()))})
                    }
                    if (filters.getLong("status")) {
                        eq('status', Opportunity.Status.getById(filters.getInteger("status")))
                    }
                    if (filters.getBoolean("excluded")) {
                        eq('excluded', filters.getBoolean("excluded"))
                    }
                    if (filters.getBoolean("archived")) {
                        eq('archived', filters.getBoolean("archived"))
                    }
                }
            }
        }

        return []
    }

    int countTotalRecords() {
        return  Opportunity.createCriteria().count(){
            if (this.client) eq("client", this.client)
            if(this.vehicle) eq("vehicleInstance", this.vehicle)
            eq("type", Opportunity.Type.SALE)
        }
    }
}