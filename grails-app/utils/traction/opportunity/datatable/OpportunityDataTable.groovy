package traction.opportunity.datatable

import grails.converters.JSON
import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j
import org.grails.datastore.mapping.query.api.Criteria
import traction.*
import traction.datatable.DataTablePaging
import traction.opportunity.OpportunitySearch
import traction.opportunity.datatable.renderer.OpportunityDataTableRenderer
import traction.opportunity.datatable.renderer.OpportunityRowDto
import traction.opportunity.datatable.stats.StatisticDataTableAbstract
import traction.opportunity.datatable.stats.StatisticDataTableInStore
import traction.opportunity.datatable.stats.StatisticDataTablePhoneUp
import traction.opportunity.datatable.stats.StatisticDataTableSalesman
import traction.opportunity.datatable.stats.StatisticDataTableWeb
import traction.opportunity.datatable.stats.StatisticDataTableAllMedia
import traction.statistics.CriteriaAppender
import traction.opportunity.Opportunity
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.ResultSet

@Slf4j
class OpportunityDataTable {

    private Connection connection
    private OpportunityDataTable.Tabs activeTab
    private List<Long> idsList
    private OpportunitySearch opportunitySearch
    private String draw
    private boolean gridMode
    private StatisticDataTableAbstract statisticDataTable
    private DataTablePaging dataTablePaging
    private CardTagLib cardTagLib = Holders.grailsApplication.mainContext.getBean(CardTagLib.class.name)

    OpportunityDataTable(Connection connection, GrailsParameterMap params) {
        ExecutionTime executionTime = new ExecutionTime()
        this.connection = connection
        this.draw = params.draw
        this.gridMode = params.boolean("gridMode", false)
        this.activeTab = OpportunityDataTable.Tabs.getTabByName(params.activeTab)
        this.statisticDataTable = activeTab.createStatisticDataTable(params)
        this.dataTablePaging = new DataTablePaging(params)
        this.opportunitySearch = new OpportunitySearch(params, this.statisticDataTable.topStatistics)
        this.idsList = opportunitySearch.listIds()
        log.debug "Collected ${idsList.size()} ids: ${executionTime.restart()}ms"
        if (params.boolean("addStatisticsDataTable", false)) {
            this.statisticDataTable.setIdsAndLoadData(connection, this.opportunitySearch, this.idsList)
            log.debug "setIdsAndLoadData: ${executionTime.restart()}ms"
        }
    }

    List<Long> getPagedIds() {
        if (idsList.size() < this.dataTablePaging.offset) return []
        int toIndex = this.dataTablePaging.offset + this.dataTablePaging.max
        return this.idsList.subList(this.dataTablePaging.offset, idsList.size() < toIndex || toIndex == -1 ? idsList.size() : toIndex)
    }

    JSON render() {
        ExecutionTime executionTime = new ExecutionTime()
        Map json = [
                draw           : draw,
                recordsTotal   : idsList.size(),
                recordsFiltered: idsList.size(),
                data           : this.gridMode ? [] : getData(),
                gridModeCards  : gridMode ? cardTagLib.cardList(opportunities: getPagedIds().collect {Opportunity.get(it) }) : "",
                statisticDataTable: statisticDataTable?.render()
        ]
        log.debug "render: ${executionTime.executionTime()}ms"
        return json as JSON
    }

    Vector<Map> getData() {
        OpportunityDataTableRenderer renderer = new OpportunityDataTableRenderer(connection, getPagedIds())
        return renderer.getData()
    }

    enum Tabs {
        ALL("leads.all", "GROUPED_STATISTIC_ALL_COL_DEF", "/leads/thead/ALL", null),
        WEB(GroupMedia.WEB.message, "GROUPED_STATISTIC_WEB_COL_DEF", "/leads/thead/WEB", new CriteriaAppender() {
            @Override
            void appendTo(Criteria builder) {
                builder.eq("_compiledData.web", 1)
            }
        }),
        PHONE_UP(GroupMedia.PHONE_UP.message, "GROUPED_STATISTIC_PHONE_UP_COL_DEF", "/leads/thead/PHONE_UP", new CriteriaAppender(){
            @Override
            void appendTo(Criteria builder) {
                builder.eq("_compiledData.phoneUp", 1)
            }
        }),
        IN_STORE(GroupMedia.IN_STORE.message, "GROUPED_STATISTIC_IN_STORE_COL_DEF", "/leads/thead/IN_STORE", new CriteriaAppender(){
            @Override
            void appendTo(Criteria builder) {
                builder.eq("_compiledData.inStoreOrWithMeeting", 1)
            }
        }),
        SALESMAN("leads.salesman", null, null, null)

        final String message
        final String groupedStatisticColumnDef
        final String groupedStatisticTableTemplate
        final CriteriaAppender filter

        Tabs(String message, String groupedStatisticColumnDef, String groupedStatisticTableTemplate, CriteriaAppender filter) {
            this.message = message
            this.groupedStatisticColumnDef = groupedStatisticColumnDef
            this.groupedStatisticTableTemplate = groupedStatisticTableTemplate
            this.filter = filter
        }

        static Tabs getTabByName(String name) {
            for (Tabs tab : Tabs.values()) {
                if (tab.name() == name) {
                    return tab
                }
            }
            return ALL
        }

        StatisticDataTableAbstract createStatisticDataTable(GrailsParameterMap params) {
            switch (this) {
                case WEB:
                    return new StatisticDataTableWeb(params)
                case PHONE_UP:
                    return new StatisticDataTablePhoneUp(params)
                case IN_STORE:
                    return new StatisticDataTableInStore(params)
                case SALESMAN:
                    return new StatisticDataTableSalesman(params)
            }
            return new StatisticDataTableAllMedia(params)
        }

    }
}