package traction.opportunity.datatable

import grails.converters.JSON
import grails.web.servlet.mvc.GrailsParameterMap
import org.apache.commons.lang3.StringUtils
import traction.datatable.DataTableOrdering
import traction.datatable.DataTablePaging
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityOption
import traction.vehicle.VehicleOption
import traction.vehicle.Vehicle
import traction.I18N

class OpportunityAccessoriesDataTable {

    DataTablePaging dataTablePaging
    DataTableOrdering dataTableOrdering
    Opportunity opportunity
    String search
    String draw

    OpportunityAccessoriesDataTable(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.dataTableOrdering = new DataTableOrdering(params)
        this.search = params["search[value]"]
        this.draw = params.draw
        this.opportunity = Opportunity.get(params.long('opportunityId'))
    }

    JSON render() {
        List<OpportunityOption> accessories = get()
        List vehicleOptions = getVehicleOptions()

        List allData = []
        // Vehicle options/accessories
        allData.addAll(vehicleOptions.collect { formatVehicleOption(it) })

        // Opportunity accessories
        allData.addAll(accessories.collect { format(it) })

        Map json = [
                draw           : draw,
                recordsTotal   : countTotalRecords(),
                recordsFiltered: countTotalRecords(),
                data           : allData
        ]

        return json as JSON
    }

    private static Map format(OpportunityOption accessory) {
        return [
                id             : accessory.id,
                picture        : [opportunityId: accessory.opportunity.id, clientId: accessory.opportunity.clientId, fileDatas: accessory.fileDatas],
                name           : accessory.name,
                description    : accessory.description,
                sellingPrice   : accessory.sellingPrice,
                costingPrice   : accessory.costingPrice,
                labourPrice    : accessory.labourPrice,
                time           : accessory.time,
                discount       : accessory.discount,
                optionNumber   : accessory.optionNumber,
                providerId     : accessory.providerId,
                quantity       : accessory.quantity,
                isVehicleOption: false
        ]
    }

    private static Map formatVehicleOption(Map vehicleOption) {
        return [
                id             : "vehicle_option_${vehicleOption.id}",
                picture        : [:],
                name           : vehicleOption.name,
                description    : "",
                sellingPrice   : vehicleOption.price ? vehicleOption.price + "\$" : "-",
                costingPrice   : "-",
                labourPrice    : "-",
                time           : "-",
                discount       : "-",
                optionNumber   : "-",
                providerId     : "-",
                quantity       : "opportunity.vehicle.options.included",
                isVehicleOption: true
        ]
    }


    private List<OpportunityOption> get() {
        if (this.opportunity) {

            // I want to collect opportunity.options
            List<OpportunityOption> opportunityOptionList = OpportunityOption.createCriteria().list(max: this.dataTablePaging.max, offset: this.dataTablePaging.offset) {
                eq("opportunity", this.opportunity)

                dataTableOrdering.addToCriteriaBuilder(delegate, [opportunity: "name"])
            }

            return opportunityOptionList
        }
        return []
    }

    private List getVehicleOptions() {
        if (this.opportunity?.vehicle?.id && this.opportunity?.vehicle?.options) {
//            return VehicleOption.findAllByVehicle(
//                    Vehicle.get(this.opportunity.getVehicleId()),
//                    [sort: 'ordre', order: 'asc']
//            )
            return this.opportunity.vehicle.options
                    .split(",")
                    .collect { String opt ->
                        def name = opt.lastIndexOf("(") > 0 ? opt.substring(0, opt.lastIndexOf("(")).trim() : opt.trim()
                        def price = StringUtils.substringBetween(opt, "(", ")")?.trim() ?: ""
                        return [id: "preInstalled", name: name, price: price]
                    }
        }
        return []
    }

    int countTotalRecords() {
        int opportunityOptionsCount = this.opportunity ? this.opportunity.options.size() : 0
        int vehicleOptionsCount = getVehicleOptions().size()
        return opportunityOptionsCount + vehicleOptionsCount
    }
}
