package traction.opportunity.datatable.stats

import grails.web.servlet.mvc.GrailsParameterMap
import traction.statistics.Card
import traction.statistics.CardRow
import traction.statistics.StatisticCard
import traction.statistics.TopStatistics

class StatisticDataTablePhoneUp extends StatisticDataTableAbstract {

    StatisticDataTablePhoneUp(GrailsParameterMap params) {
        super(params)
        this.topStatistics = new TopStatistics(
                activeFilterCardIds: params.list("topStatisticsFilters[]"),
                cards: [
                        [
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_phoneUp",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                )
                                        ]),
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_withTake",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.withTakeOver],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                )
                                        ]),
                                ])

                        ],
                        [
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_phoneUpCategorySold",
                                                        messageEmphasis: true,
                                                        icon: "fa-solid fa-phone text-info",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.phoneUpCategorySold],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_phone_up_average_duration",
                                                        messageEmphasis: true,
                                                        icon: "fa-solid fa-timer text-info",
                                                        data: phoneUpAverageDuration
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_withAppointment",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.withAppointment],
                                                        messageEmphasis: true,
                                                        icon: "fa-solid fa-calendar-day text-info",
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_delivered",
                                                        messageEmphasis: true,
                                                        icon: "fa fa-truck-fast text-accent",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.delivered],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                ),
                                        ])
                                ])
                        ]
                ]
        )
    }

}
