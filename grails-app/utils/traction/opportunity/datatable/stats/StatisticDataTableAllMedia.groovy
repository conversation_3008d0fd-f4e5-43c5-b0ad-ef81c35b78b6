package traction.opportunity.datatable.stats

import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j
import traction.statistics.Card
import traction.statistics.CardRow
import traction.statistics.StatisticCard
import traction.statistics.TopStatistics

@Slf4j
class StatisticDataTableAllMedia extends StatisticDataTableAbstract {

    StatisticDataTableAllMedia(GrailsParameterMap params) {
        super(params)
        this.topStatistics = new TopStatistics(
                activeFilterCardIds: params.list("topStatisticsFilters[]"),
                cards: [
                        [
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_total",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_web",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.web],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_inStore",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.inStore],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_phoneUp",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.phoneUp],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_withTaskTodo",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.withTaskTodo],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_withTaskLate",
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.withTaskLate],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                        ],
                        [
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_categorySold",
                                                        icon: "fa fa-cart-shopping text-info",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.categorySold],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_webCategorySold",
                                                        icon: "fa fa-globe text-info",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.webCategorySold],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.web]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_inStoreCategorySold",
                                                        icon: "fa fa-shop text-info",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.inStoreCategorySold],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.inStore]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_phoneUpCategorySold",
                                                        icon: "fa fa-phone text-info",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.phoneUpCategorySold],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.phoneUp]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_withAppointment",
                                                        icon: "fa fa-calendar-day text-info",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.withAppointment],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.total]
                                                ),
                                        ])
                                ]),
                                new Card([
                                        new CardRow([
                                                new StatisticCard(
                                                        id: "CARD_delivered",
                                                        icon: "fa fa-truck-fast text-accent",
                                                        messageEmphasis: true,
                                                        data: topStatisticDatasMap[CompiledDataSumProperty.delivered],
                                                        denominator: topStatisticDatasMap[CompiledDataSumProperty.categorySold]
                                                ),
                                        ])
                                ]),
                        ]
                ]
        )
    }

}