package traction.opportunity.datatable.stats

import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j
import org.grails.datastore.mapping.query.api.Criteria
import traction.DateUtils
import traction.ExecutionTime
import traction.I18N
import traction.VisitLog
import traction.communication.CallData
import traction.goal.DailyUserData
import traction.opportunity.Opportunity
import traction.opportunity.OpportunitySearch
import traction.statistics.CriteriaAppender
import traction.statistics.GroupedStatisticDataTable
import traction.statistics.StatisticData
import traction.statistics.StatisticRows
import traction.statistics.StatisticsTagLib
import traction.statistics.TopStatistics
import java.sql.Connection

@Slf4j
abstract class StatisticDataTableAbstract {

    enum CompiledDataSumProperty {
        total,
        createdLast7Days,
        web,
        inStore,
        inStoreOrWithMeeting,
        phoneUp,
        webCategorySold,
        inStoreCategorySold,
        phoneUpCategorySold,
        withTaskTodo,
        withTask<PERSON>ate,
        categorySold,
        withAppointment,
        withAppointmentLate,
        withAppointmentCancelOrNoShow,
        withAppointmentTodo,
        delivered,
        deliveryThisMonth,
        categorySoldWithoutDeliveryDate,
        deliveryScheduled,
        webOnlyCommunicationStatusUserNoContact,
        communicationStatusUserNoContact,
        communicationStatusUserAttempt,
        communicationStatusClientReached,
        communicationStatusClientPending,
        withAppointmentTodoNext7d,
        withFirstMeeting,
        withTakeOver,
        withoutMeeting, // Not identified
        withFirstMeetingWalkin,
        withFirstMeetingWalkinCategorySold,
        withFirstMeetingAppointment,
        withFirstMeetingAppointmentCategorySold,
        withSecondMeeting,
        withSecondMeetingCategorySold,
        outboundCommunicationsCount,
        verbalCommunicationsCount,
        categoryActive,
        categoryActiveWithoutTasks,
        categoryActiveWithoutContactLast30,
        firstResponseTime,
        firstResponseTimeCount
    }

    private final static StatisticsTagLib statisticsTagLib

    static {
        statisticsTagLib = (StatisticsTagLib) Holders.grailsApplication.mainContext.getBean("traction.statistics.StatisticsTagLib")
    }

    GroupedStatisticDataTable groupedStatisticDataTable
    List<Long> idsList
    OpportunitySearch opportunitySearch

    private Connection connection
    TopStatistics topStatistics
    OpportunityGroupedStatisticDataTableFilters groupedStatisticDataTableFilters

    // TODO: Prends 50ms?
    Map<CompiledDataSumProperty, StatisticData> topStatisticDatasMap = CompiledDataSumProperty.values().collectEntries {
        [(it): createSumStatisticData(it)]
    }

    StatisticDataTableAbstract(GrailsParameterMap params) {
        this.opportunitySearch = opportunitySearch
        this.idsList = idsList
        this.groupedStatisticDataTableFilters = new OpportunityGroupedStatisticDataTableFilters(params.groupedStatisticDataTableFilters)
        this.groupedStatisticDataTable = new GroupedStatisticDataTable(groupProperty1, groupProperty2, CompiledDataSumProperty.values().collect { it.name() })
    }

    void setIdsAndLoadData(Connection connection, OpportunitySearch opportunitySearch, List<Long> idsList) {
        this.connection = connection
        this.opportunitySearch = opportunitySearch
        this.idsList = idsList
        loadCommonData()
    }

    String getGroupProperty1() {
        return groupedStatisticDataTableFilters.groupProperty1
    }

    String getGroupProperty2() {
        return groupedStatisticDataTableFilters.groupProperty2
    }

    Map render() {
        return [
                data         : groupedStatisticDataTable.getData(),
                topStatistics: statisticsTagLib.topStatistics(topStatistics: topStatistics)
        ]
    }

    private void loadCommonData() {
        ExecutionTime executionTime = new ExecutionTime()
        List data = Opportunity.createCriteria().list {
            'in'("id", idsList)
            eq("excluded", false)
            createAlias("compiledData", "_compiledData")
            if (groupProperty1.startsWith("vehicle.") || groupProperty2.startsWith("vehicle.")) {
                createAlias("vehicleInstance", "vehicle")
            }
            projections {
                groupProperty(groupProperty1)
                groupProperty(groupProperty2)
                CompiledDataSumProperty.values().each {
                    sum("_compiledData.${it.name()}")
                }
            }
        }
        println "loadCommonData data:${executionTime.restart()}"
        data.each {
            //println("data.each:$it")
            StatisticRows rows = groupedStatisticDataTable.getRows(it[0], it[1])
            CompiledDataSumProperty.values().eachWithIndex { sumProperty, int index ->
                Integer sum = it[2 + index]
                if (sum) {
                    addToTopStatisticsSums(sumProperty, sum)
                    rows.add(sumProperty.name(), sum)
                }
            }
        }
        println "loadCommonData process:${executionTime.restart()}" // TODO: optimiser 100 ms pour 100 lignes et 200ms pour ++ lignes
        ExecutionTime executionTime1 = new ExecutionTime()
        pool.add(getNoAssignedOpportunitiesCount())
        println "pool getNoAssignedOpportunitiesCount:${executionTime.restart()}"
        todayPoints.setValueString(getPointsCount(DateUtils.getToday(), DateUtils.getTomorrow()))
        println "todayPoints getPointsCount:${executionTime.restart()}"
        phoneUpAverageDuration.setValueString(DateUtils.getMinuteSecondsDurationString(getAverageCallDurationSeconds() * 1000))
        println "phoneUpAverageDuration getAverageCallDurationSeconds:${executionTime.restart()}"
        visitLog.add(getVisitLogCount())
        int firstResponseTimeCount = topStatisticDatasMap[CompiledDataSumProperty.firstResponseTimeCount].value
        int averageFirstResponseValue = firstResponseTimeCount ? topStatisticDatasMap[CompiledDataSumProperty.firstResponseTime].value / firstResponseTimeCount : 0
        averageFirstResponse.setValueString(DateUtils.getHourMinutesDurationString(averageFirstResponseValue))
        println "visitLog getVisitLogCount:${executionTime.restart()}"

        println("RANDOM STATS TIME:" + executionTime1.executionTime())

        /*
        //TODO: pt un 500ms a aller chercher ici quand beaucoup de resultats
        String idsString = idsList.join(",") // for the IN clause

        String groupBy1 = "opp.${VehicleFormulaExpressionUtil.camelCaseToSnakeCase(groupProperty1)}"
        String groupBy2 = "opp.${VehicleFormulaExpressionUtil.camelCaseToSnakeCase(groupProperty2)}"

        String sumFields = CompiledDataSumProperty.values().collect {
            it == CompiledDataSumProperty.total ?
                    "COUNT(*) AS total" :
                    "SUM(_compiledData.${VehicleFormulaExpressionUtil.camelCaseToSnakeCase(it.name())}) AS ${it.name()}"
        }.join(", ")

        String sql = """
    SELECT
        ${groupBy1},
        ${groupBy2},
        ${sumFields}
    FROM opportunity opp
    LEFT JOIN opportunity_compiled_data _compiledData
        ON opp.compiled_data_id = _compiledData.id
    WHERE opp.id IN (${idsString})
      AND opp.excluded = FALSE
    GROUP BY ${groupBy1}, ${groupBy2}
"""
        Statement stmt = connection.createStatement()
        ResultSet rs = stmt.executeQuery(sql)

        while (rs.next()) {
            StatisticRows rows = groupedStatisticDataTable.getRows(rs.getObject(1), rs.getObject(2))

            CompiledDataSumProperty.values().eachWithIndex { sumProperty, int index ->
                int sum = rs.getObject(3 + index)
                addToTopStatisticsSums(sumProperty, sum)
                rows.add(sumProperty.name(), sum)
            }
        }
        rs.close()
        stmt.close()

        println "ExecutionTime:${executionTime.restart()}"
*/
    }

    private void addToTopStatisticsSums(CompiledDataSumProperty field, def sum) {
        topStatisticDatasMap[field].add(sum)
    }

    final StatisticData pool = new StatisticData(
            message: I18N.m("leads.topStat.pool"),
            tooltip: I18N.m("leads.topStat.pool.tooltip"),
            modal: "/opportunity/modalPool"
    )

    // TODO: calcul
    final StatisticData averageFirstResponse = new StatisticData(
            message: I18N.m("leads.topStat.averageFirstResponse"),
            tooltip: I18N.m("leads.topStat.averageFirstResponse.tooltip"),
            valueString: "--"
    )

    final StatisticData todayPoints = new StatisticData(
            message: I18N.m("leads.topStat.pointsToday")
    )

    final StatisticData phoneUpAverageDuration = new StatisticData(
            message: I18N.m("leads.topStat.averageDuration"),
            tooltip: I18N.m("leads.topStat.averageDuration.tooltip")
    )

    final StatisticData visitLog = new StatisticData(
            message: I18N.m("leads.topStat.visitLog"),
            tooltip: I18N.m("leads.topStat.visitLog.tooltip"),
            href: "/visitLog/index"
    )

    static StatisticData createSumStatisticData(CompiledDataSumProperty sumProperty) {
        return new StatisticData(
                message: I18N.m("OpportunityCompiledData.top.${sumProperty.name()}"),
                tooltip: I18N.m("OpportunityCompiledData.top.${sumProperty.name()}.tooltip"),
                filter: new CriteriaAppender() {
                    @Override
                    void appendTo(Criteria builder) {
                        builder.eq("_compiledData.${sumProperty.name()}", 1)
                    }
                }
        )
    }

    // TODO SLOW: 1s
    // TODO: voir si c'est bon pour quand on a pas le filtre du user?
    Integer getAverageCallDurationSeconds() {
        return CallData.createCriteria().get {
            if (opportunitySearch.user) eq("user", opportunitySearch.user)
            if (opportunitySearch.date) opportunitySearch.date.addToCriteriaBuilder(delegate)
            projections {
                avg("duration")
            }
        } ?: 0
    }

    // TODO: fix speed
    private int getNoAssignedOpportunitiesCount() {
        def opportunityService = Holders.grailsApplication.mainContext.getBean("opportunityService")
        return opportunityService.getNoAssignedOpportunities(null, true)
    }

    private String getPointsCount(Date min, Date max) {
        return opportunitySearch.user ? DailyUserData.createCriteria().get {
            ge("date", min)
            lt("date", max)
            eq("user", opportunitySearch.user)
            projections {
                sum("points")
            }
        } : "--"
    }
    // TODO: Pt pas super fonctionnel avec le filtre du user. Je ne suis pas sur quel info on veut mettre ou si le href sur la topstat est correct
    private int getVisitLogCount() {
        return VisitLog.createCriteria().count {
            eq("category", VisitLog.Category.NEW)
            if (opportunitySearch.user) {
                or {
                    eq("user", opportunitySearch.user)
                    if (opportunitySearch.user.departmentGroups) {
                        and {
                            isNull("user")
                            'in'("departmentGroup", opportunitySearch.user.departmentGroups)
                        }
                    }
                }
            }
        }
    }
}
