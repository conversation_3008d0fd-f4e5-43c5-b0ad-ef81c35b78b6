package traction.opportunity


import traction.communication.Communication
import traction.history.History

class AutoActiveStatusUtils {

    static List<History.Status> IN_PERSON_MEETING_HISTORIES = [
            History.Status.APPOINTMENT,
            History.Status.WALK_IN
    ]

    static boolean set(Opportunity opportunity) {
        Opportunity.Status before = opportunity.status
        if (opportunity.isService() && !opportunity.archived) {
            opportunity.status = Opportunity.Status.NO_APPOINTMENT
            opportunity.category = Opportunity.Category.ACTIVE
        } else if (opportunity.isActive()) {
            opportunity.status = getForSalesOpportunity(opportunity)
        }
        return before != opportunity.status
    }

    private static Opportunity.Status getForSalesOpportunity(Opportunity opportunity) {
        List<Date> inPersonMeetingDates = getTwoFirstsInPersonUserSalesBdcMeetingDates(opportunity)
        Date lastCommunicationDate = getLastUserSalesBdcCommunicationDate(opportunity)
        if (!inPersonMeetingDates) {
            return lastCommunicationDate ?
                    Opportunity.Status.NO_APPOINTMENT :
                    Opportunity.Status.POTENTIAL
        }
        Date lastMeetingDate = inPersonMeetingDates.max()
        boolean hasCommunicationAfterLastMeeting = lastCommunicationDate > lastMeetingDate
        if (inPersonMeetingDates.size() == 1) {
            return hasCommunicationAfterLastMeeting ?
                    Opportunity.Status.COMMUNICATION_AFTER_ONE_APPOINTMENT :
                    Opportunity.Status.ONE_APPOINTMENT
        }
        return hasCommunicationAfterLastMeeting ?
                Opportunity.Status.COMMUNICATION_AFTER_TWO_APPOINTMENT :
                Opportunity.Status.TWO_APPOINTMENT
    }

    private static List<Date> getTwoFirstsInPersonUserSalesBdcMeetingDates(Opportunity opportunity) {
        return History.createCriteria().list(max: 2) {
            eq("client", opportunity.client)
            'in'("user", [opportunity.userSales, opportunity.userBdc])
            ge("date", opportunity.getCommunicationsMinDate())
            'in'("status", IN_PERSON_MEETING_HISTORIES)
            order("date", "asc")
            projections {
                property("date")
            }
        }
    }

    private static Date getLastUserSalesBdcCommunicationDate(Opportunity opportunity) {
        return Communication.createCriteria().get {
            'in'("class", Communication.getContactSubTypes())
            eq("client", opportunity.client)
            ge("date", opportunity.getCommunicationsMinDate())
            'in'("user", [opportunity.userSales, opportunity.userBdc])
            order("date", "desc")
            maxResults(1)
            projections {
                property("date")
            }
        }
    }

}
