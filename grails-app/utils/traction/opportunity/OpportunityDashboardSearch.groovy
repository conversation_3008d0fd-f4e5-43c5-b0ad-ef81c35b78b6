package traction.opportunity


import groovy.util.logging.Slf4j
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.client.ClientInterest
import traction.origin.Origin
import traction.security.User

@Slf4j
class OpportunityDashboardSearch {
    /*
        !!!! IMPORTANT !!!!
        DON'T FORGET TO CHANGE VERSION OF FILTER IN DASHBOARD.JS IF FILTER HAS BEEN CHANGED!!!
     */

    boolean countByStatus = false
    boolean isCount = false

    boolean awaitingOnly = false
    boolean awaitingContactedOnly = false
    boolean taskTodoOnly = false
    boolean taskSoonOnly = false
    boolean deliveryOnly = false
    boolean appointmentOnly = false

    List<OpportunityLabel> labels = []
    List<ClientInterest> interests = []
    List<Origin> origins = []
    List<User> users

    Opportunity.Status status
    Opportunity.Category category

    int max = -1
    int offset = -1
    String orderCol = "actDate"
    String orderDir = "desc"
    Date from
    Date to
    String dateTypeFilter
    String dateProperty
    Date datePropertyFrom
    Date datePropertyTo
    boolean archived = false
    String cotations = "all"
    String search = ""

    OpportunityDashboardSearch() {}

    OpportunityDashboardSearch(JSONObject jsonObject) {
        JSONArray usersJSON = jsonObject.getJSONArray('userIds')
        List<User> users = []
        for (Object it : usersJSON) {
            User user
            try {
                user = User.get(it as long)
            } catch (Exception e) {
                log.debug "Not a user : ${it}"
            }
            if (user) {
                users.add(user)
            }
        }
        this.users = users
        jsonObject.getJSONArray("booleans").each {
            this.setProperty(it.toString(), true)
        }

        jsonObject.getJSONArray("labels").each {
            OpportunityLabel label = OpportunityLabel.get(it.toString() as long)
            if (label) {
                labels.add(label)
            }
        }

        jsonObject.getJSONArray("interests").each {
            ClientInterest interest = ClientInterest.get(it.toString() as long)
            if (interest) {
                interests.add(interest)
            }
        }

        jsonObject.getJSONArray("origins").each {
            Origin origin = Origin.get(it.toString() as long)
            if (origin) {
                origins.add(origin)
            }
        }

        this.dateTypeFilter = jsonObject.getString('duration')
        this.search = jsonObject.getString('search')
        this.orderCol = jsonObject.getString("orderCol")
        this.orderDir = jsonObject.getString("orderDir")


        final Calendar cal = Calendar.getInstance()
        cal.add(Calendar.DATE, 1)
        this.to = cal.getTime().clearTime()
        switch (this.dateTypeFilter) {
            case "week":
                cal.add(Calendar.DATE, -7)
                this.from = cal.getTime().clearTime()
                break
            case "month":
                cal.add(Calendar.DATE, -30)
                this.from = cal.getTime().clearTime()
                break
            default:
                cal.add(Calendar.DATE, -7)
                this.from = cal.getTime().clearTime()
                break
        }

        this.archived = jsonObject.getString("archived") == "true"
        this.cotations = jsonObject.getString("cotations")

        this.dateProperty = jsonObject.getString("dateProperty")
        def dateFormat = "yyyy-MM-dd"
        if (jsonObject.getString("datePropertyFrom") != "") {
            this.datePropertyFrom = Date.parse(dateFormat, jsonObject.getString("datePropertyFrom"))
        }
        if (jsonObject.getString("datePropertyTo") != "") {
            Date date = Date.parse(dateFormat, jsonObject.getString("datePropertyTo"))
            final Calendar cal2 = Calendar.getInstance()
            cal2.setTime(date)
            cal2.add(Calendar.DATE, 1)
            this.datePropertyTo = cal2.getTime();
        }
    }
}
