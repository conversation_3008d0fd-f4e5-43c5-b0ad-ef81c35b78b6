package traction.opportunity

import traction.UserGroup
import traction.communication.CallData
import traction.communication.Communication
import traction.opportunity.OpportunityCompiledData.CommunicationStatus

// TODO: should optimise getLatestApplicableCommunication, maybe change all code to do a single request with projetions
class CommunicationStatusUtils {

    static CommunicationStatus getUpdated(OpportunityCompiledData data) {
        Long excludeId = getExcludedFirstInboundWebCommunicationId(data)
        Communication latestCommunication = getLatestApplicableCommunication(data, excludeId)
        if (!latestCommunication) {
            return getUserNoContactIfActive(data.opportunity)
        }
        CallData callData = latestCommunication.asCallData()
        if (callData) {
            return getCallDataStatus(callData, data, excludeId)
        }
        if (latestCommunication.asCommunicationMeeting()) {
            return CommunicationStatus.CLIENT_REACHED
        }
        return getWebCommunicationStatus(latestCommunication, data, excludeId)
    }

    private static CommunicationStatus getCallDataStatus(CallData callData, OpportunityCompiledData data, Long excludeId) {
        if (isCallDataReached(callData)) {
            return CommunicationStatus.CLIENT_REACHED
        }
        if (!callData.outbound) {
            return CommunicationStatus.CLIENT_PENDING
        }
        if (!hasInboundCommunication(data, excludeId) && !hasCallDataReached(data, excludeId)) {
            return CommunicationStatus.USER_ATTEMPT
        }
        return CommunicationStatus.CLIENT_REACHED
    }

    private static CommunicationStatus getWebCommunicationStatus(Communication latestCommunication, OpportunityCompiledData data, Long excludeId) {
        boolean hasCallDataReached = hasCallDataReached(data, excludeId)
        if (!hasOutboundCommunication(data, excludeId) && !hasCallDataReached) {
            return getUserNoContactIfActive(data.opportunity)
        }
        if (latestCommunication.outbound) {
            if (!hasInboundCommunication(data, excludeId) && !hasCallDataReached) {
                return CommunicationStatus.USER_ATTEMPT
            }
            return CommunicationStatus.CLIENT_REACHED
        }
        if (latestCommunication.isUnread) {
            return CommunicationStatus.CLIENT_PENDING
        }
        return CommunicationStatus.CLIENT_REACHED
    }

    private static CommunicationStatus getUserNoContactIfActive(Opportunity opportunity) {
        return opportunity.isActive() ? CommunicationStatus.USER_NO_CONTACT : CommunicationStatus.CLIENT_REACHED
    }

    private static Communication getLatestApplicableCommunication(OpportunityCompiledData data, Long excludeId) {
        return Communication.createCriteria().get {
            if (excludeId) ne("id", excludeId)
            eq("completed", true)
            'in'("class", Communication.getContactSubTypes())
            eq("client", data.opportunity.client)
            ge("date", data.opportunity.getCommunicationsMinDate())
            'in'("userGroup", [UserGroup.SALES, UserGroup.BDC])
            order("date", "desc")
            maxResults(1)
        }
    }

    private static boolean hasOutboundCommunication(OpportunityCompiledData data, Long excludeId) {
        return hasCommunication(data, excludeId, true)
    }

    private static boolean hasInboundCommunication(OpportunityCompiledData data, Long excludeId) {
        return hasCommunication(data, excludeId, false)
    }

    private static boolean hasCommunication(OpportunityCompiledData data, Long excludeId, boolean outbound) {
        return Communication.createCriteria().count {
            if (excludeId) ne("id", excludeId)
            eq("completed", true)
            'in'("class", Communication.getContactSubTypes())
            eq("client", data.opportunity.client)
            ge("date", data.opportunity.getCommunicationsMinDate())
            eq("outbound", outbound)
            'in'("userGroup", [UserGroup.SALES, UserGroup.BDC])
        } ? true : false
    }

    private static Long getExcludedFirstInboundWebCommunicationId(OpportunityCompiledData data) {
        List result = Communication.createCriteria().get {
            'in'("class", Communication.getWebSubTypes())
            eq("completed", true)
            eq("client", data.opportunity.client)
            ge("date", data.opportunity.getCommunicationsMinDate())
            'in'("userGroup", [UserGroup.SALES, UserGroup.BDC])
            order("date", "asc")
            maxResults(1)
            projections {
                property("outbound")
                property("id")
            }
        }
        if (!result) return null
        return result[0] ? null : result[1]
    }

    private static boolean hasCallDataReached(OpportunityCompiledData data, Long excludeId) {
        return CallData.createCriteria().list {
            if (excludeId) ne("id", excludeId)
            eq("hasBeenConnected", true)
            eq("completed", true)
            eq("client", data.opportunity.client)
            ge("date", data.opportunity.getCommunicationsMinDate())
            'in'("userGroup", [UserGroup.SALES, UserGroup.BDC])
        }.any { isCallDataReached(it) }
    }

    private static boolean isCallDataReached(CallData callData) {
        return callData.hasBeenConnected && (!callData.outbound || callData.isOutboundAndMatchingMinimumConnectedTimeConfig())
    }

}
