package traction.opportunity

import groovy.util.logging.Slf4j
import org.grails.web.json.JSONObject
import traction.department.Department
import traction.Media
import traction.UserGroup
import traction.workflow.WorkflowBoard
import traction.workflow.WorkflowMaster

@Slf4j
class OpportunitySearchOld {

    /**
     * Determine how many values in the filter will be user in the query
     * eg:
     * 1 = filter type, archived, workflow
     * 2 = filter type, archived, workflow, departments, userGroups
     * 3 = filter type, archived, workflow, departments, userGroups, originIds
     * 4 = filter type, archived, workflow, departments, userGroups, originIds, medias
     * 5 = filter type, archived, workflow, departments, userGroups, originIds, medias, status
     * 6+ = (all filters) filter, archived, workflow, departments, userGroups, originIds, medias, status, actDate, dateMOD, dateSOLD, date, search
     *
     */
    int steps = 999

    boolean isCount = false

    int max = -1
    int offset = -1

    String orderCol = "id"
    String orderDir = "asc"
    String search

    Long id
    String email
    String phone
    String phonecell
    String client
    String name
    String product
    String make
    String model
    String year
    String modelcode
    String stocknumber

    String groupMedia
    OpportunityCompiledData.CommunicationStatus communicationStatus

    Opportunity.Type type
    boolean archived = false
    WorkflowMaster workflowMaster
    List<WorkflowBoard> workflowBoards = []
    List<Department> departments = []
    List<UserGroup> userGroups = []
    List<Long> originIds = []  //List of id's
    List<Media> medias = []
    List<Opportunity.Status> status = []
    List<Opportunity.Category> categories = []

    Date actDateFrom
    Date dateSOLDFrom
    Date dateMODFrom
    Date dateFrom
    Date dateEndFrom

    Date actDateTo
    Date dateSOLDTo
    Date dateMODTo
    Date dateTo
    Date dateEndTo
    Date delivered

    Date initFrom
    Date initTo
    List<String> initDateFields = []

    Integer activityCount
    String activityCountOperation = ""

    Integer takeOverCount
    String takeOverCountOperation = ""

    Integer linkedUser
    List<Long> userList = []

    OpportunitySearchOld(JSONObject jsonObject) {
        if (jsonObject) {
            this.id = jsonObject.has("id") && jsonObject.get("id") ? jsonObject.getLong("id") : null
            this.email = jsonObject.has("email") ? jsonObject.getString("email") : ""
            this.client = jsonObject.has("client") ? jsonObject.getString("client") : ""
            this.phone = jsonObject.has("phone") ? jsonObject.getString("phone") : ""
            this.phonecell = jsonObject.has("phonecell") ? jsonObject.getString("phonecell") : ""
            this.name = jsonObject.has("name") ? jsonObject.getString("name") : ""
            this.product = jsonObject.has("product") ? jsonObject.getString("product") : ""
            this.make = jsonObject.has("make") ? jsonObject.getString("make") : ""
            this.model = jsonObject.has("model") ? jsonObject.getString("model") : ""
            this.year = jsonObject.has("year") ? jsonObject.getString("year") : ""
            this.modelcode = jsonObject.has("modelcode") ? jsonObject.getString("modelcode") : ""
            this.stocknumber = jsonObject.has("stocknumber") ? jsonObject.getString("stocknumber") : ""
            this.groupMedia = jsonObject.has("groupMedia") ? jsonObject.getString("groupMedia") : ""
            this.communicationStatus = jsonObject.has("communicationStatus") ? OpportunityCompiledData.CommunicationStatus.getById(jsonObject.getInt("communicationStatus")) : null


            this.type = jsonObject.has("type") ? Opportunity.Type.getById(jsonObject.getInt("type")) : null
            this.archived = jsonObject.has("archived") ? jsonObject.getString("archived") == "true" : false
            if (jsonObject.has("workflowMaster")) {
                this.workflowMaster = WorkflowMaster.get(jsonObject.get("workflowMaster"))
            }
            if (jsonObject.has("workflowBoardIds")) {
                jsonObject.getJSONArray("workflowBoardIds").each {
                    if (it.toString().isInteger()) {
                        WorkflowBoard board = WorkflowBoard.get(it as long)
                        if (board) {
                            this.workflowBoards.add(board)
                        }
                    }
                }
            }
            if (jsonObject.has("departments")) {
                jsonObject.getJSONArray("departments").each {
                    if (it.toString().isInteger()) {
                        Department department = Department.get(it as long)
                        if (department) {
                            this.departments.add(department)
                        }
                    }
                }
            }
            if (jsonObject.has("categories")) {
                jsonObject.getJSONArray("categories").each {
                    if (it.toString().isInteger()) {
                        Opportunity.Category category = Opportunity.Category.getById(Integer.parseInt(it))
                        if (category) {
                            this.categories.add(category)
                        }
                    }
                }
            }
            if (jsonObject.has("userGroups")) {
                jsonObject.getJSONArray("userGroups").each {
                    UserGroup g = UserGroup.getById(Integer.valueOf(it))
                    if (g) {
                        this.userGroups.add(g)
                    }
                }
            }
            if (jsonObject.has("origins")) {
                jsonObject.getJSONArray("origins").each {
                    if (it.toString().isInteger()) {
                        this.originIds.add(Long.valueOf(it))
                    }
                }
            }
            if (jsonObject.has("medias")) {
                jsonObject.getJSONArray("medias").each {
                    this.medias.add(Media.getById(Integer.valueOf(it)))
                }
            }
            if (jsonObject.has("status")) {
                jsonObject.getJSONArray("status").each {
                    this.status.add(Opportunity.Status.getById(Integer.valueOf(it)))
                }
            }
            String dateFormat = "MM/dd/yyyy"

            if (jsonObject.has("initDate")) {
                String actDate = jsonObject.getString("initDate")
                if (actDate.contains(" - ")) {
                    String[] split = actDate.split(" - ")
                    this.initFrom = Date.parse(dateFormat, split[0])
                    this.initTo = Date.parse(dateFormat, split[1]) + 1
                }
            }
            if (jsonObject.has("initDateFields")) {
                jsonObject.getJSONArray("initDateFields").each {
                    this.initDateFields.add(it)
                }
            }

            if (jsonObject.has("actDate")) {
                String actDate = jsonObject.getString("actDate")
                if (actDate.contains(" - ")) {
                    String[] split = actDate.split(" - ")
                    this.actDateFrom = Date.parse(dateFormat, split[0])
                    this.actDateTo = Date.parse(dateFormat, split[1]) + 1
                }
            }

            if (jsonObject.has("dateSOLD")) {
                String dateSOLD = jsonObject.getString("dateSOLD")
                if (dateSOLD.contains(" - ")) {
                    String[] split = dateSOLD.split(" - ")
                    this.dateSOLDFrom = Date.parse(dateFormat, split[0])
                    this.dateSOLDTo = Date.parse(dateFormat, split[1]) + 1
                }
            }

            if (jsonObject.has("dateMOD")) {
                String dateMOD = jsonObject.getString("dateMOD")
                if (dateMOD.contains(" - ")) {
                    String[] split = dateMOD.split(" - ")
                    this.dateMODFrom = Date.parse(dateFormat, split[0])
                    this.dateMODTo = Date.parse(dateFormat, split[1]) + 1
                }
            }

            if (jsonObject.has("date")) {
                String date = jsonObject.getString("date")
                if (date.contains(" - ")) {
                    String[] split = date.split(" - ")
                    this.dateFrom = Date.parse(dateFormat, split[0])
                    this.dateTo = Date.parse(dateFormat, split[1]) + 1
                }
            }

            if (jsonObject.has("dateEnd")) {
                String dateEnd = jsonObject.getString("dateEnd")
                if (dateEnd.contains(" - ")) {
                    String[] split = dateEnd.split(" - ")
                    this.dateEndFrom = Date.parse(dateFormat, split[0])
                    this.dateEndTo = Date.parse(dateFormat, split[1]) + 1
                }
            }

            if (jsonObject.has('delivered')) {
                this.delivered = Date.parse(dateFormat, jsonObject.getString("delivered"))
            }

            this.activityCount = jsonObject.has("activityCount") ? jsonObject.getInt("activityCount") : null
            this.activityCountOperation = jsonObject.has("activityCountOperation") ? jsonObject.getString("activityCountOperation") : ""

            this.takeOverCount = jsonObject.has("takeOverCount") ? jsonObject.getInt("takeOverCount") : null
            this.takeOverCountOperation = jsonObject.has("takeOverCountOperation") ? jsonObject.getString("takeOverCountOperation") : ""

            this.search = jsonObject.has("search") ? jsonObject.getString("search") : ""

            this.linkedUser = jsonObject.has("linkedUser") && jsonObject.get("linkedUser") ? jsonObject.getInt("linkedUser") : 0

            if (jsonObject.has("userSales")) {
                jsonObject.getJSONArray("userSales").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userFni")) {
                jsonObject.getJSONArray("userFni").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userPart")) {
                jsonObject.getJSONArray("userPart").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userService")) {
                jsonObject.getJSONArray("userService").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userShipping")) {
                jsonObject.getJSONArray("userShipping").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userShop")) {
                jsonObject.getJSONArray("userShop").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }

            if (jsonObject.has("userTech")) {
                jsonObject.getJSONArray("userTech").each {
                    if (it.toString().isInteger()) {
                        this.userList.add(Long.parseLong(it))
                    }
                }
            }
        }
    }

    List<Integer> getStepsThatChangesNothing() {
        List<Integer> uselessSteps = []
        if (this.userGroups.size() == 0 && this.departments.size() == 0) {
            uselessSteps.add(2)
        }
        if (this.originIds.size() == 0) {
            uselessSteps.add(3)
        }
        if (this.medias.size() == 0) {
            uselessSteps.add(4)
        }
        if (this.status.size() == 0) {
            uselessSteps.add(5)
        }
        if (!this.dateSOLDFrom && !this.dateSOLDTo && !this.actDateFrom && !this.dateMODFrom && !this.dateFrom && !this.actDateTo && !this.dateMODTo && !this.dateTo && !this.dateEndTo && !this.dateEndFrom &&
                this.activityCount == 0 && this.activityCountOperation == "" && this.linkedUser == 0 && !this.search) {
            uselessSteps.add(6)
        }
        return uselessSteps
    }

    OpportunitySearchOld getOpportunitySearchWithStep(int step) {
        OpportunitySearchOld newOppSearch = new OpportunitySearchOld(null)

        newOppSearch.isCount = this.isCount

        if (step > 0) {
            newOppSearch.type = this.type
            newOppSearch.archived = this.archived
            newOppSearch.workflowMaster = this.workflowMaster
            newOppSearch.workflowBoards = this.workflowBoards

        }
        if (step > 1) {
            newOppSearch.departments = this.departments
            newOppSearch.userGroups = this.userGroups
        }
        if (step > 2) {
            newOppSearch.originIds = this.originIds
        }
        if (step > 3) {
            newOppSearch.medias = this.medias
        }
        if (step > 4) {
            newOppSearch.status = this.status
        }
        if (step > 5) {
            newOppSearch.dateFrom = this.dateFrom
            newOppSearch.dateMODFrom = this.dateMODFrom
            newOppSearch.actDateFrom = this.actDateFrom
            newOppSearch.dateSOLDFrom = this.dateSOLDFrom
            newOppSearch.dateEndFrom = this.dateEndFrom
            newOppSearch.dateTo = this.dateTo
            newOppSearch.dateMODTo = this.dateMODTo
            newOppSearch.actDateTo = this.actDateTo
            newOppSearch.dateSOLDTo = this.dateSOLDTo
            newOppSearch.dateEndTo = this.dateEndTo
            newOppSearch.activityCount = this.activityCount
            newOppSearch.activityCountOperation = this.activityCountOperation
            newOppSearch.search = this.search
            newOppSearch.linkedUser = this.linkedUser
        }

        return newOppSearch
    }
}
