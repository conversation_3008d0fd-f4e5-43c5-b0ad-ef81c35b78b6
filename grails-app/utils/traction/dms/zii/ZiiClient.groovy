package traction.dms.zii

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.Session
import traction.*
import traction.client.*
import traction.communication.PhoneUtils
import traction.communication.email.EmailUtils
import traction.datatable.DataTablePaging
import traction.utils.BatchSessionManager
import traction.dms.zii.api.customerGarage.CustomerGarageApiClient
import traction.dms.zii.api.customerGarage.CustomerGarageResponseItem
import traction.vehicle.Vehicle
import traction.vehicle.VehicleService

@Slf4j
class ZiiClient extends TractionZii {
    private final static ClientService clientService
    private final static VehicleService vehicleService
    static {
        clientService = (ClientService) Holders.grailsApplication.mainContext.getBean("clientService")
        vehicleService = (VehicleService) Holders.grailsApplication.mainContext.getBean("vehicleService")
    }

    traction.dms.zii.api.customer.CustomerApiClient customerApiClient
    CustomerGarageApiClient customerGarageApiClient

    ZiiClient(DMS dms, traction.dms.zii.api.ZiiApi ziiApi) {
        super(dms, ziiApi)
        this.customerApiClient = new traction.dms.zii.api.customer.CustomerApiClient(ziiApi)
        this.customerGarageApiClient = new CustomerGarageApiClient(ziiApi)
    }

    void importClients(Date updatedFrom, Date updatedTo) {
        DataTablePaging dataTablePaging = new DataTablePaging(0, 1000)
        BatchSessionManager batchSessionManager = new BatchSessionManager(sessionFactory, 10)
        for (;;) {
            log.debug "dataTablePaging.max: ${dataTablePaging.max} dataTablePaging.offset: ${dataTablePaging.offset}"
            traction.dms.zii.api.customer.CustomerSearchResponse customerSearchResponse = customerApiClient.search(new traction.dms.zii.api.customer.CustomerSearchCriteria(
                    updatedFrom: updatedFrom,
                    updatedTo: updatedTo,
                    take: dataTablePaging.max,
                    skip: dataTablePaging.offset
            ))
            int totalCount = customerSearchResponse.items.size()
            customerSearchResponse.items.eachWithIndex { traction.dms.zii.api.customer.CustomerSearchResponseItem customerSearchResponseItem, int i ->
                try {
                    if (!customerSearchResponseItem.customerId) {
                        AntiSpamLogger.error "No extId for client found : ${customerSearchResponseItem.toString()}"
                        return
                    }
                    log.debug "update: ${customerSearchResponseItem.customerId} (${dataTablePaging.offset + i}/$totalCount)"
                    update(customerSearchResponseItem.customerId, batchSessionManager.session)
                } catch (Exception ex) {
                    ex.printStackTrace()
                    AntiSpamLogger.error "Error importClients: ${ex.getMessage()}"
                }
                batchSessionManager.loop()
            }
            /**/
            if (customerSearchResponse.items.size() != dataTablePaging.max) break
            /**/
            dataTablePaging.offset += dataTablePaging.max
        }
        batchSessionManager.flushAndCommitTransaction()
    }

    Client update(Integer customerId, Session session) {
        ExecutionTime time = new ExecutionTime()
        log.debug "update: $customerId"
        traction.dms.zii.api.customer.CustomerDto customer = customerApiClient.get(customerId)
        log.debug "EXECUTION TIME GET: ${time.executionTime()}"
        if (!customer) {
            AntiSpamLogger.error "Customer not found for $customerId"
            return null
        }
        ignoreTestPhoneNumbers(customer)
        if (customer.notes) { // TODO: Sync notes?
            log.debug("noooooootes")
        }
        Client client = Client.findByExtID(customer.customerId.toString(), dms)
        boolean doSessionFlush = false
        // Find the client by email
        boolean validEmail = EmailUtils.isValid(customer.email)
        boolean setEmailOfNewClient = false
        if (validEmail) {
            setEmailOfNewClient = true
            Client sameEmail = Client.findByEmail(customer.email)
            if (sameEmail) {
                log.debug "sameEmail:" + sameEmail
                if (!client && !sameEmail.getExtID(dms)) {
                    client = sameEmail
                    log.debug "Takes the sameEmail client"
                } else {
                    setEmailOfNewClient = false
                }
            } else if (client && EmailUtils.isNoMail(client.email)) {
                client.email = customer.email
                doSessionFlush = true
                log.debug "Set the email"
            }
        }
        if (!client) {
            List<Client> sameTel = clientService.findAllByPhoneWithoutExtID([customer.phone, customer.mobile], dms)
            Client sameTelSameName = sameTel.find { it.firstname?.toLowerCase() == customer.firstName.toLowerCase() && it.lastname?.toLowerCase() == customer.lastName.toLowerCase() }
            if (!sameTelSameName) {
                sameTelSameName = sameTel.find { it.firstname?.toLowerCase() == customer.firstName.toLowerCase() || it.lastname?.toLowerCase() == customer.lastName.toLowerCase() }
            }
            if (sameTelSameName) {
                client = sameTelSameName
            } else if (sameTel) {
                client = sameTel.first()
            }
            if (!client) {
                client = new IndividualClient()
                if (setEmailOfNewClient) {
                    client.email = customer.email
                    doSessionFlush = true
                }
                client.media = Media.DMS
            } else if (validEmail && EmailUtils.isNoMail(client.email) && setEmailOfNewClient) {
                client.email = customer.email
                doSessionFlush = true
            }
        }

        if (!client.getExtID(dms, customer.customerId.toString())) {
            client.addToExternalIdentifiers(new ExternalIdentifier(extId: customer.customerId.toString(), dms: dms))
            doSessionFlush = true
        }

        // TODO: fix this IndividualClient?
        if (!client.firstname && customer.firstName) {
            client.firstname = customer.firstName
        }
        if (!client.lastname && customer.lastName) {
            client.lastname = customer.lastName
        }
        if (!client.phone && customer.phone) {
            client.phone = PhoneUtils.getValidPhoneNumber(customer.phone)
        }
        if (!client.phonecell && customer.mobile) {
            client.phonecell = PhoneUtils.getValidPhoneNumber(customer.mobile)
        }
        /*
        if (!client.companyName && customer.companyName) {
            client.companyName = customer.companyName
        }
        */
        if (!client.gender && customer.gender) {
            client.gender = IndividualClient.Gender.getFromString(customer.gender)
        }
        if (clientService.save(client, true, session)) {
            if (doSessionFlush) {
                session.flush()
            }
            if (customer.fax) {
                clientService.setClientAttribute(client, ClientAttributes.FAX.message, customer.fax, false, session)
            }
            if (customer.street) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_ADDRESS.message, customer.street, false, session)
            }
            if (customer.city) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_CITY.message, customer.city, false, session)
            }
            if (customer.state) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_PROVINCE.message, customer.state, false, session)
            }
            if (customer.country) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_COUNTRY.message, customer.country, false, session)
            }
            if (customer.zipCode) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_POSTAL.message, customer.zipCode, false, session)
            }
            if (customer.birthDate) {
                clientService.setClientAttribute(client, ClientAttributes.BIRTHDAY.message, customer.birthDate.format("yyyy-MM-dd"), false, session)
            }

            List<CustomerGarageResponseItem> customerGarageResponseItemList = customerGarageApiClient.get(customerId).items
            customerGarageResponseItemList.each { CustomerGarageResponseItem customerGarageResponseItem ->
                /*
                Vehicle vehicle = Vehicle.findByExtID(customerGarageResponseItem.dealerInventoryId.toString(), dms) ?: new Vehicle()
                if (!vehicle.id) {
                    vehicle.addToExternalIdentifiers(new ExternalIdentifier(extId: customerGarageResponseItem.dealerInventoryId.toString(), dms: dms))
                    doSessionFlush = true
                }
                vehicle.client = client
                vehicle.category = customerGarageResponseItem.category
                vehicle.type = Vehicle.Type.CLIENT
                vehicle.make = customerGarageResponseItem.manufacturerName
                vehicle.model = customerGarageResponseItem.manufacturerModelName
                vehicle.year = customerGarageResponseItem.modelYear.isInteger() ? customerGarageResponseItem.modelYear.toInteger() : null
                vehicle.serialNumber = customerGarageResponseItem.vin
                vehicle.exteriorColor = customerGarageResponseItem.color
                vehicleService.save(vehicle, session)
                 */
            }
        }
        log.debug "EXECUTION TIME TOTAL: ${time.executionTime()}"
        return client
    }

    Client createOrGetClient(Integer customerId, Session session) {
        Client client = Client.findByExtID(customerId.toString(), dms)
        if (client) return client
        return update(customerId, session)
    }

    private void ignoreTestPhoneNumbers(traction.dms.zii.api.customer.CustomerDto customer) {
        if (PhoneUtils.cleanNumber(customer.phone) == "5555555555" || PhoneUtils.cleanNumber(customer.phone).startsWith("123456")) {
            customer.phone = null
        }
        if (PhoneUtils.cleanNumber(customer.mobile) == "5555555555" || PhoneUtils.cleanNumber(customer.mobile).startsWith("123456")) {
            customer.mobile = null
        }
    }
}
