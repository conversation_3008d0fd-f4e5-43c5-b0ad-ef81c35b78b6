package traction.dms.zii

import groovy.util.logging.Slf4j
import org.hibernate.Session
import traction.AntiSpamLogger
import traction.DMS
import traction.opportunity.OpportunityFee
import traction.utils.BatchSessionManager
import traction.dms.zii.api.partsInventory.FeeItemResponse
import traction.dms.zii.api.partsInventory.PartsInventoryApiClient

@Slf4j
class ZiiPartsInventory extends TractionZii {

    PartsInventoryApiClient partsInventoryApiClient

    ZiiPartsInventory(DMS dms, traction.dms.zii.api.ZiiApi ziiApi) {
        super(dms, ziiApi)
        this.partsInventoryApiClient = new PartsInventoryApiClient(ziiApi)
    }

    void importFeeItems() {
        BatchSessionManager batchSessionManager = new BatchSessionManager(sessionFactory, 10)
        FeeItemResponse[] feeItemsResponses = partsInventoryApiClient.getAllFeeItem()
        feeItemsResponses.eachWithIndex { FeeItemResponse feeItemResponse, int i ->
            try {
                updateFee(feeItemResponse, batchSessionManager.session)
            } catch (Exception ex) {
                ex.printStackTrace()
                AntiSpamLogger.error "Error importFeeItems: ${ex.getMessage()}"
            }
            batchSessionManager.loop()
        }
        batchSessionManager.flushAndCommitTransaction()
    }

    OpportunityFee updateFee(FeeItemResponse feeItemResponse, Session session) {
        log.debug "update: $feeItemResponse.id"
        OpportunityFee fee = OpportunityFee.findByExternalIdAndDmsAndOpportunityIsNull(feeItemResponse.id, dms)
        if (!fee) {
            fee = new OpportunityFee(
                    externalId: feeItemResponse.id,
                    dms: dms,

                    // TODO: ces proprietes devraient etre updater a chaques fois puiqu'elles ne sont pas dans le findByExternalIdAndDmsAndOpportunityIsNull?
                    name: feeItemResponse.name,
                    taxable: true, // TODO: COMMENT SAVOIR SI LE FRAIS EST TAXABLE? VOIR AVEC ZII
                    type: OpportunityFee.Type.FEE
            )
        }
        fee.sellingPrice = feeItemResponse.price
        fee.costingPrice = feeItemResponse.price
        session.save(fee)
        return fee
    }
}
