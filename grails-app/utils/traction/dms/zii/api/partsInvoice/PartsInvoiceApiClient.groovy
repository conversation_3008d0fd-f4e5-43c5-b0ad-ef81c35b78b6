package traction.dms.zii.api.partsInvoice

import traction.dms.zii.api.BaseApiClient
import traction.dms.zii.api.ZiiApi

class PartsInvoiceApiClient extends BaseApiClient {
    static final String ENDPOINT = "/sdk/PartsInvoice"

    PartsInvoiceApiClient(ZiiApi ziiApi) {
        super(ziiApi)
    }

    PartsInvoiceResponse get(Integer id) {
        return getResponseObject("$ENDPOINT/get?id=$id", PartsInvoiceResponse)
    }

    PartsInvoiceSearchResponse search(PartsInvoiceSearchCriteria criteria) {
        return postResponseObject("$ENDPOINT/search", criteria, PartsInvoiceSearchResponse)
    }
}