package traction.dms.zii.api.partsInventory

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class PartsSearchResponse {
    @JsonProperty("TotalCount")
    Integer totalCount

    @JsonProperty("Items")
    List<PartsSearchResponseItem> items

    static class PartsSearchResponseItem {
        @JsonProperty("DealerId")
        Integer dealerId

        @JsonProperty("PartId")
        Integer partId

        @JsonProperty("DealerInventoryPartId")
        Integer dealerInventoryPartId

        @JsonProperty("PartNumber")
        String partNumber

        @JsonProperty("Supplier")
        String supplier

        @JsonProperty("SupplierName")
        String supplierName

        @JsonProperty("DateGathered")
        String dateGathered

        @JsonProperty("Description")
        String description

        @JsonProperty("OnHand")
        Double onHand

        @JsonProperty("OnOrder")
        Double onOrder

        @JsonProperty("Cost")
        Double cost

        @JsonProperty("CurrentActivePrice")
        Double currentActivePrice

        @JsonProperty("Retail")
        Double retail
    }
}