package traction.dms.zii.api.serviceInvoice.labor

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class ServiceInvoiceSearchResponseLabor {

    @JsonProperty("DealerSaleServiceJobId")
    Integer dealerSaleServiceJobId

    @JsonProperty("DealerSaleId")
    Integer dealerSaleId

    @JsonProperty("DealerSaleMUId")
    Integer dealerSaleMUId

    @JsonProperty("ROLaborId")
    Integer ROLaborId

    @JsonProperty("LaborName")
    String laborName

    @JsonProperty("LaborDescription")
    String laborDescription

    @JsonProperty("Hours")
    Double hours

    @JsonProperty("Rate")
    Double rate

    @JsonProperty("Total")
    Double total

    @JsonProperty("ActualHours")
    Double actualHours

    @JsonProperty("TechnicianName")
    String technicianName

    @JsonProperty("DiscountTotalCharge")
    Double discountTotalCharge

    @JsonProperty("TotalCharge")
    Double totalCharge

    @JsonProperty("Misccharge1")
    Double misccharge1

    @JsonProperty("Misccharge2")
    Double misccharge2

    @JsonProperty("Misccharge3")
    Double misccharge3

    @JsonProperty("Misccharge4")
    Double misccharge4

    @JsonProperty("DiscountPrice")
    Double discountPrice

    @JsonProperty("LaborJobCode")
    String laborJobCode

    @JsonProperty("UnitDiscountAmount")
    Double unitDiscountAmount

    @JsonProperty("TotalExtension")
    Double totalExtension

    @JsonProperty("TotalDiscountAmount")
    Double totalDiscountAmount

    @JsonProperty("TotalTax")
    Double totalTax

    @JsonProperty("Manufacturer")
    String manufacturer

    @JsonProperty("CategoryName")
    String categoryName

    @JsonProperty("Tech")
    List<traction.dms.zii.api.serviceInvoice.tech.ServiceInvoiceGetResponseTech> tech
}
