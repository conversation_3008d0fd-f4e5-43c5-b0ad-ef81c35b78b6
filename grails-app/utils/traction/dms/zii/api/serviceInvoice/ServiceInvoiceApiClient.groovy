package traction.dms.zii.api.serviceInvoice

class ServiceInvoiceApiClient extends traction.dms.zii.api.BaseApiClient {
    static final String ENDPOINT = "/sdk/ServiceInvoice"

    ServiceInvoiceApiClient(traction.dms.zii.api.ZiiApi ziiApi) {
        super(ziiApi)
    }

    ServiceInvoiceSearchResponseItem get(Integer id) {
        return getResponseObject("$ENDPOINT/get?id=$id", ServiceInvoiceSearchResponseItem)
    }

    ServiceInvoiceSearchResponse search(ServiceInvoiceSearchRequest criteria) {
        return postResponseObject("$ENDPOINT/search", criteria, ServiceInvoiceSearchResponse)
    }
}