package traction.dms.zii.api.serviceInvoice

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class ServiceInvoiceGetResponse {

    @JsonProperty("DealerId")
    Integer dealerId

    @JsonProperty("DealId")
    Integer dealId

    @JsonProperty("DealNo")
    String dealNo

    @JsonProperty("ROHeaderID")
    Integer ROHeaderID

    @JsonProperty("RONo")
    String RONo

    @JsonProperty("DateIn")
    Date dateIn

    @JsonProperty("CloseDate")
    Date closeDate

    @JsonProperty("PuDate")
    Date puDate

    @JsonProperty("CustomerId")
    Integer customerId

    @JsonProperty("Shopsupply")
    Double shopSupply

    @JsonProperty("MiscCharge1")
    Double miscCharge1

    @JsonProperty("MiscCharge2")
    Double miscCharge2

    @JsonProperty("MiscCharge3")
    Double miscCharge3

    @JsonProperty("MiscCharge4")
    Double miscCharge4

    @JsonProperty("ServiceWriterName")
    String serviceWriterName

    @JsonProperty("TotSubCost")
    Double totSubCost

    @JsonProperty("TotSubSales")
    Double totSubSales

    @JsonProperty("ROUnitID")
    Integer ROUnitID

    @JsonProperty("Year")
    Integer year

    @JsonProperty("Make")
    String make

    @JsonProperty("Model")
    String model

    @JsonProperty("VIN")
    String vin

    @JsonProperty("Engineno")
    String engineno

    @JsonProperty("Class")
    String className

    @JsonProperty("Odometer")
    Double odometer

    @JsonProperty("StockNumber")
    String stockNumber

    @JsonProperty("ROJobId")
    Integer ROJobId

    @JsonProperty("JobDescription")
    String jobDescription

    @JsonProperty("DealStatus")
    String dealStatus

    @JsonProperty("ExternalId")
    String externalId

    @JsonProperty("ServiceStatus")
    String serviceStatus

    @JsonProperty("SalespersonNames")
    String salespersonNames

    @JsonProperty("SalespersonIds")
    String salespersonIds

    @JsonProperty("UpdatedDate")
    Date updatedDate

    @JsonProperty("Units")
    List<traction.dms.zii.api.serviceInvoice.unit.ServiceInvoiceGetResponseUnit> units
}