package traction.dms.zii.api.customerGarage

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerGarageResponseItem {

    @JsonProperty("DealerInventoryId")
    Integer dealerInventoryId

    @JsonProperty("VIN")
    String vin

    @JsonProperty("EngineNumber")
    String engineNumber

    @JsonProperty("Unit")
    String unit

    @JsonProperty("ModelYear")
    String modelYear

    @JsonProperty("ModelCode")
    String modelCode

    @JsonProperty("Category")
    String category

    @JsonProperty("ManufacturerName")
    String manufacturerName

    @JsonProperty("ManufacturerModelName")
    String manufacturerModelName

    @JsonProperty("Color")
    String color

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("CreatedDate")
    Date createdDate

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("UpdatedDate")
    Date updatedDate
}