package traction.dms.zii.api.customerGarage

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty


@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerUnitPutResponse {
    @JsonProperty("Data")
    Boolean data

    @JsonProperty("Key")
    String key

    @JsonProperty("Messages")
    List<Message> messages

    @JsonProperty("StartTime")
    Date startTime

    @JsonProperty("StopTime")
    Date stopTime

    static class Message {

        @JsonProperty("Code")
        String code

        @JsonProperty("Description")
        String description

        @JsonProperty("Key")
        String key

        @JsonProperty("SqlErrorMessage")
        String sqlErrorMessage

        @JsonProperty("SqlErrorNumber")
        Integer sqlErrorNumber

        @JsonProperty("IsQuietMode")
        Boolean isQuietMode

        @JsonProperty("Type")
        MessageType type

        enum MessageType {
            Error,
            Information,
            Warning
        }
    }
}