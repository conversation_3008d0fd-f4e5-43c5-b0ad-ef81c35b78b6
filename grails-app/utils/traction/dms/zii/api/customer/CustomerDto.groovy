package traction.dms.zii.api.customer

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerDto {

    @JsonProperty("CustomerId")
    Integer customerId

    @JsonProperty("OrganizationId")
    int organizationId

    @JsonProperty("DealershipId")
    int dealershipId

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("ActiveDate")
    Date activeDate

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("UpdatedDate")
    Date updatedDate

    @JsonProperty("CreatedBy")
    int createdBy

    @JsonProperty("UpdatedBy")
    int updatedBy

    @JsonProperty("IsBusiness")
    boolean isBusiness

    @JsonProperty("CompanyName")
    String companyName

    @JsonProperty("FirstName")
    String firstName

    @JsonProperty("LastName")
    String lastName

    @JsonProperty("MiddleName")
    String middleName

    @JsonProperty("Email")
    String email

    @JsonProperty("Phone")
    String phone

    @JsonProperty("Mobile")
    String mobile

    @JsonProperty("Fax")
    String fax

    @JsonProperty("Gender")
    String gender

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("BirthDate")
    Date birthDate

    @JsonProperty("AddressType")
    String addressType

    @JsonProperty("Street")
    String street

    @JsonProperty("City")
    String city

    @JsonProperty("County")
    String county

    @JsonProperty("StateCode")
    String stateCode

    @JsonProperty("State")
    String state

    @JsonProperty("ZipCode")
    String zipCode

    @JsonProperty("Country")
    String country

    @JsonProperty("PreferredContactTime")
    String preferredContactTime

    @JsonProperty("PreferredContactMethod")
    String preferredContactMethod

    @JsonProperty("IsEmailAllowed")
    boolean isEmailAllowed

    @JsonProperty("IsSmsAllowed")
    boolean isSmsAllowed

    @JsonProperty("CardNumber")
    String cardNumber

    @JsonProperty("Notes")
    List<CustomerResponseNoteItem> notes

    static class CustomerResponseNoteItem {

        @JsonProperty("NoteId")
        int noteId

        @JsonProperty("Note")
        String note

        @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
        @JsonProperty("ActiveDate")
        Date activeDate

        @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
        @JsonProperty("UpdatedDate")
        Date updatedDate

        @JsonProperty("CreatedBy")
        int createdBy
    }
}