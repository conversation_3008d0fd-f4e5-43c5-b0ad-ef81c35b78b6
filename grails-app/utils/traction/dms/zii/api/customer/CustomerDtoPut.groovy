package traction.dms.zii.api.customer

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerDtoPut {

    @JsonProperty("IsArCustomer")
    Boolean isArCustomer

    @JsonProperty("IsBusiness")
    Boolean isBusiness

    @JsonProperty("EltCode")
    String eltCode

    @JsonProperty("Gender")
    String gender

    @JsonProperty("NameParsed")
    ParsedName NameParsed

    @JsonProperty("CustomerStatus")
    String customerStatus

    @JsonProperty("CustomerNumber")
    String customerNumber

    @JsonProperty("Customfield1")
    String customfield1

    @JsonProperty("Customfield2")
    String customfield2

    @JsonProperty("Customfield3")
    String customfield3

    @JsonProperty("Customfield4")
    String customfield4

    @JsonProperty("Customfield5")
    String customfield5

    @JsonProperty("Contact")
    ZtContact contact

    @JsonProperty("ParentCustomerId")
    Integer parentCustomerId

    @JsonProperty("ParentCustomerName")
    String parentCustomerName

    @JsonProperty("BillWithParent")
    Boolean billWithParent

    @JsonProperty("ShippingSameAsBilling")
    Boolean shippingSameAsBilling

    @JsonProperty("CustomerType")
    String customerType

    @JsonProperty("IsSubCustomer")
    Boolean isSubCustomer

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss")
    @JsonProperty("BirthDate")
    Date birthDate

    @JsonProperty("MaritalStatus")
    String maritalStatus

    @JsonProperty("IncomeLevel")
    String incomeLevel

    @JsonProperty("PreferredContact")
    String preferredContact

    @JsonProperty("PreferredTime")
    String preferredTime

    @JsonProperty("EmailBlocked")
    Boolean emailBlocked

    @JsonProperty("SmsBlocked")
    Boolean smsBlocked

    @JsonProperty("TaxCode")
    String taxCode

    @JsonProperty("Taxable")
    Boolean taxable

    @JsonProperty("ResaleNumber")
    String resaleNumber

    @JsonProperty("TaxPercentageOverride")
    Boolean taxPercentageOverride

    @JsonProperty("TaxPercentage")
    Double TaxPercentage

    @JsonProperty("SalePriceOverrideDiscount")
    Boolean salePriceOverrideDiscount

    @JsonProperty("PartsDiscountCode")
    String partsDiscountCode

    @JsonProperty("PartsDiscountPercentage")
    Double partsDiscountPercentage

    @JsonProperty("PartsDiscountStartDate")
    Date partsDiscountStartDate

    @JsonProperty("PartsDiscountEndDate")
    Date partsDiscountEndDate

    @JsonProperty("ServicePartDiscountCode")
    String servicePartDiscountCode

    @JsonProperty("ServiceLaborDiscountCode")
    String serviceLaborDiscountCode

    @JsonProperty("ServiceLaborDiscountPercentage")
    Double serviceLaborDiscountPercentage

    @JsonProperty("ServiceLaborDiscountStartDate")
    Date serviceLaborDiscountStartDate

    @JsonProperty("ServiceLaborDiscountEndDate")
    Date serviceLaborDiscountEndDate

    @JsonProperty("SubletDiscountCode")
    String subletDiscountCode

    @JsonProperty("ArAccountId")
    Integer arAccountId

    @JsonProperty("TermId")
    Integer termId

    @JsonProperty("TermName")
    String termName

    @JsonProperty("CreditLimit")
    Double creditLimit

    @JsonProperty("CurrentBalance")
    Double currentBalance

    @JsonProperty("AsOfDate")
    Date asOfDate

    @JsonProperty("Address")
    ZtAddress address

    @JsonProperty("ShippingAddress")
    ZtAddress shippingAddress

    @JsonProperty("Notes")
    List<NotesDtoV2> notes

    @JsonProperty("SkipCheckingSimilar")
    Boolean skipCheckingSimilar

    @JsonProperty("ImageUrl")
    String imageUrl

    @JsonProperty("IsLienHolder")
    Boolean isLienHolder

    @JsonProperty("Payroll")
    Boolean payroll

    @JsonProperty("IsReloadCachePayroll")
    Boolean isReloadCachePayroll

    @JsonProperty("FullImageUrl")
    String fullImageUrl

    @JsonProperty("TaxExemptionReasonId")
    Integer taxExemptionReasonId

    @JsonProperty("DealershipId")
    Integer dealershipId

    @JsonProperty("SourceData")
    String sourceData

    @JsonProperty("MexTaxSystem")
    String mexTaxSystem

    @JsonProperty("MexTaxId")
    String mexTaxId

    @JsonProperty("BillingStreet")
    String billingStreet

    @JsonProperty("BillingCity")
    String billingCity

    @JsonProperty("BillingCounty")
    String billingCounty

    @JsonProperty("BillingState")
    String billingState

    @JsonProperty("BillingZipCode")
    String billingZipCode

    @JsonProperty("BillingCountry")
    String billingCountry

    @JsonProperty("BillingFullAddress")
    String billingFullAddress

    @JsonProperty("ShippingFullAddress")
    String shippingFullAddress

    @JsonProperty("BillingLatitude")
    String billingLatitude

    @JsonProperty("BillingLongitude")
    String billingLongitude

    @JsonProperty("ShippingStreet")
    String shippingStreet

    @JsonProperty("ShippingCity")
    String shippingCity

    @JsonProperty("ShippingCounty")
    String shippingCounty

    @JsonProperty("ShippingState")
    String shippingState

    @JsonProperty("ShippingZipCode")
    String shippingZipCode

    @JsonProperty("ShippingCountry")
    String shippingCountry

    @JsonProperty("ShippingLatitude")
    String shippingLatitude

    @JsonProperty("ShippingLongitude")
    String shippingLongitude

    @JsonProperty("ErrorCode")
    String errorCode

    @JsonProperty("InvoiceHistory")
    List<CustomerHistoryDto> invoiceHistory

    @JsonProperty("CustomerSaleHistory")
    List<CustomerSalesHistoryDto> customerSaleHistory

    @JsonProperty("MajorunitPartDiscountCode")
    String majorunitPartDiscountCode

    @JsonProperty("MajorunitPartDiscountPercentage")
    Double majorunitPartDiscountPercentage

    @JsonProperty("LoyalActionCardNumber")
    String loyalActionCardNumber

    @JsonProperty("CreatedBy")
    Integer createdBy

    @JsonProperty("CreatedDate")
    Date createdDate

    @JsonProperty("Id")
    Integer id

    @JsonProperty("SyncToken")
    Integer syncToken

    @JsonProperty("IsActive")
    Boolean isActive

    @JsonProperty("IsSavingFromAudit")
    Boolean isSavingFromAudit

    @JsonProperty("UpdatedBy")
    Integer updatedBy

    @JsonProperty("UpdatedDate")
    Date updatedDate

    @JsonProperty("SkipValidation")
    Boolean skipValidation

    static class ParsedName {

        @JsonProperty("CompanyName")
        String companyName

        @JsonProperty("Title")
        String title

        @JsonProperty("FirstName")
        String firstName

        @JsonProperty("MiddleName")
        String middleName

        @JsonProperty("LastName")
        String lastName

        @JsonProperty("Suffix")
        String suffix

        @JsonProperty("DisplayName")
        String displayName
    }

    static class ZtContact {

        @JsonProperty("Email")
        String email

        @JsonProperty("Phone")
        String phone

        @JsonProperty("Mobile")
        String mobile

        @JsonProperty("Fax")
        String fax

        @JsonProperty("SocialUrl")
        String socialUrl

        @JsonProperty("Website")
        String website

        @JsonProperty("MainEmail")
        String mainEmail

        @JsonProperty("MainPhone")
        String mainPhone

    }

    static class ZtAddress {

        @JsonProperty("Street")
        String street

        @JsonProperty("City")
        String city

        @JsonProperty("County")
        String county

        @JsonProperty("State")
        String state

        @JsonProperty("ZipCode")
        String zipCode

        @JsonProperty("Country")
        String country

        @JsonProperty("FullAddress")
        String fullAddress

    }

    static class CustomerHistoryDto {

        @JsonProperty("InvoiceType")
        String invoiceType

        @JsonProperty("InvoiceNumber")
        String invoiceNumber

        @JsonProperty("InvoiceStatus")
        String invoiceStatus

        @JsonProperty("SaleDate")
        Date saleDate

        @JsonProperty("TotalAmount")
        Double totalAmount

    }

    static class CustomerSalesHistoryDto {

        @JsonProperty("Id")
        Integer id

        @JsonProperty("InvoiceType")
        String invoiceType

        @JsonProperty("InvoiceNumber")
        String invoiceNumber

        @JsonProperty("InvoiceStatus")
        String invoiceStatus

        @JsonProperty("Revision")
        Integer revision

        @JsonProperty("Date")
        Date date

        @JsonProperty("Total")
        Double total

        @JsonProperty("DealershipName")
        String dealershipName

        @JsonProperty("PdfUrl")
        String pdfUrl
    }

    static class NotesDtoV2 {

        @JsonProperty("Id")
        int id

        @JsonProperty("IsActive")
        Boolean isActive

        @JsonProperty("AttachmentName")
        String attachmentName

        @JsonProperty("AttachmentType")
        String attachmentType

        @JsonProperty("Description")
        String description

        @JsonProperty("RefId")
        Integer refId

        @JsonProperty("NoteId")
        Integer noteId

        @JsonProperty("DefaultPin")
        Boolean defaultPin

        @JsonProperty("IsPin")
        Boolean isPin

        @JsonProperty("LineId")
        Integer lineId

        @JsonProperty("DetailId")
        Integer detailId

        @JsonProperty("NoteDate")
        Date noteDate

        @JsonProperty("PinDate")
        Date pinDate

        @JsonProperty("SubType")
        String subType

        @JsonProperty("Type")
        String type

        @JsonProperty("UpdatedByName")
        String updatedByName

        @JsonProperty("SourceData")
        String sourceData

        @JsonProperty("SourceId")
        Integer sourceId

        @JsonProperty("AddedBy")
        String addedBy

        @JsonProperty("UpdatedDate")
        Date updatedDate

    }
}