package traction.dms.zii.api.majorUnitInventory

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class MajorUnitInventoryResponse {

    @JsonProperty("DealerInventoryId")
    int dealerInventoryId

    @JsonProperty("DateGathered")
    Date dateGathered

    @JsonProperty("UnitStatus")
    String unitStatus

    @JsonProperty("StockNumber")
    String stockNumber

    @JsonProperty("ModelYear")
    String modelYear

    @JsonProperty("NewUsed")
    String newUsed

    @JsonProperty("Make")
    String make

    @JsonProperty("Model")
    String model

    @JsonProperty("VIN")
    String VIN

    @JsonProperty("Location")
    String location

    @JsonProperty("Odometer")
    Double odometer

    @JsonProperty("Cylinders")
    String cylinders

    @JsonProperty("HP")
    String HP

    @JsonProperty("BodyStyle")
    String bodyStyle

    @JsonProperty("Color")
    String color

    @JsonProperty("Brand")
    String brand

    @JsonProperty("Condition")
    String condition

    @JsonProperty("Class")
    String className

    @JsonProperty("CodeName")
    String codeName

    @JsonProperty("InvoiceDate")
    Date invoiceDate

    @JsonProperty("InvoiceAmt")
    Double invoiceAmt

    @JsonProperty("Pack")
    Double pack

    @JsonProperty("Holdback")
    Double holdback

    @JsonProperty("FlooringAmount")
    Double flooringAmount

    @JsonProperty("TotalCost")
    Double totalCost

    @JsonProperty("Dsp")
    Double dsp

    @JsonProperty("Msrp")
    Double msrp

    @JsonProperty("OnHold")
    String onHold

    @JsonProperty("DateReceived")
    Date dateReceived

    @JsonProperty("Length")
    String length

    @JsonProperty("Height")
    String height

    @JsonProperty("Width")
    String width

    @JsonProperty("Draft")
    String draft

    @JsonProperty("Beam")
    String beam

    @JsonProperty("GVWR")
    String GVWR

    @JsonProperty("GDW")
    String GDW

    @JsonProperty("InteriorColor")
    String interiorColor

    @JsonProperty("ExteriorColor")
    String exteriorColor

    @JsonProperty("Manufacturer")
    String manufacturer

    @JsonProperty("UnitType")
    String unitType

    @JsonProperty("FloorLayout")
    String floorLayout

    @JsonProperty("FreightCost")
    Double freightCost

    @JsonProperty("FuelType")
    String fuelType

    @JsonProperty("UnitName")
    String unitName

    @JsonProperty("HullConstruction")
    String hullConstruction

    @JsonProperty("Comments")
    String comments

    @JsonProperty("WebUnit")
    Boolean webUnit

    @JsonProperty("WebTitle")
    String webTitle

    @JsonProperty("WebDescription")
    String webDescription

    @JsonProperty("WebPrice")
    Double webPrice

    @JsonProperty("WebPriceHidden")
    Boolean webPriceHidden

    @JsonProperty("Titlestatus")
    String titlestatus

    @JsonProperty("Trimcolor")
    String trimcolor

    @JsonProperty("Unitcondition")
    String unitcondition

    @JsonProperty("Carbcompliance")
    String carbcompliance

    @JsonProperty("DriveType")
    String driveType

    @JsonProperty("Enginecycles")
    String enginecycles

    @JsonProperty("Powertype")
    String powertype

    @JsonProperty("Starttype")
    String starttype

    @JsonProperty("Numacunits")
    String numacunits

    @JsonProperty("Sleepcapacity")
    String sleepcapacity

    @JsonProperty("Barlength")
    String barlength

    @JsonProperty("Bladelength")
    String bladelength

    @JsonProperty("Cuttingwidth")
    String cuttingwidth

    @JsonProperty("VideoUrl")
    String videoUrl

    @JsonProperty("Numslideouts")
    String numslideouts

    @JsonProperty("Turningradius")
    String turningradius

    @JsonProperty("FlooredBy")
    String flooredBy

    @JsonProperty("FloorPlan")
    String floorPlan

    @JsonProperty("PlanStartingDate")
    Date planStartingDate

    @JsonProperty("PlanEndingDate")
    Date planEndingDate

    @JsonProperty("OriginalAmt")
    Double originalAmt

    @JsonProperty("OriginalDate")
    Date originalDate

    @JsonProperty("CurtailmentDate")
    String curtailmentDate

    @JsonProperty("flooredbalance")
    Double flooredbalance

    @JsonProperty("StatusName")
    String statusName

    @JsonProperty("ProductCategoryName")
    String productCategoryName

    @JsonProperty("Dx1ModelId")
    String dx1ModelId

    @JsonProperty("Options")
    List<MajorUnitInventoryResponseOptionItem> options

    @JsonProperty("Parts")
    List<MajorUnitInventoryResponsePartItem> parts

    @JsonProperty("Labors")
    List<MajorUnitInventoryResponseLaborItem> labors
}