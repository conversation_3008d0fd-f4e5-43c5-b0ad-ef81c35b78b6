package traction.dms.zii.api.majorUnitInventory

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class MajorUnitNonInventorySearchCriteria {
    @JsonProperty("Keyword")
    String keyword

    @JsonProperty("Year")
    String year

    @JsonProperty("SupplierName")
    String supplierName

    @JsonProperty("Name")
    String name

    @JsonProperty("Skip")
    Integer skip

    @JsonProperty("Take")
    Integer take
}