package traction.dms.zii.api.majorUnitInventory

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class MajorUnitNonInventorySearchResponse {
    @JsonProperty("TotalCount")
    Integer totalCount

    @JsonProperty("Items")
    List<MajorUnitNonInventorySearchResponseItem> items

    static class MajorUnitNonInventorySearchResponseItem {
        @JsonProperty("ModelId")
        Integer modelId

        @JsonProperty("DisplayName")
        String displayName

        @JsonProperty("CategoryName")
        String categoryName

        @JsonProperty("Year")
        String yaar

        @JsonProperty("SupplierName")
        String supplierName

        @JsonProperty("Name")
        String name

        @JsonProperty("Code")
        String code
    }
}