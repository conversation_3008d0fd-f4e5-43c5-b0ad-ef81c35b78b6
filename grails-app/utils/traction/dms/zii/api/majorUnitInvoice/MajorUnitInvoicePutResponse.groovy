package traction.dms.zii.api.majorUnitInvoice

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class MajorUnitInvoicePutResponse {

    @JsonProperty("MajorUnitInvoiceData")
    MajorUnitInvoicePutRequest majorUnitInvoiceData

    @JsonProperty("ErrorMessage")
    String errorMessage

    @JsonProperty("SaveResult")
    String saveResult

    @JsonProperty("ExternalId")
    String externalId

    @JsonProperty("DealId")
    int dealId

    @JsonProperty("InvoiceNumber")
    String invoiceNumber

    @JsonProperty("InvoiceStatus")
    String invoiceStatus

    @JsonProperty("Revision")
    int revision

}
