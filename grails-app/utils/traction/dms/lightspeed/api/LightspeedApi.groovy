package traction.dms.lightspeed.api

import org.grails.web.json.JSONObject

/**
 * https://cdkglobal.bravais.com/s/tAoPTcpYICnqDLcT67On
 */
class LightspeedApi {
    String name
    String domain
    String encodedAuth // define username and password in config JSON
    String cmf

    LightspeedApi(JSONObject config) {
        name = config.name
        domain = config.domain
        encodedAuth = Base64.getEncoder().encodeToString("${config.username}:${config.password}".getBytes())
        cmf = config.cmf
    }
}
