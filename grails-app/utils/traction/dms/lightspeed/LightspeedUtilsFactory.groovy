package traction.dms.lightspeed

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.AntiSpamLogger
import traction.ConfigService
import traction.DMS
import traction.dms.lightspeed.api.LightspeedApi

@Slf4j
class LightspeedUtilsFactory {

    private final static ConfigService configService
    static {
        configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
    }

    private static Map<String, LightspeedApi> apiMap = [:]

    static <T extends TractionLightspeed> T getUtil(Class<T> clazz, String clientId) {
        LightspeedApi lightspeedApi = getLightspeedApi(clientId)
        if (!lightspeedApi) {
            AntiSpamLogger.error "Can't connect to Lightspeed API: ${clientId}"
            return null
        }
        DMS connectedDms = DMS.findByDmsIdAndSource(lightspeedApi.cmf, DMS.Source.LIGHTSPEED)
        if (!connectedDms) {
            AntiSpamLogger.error "No DMS defined for import Lightspeed clients : ${lightspeedApi.cmf}"
            return null
        }
        switch (clazz) {
            case LightspeedAll.class:
                return new LightspeedAll(connectedDms, lightspeedApi)
            case LightspeedClient.class:
                return new LightspeedClient(connectedDms, lightspeedApi)
        }
        AntiSpamLogger.error "Can't get getUtil of type: ${clazz}"
        return null
    }

    static private LightspeedApi getLightspeedApi(String cmf) {
        if (!cmf) return null
        JSONObject config = (JSONObject) new JSONArray(configService.get("Lightspeed", "credentials")).stream()
                .filter({ obj -> obj instanceof JSONObject })
                .map({ obj -> (JSONObject) obj })
                .filter({ jsonObject -> cmf == jsonObject.get("cmf") })
                .findFirst()
                .orElse(null)
        if (!config) return null
        LightspeedApi existingLightspeedApi = apiMap[cmf]
        if (existingLightspeedApi) {
            return existingLightspeedApi
        }
        LightspeedApi lightspeedApi = new LightspeedApi(config)
        return lightspeedApi
    }

}
