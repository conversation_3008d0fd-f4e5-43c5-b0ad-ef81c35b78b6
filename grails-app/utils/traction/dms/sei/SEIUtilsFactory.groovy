package traction.dms.sei

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.AntiSpamLogger
import traction.ConfigService
import traction.DMS
import traction.dms.sei.api.SEIApi

@Slf4j
class SEIUtilsFactory {

    private final static ConfigService configService
    static {
        configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
    }

    private static Map<String, SEIApi> apiMap = [:]

    static <T extends TractionSEI> T getUtil(Class<T> clazz, String clientId) {
        SEIApi seiApi = getSEIApi(clientId)
        if (!seiApi) {
            AntiSpamLogger.error "Can't connect to SEI API: ${clientId}"
            return null
        }
        DMS connectedDms = DMS.findByDmsIdAndSource(seiApi.cie, DMS.Source.SEI)
        if (!connectedDms) {
            AntiSpamLogger.error "No DMS defined for import SEI clients : ${seiApi.cie}"
            return null
        }
        switch (clazz) {
            case SEIAll.class:
                return new SEIAll(connectedDms, seiApi)
            case SEIClient.class:
                return new SEIClient(connectedDms, seiApi)
        }
        AntiSpamLogger.error "Can't get getUtil of type: ${clazz}"
        return null
    }

    static private SEIApi getSEIApi(String cmf) {
        if (!cmf) return null
        JSONObject config = (JSONObject) new JSONArray(configService.get("SEI", "credentials")).stream()
                .filter({ obj -> obj instanceof JSONObject })
                .map({ obj -> (JSONObject) obj })
                .filter({ jsonObject -> cmf == jsonObject.get("cie") })
                .findFirst()
                .orElse(null)
        if (!config) return null
        SEIApi existingSEIApi = apiMap[cmf]
        if (existingSEIApi) {
            return existingSEIApi
        }
        SEIApi seiApi = new SEIApi(config)
        return seiApi
    }

}
