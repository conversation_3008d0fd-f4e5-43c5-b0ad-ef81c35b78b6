package traction.dms.sei

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.SessionFactory
import traction.ConfigService
import traction.DMS
import traction.dms.sei.api.SEIApi

@Slf4j
abstract class TractionSEI {
    protected final static SessionFactory sessionFactory
    protected final static ConfigService configService
    static {
        sessionFactory = (SessionFactory) Holders.grailsApplication.mainContext.getBean("sessionFactory")
        configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
    }

    TractionSEI(DMS dms, SEIApi seiApi) {
        this.dms = dms
        this.seiApi = seiApi
    }

    protected DMS dms
    protected SEIApi seiApi

}
