package traction.dms.sei.api.customer

class CustomerApiClient extends traction.dms.sei.api.BaseApiClient {
    static final String GET_ENDPOINT = "/api/Person/GetretourneclientTraction"

    CustomerApiClient(traction.dms.sei.api.SEIApi seiApi) {
        super(seiApi)
    }

    Customer get(Integer id) {
        Customer[] customers = getResponseObject("$GET_ENDPOINT?SLecteur=S&SCie=${this.seiApi.cie}&SCieComp=${this.seiApi.cie}&SDatecreation&SDatemodification&SIddebut=$id&Idfin&SNBrecordapasser=&SNBrecordretourner=", Customer[])
        return customers.size() > 0 ? customers[0] : null
    }

    Customer[] search(CustomerSearchCriteria criteria) {
        return getResponseObject("$GET_ENDPOINT?SLecteur=S&SCie=${this.seiApi.cie}&SCieComp=${this.seiApi.cie}&SDatecreation&SDatemodification&SIddebut=&Idfin&SNBrecordapasser=${criteria.skip}&SNBrecordretourner=${criteria.take}", Customer[])
    }
}