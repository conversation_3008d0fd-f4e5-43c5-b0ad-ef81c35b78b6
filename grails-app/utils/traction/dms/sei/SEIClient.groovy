package traction.dms.sei

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.Session
import traction.*
import traction.client.Client
import traction.client.ClientAttributes
import traction.client.ClientService
import traction.client.IndividualClient
import traction.communication.PhoneUtils
import traction.communication.email.EmailUtils
import traction.datatable.DataTablePaging
import traction.dms.sei.api.SEIApi
import traction.dms.sei.api.customer.Customer
import traction.dms.sei.api.customer.CustomerApiClient
import traction.dms.sei.api.customer.CustomerSearchCriteria
import traction.dms.sei.api.customerUnit.CustomerUnit
import traction.dms.sei.api.customerUnit.CustomerUnitApiClient
import traction.utils.BatchSessionManager
import traction.vehicle.Vehicle
import traction.vehicle.VehicleService

@Slf4j
class SEIClient extends TractionSEI {
    private final static ClientService clientService
    private final static VehicleService vehicleService
    static {
        clientService = (ClientService) Holders.grailsApplication.mainContext.getBean("clientService")
        vehicleService = (VehicleService) Holders.grailsApplication.mainContext.getBean("vehicleService")
    }

    CustomerApiClient customerApiClient
    CustomerUnitApiClient customerUnitApiClient

    SEIClient(DMS dms, SEIApi seiApi) {
        super(dms, seiApi)
        this.customerApiClient = new CustomerApiClient(seiApi)
        this.customerUnitApiClient = new CustomerUnitApiClient(seiApi)
    }


    void importClients(Date updatedFrom, Date updatedTo) {
        DataTablePaging dataTablePaging = new DataTablePaging(0, 1000)
        BatchSessionManager batchSessionManager = new BatchSessionManager(sessionFactory, 10)
        for (; ;) {
            log.debug "dataTablePaging.max: ${dataTablePaging.max} dataTablePaging.offset: ${dataTablePaging.offset}"
            Customer[] customers = customerApiClient.search(new CustomerSearchCriteria(
//                    updatedFrom: updatedFrom,
//                    updatedTo: updatedTo,
                    take: dataTablePaging.max,
                    skip: dataTablePaging.offset
            )).findAll { it.client }

            int totalCount = customers.size()
            customers.eachWithIndex { Customer customer, int i ->
                try {
                    log.debug "update: ${customer.client} (${dataTablePaging.offset + i}/$totalCount)"
                    update(customer, batchSessionManager.session)
                } catch (Exception ex) {
                    ex.printStackTrace()
                    AntiSpamLogger.error "Error importClients: ${ex.getMessage()}"
                }
                batchSessionManager.loop()
            }
            /**/
            if (customers.size() != dataTablePaging.max) break
            /**/
            dataTablePaging.offset += dataTablePaging.max
        }
        batchSessionManager.flushAndCommitTransaction()
    }

    Client update(Customer customer, Session session) {
        if (!customer) return null
        ExecutionTime time = new ExecutionTime()
        log.debug "update: ${customer.client}"
        log.debug "EXECUTION TIME GET: ${time.executionTime()}"
        ignoreTestPhoneNumbers(customer)
        Client client = Client.findByExtID(customer.client.toString(), dms)
        boolean doSessionFlush = false
        // Find the client by email
        boolean validEmail = EmailUtils.isValid(customer.email)
        boolean setEmailOfNewClient = false
        if (validEmail) {
            setEmailOfNewClient = true
            Client sameEmail = Client.findByEmail(customer.email)
            if (sameEmail) {
                log.debug "sameEmail:" + sameEmail
                if (!client && !sameEmail.getExtID(dms)) {
                    client = sameEmail
                    log.debug "Takes the sameEmail client"
                } else {
                    setEmailOfNewClient = false
                }
            } else if (client && EmailUtils.isNoMail(client.email)) {
                client.email = customer.email
                doSessionFlush = true
                log.debug "Set the email"
            }
        }
        if (!client) {
            List<Client> sameTel = clientService.findAllByPhoneWithoutExtID([customer.homePhone, customer.cellPhone], dms)
            Client sameTelSameName = sameTel.find { customer.name.toLowerCase().contains(it.firstname?.toLowerCase()) && customer.name.toLowerCase().contains(it.lastname?.toLowerCase()) }
            if (!sameTelSameName) {
                sameTelSameName = sameTel.find { customer.name.toLowerCase().contains(it.firstname?.toLowerCase()) || customer.name.toLowerCase().contains(it.lastname?.toLowerCase()) }
            }
            if (sameTelSameName) {
                client = sameTelSameName
            } else if (sameTel) {
                client = sameTel.first()
            }
            if (!client) {
                client = new IndividualClient()
                if (setEmailOfNewClient) {
                    client.email = customer.email
                    doSessionFlush = true
                }
                client.media = Media.DMS
            } else if (validEmail && EmailUtils.isNoMail(client.email) && setEmailOfNewClient) {
                client.email = customer.email
                doSessionFlush = true
            }
        }

        if (!client.getExtID(dms, customer.client.toString())) {
            client.addToExternalIdentifiers(new ExternalIdentifier(extId: customer.client.toString(), dms: dms))
            doSessionFlush = true
        }
        IndividualClient individualClient = client.asIndividualClient()
        if (individualClient) {
            if (!individualClient.firstname && customer.name) {
                individualClient.firstname = customer.name
            }
            if (!individualClient.phonecell && customer.cellPhone) {
                individualClient.phonecell = PhoneUtils.getValidPhoneNumber(customer.cellPhone)
            }
        }
        if (!client.phone && customer.homePhone) {
            client.phone = PhoneUtils.getValidPhoneNumber(customer.homePhone)
        }
        if (clientService.save(client, true, session)) {
            if (doSessionFlush) {
                session.flush()
            }
            if (customer.address1) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_ADDRESS.message, customer.address1, false, session)
            }
            if (customer.city) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_CITY.message, customer.city, false, session)
            }
            if (customer.province) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_PROVINCE.message, customer.province, false, session)
            }
            if (customer.country) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_COUNTRY.message, customer.country, false, session)
            }
            if (customer.zip) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_POSTAL.message, customer.zip, false, session)
            }

            CustomerUnit[] CustomerUnitList = customerUnitApiClient.get(customer.client)
            CustomerUnitList.each { CustomerUnit customerUnit ->
                if (customerUnit.client == customer.client) {
                    Vehicle vehicle = Vehicle.findByExtID(customerUnit.serialnumber, dms) ?: new Vehicle()
                    if (!vehicle.id) {
                        vehicle.addToExternalIdentifiers(new ExternalIdentifier(extId: customerUnit.serialnumber, dms: dms))
                        doSessionFlush = true
                    }
                    log.debug "Updating unit client ${customerUnit.serialnumber}"
                    vehicle.dms = dms
                    vehicle.client = client
                    vehicle.category = customerUnit.category
                    vehicle.type = Vehicle.Type.CLIENT
                    vehicle.make = customerUnit.make
                    vehicle.model = customerUnit.model
                    vehicle.stockNumber = customerUnit.stocknumber
                    try {
                        vehicle.year = Integer.parseInt(customerUnit.year)
                    } catch(Exception ex) {}
                    vehicle.serialNumber = customerUnit.serialnumber
                    vehicleService.save(vehicle)
                }
            }
        }
        log.debug "EXECUTION TIME TOTAL: ${time.executionTime()}"
        return client
    }

    Client createOrGetClient(Integer customerId, String cmf, Session session) {
        Client client = Client.findByExtID(customerId.toString(), dms)
        if (client) return client
        SEIClient seiClient = SEIUtilsFactory.getUtil(SEIClient.class, cmf)
        if (seiClient) {
            Customer customer = seiClient.customerApiClient.get(customerId)
            return update(customer, session)
        }
        return null
    }

    private void ignoreTestPhoneNumbers(Customer customer) {
        if (customer.homePhone && (PhoneUtils.cleanNumber(customer.homePhone) == "5555555555" || PhoneUtils.cleanNumber(customer.homePhone).startsWith("123456"))) {
            customer.homePhone = null
        }
        if (customer.cellPhone && (PhoneUtils.cleanNumber(customer.cellPhone) == "5555555555" || PhoneUtils.cleanNumber(customer.cellPhone).startsWith("123456"))) {
            customer.cellPhone = null
        }
    }
}
