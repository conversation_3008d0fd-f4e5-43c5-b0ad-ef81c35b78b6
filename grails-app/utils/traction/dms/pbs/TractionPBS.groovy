package traction.dms.pbs

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.SessionFactory
import traction.ConfigService
import traction.DMS

@Slf4j
abstract class TractionPBS {
    protected final static SessionFactory sessionFactory
    protected final static ConfigService configService
    static {
        sessionFactory = (SessionFactory) Holders.grailsApplication.mainContext.getBean("sessionFactory")
        configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
    }

    TractionPBS(DMS dms, traction.dms.pbs.api.PBSApi pbsApi) {
        this.dms = dms
        this.pbsApi = pbsApi
    }

    protected DMS dms
    protected traction.dms.pbs.api.PBSApi pbsApi

}
