package traction.dms.pbs

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.Session
import traction.*
import traction.client.*
import traction.communication.PhoneUtils
import traction.dms.pbs.api.PBSApi
import traction.dms.pbs.api.customer.CustomerApiClient
import traction.dms.pbs.api.customer.CustomerResponse
import traction.dms.pbs.api.customerUnit.CustomerUnit
import traction.dms.pbs.api.customerUnit.CustomerUnitApiClient
import traction.dms.pbs.api.customerUnit.CustomerUnitCriteria
import traction.dms.pbs.api.customerUnit.CustomerUnitResponse
import traction.utils.BatchSessionManager
import traction.vehicle.Vehicle
import traction.vehicle.VehicleService
import traction.vehicle.enums.Odometer

@Slf4j
class PBSClient extends TractionPBS {
    private final static ClientService clientService
    private final static VehicleService vehicleService
    static {
        clientService = (ClientService) Holders.grailsApplication.mainContext.getBean("clientService")
        vehicleService = (VehicleService) Holders.grailsApplication.mainContext.getBean("vehicleService")
    }

    CustomerApiClient customerApiClient
    CustomerUnitApiClient customerUnitApiClient

    PBSClient(DMS dms, PBSApi pbsApi) {
        super(dms, pbsApi)
        this.customerApiClient = new CustomerApiClient(pbsApi)
        this.customerUnitApiClient = new CustomerUnitApiClient(pbsApi)
    }


    void importClients(Date updatedFrom, Date updatedTo) {
        BatchSessionManager batchSessionManager = new BatchSessionManager(sessionFactory, 10)
        CustomerResponse customers = customerApiClient.search(new traction.dms.pbs.api.customer.CustomerSearchCriteria(
                modifiedSince: updatedFrom,
                modifiedUntil: updatedTo,
        ))
        int totalCount = customers.contacts.size()
        customers.contacts.eachWithIndex { traction.dms.pbs.api.customer.Customer customer, int i ->
            try {
                if (!customer.code) {
                    AntiSpamLogger.error "No extId for client found : ${customer.toString()}"
                    return
                }
                log.debug "update: ${customer.code} ($i/$totalCount)"
                update(customer, batchSessionManager.session)
            } catch (Exception ex) {
                ex.printStackTrace()
                AntiSpamLogger.error "Error importClients: ${ex.getMessage()}"
            }
            batchSessionManager.loop()
        }
        batchSessionManager.flushAndCommitTransaction()
    }

    Client update(traction.dms.pbs.api.customer.Customer customer, Session session) {
        ExecutionTime time = new ExecutionTime()
        log.debug "update: ${customer.code}"
        log.debug "EXECUTION TIME GET: ${time.executionTime()}"
        if (!customer) {
            AntiSpamLogger.error "Customer not found for ${customer.code}"
            return null
        }
        ignoreTestPhoneNumbers(customer)
        customer.emailAddress = EmailUtils.cleanEmail(customer.emailAddress)
        Client client = Client.findByExtID(customer.code.toString(), dms)
        boolean doSessionFlush = false
        // Find the client by email
        boolean validEmail = EmailUtils.isValid(customer.emailAddress)
        boolean setEmailOfNewClient = false
        if (validEmail) {
            setEmailOfNewClient = true
            Client sameEmail = Client.findByEmail(customer.emailAddress)
            if (sameEmail) {
                log.debug "sameEmail:" + sameEmail
                if (!client && !sameEmail.getExtID(dms)) {
                    client = sameEmail
                    log.debug "Takes the sameEmail client"
                } else {
                    setEmailOfNewClient = false
                }
            } else if (client && EmailUtils.isNoMail(client.email)) {
                client.email = customer.emailAddress
                doSessionFlush = true
                log.debug "Set the email"
            }
        }
        if (!client) {
            List<Client> sameTel = clientService.findAllByPhoneWithoutExtID([customer.homePhone, customer.cellPhone], dms)
            Client sameTelSameName = sameTel.find { it.firstname?.toLowerCase() == customer.firstName.toLowerCase() && it.lastname?.toLowerCase() == customer.lastName.toLowerCase() }
            if (!sameTelSameName) {
                sameTelSameName = sameTel.find { it.firstname?.toLowerCase() == customer.firstName.toLowerCase() || it.lastname?.toLowerCase() == customer.lastName.toLowerCase() }
            }
            if (sameTelSameName) {
                client = sameTelSameName
            } else if (sameTel) {
                client = sameTel.first()
            }
            if (!client) {
                client = new IndividualClient()
                if (setEmailOfNewClient) {
                    client.email = customer.emailAddress
                    doSessionFlush = true
                }
                client.media = Media.DMS
            } else if (validEmail && EmailUtils.isNoMail(client.email) && setEmailOfNewClient) {
                client.email = customer.emailAddress
                doSessionFlush = true
            }
        }

        if (!client.getExtID(dms, customer.code.toString())) {
            client.addToExternalIdentifiers(new ExternalIdentifier(extId: customer.code.toString(), dms: dms))
            doSessionFlush = true
        }

        if (!client.firstname && customer.firstName) {
            client.firstname = customer.firstName
        }
        if (!client.lastname && customer.lastName) {
            client.lastname = customer.lastName
        }
        if (!client.phone && customer.homePhone) {
            client.phone = PhoneUtils.getValidPhoneNumber(customer.homePhone)
        }
        if (!client.phonecell && customer.cellPhone) {
            client.phonecell = PhoneUtils.getValidPhoneNumber(customer.cellPhone)
        }
        if (clientService.save(client, true, session)) {
            if (doSessionFlush) {
                session.flush()
                doSessionFlush = false
            }
            if (customer.address) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_ADDRESS.message, customer.address, false, session)
            }
            if (customer.city) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_CITY.message, customer.city, false, session)
            }
            if (customer.state) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_PROVINCE.message, customer.state, false, session)
            }
            if (customer.zipCode) {
                clientService.setClientAttribute(client, ClientAttributes.HOME_POSTAL.message, customer.zipCode, false, session)
            }

            CustomerUnitResponse customerUnitResponse = customerUnitApiClient.search(new CustomerUnitCriteria(contactCode: customer.code))
            customerUnitResponse.items.each { CustomerUnit customerUnit ->
                Vehicle vehicle = Vehicle.findByExtID(customerUnit.vehicleId, dms) ?: new Vehicle()
                if (!vehicle.id) {
                    vehicle.addToExternalIdentifiers(new ExternalIdentifier(extId: customerUnit.vehicleId, dms: dms))
                    doSessionFlush = true
                }
                vehicle.client = client
                vehicle.category = customerUnit.vehicleTrim
                vehicle.type = Vehicle.Type.CLIENT
                vehicle.make = customerUnit.vehicleMake
                vehicle.model = customerUnit.vehicleModel
                try {
                    vehicle.year = Integer.parseInt(customerUnit.vehicleYear)
                } catch(Exception ex) {}
                vehicle.serialNumber = customerUnit.vehicleVIN
                vehicle.exteriorColor = customerUnit.vehicleExteriorColor?.description ?: ""
                //vehicle.specifications = customerUnit.vehicleInteriorColor?.description ?: ""
                vehicle.odometer = Odometer.fromKilometers(customerUnit.vehicleOdometer)
                vehicle.stockNumber = customerUnit.vehicleStockNumber
                //product.engine = customerUnit.vehicleEngine
                vehicleService.save(vehicle)
                if (doSessionFlush) {
                    session.flush()
                    doSessionFlush = false
                }
            }
        }
        log.debug "EXECUTION TIME TOTAL: ${time.executionTime()}"
        return client
    }

    Client createOrGetClient(String customerId, Session session) {
        Client client = Client.findByExtID(customerId.toString(), dms)
        if (client) return client
        PBSClient pbsClient = PBSUtilsFactory.getUtil(PBSClient.class, pbsApi.serialNumber)
        if (pbsClient) {
            traction.dms.pbs.api.customer.Customer customer = pbsClient.customerApiClient.get(customerId)
            return update(customer, session)
        }
        return null
    }

    private void ignoreTestPhoneNumbers(traction.dms.pbs.api.customer.Customer customer) {
        if (customer.homePhone && (PhoneUtils.cleanNumber(customer.homePhone) == "5555555555" || PhoneUtils.cleanNumber(customer.homePhone).startsWith("123456"))) {
            customer.homePhone = null
        }
        if (customer.cellPhone && (PhoneUtils.cleanNumber(customer.cellPhone) == "5555555555" || PhoneUtils.cleanNumber(customer.cellPhone).startsWith("123456"))) {
            customer.cellPhone = null
        }
        if (customer.businessPhone && (PhoneUtils.cleanNumber(customer.businessPhone) == "5555555555" || PhoneUtils.cleanNumber(customer.businessPhone).startsWith("123456"))) {
            customer.businessPhone = null
        }
    }
}
