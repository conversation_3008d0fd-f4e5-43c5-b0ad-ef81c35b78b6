package traction.dms.pbs

import groovy.util.logging.Slf4j
import org.grails.web.json.JSONArray
import traction.DMS

@Slf4j
class PBSAll extends TractionPBS {

    private PBSClient pbsClient
    private PBSRepairOrder pbsRepairOrder

    PBSAll(DMS dms, traction.dms.pbs.api.PBSApi pbsApi) {
        super(dms, pbsApi)
        this.pbsClient = new PBSClient(dms, pbsApi)
        this.pbsRepairOrder = new PBSRepairOrder(dms, pbsApi)
    }

    static void executeSyncJob() {
        JSONArray configList = new JSONArray(configService.get("PBS", "credentials"))
        configList.each { config ->
            PBSAll pbsAll = PBSUtilsFactory.getUtil(PBSAll.class, config.serialNumber)
            if (!pbsAll) return
            Date updatedFrom = new Date(System.currentTimeMillis() - 300 * 1000)
            Date updatedTo = new Date(System.currentTimeMillis())
            pbsAll.importAll(updatedFrom, updatedTo)
        }
    }

    void importAll(Date updatedFrom, Date updatedTo) {
        log.info "importAll($updatedFrom, $updatedTo)"
        pbsClient.importClients(updatedFrom, updatedTo)
        //pbsRepairOrder.importRepairOrder(updatedFrom, updatedTo)
    }

}
