package traction.dms.pbs

import grails.util.Holders
import groovy.util.logging.Slf4j
import org.hibernate.Session
import traction.AntiSpamLogger
import traction.DMS
import traction.client.Client
import traction.security.User
import traction.service.*
import traction.utils.BatchSessionManager

@Slf4j
class PBSRepairOrder extends TractionPBS {
    private final static ServiceScheduleService serviceScheduleService
    static {
        serviceScheduleService = (ServiceScheduleService) Holders.grailsApplication.mainContext.getBean("serviceScheduleService")
    }

    traction.dms.pbs.api.repairOrder.RepairOrderApiClient repairOrderApiClient
    traction.dms.pbs.api.unit.UnitApiClient unitApiClient
    PBSClient pbsClient

    PBSRepairOrder(DMS dms, traction.dms.pbs.api.PBSApi pbsApi) {
        super(dms, pbsApi)
        this.pbsClient = new PBSClient(dms, pbsApi)
        this.repairOrderApiClient = new traction.dms.pbs.api.repairOrder.RepairOrderApiClient(pbsApi)
        this.unitApiClient = new traction.dms.pbs.api.unit.UnitApiClient(pbsApi)
    }

    void importRepairOrder(Date updatedFrom, Date updatedTo) {
        BatchSessionManager batchSessionManager = new BatchSessionManager(sessionFactory, 10)
        traction.dms.pbs.api.repairOrder.RepairOrderResponse repairOrderResponse = repairOrderApiClient.search(new traction.dms.pbs.api.repairOrder.RepairOrderCriteria(
                modifiedSince: updatedFrom,
                modifiedUntil: updatedTo,
        ))
        repairOrderResponse.repairOrders.eachWithIndex { traction.dms.pbs.api.repairOrder.RepairOrder repairOrder, int i ->
            try {
                update(repairOrder, batchSessionManager.session)
            } catch (Exception ex) {
                ex.printStackTrace()
                AntiSpamLogger.error "Error importServiceInvoice: ${ex.getMessage()}"
            }
            batchSessionManager.loop()
        }
        batchSessionManager.flushAndCommitTransaction()
    }

    WorkOrder update(traction.dms.pbs.api.repairOrder.RepairOrder repairOrder, Session session) {
        Long roID = repairOrder.repairOrderNumber
        traction.dms.pbs.api.unit.Unit unit = unitApiClient.get(repairOrder.vehicleRef)
        if (!unit) {
            log.warn "No vehicle found for this repair order: ${roID}"
            return null
        }
        log.debug "update: ${roID}"
        Client client = pbsClient.createOrGetClient(repairOrder.contactRef, session)
        WorkOrder.InvoiceType invoiceType = WorkOrder.InvoiceType.SERVICE
        User user = User.findByExtID(repairOrder.bookingUser, dms)
        WorkOrder workOrder = WorkOrder.findByDmsAndRoIDAndInvoiceType(dms, roID, invoiceType) ?: new WorkOrder()
        boolean isNewWorkOrder = workOrder.id ? false : true
        List<WorkOrderJob> newWorkOrderJobs = []
        workOrder.type = WorkOrder.Type.BILL
        workOrder.roID = roID
        workOrder.invoiceType = invoiceType
        workOrder.title = "#${roID}"
        workOrder.client = client
        workOrder.date = repairOrder.dateOpened
        workOrder.promisedDate = repairOrder.datePromised
        //workOrder.appointmentDate = repairOrder.datePromised
        workOrder.status = repairOrder.status ? WorkOrder.Status.getFromFileString(repairOrder.status) : WorkOrder.Status.RO_OPEN
        workOrder.department = user?.currentDepartment
        workOrder.serialnumber = unit.vin
        workOrder.dms = dms
        workOrder.openingAdvisor = user
        workOrder.closingAdvisor = user
        workOrder.setClosed()

        WorkOrderUnit workOrderUnit = workOrder.units.find { it.vin == unit.vin } ?: new WorkOrderUnit()
        workOrderUnit.workOrder = workOrder
        workOrder.addToUnits(workOrderUnit)
        try {
            workOrderUnit.year = Integer.parseInt(unit.year)
        } catch (Exception ex) {
        }
        workOrderUnit.make = unit.make
        workOrderUnit.model = unit.model
        workOrderUnit.vin = unit.vin
        workOrderUnit.engineno = unit.engine
        workOrderUnit.className = unit.trim
        workOrderUnit.odometer = unit.odometer
        workOrderUnit.stockNumber = unit.stockNumber
        workOrderUnit.lineStatus = WorkOrderUnit.LineStatus.getFromString(unit.status)

        repairOrder.requests.each { job ->
            // TODO: fix ca ??
            //workOrder.appointmentDate = job.serviceJobAppointmentDate

            WorkOrderJob workOrderJob = workOrderUnit.jobs.find { it.job == job.repairOrderRequestId } ?: new WorkOrderJob()
            if (!workOrderJob.id) {
                newWorkOrderJobs.add(workOrderJob)
            }
            workOrderJob.workOrderUnit = workOrderUnit
            workOrderUnit.addToJobs(workOrderJob)
            workOrderJob.job = job.repairOrderRequestId
            workOrderJob.description = "${job.requestCode}${job.requestDescription ? ": " + job.requestDescription : ""}"
            workOrderJob.status = WorkOrderJob.Status.getFromFileString(job.status)
            if (!workOrderJob.status) {
                AntiSpamLogger.error "Status not found for : ${job.status}"
                workOrderJob.status = WorkOrderJob.Status.INACTIVE
            }
            workOrderJob.assignmentStatus = WorkOrderJob.AssignmentStatus.getFromString(job.status)
            // TODO: fix ca ??
            workOrderJob.lautopakPartReservationStatus = job.partLines?.size() > 0 ? LautopakPartReservation.Status.AVAILABLE : LautopakPartReservation.Status.NO_PARTS
            if (!workOrderJob.lautopakPartReservationStatus) {
                AntiSpamLogger.error "lautopakPartReservationStatus not found for : ${job.status}"
                workOrderJob.lautopakPartReservationStatus = LautopakPartReservation.Status.NO_PARTS
            }
            int estimatedTime = job.labourLines ? Math.round(job.labourLines.actualHours.sum() * 60 * 60) : 0
            int timeWorked = job.labourLines ? Math.round(job.labourLines.soldHours.sum() * 60 * 60) : 0
            workOrderJob.setTimes(estimatedTime, timeWorked)
            if (workOrderJob.techs) {
                workOrderJob.techs.clear()
            }
            job.labourLines.each { labor ->
                User userTech = User.findByExtID(labor.techRef, dms)
                if (userTech) {
                    workOrderJob.addToTechs(userTech)
                    workOrderJob.addToPreAssignedTechs(userTech)
                } else {
                    log.debug "tech.technicianId not found: ${labor.techRef}"
                }
            }

            job.partLines.each { part ->
                // todo: Il y a jamais de partLines dans leur API, toujours vide!
//                String extId = "W${workOrder.roID}J${workOrderJob.job}P${part.ROPartID.toString()}"
//                LautopakPartReservation reservation = LautopakPartReservation.findByDmsAndExtId(dms, extId) ?: new LautopakPartReservation()
//                reservation.department = workOrder.department
//                reservation.extId = extId
//                reservation.partName = part.partDescription
//                reservation.partSerialNumber = part.partNumber
//                reservation.qty = part.qty
//                reservation.status = LautopakPartReservation.Status.AVAILABLE
//                reservation.dms = dms
//                reservation.workOrder = workOrder
//                workOrder.addToLautopakPartReservations(reservation)
            }
            workOrderJob.setClosed()
        }
        workOrder.calculateTimes()
        workOrder.lautopakPartReservationStatus = LautopakPartReservation.Status.getReservationListStatus(workOrder.jobs.lautopakPartReservationStatus)
        session.save(workOrder)
        session.flush()

        if (isNewWorkOrder || newWorkOrderJobs) {
            session.flush()
            session.clear()
            session.getTransaction().commit()
            if (isNewWorkOrder) {
                serviceScheduleService.newWorkOrder(workOrder)
            }
            newWorkOrderJobs.each {
                serviceScheduleService.newWorkOrderJob(it)
            }
            session.beginTransaction()
        }

        return workOrder
    }
}
