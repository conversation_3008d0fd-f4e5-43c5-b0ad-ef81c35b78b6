package traction.dms.pbs.api

import org.grails.web.json.JSONObject

class PBSApi {
    String name
    String domain
    String encodedAuth // define username and password in config JSON
    String serialNumber

    PBSApi(JSONObject config) {
        name = config.name
        domain = config.domain
        encodedAuth = Base64.getEncoder().encodeToString("${config.username}:${config.password}".getBytes())
        serialNumber = config.serialNumber
    }
}
