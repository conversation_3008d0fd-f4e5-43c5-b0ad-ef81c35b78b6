package traction.dms.pbs.api.customer

class CustomerApiClient extends traction.dms.pbs.api.BaseApiClient {
    static final String POST_ENDPOINT = "/json/reply/ContactGet"

    CustomerApiClient(traction.dms.pbs.api.PBSApi pbsApi) {
        super(pbsApi)
    }

    Customer get(String contactId) {
        CustomerResponse customerResponse = search(new CustomerSearchCriteria(contactId: contactId, serialNumber: this.pbsApi.serialNumber))
        return customerResponse.contacts?.size() > 0 ? customerResponse.contacts[0] : null
    }

    CustomerResponse search(CustomerSearchCriteria criteria) {
        criteria.serialNumber = this.pbsApi.serialNumber
        return postResponseObject("$POST_ENDPOINT", criteria, CustomerResponse)
    }
}