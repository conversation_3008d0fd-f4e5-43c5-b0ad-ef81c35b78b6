package traction.dms.pbs.api.customerUnit

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerUnitCriteria {

    @JsonProperty("SerialNumber")
    String serialNumber

    @JsonProperty("ContactId")
    String contactId

    @JsonProperty("ContactCode")
    String contactCode

    @JsonProperty("ContactLastName")
    String contactLastName

    @JsonProperty("ContactFirstName")
    String contactFirstName

    @JsonProperty("ContactPhoneNumber")
    String contactPhoneNumber

    @JsonProperty("ContactEmailAddress")
    String contactEmailAddress

    @JsonProperty("ContactModifiedSince")
    Date contactModifiedSince

    @JsonProperty("ContactModifiedUntil")
    Date contactModifiedUntil

    @JsonProperty("VehicleId")
    String vehicleId

    @JsonProperty("VehicleStockNumber")
    String vehicleStockNumber

    @JsonProperty("VehicleVIN")
    String vehicleVIN

    @JsonProperty("VehicleLicenseNumber")
    String vehicleLicenseNumber

    @JsonProperty("VehicleModifiedSince")
    Date vehicleModifiedSince

    @JsonProperty("VehicleModifiedUntil")
    Date vehicleModifiedUntil

    @JsonProperty("ShortVIN")
    String shortVIN

}