package traction.dms.pbs.api.customerUnit

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class CustomerUnit {

    @JsonProperty("HubContactId")
    String hubContactId

    @JsonProperty("ContactId")
    String contactId

    @JsonProperty("ContactCode")
    String contactCode

    @JsonProperty("ContactLastName")
    String contactLastName

    @JsonProperty("ContactFirstName")
    String contactFirstName

    @JsonProperty("ContactSalutation")
    String contactSalutation

    @JsonProperty("ContactMiddleName")
    String contactMiddleName

    @JsonProperty("ContactIsInactive")
    boolean contactIsInactive

    @JsonProperty("ContactIsBusiness")
    boolean contactIsBusiness

    @JsonProperty("ContactApartmentNumber")
    String contactApartmentNumber

    @JsonProperty("ContactAddress")
    String contactAddress

    @JsonProperty("ContactCity")
    String contactCity

    @JsonProperty("ContactCounty")
    String contactCounty

    @JsonProperty("ContactState")
    String contactState

    @JsonProperty("ContactZipCode")
    String contactZipCode

    @JsonProperty("ContactBusinessPhone")
    String contactBusinessPhone

    @JsonProperty("ContactHomePhone")
    String contactHomePhone

    @JsonProperty("ContactCellPhone")
    String contactCellPhone

    @JsonProperty("ContactFaxNumber")
    String contactFaxNumber

    @JsonProperty("ContactEmailAddress")
    String contactEmailAddress

    @JsonProperty("ContactNotes")
    String contactNotes

    @JsonProperty("ContactCriticalMemo")
    String contactCriticalMemo

    @JsonProperty("ContactCommunicationPreferences")
    CommunicationPreferences contactCommunicationPreferences

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'")
    @JsonProperty("ContactLastUpdate")
    Date contactLastUpdate

    @JsonProperty("ContactRepairOrderRequiresPO")
    boolean contactRepairOrderRequiresPO

    @JsonProperty("HubVehicleID")
    String hubVehicleID

    @JsonProperty("VehicleId")
    String vehicleId

    @JsonProperty("VehicleStockNumber")
    String vehicleStockNumber

    @JsonProperty("VehicleVIN")
    String vehicleVIN

    @JsonProperty("VehicleLicenseNumber")
    String vehicleLicenseNumber

    @JsonProperty("VehicleFleetNumber")
    String vehicleFleetNumber

    @JsonProperty("VehicleStatus")
    String vehicleStatus

    @JsonProperty("VehicleModelNumber")
    String vehicleModelNumber

    @JsonProperty("VehicleMake")
    String vehicleMake

    @JsonProperty("VehicleModel")
    String vehicleModel

    @JsonProperty("VehicleTrim")
    String vehicleTrim

    @JsonProperty("VehicleType")
    String vehicleType

    @JsonProperty("VehicleYear")
    String vehicleYear

    @JsonProperty("VehicleOdometer")
    Integer vehicleOdometer

    @JsonProperty("VehicleExteriorColor")
    VehicleColor vehicleExteriorColor

    @JsonProperty("VehicleInteriorColor")
    VehicleColor vehicleInteriorColor

    @JsonProperty("VehicleEngine")
    String vehicleEngine

    @JsonProperty("VehicleCylinders")
    String vehicleCylinders

    @JsonProperty("VehicleTransmission")
    String vehicleTransmission

    @JsonProperty("VehicleFuel")
    String vehicleFuel

    @JsonProperty("VehicleNotes")
    String vehicleNotes

    @JsonProperty("VehicleCriticalMemo")
    String vehicleCriticalMemo

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'")
    @JsonProperty("VehicleLastServiceDate")
    Date vehicleLastServiceDate

    @JsonProperty("VehicleLastServiceMileage")
    Integer vehicleLastServiceMileage

    @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'")
    @JsonProperty("VehicleLastUpdate")
    Date vehicleLastUpdate

    @JsonIgnoreProperties(ignoreUnknown = true)
    class VehicleColor {

        @JsonProperty("Code")
        String code

        @JsonProperty("Description")
        String description
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    class CommunicationPreferences {

        @JsonProperty("Email")
        String email

        @JsonProperty("Phone")
        String phone

        @JsonProperty("TextMessage")
        String textMessage

        @JsonProperty("Letter")
        String letter

        @JsonProperty("Preferred")
        String preferred
    }
}