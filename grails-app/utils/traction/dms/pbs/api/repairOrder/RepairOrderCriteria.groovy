package traction.dms.pbs.api.repairOrder

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
class RepairOrderCriteria {

    @JsonProperty("SerialNumber")
    String serialNumber

    @JsonProperty("RepairOrderId")
    String repairOrderId

    @JsonProperty("RepairOrderNumber")
    String repairOrderNumber

    @JsonProperty("Tag")
    String tag

    @JsonProperty("ContactRef")
    String contactRef

    @JsonProperty("ContactRefList")
    List<String> contactRefList

    @JsonProperty("VehicleRef")
    String vehicleRef

    @JsonProperty("VehicleRefList")
    List<String> vehicleRefList

    @JsonProperty("Status")
    String status

    @JsonProperty("CashieredSince")
    Date cashieredSince

    @JsonProperty("CashieredUntil")
    Date cashieredUntil

    @JsonProperty("OpenDateSince")
    Date openDateSince

    @JsonProperty("OpenDateUntil")
    Date openDateUntil

    @JsonProperty("ModifiedSince")
    Date modifiedSince

    @JsonProperty("ModifiedUntil")
    Date modifiedUntil

    @JsonProperty("Shop")
    String shop

}