package traction.config

import traction.category.CategoryFile
import traction.file.FileData

class ImageConfig {
    
    ImageConfig() {
        
    }
    
    ImageConfig(map) {
        this.location = map.location
        this.sizeLimit = map.sizeLimit        
        this.heightLimit = map.heightLimit
        this.widthLimit = map.widthLimit
    }
    
    String location = "/big_data/traction/images/"
    int sizeLimit = 100000;
    int widthLimit = 800;
    int heightLimit = 600;

    String getFileDataPath(FileData fileData) {
        if (fileData.category == CategoryFile.VIDEORECORD.message) {
            return fileData.name
        }
        int indexPt = fileData.name.lastIndexOf(".");
        String fileName = (indexPt == -1) ? fileData.id : fileData.id + fileData.name.substring(indexPt)
        return this.location + fileData.category + "/" + fileName
    }
}

