package traction.category

import traction.permissions.Permission

enum CategoryFile {
    // TODO: SYSTEM pas utilise? DV1-T641
    SYSTEM('category.files.system', null, null),
    /** User profile images */
    USER('category.files.user', null, null, true),
    /** Client profile images */
    CLIENT('category.files.client', null, null),
    /** Draft email inline images */
    DRAFT('category.files.draft', null, null, true),
    /** Department image */
    DEPARTMENT('category.files.department', null, null, true),
    /** Training file */
    TRAINING('category.files.training', Permission.PERM_TRAINING_WRITE, Permission.PERM_TRAINING_READ),

    /** Vehicles prices files */
    PRICE('document.price', Permission.PERM_DOCUMENT_WRITE, Permission.PERM_DOCUMENT_READ),
    /** Vehicles promotions files */
    PROMOTION('document.promotion', Permission.PERM_DOCUMENT_WRITE, Permission.PERM_DOCUMENT_READ),
    /** Vehicles warranties files */
    WARRANTIES('document.warranties', Permission.PERM_DOCUMENT_WRITE, Permission.PERM_DOCUMENT_READ),

    /** Outbound communications files */
    ATTACHMENTS('client.attachments', null, null),

    VEHICLE_ATTACHMENTS('client.vehicleattachments', null, null),

    /** Inbound communications files */
    CLIENT_ATTACHMENTS('client.clientattachments', null, null),
    /** FreePBX Voicemail files */
    VOICEMAIL('filedata.category.voicemail', null, null),
    /** Inbound/outbound communications files removed from client view attachements tab */
    COMMUNICATION('communication', null, null),
    /** Communication draft attachements */
    DRAFT_ATTACHMENTS('client.draftattachments', null, null),
    /** Communication draft bulk attachements */
    DRAFT_BULK_ATTACHMENTS('client.draftBulkAttachments', null, null),
    DRAFT_BULK_ATTACHMENTS_PENDING('client.draftBulkAttachmentsPending', null, null),
    /** Task attachements */
    TASK_ATTACHMENTS('task.attachments', null, null),
    /** WorkflowBoard attachements */
    WORKFLOW_BOARD('category.files.workflowboard', Permission.PERM_WORKFLOW_WRITE, Permission.PERM_WORKFLOW_READ),
    FORM_IMAGES('form.images', null, null, true),
    FORM_DAILYSUMMARY('form.dailysummary', Permission.PERM_FORM_PROFILE, null),
    PRODUCTS('document.products', Permission.PERM_VEHICLE_WRITE, null),
    /** Background images for Traction login */
    LOGIN_BACKGROUND('category.files.login', Permission.PERM_CONFIG, null, true),
    /** Background images for Traction login */
    LOGO('category.files.logo', Permission.PERM_CONFIG, null, true),
    /** Lautopak sync files */
    LAUTOPAK_SYNC('category.files.lautopak', Permission.PERM_CONFIG, Permission.PERM_CONFIG),

    VEHICLE_PICTURE('document.managerproductimage', null, Permission.PERM_VEHICLE_WRITE),

    VEHICLE_OPTION_PICTURE('document.configurator_packcustom', Permission.PERM_VEHICLE, null),

    VEHICLE_WATERMARK('document.managerwatermarkimage', Permission.PERM_VEHICLE_WATERMARK, null),

    VEHICLE_INSPECTION_ITEM('document.trade.option', Permission.PERM_VEHICLE_INSPECTION, Permission.PERM_VEHICLE_INSPECTION),

    @Deprecated // Maintenant VEHICLE_PICTURE
    MANAGERPRODUCTIMAGE('document.managerproductimage', Permission.PERM_VEHICLE_WRITE, null), // de base
    @Deprecated // Maintenant VEHICLE_WATERMARK
    MANAGERWATERMARKIMAGE('document.managerwatermarkimage', Permission.PERM_VEHICLE_WATERMARK, null), // de base + watermark appliquer
    @Deprecated // Maintenant plus besoin puisque les preview ne sont pas storer en fichiers
    MANAGERWATERMARKPREVIEW('document.managerwatermarkpreview', Permission.PERM_VEHICLE_WATERMARK, null), // preview dans interface watermark

    CONFIGURATOR('document.configurator', Permission.PERM_CONFIGURATOR, null),
    CONFIGURATOR_PACKCUSTOM('document.configurator_packcustom', Permission.PERM_CONFIGURATOR, null),
    PDF('document.pdf', null, null),
    /** Trade files */
    TRADE('document.trade', null, null),
    /** Opportunity files */
    OPPORTUNITY('document.opportunity', null, null),
    @Deprecated // Maintenant VEHICLE_INSPECTION_ITEM
    TRADE_OPTION('document.trade.option', null, null),
    OPPORTUNITY_OPTION('document.opportunity.option', null, null),
    /** WorkOrder files */
    WORKORDER('document.workorder', null, null),
    STRIPE_SESSION('category.files.stripesession', null, null),
    /** VideoRoom files */
    VIDEORECORD('video.record', null, null),
    MAIL_TEMPLATE('client.mailtemplate', null, null),

    final String message
    final Permission readPermission
    final Permission writePermission
    final boolean isPublic = false

    private CategoryFile(String message, Permission readPermission, Permission writePermission, boolean isPublic = false) {
        this.message = message
        this.readPermission = readPermission
        this.writePermission = writePermission
        this.isPublic = isPublic
    }

    static CategoryFile getCategory(String message) {
        return values().find { it.message == message }
    }

    static List<CategoryFile> getAllVehicle() {
        return [
                VEHICLE_PICTURE,
                VEHICLE_WATERMARK
        ]
    }

    static List<CategoryFile> getAll() {
        return values() as List<CategoryFile>
    }
}
