package traction

class ChangeTracker {
    private int changesCount = 0

    private ChangeTracker() {}

    static ChangeTracker create() {
        return new ChangeTracker()
    }

    ChangeTracker add(boolean hasChanged) {
        if (hasChanged) change()
        return this
    }

    void change() {
        changesCount++
    }

    boolean hasChanged() {
        return changesCount ? true : false
    }

}
