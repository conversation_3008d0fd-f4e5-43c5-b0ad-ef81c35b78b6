package traction.salesTracking.dataTable

import grails.converters.JSON
import grails.web.servlet.mvc.GrailsParameterMap
import traction.ExecutionTime
import traction.cotation.Cotation
import traction.cotation.CotationElement
import traction.cotation.CotationElementSearch
import traction.datatable.DataTableColumnFilters
import traction.salesTracking.dataTable.statistics.SalesTrackingDataTableFilters
import traction.salesTracking.dataTable.statistics.SalesTrackingStatisticDataTable
import traction.salesTracking.dataTable.statistics.SalesTrackingTopStatistics

class SalesTrackingDataTable {
    private CotationElementSearch cotationElementSearch
    private String draw
    private String sessionCurrentlocale
    private SalesTrackingStatisticDataTable statisticDataTable
    private List<Long> idsList

    SalesTrackingDataTable(GrailsParameterMap params){
        boolean statistics = params.boolean('statistics', false)
        this.draw = params.draw
        DataTableColumnFilters dataTableColumnFilters = new DataTableColumnFilters(params.filter)
        SalesTrackingTopStatistics salesTrackingTopStatistics = new SalesTrackingTopStatistics(params.list('salesTrackingTopStatisticsFilters[]'))
        SalesTrackingDataTableFilters salesTrackingDataTableFilters = statistics ? new SalesTrackingDataTableFilters(params.statisticsTableFilters) : null
        this.cotationElementSearch = new CotationElementSearch(dataTableColumnFilters, params, salesTrackingTopStatistics, salesTrackingDataTableFilters)
        this.idsList = cotationElementSearch.listIds()
        if(statistics){
            println "went in statistics"
            println "statisticDataTable: ${salesTrackingTopStatistics}"

            this.statisticDataTable = new SalesTrackingStatisticDataTable(salesTrackingTopStatistics, idsList)
            println "statisticDataTable: ${this.statisticDataTable}"
        }
    }

    JSON getData(){
        ExecutionTime executionTime = new ExecutionTime()
        int recordsTotal = this.cotationElementSearch.countTotal()
        println "getData countTotal: " + executionTime.restart()
        int recordsFiltered = this.cotationElementSearch.count()
        println "getData count: " + executionTime.restart()
        List<CotationElement> cotationElements = this.cotationElementSearch.list()
        println "getData list: " + executionTime.restart()

        Map map = [
                draw                : draw,
                recordsTotal        : recordsTotal,
                recordsFiltered     : recordsFiltered,
                data                : cotationElements.collect {CotationElement cotationElement ->
                    new CotationElementDataTableRow(cotationElement)
                },
                statisticDataTable: statisticDataTable?.render()
        ]
        println "getData Map: " + executionTime.executionTime()
        JSON json = map as JSON
        println "getData JSON:" + executionTime.executionTime()

        return json
    }

    int count() {
        return cotationElementSearch.count()
    }

    List<CotationElement> list() {
        return cotationElementSearch.list()
    }
}
