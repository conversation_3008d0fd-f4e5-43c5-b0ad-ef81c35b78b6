package traction

import grails.util.Holders
import groovy.transform.CompileStatic
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder

@CompileStatic
class I18N {

    private final static MessageSource messageSource
    static {
        messageSource = (MessageSource) Holders.grailsApplication.mainContext.getBean("messageSource")
    }

    static String m(def key) {
        return m(key, null, null)
    }

    static String m(def key, java.lang.Object[] args) {
        return m(String.valueOf(key), args, null)
    }

    static String m(def key, java.lang.Object[] args, Locale locale) {
        return m(key, args, String.valueOf(key), locale)
    }

    static String m(def key, java.lang.Object[] args, String defaultValue, Locale locale) {
        return key ? messageSource.getMessage(String.valueOf(key), args, defaultValue, locale ?: LocaleContextHolder.getLocale()) : ""
    }

}