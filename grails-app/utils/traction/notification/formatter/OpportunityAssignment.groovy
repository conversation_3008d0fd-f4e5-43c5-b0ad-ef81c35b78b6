package traction.notification.formatter

import traction.I18N
import traction.opportunity.Opportunity
import traction.security.User

class OpportunityAssignment implements NotificationFormatter {
    Opportunity opportunity

    OpportunityAssignment(Opportunity opportunity) {
        this.opportunity = opportunity
    }

    @Override
    User getAvatar() {
        return null // Missing notification sender *creator*
    }

    @Override
    String getBody() {
        return I18N.m('notification.event.opportunity.assigned.text', [getLinkText()] as String[])
    }

    @Override
    String getBodyHtml() {
        return I18N.m('notification.event.opportunity.assigned.text', [makeLink(getLinkText(), getHref())] as String[])
    }

    @Override
    String getTitle() {
        return I18N.m("notification.event.opportunity.title")
    }

    @Override
    String getHref() {
        if (opportunity) return "${getTractionBaseUrl()}/opportunity/index/${opportunity.id}"
        return null
    }

    private String getLinkText() {
        return "${opportunity.name} (${opportunity.client.fullName})"
    }
}
