package traction.notification.formatter

import traction.I18N
import traction.VisitLog
import traction.security.User

class NewVisitLog implements NotificationFormatter {

    VisitLog visitLog

    NewVisitLog(VisitLog visitLog) {
        this.visitLog = visitLog
    }

    @Override
    User getAvatar() {
        return visitLog.creator
    }

    @Override
    String getTitle() {
        return I18N.m("notification.event.visitlog.title")
    }

    @Override
    String getBody() {
        return I18N.m('notification.event.newvisitlog.text', [
                visitLog.creator?.fullName,
                getNoteOnClient()
        ] as String[])
    }

    @Override
    String getBodyHtml() {
        return I18N.m('notification.event.newvisitlog.text', [
                visitLog.creator?.fullName,
                visitLog.client ? makeLink(getNoteOnClient(), getHref()) : getNoteOnClient()
        ] as String[])
    }

    @Override
    String getHref() {
        return visitLog.client ? "${getTractionBaseUrl()}/client/index/${visitLog.clientId}" : "${getTractionBaseUrl()}/visitLog/index"
    }

    private String getNoteOnClient() {
        return visitLog.client?.fullName ?: visitLog.noteOnClient
    }
}