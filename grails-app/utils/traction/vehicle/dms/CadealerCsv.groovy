package traction.vehicle.dms


import groovy.util.logging.Slf4j

@Slf4j
public class CadealerCsv extends DmsConnector {

    static final Map rules = [
            delimiter: "vehicle",
            columns  : [
                    [
                            "default": "DATE_TODAY_YEAR",
                            "prop": "vehicle.year",
                            "column": "year"
                    ],
                    [
                            "prop": "vehicle.make",
                            "column": "make"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.model",
                            "column": "description"
                    ],
                    [
                            "prop": "vehicle.stockNumber",
                            "column": "Stocknumber"
                    ],
                    [
                            "default": "false",
                            "prop": "vehicle.reserved",
                            "column": "reserved",
                            "mapping": [
                                    "False": "false",
                                    "True": "true"
                            ]
                    ],
                    [
                            "default": "false",
                            "mapping": [
                                    "False": "false",
                                    "True": "true"
                            ],
                            "prop": "vehicle.isUsed",
                            "column": "isused"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.modelCode",
                            "column": "model"
                    ],
                    [
                            "mapping": [
                                    "True": "USED"
                            ],
                            "prop": "vehicle.modelCode",
                            "column": "isused"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.dateReceipted",
                            "column": "datereceived"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.dateDelivery",
                            "column": "datedelivered"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.dateDue",
                            "column": "datedue"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.dateSold",
                            "column": "datepaid"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "NbHP",
                            "specification": [
                                    "name": "ns1:Puissance",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "Height",
                            "specification": [
                                    "name": "Cabine",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "Width",
                            "specification": [
                                    "name": "Materiel",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "Draft",
                            "specification": [
                                    "name": "Plan",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "Beam",
                            "specification": [
                                    "name": "auxiliairehydra",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.transportCost",
                            "column": "transport"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.cost",
                            "column": "cost"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.dealerNet",
                            "column": "dealernet"
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.options",
                            "column": "OptionsList"
                    ],
                    [
                            "prop": "vehicle.listingStatus",
                            "value": "TRACTION"
                    ],
                    [
                            "prop": "vehicle.category",
                            "column": "category"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.msrp",
                            "column": "priceSold"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.promoSpecialPrice",
                            "column": "discount"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.customPrice2",
                            "column": "cost|additionalCost",
                            "function": "SUM"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.weight.KILOGRAMS",
                            "column": "weight"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "weight",
                            "specification": [
                                    "name": "Poids",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.odometer.KILOMETERS",
                            "column": "odometer"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.specifications",
                            "column": "odometer",
                            "specification": [
                                    "name": "Kilometers",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "prop": "vehicle.specifications",
                            "column": "FuelType",
                            "specification": [
                                    "name": "fueltype",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "prop": "vehicle.specifications",
                            "column": "color",
                            "specification": [
                                    "name": "Couleur",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.exteriorColor",
                            "column": "color"
                    ],
                    [
                            "default": "0",
                            "prop": "vehicle.length",
                            "column": "Longueur"
                    ],
                    [
                            "prop": "vehicle.specifications",
                            "column": "Longueur",
                            "specification": [
                                    "name": "length",
                                    "category": "SPECIFICATION"
                            ]
                    ],
                    [
                            "prop": "vehicle.serialNumber",
                            "column": "serialnumber"
                    ],
                    [
                            "default": "4",
                            "prop": "vehicle.status",
                            "column": "status",
                            "mapping": [
                                    "SOLD_DELIVERED": "8",
                                    "IN_STOCK": "4",
                                    "TRADE": "9",
                                    "VENTE EN GROS": "4"
                            ]
                    ],
                    [
                            "default": "NULL",
                            "prop": "vehicle.location",
                            "column": "location"
                    ],
                    [
                            "prop": "vehicle.specifications",
                            "column": "cylinders",
                            "specification": [
                                    "name": "ns1:Nb_cylindre",
                                    "category": "SPECIFICATION"
                            ]
                    ]
            ]
    ]

    @Override
    public String getTitle() {
        return "vehicle.dms.cadealercsv";
    }
}

