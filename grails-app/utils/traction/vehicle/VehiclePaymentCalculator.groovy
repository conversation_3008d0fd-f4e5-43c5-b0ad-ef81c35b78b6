package traction.vehicle

class VehiclePaymentCalculator {

    static final double MONTHS_IN_YEAR = 12
    static final double WEEKS_IN_YEAR = 52

    static Double calculateWeeklyPayment(Vehicle vehicle) {
        if (!vehicle.compiledData.estimatedLoanAmount || !vehicle.loanTermWeeks) return null
        try {
            if (!vehicle.loanRate) {
                return vehicle.compiledData.estimatedLoanAmount / vehicle.loanTermWeeks
            }
            double loanPercentRate = vehicle.loanRate / 100
            return (vehicle.compiledData.estimatedLoanAmount * (loanPercentRate / WEEKS_IN_YEAR)) / (1 - Math.pow(1 + (loanPercentRate / WEEKS_IN_YEAR), -vehicle.loanTermWeeks))
        } catch (Exception e) {
        }
        return null
    }

    static Double calculateMonthlyPayment(Vehicle vehicle) {
        if (!vehicle.compiledData.estimatedLoanAmount || !vehicle.loanTermMonths) return null
        try {
            if (!vehicle.loanRate) {
                return vehicle.compiledData.estimatedLoanAmount / vehicle.loanTermMonths
            }
            double loanPercentRate = vehicle.loanRate / 100
            return (vehicle.compiledData.estimatedLoanAmount * loanPercentRate / MONTHS_IN_YEAR) / (1 - Math.pow(1 + loanPercentRate / MONTHS_IN_YEAR, -vehicle.loanTermMonths))
        } catch (Exception e) {
        }
        return null
    }
}

