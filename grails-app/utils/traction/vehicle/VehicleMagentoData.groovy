package traction.vehicle

import traction.vehicle.annotations.VehicleFeedVariableField
import traction.vehicle.annotations.VehicleSelectionnableOptionField
import traction.vehicle.annotations.VehicleTextVariableField
import traction.vehicle.annotations.VehicleXlsVariableField

class VehicleMagentoData {

    // TODO: check to add indexes
    static constraints = {
        crsell nullable: true
        upsell nullable: true
        vehicleNavAll size: 0..16777215, nullable: true
        vehicleNavCur size: 0..16777215, nullable: true
        category size: 0..16777215, nullable: true
        metaDescription size: 0..16777215, nullable: true
        metaTitle size: 0..16777215, nullable: true
        metaKey size: 0..16777215, nullable: true
        htmlH1 nullable: true
        htmlH2 nullable: true
        sku nullable: true
        attrSet nullable: true
        visibility nullable: true
        onWebsite nullable: true
    }

    static mapping = {
        htmlH1 column: 'magento_data_html_h1'
        htmlH2 column: 'magento_data_html_h2'
    }

    // Contiennent des liste d'ids separer par ,
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String crsell
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String upsell
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String vehicleNavAll
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String vehicleNavCur

    /***
     * Hold multiple values separated by ;
     */
    @VehicleSelectionnableOptionField("magentoData_category")
    @VehicleTextVariableField(group="magento")
    @VehicleXlsVariableField
    String category
    @VehicleSelectionnableOptionField("magentoData_attrSet")
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String attrSet
    @VehicleSelectionnableOptionField("magentoData_visibility")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String visibility
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String sku

    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String metaDescription
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String metaTitle
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String metaKey

    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String htmlH1
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String htmlH2
    @VehicleTextVariableField(group="magento")
    @VehicleFeedVariableField
    Boolean onWebsite = false

    static List<String> getVehicleFeedVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it.name }
    }

    static List<String> getVehicleBuildVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it }
    }
}
