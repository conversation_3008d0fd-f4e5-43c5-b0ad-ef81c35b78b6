package traction.vehicle.valuemodifier

import traction.vehicle.VehicleSpecification
import traction.vehicle.inheritance.InheritedVehicle
import traction.vehicle.inheritance.InheritedVehicleSpecification

// GetSpecificationJsonData
class DataToJSon implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        // TODO: tester si le code fonctionne bien
        InheritedVehicle inheritedVehicle = vehicle.asInheritedVehicle()
        Set<InheritedVehicleSpecification> vehicleListSpecs = inheritedVehicle.getInheritedSpecifications().getCompiled()
        if (vehicleListSpecs) {
            def curdatatxt = ""

            // TODO: simplifier?
            curdatatxt = "{"
            String oldcat = ""
            int first = 0
            String finalcat = ""
            String finalname = ""
            String finalval = ""
            vehicleListSpecs.each {
                String curcat = it.category
                String curname = it.name
                String curvalue = it.value
                curcat = curcat.replace('"', '')
                curname = curname.replace('"', '')
                curvalue = curvalue.replace('"', '')
                if (finalcat.equals("")) {
                    finalcat = curcat
                } else {
                    finalcat = finalcat + "|" + curcat
                }
                if (finalname.equals("")) {
                    finalname = curname
                } else {
                    finalname = finalname + "|" + curname
                }
                if (finalval.equals("")) {
                    finalval = curvalue
                } else {
                    finalval = finalval + "|" + curvalue
                }
                if (oldcat.equalsIgnoreCase("") || !oldcat) {
                    oldcat = curcat
                }
                if (curcat.equalsIgnoreCase(oldcat) == false) {
                    oldcat = curcat
                    first = 0
                    curdatatxt = curdatatxt + "},";
                }

                if (first == 0) {
                    curdatatxt = curdatatxt + '"' + curcat + '"' + ':{'
                    curdatatxt = curdatatxt + '"' + curname + '"' + ":" + '"' + curvalue + '"';
                    first = 1
                } else {
                    curdatatxt = curdatatxt + "," + '"' + curname + '"' + ":" + '"' + curvalue + '"';
                }
            }
            curdatatxt = curdatatxt + "}}"

            switch (value1) {
                case "json":
                    return curdatatxt
                case "category":
                    return finalcat
                case "name":
                    return finalname
                case "value":
                    return finalval
            }
        }
        return originalValue
    }
}

