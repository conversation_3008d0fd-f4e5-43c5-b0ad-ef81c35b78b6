package traction.vehicle.valuemodifier

class Otherimagesmax implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        def inherited = vehicle.asInheritedVehicle()
        if (inherited.getInheritedPictures()) {
            List<String> images = inherited.getInheritedPictures().getListWithWatermarkedPicture().collect { it.getPublicUrl(false) }
            String floorplan = inherited?.getInheritedFloorPlanPicture()?.getFloorPlanPicture()?.getPublicUrl(false)
            boolean first = true
            int cursize = 0
            int maxsize = Integer.parseInt(value3)
            images.each {
                if (first.equals(false) && cursize < maxsize) {
                    if (originalValue.equals("")) {
                        originalValue = value1 + it
                    } else {
                        originalValue = originalValue + value2 + value1 + it
                    }
                    cursize = cursize + 1
                }
                first = false
            }
            if (floorplan){
                if (originalValue.equals("")) {
                    originalValue = value1 + floorplan
                } else {
                    originalValue = originalValue + value2 + value1 + floorplan.toString()
                }
            }
        }
        return originalValue
    }
}
