package traction.vehicle.valuemodifier

class IfGreatherthan implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        String cellValue1 = ValueModifierUtils.getValueFromVehicle(vehicle, value1)
        String cellValue2 = ValueModifierUtils.getValueFromVehicle(vehicle, value2)
        try {
            if (cellValue1 && cellValue2 && Double.parseDouble(cellValue1) >= (Double.parseDouble(cellValue2))) {
                return originalValue + cellValue1
            } else {
                return originalValue + cellValue2
            }
        } catch (Exception e) {
            try {
                if (cellValue1 && cellValue2 && cellValue1 >= (Double.parseDouble(cellValue2))) {
                    return originalValue + cellValue1.toString()
                } else {
                    return originalValue + cellValue2
                }
            } catch (Exception e2) {
                try {
                    if (cellValue1 && cellValue2 && Double.parseDouble(cellValue1) >= (cellValue2)) {
                        return originalValue + cellValue1
                    } else {
                        return originalValue + cellValue2.toString()
                    }
                }
                catch (Exception e3) {
                    try {
                        if (cellValue1 && cellValue2 && cellValue1 >= (cellValue2)) {
                            return originalValue + cellValue1.toString()
                        } else {
                            return originalValue + cellValue2.toString()
                        }
                    }
                    catch (Exception e4) {
                    }
                }
            }
        }
        return originalValue
    }
}
