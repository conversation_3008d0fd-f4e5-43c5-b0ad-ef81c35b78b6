package traction.vehicle.valuemodifier
// AppendCellReplaceIfEqual?
class CellReplaceText implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        String cellValue1 = ValueModifierUtils.getValueFromVehicle(vehicle, value1)
        if (cellValue1.equals(value2)) {
            return originalValue + cellValue1.replace(value2, value3)
        }
        return originalValue
    }
}