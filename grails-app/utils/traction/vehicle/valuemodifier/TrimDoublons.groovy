package traction.vehicle.valuemodifier
// AppendCellUniqueWords
class TrimDoublons implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        String cellValue = ValueModifierUtils.getValueFromVehicle(vehicle, value1)
        if (cellValue) {
            String[] words = null;
            try {
                words = cellValue.split("\\W+")
            }
            catch (Exception e) {
                words = cellValue
            }
            def doneword = []
            words.each {
                int wordtoadd = 1
                def curword = it
                doneword.each {
                    if (curword.equals(it)) {
                        wordtoadd = 0
                    }
                }
                if (wordtoadd.equals(1)) {
                    doneword.add(curword)
                }
            }
            String newreturnString = ""
            doneword.each {
                if (newreturnString.equals("")) {
                    newreturnString = it
                } else {
                    newreturnString = newreturnString + " " + it
                }
            }
            return originalValue + newreturnString
        }
        return originalValue
    }
}
