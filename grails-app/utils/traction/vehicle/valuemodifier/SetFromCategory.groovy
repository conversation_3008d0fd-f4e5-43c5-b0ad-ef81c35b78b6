package traction.vehicle.valuemodifier

import traction.DateUtils

class SetFromCategory implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        if (value2) {
            List<String> categories = DateUtils.splitNonRegex(value2, "|")
            if (categories.contains(vehicle.category)) {
                if (value1.contains("[%")) {
                    DateUtils.splitNonRegex(value1, "[%").each {
                        if (it.contains("%]")) {
                            value1 = value1.replace("[%" + DateUtils.splitNonRegex(it, "%]")[0] + "%]", vehicle[DateUtils.splitNonRegex(it, "%]")[0]])
                        }
                    }
                }
                if (value1) {
                    return value1
                }
            }
        }
        return originalValue
    }
}
