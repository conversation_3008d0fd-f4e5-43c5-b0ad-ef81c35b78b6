package traction.vehicle.valuemodifier
// GetCellValueIfGreaterThan0
class IfGreaterthan0 implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        String cellValue1 = ValueModifierUtils.getValueFromVehicle(vehicle, value1)
        if (Double.parseDouble(cellValue1) > 0) {
            return cellValue1
        }
        return originalValue
    }
}

