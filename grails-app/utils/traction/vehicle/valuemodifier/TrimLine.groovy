package traction.vehicle.valuemodifier
// AppendCellWithoutLineBreaks
class TrimLine implements ValueModifier {
    @Override
    String execute(String originalValue, def vehicle, String value1, String value2, String value3, String value4, String value5) {
        String cellValue = ValueModifierUtils.getValueFromVehicle(vehicle, value1)
        if (cellValue) {
            return originalValue + cellValue.replaceAll("[\r\n]", " ")
        }
        return originalValue
    }
}
