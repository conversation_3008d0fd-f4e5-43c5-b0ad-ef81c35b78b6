package traction.vehicle.group

import product.ManagerProductGroup
import traction.vehicle.VehicleAnnotated
import traction.vehicle.annotations.VehicleGroupFormulaVariableField
import traction.vehicle.VehicleCompiledData
import traction.vehicle.inheritance.InheritedVehicle

class VehicleFormulaExpressionUtil {

    /**
     * Example: ["promoInternalAmount": "promo_internal_end_date"]
     */
    private static Map<String, String> getVehicleEndDateMap() {
        return VehicleAnnotated.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleGroupFormulaVariableField) }.collectEntries {
            [(it.name): it.getAnnotation(VehicleGroupFormulaVariableField).promoEndDateProperty()]
        }
    }

    private static Map<String, String> getVehicleStartDateMap() {
        return VehicleAnnotated.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleGroupFormulaVariableField) }.collectEntries {
            [(it.name): it.getAnnotation(VehicleGroupFormulaVariableField).promoStartDateProperty()]
        }
    }

    static String camelCaseToSnakeCase(String camelCase) {
        return camelCase.replaceAll(/([a-z])([A-Z])/, '$1_$2').toLowerCase()
    }

    static String buildSqlInheritanceValueExpression(String vehicleTableAlias, String vehiclePropertyName, String vehicleTableColumnName, Integer defaultValueIfNull = null) {
        if (!InheritedVehicle.POSSIBLE_INHERITED_FIELDS.contains(vehiclePropertyName)) {
            return vehicleTableAlias + "." + vehicleTableColumnName
        }

        if (vehicleTableColumnName == "group_id") {
            ManagerProductGroup defaultGroup = ManagerProductGroup.getDefault()
            if (defaultGroup) {
                defaultValueIfNull = defaultGroup.id
            }
        }
        return """
IFNULL(
    IF(
        ${buildSqlPromoDatesValidCondition(vehicleTableAlias, vehiclePropertyName)},
        ${buildSqlSimpleInheritanceValue(vehicleTableAlias, vehiclePropertyName, vehicleTableColumnName)},
        ${defaultValueIfNull}
    ),
    ${defaultValueIfNull}
)
"""
    }

    static String buildSqlPromoDatesValidCondition(String vehicleTableAlias, String vehiclePropertyName) {
        List<String> conditions = []
        String endDateProperty = getVehicleEndDateMap()[vehiclePropertyName]
        if (endDateProperty) {
            String endDateValueSql = buildSqlSimpleInheritanceValue(vehicleTableAlias, endDateProperty, camelCaseToSnakeCase(endDateProperty))
            conditions.add("(${endDateValueSql} IS NOT NULL AND ${endDateValueSql} > NOW())")
        }

        String startDateProperty = getVehicleStartDateMap()[vehiclePropertyName]
        if (startDateProperty) {
            String startDateValueSql = buildSqlSimpleInheritanceValue(vehicleTableAlias, startDateProperty, camelCaseToSnakeCase(startDateProperty))
            conditions.add("(${startDateValueSql} IS NULL OR ${startDateValueSql} < NOW())")
        }

        return conditions ? conditions.join(" AND ") : "true"
    }

    static String buildSqlSimpleInheritanceValue(String vehicleTableAlias, String vehiclePropertyName, String vehicleTableColumnName) {
        return """
IF(
    ${buildSqlHasInheritanceOnField(vehicleTableAlias, vehiclePropertyName)},
    (SELECT root.$vehicleTableColumnName FROM vehicle root WHERE root.id = ${vehicleTableAlias}.root_vehicle_id),
    ${vehicleTableAlias}.$vehicleTableColumnName
)
"""
    }

    static String buildSqlHasInheritanceOnField(String vehicleTableAlias, String vehiclePropertyName) {
        String vehicleInheritancePropertyName = InheritedVehicle.getInheritanceFieldName(vehiclePropertyName)
        return """
${vehicleTableAlias}.root_vehicle_id IS NOT NULL AND EXISTS (
    SELECT 1
    FROM vehicle_inheritance_rule_inherited_fields ${vehicleTableAlias}_fields
    WHERE 
        ${vehicleTableAlias}_fields.vehicle_inheritance_rule_id = ${vehicleTableAlias}.vehicle_inheritance_rule_id AND
        ${vehicleTableAlias}_fields.inherited_fields_string = '$vehicleInheritancePropertyName'
) AND NOT EXISTS (
    SELECT 1
    FROM vehicle_excluded_inheritance_fields ${vehicleTableAlias}_excluded_fields
    WHERE 
        ${vehicleTableAlias}_excluded_fields.vehicle_id = ${vehicleTableAlias}.id AND
        ${vehicleTableAlias}_excluded_fields.excluded_inheritance_fields_string = '$vehicleInheritancePropertyName'
)
"""
    }

    static List<String> getPossibleFormulaFields() {
        List<String> fields = VehicleAnnotated.getVehicleGroupFormulaFields()
        fields.addAll(VehicleCompiledData.getVehicleGroupFormulaFields().collect { "compiledData_" + it })
        return fields
    }

    static VehicleFormulaExpression parse(ManagerProductGroup group, String originalGroupProperty, String formula) {
        if (!formula) return new NullVehicleFormulaExpression()
        List<String> tokens = tokenize(formula)
        List<String> rpn = toRPN(tokens)
        VehicleFormulaExpression ast = parseRPN(group, originalGroupProperty, rpn)
        return ast
    }

    // Tokenize the input formula
    private static List<String> tokenize(String str) {
        List<String> tokens = []
        def matcher = str =~ /(\d+\.?\d*|\+|\-|\*|\/|\(|\)|[a-zA-Z_]\w*)/
        while (matcher.find()) {
            tokens << matcher.group()
        }
        return tokens
    }

    // Convert tokens to Reverse Polish Notation using the Shunting Yard algorithm
    private static List<String> toRPN(tokens) {
        Map precedence = ['+': 1, '-': 1, '*': 2, '/': 2]
        List<String> output = []
        List<String> operators = []

        tokens.each { token ->
            if (token ==~ /\d+\.?\d*/) {
                output << token
            } else if (token ==~ /[a-zA-Z_]\w*/) {
                output << token
            } else if (token == '(') {
                operators << token
            } else if (token == ')') {
                while (operators && operators.last() != '(') {
                    output << operators.removeLast()
                }
                operators.removeLast() // removeLast '('
            } else if (token in precedence.keySet()) {
                while (operators && operators.last() != '(' && precedence[operators.last()] >= precedence[token]) {
                    output << operators.removeLast()
                }
                operators << token
            }
        }
        while (operators) {
            output << operators.removeLast()
        }

        return output
    }

    // Build an abstract syntax tree from RPN
    private static VehicleFormulaExpression parseRPN(ManagerProductGroup group, String originalGroupProperty, def rpn) {
        def stack = []
        rpn.each { token ->
            if (token ==~ /\d+\.?\d*/) {
                stack << new NumberVehicleFormulaExpression(Double.parseDouble(token))
            } else if (token ==~ /[a-zA-Z_]\w*/) {
                stack << new VariableVehicleFormulaExpression(group, originalGroupProperty, token)
            } else {
                try {
                    def right = stack.removeLast()
                    def left = stack.removeLast()
                    stack << new BinaryVehicleFormulaExpression(left, token, right)
                }
                catch (Exception e) {
                    throw new VehicleFormulaException("Invalid formula format.")
                }
            }
        }
        return stack.removeLast()
    }
}