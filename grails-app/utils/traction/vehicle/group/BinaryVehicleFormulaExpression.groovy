package traction.vehicle.group


class BinaryVehicleFormulaExpression extends VehicleFormulaExpression {
    VehicleFormulaExpression left
    String operator
    VehicleFormulaExpression right

    BinaryVehicleFormulaExpression(VehicleFormulaExpression left, String operator, VehicleFormulaExpression right) {
        this.left = left
        this.operator = operator
        this.right = right
    }

    @Override
    String toSql() {
        String sql = "(${left.toSql()} $operator ${right.toSql()})"
        if (operator == "/") {
            return "IF(${right.toSql()} = 0, NULL, ${sql})"
        }
        return sql
    }

    @Override
    String toString() {
        return "(${left.toString()} $operator ${right.toString()})"
    }

    @Override
    List<VariableVehicleFormulaExpression> getAllVariables() {
        List<VariableVehicleFormulaExpression> variables = []
        variables.addAll(left.getAllVariables())
        variables.addAll(right.getAllVariables())
        return variables
    }

    @Override
    VehicleFormulaExpression replace(VariableVehicleFormulaExpression target, NumberVehicleFormulaExpression replacement) {
        this.left = this.left.replace(target, replacement)
        this.right = this.right.replace(target, replacement)
        return this
    }
}