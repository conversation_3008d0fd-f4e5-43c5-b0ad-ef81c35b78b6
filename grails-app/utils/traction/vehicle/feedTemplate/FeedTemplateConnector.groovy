package traction.vehicle.feedTemplate

import groovy.util.logging.Slf4j

@Slf4j
public abstract class FeedTemplateConnector {

    public abstract String getTitle();

    public static FeedTemplateConnector getConnector(String name){
        switch(name.toLowerCase()){
            case "vehicle.feed.template.kijiji": return new Kijiji();
            case "vehicle.feed.template.trader": return new Trader();
            case "vehicle.feed.template.autodealer": return new AutoDealer();
            case "vehicle.feed.template.powergo": return new Powergo();
        }
    }
}