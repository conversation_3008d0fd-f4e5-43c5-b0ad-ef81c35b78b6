package traction.vehicle.feedTemplate


import groovy.util.logging.Slf4j
import traction.vehicle.dms.DmsConnector

@Slf4j
public class Powergo extends FeedTemplateConnector {

    static final Map rules = [
            file:"Powergo.csv",
            rules  : [
                    [
                            "name":"CSV_HEADER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"stock,make,model,category,last_modified_date,dealer_id,submodel,year,exterior_color,km,price,sale_price,description_fr,description_en,vin,is_certified,is_demo,is_featured,seat_qty,cylinder_qty,warranty_text,drivetrain,fuel_type,horsepower,cooling_system,engine,condition,images,is_sold,transmission_speeds,transmission,engine_capacity,video_url,nb_hours_used,in_service_date,clearance,spec_labels_fr,spec_values_fr,spec_labels_en,spec_values_en,inorder,location,length,weight,showroom"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_HEADER_INCLUDE",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"true"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_ENCLOSURE",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":'"'
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_DELIMITER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":","
                                    ]
                            ]
                    ],
                    [
                        "name":"CSV_COLUMN_stock",
                        actions:[
                                [
                                        "name":"GetCellValue",
                                        "value":"stockNumber"
                                ]
                        ]
                    ],
                    [
                        "name":"CSV_COLUMN_make",
                        actions:[
                                [
                                        "name":"GetCellValue",
                                        "value":"make"
                                ],
                                [
                                        "name":"Lowercase",
                                        "value":""
                                ],
                                [
                                        "name":"Capitalize",
                                        "value":""
                                ]
                        ]
                    ],
                    [
                        "name":"CSV_COLUMN_model",
                        actions:[
                                [
                                        "name":"GetCellValue",
                                        "value":"model"
                                ]
                        ]
                    ],
                    [
                            "name":"CSV_COLUMN_category",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"category"
                                    ],
                                    [
                                            "name":"MapRedirect",
                                            "value":"category",
                                            "value2":"[[name:BATEAU ENBORD FI, value:Boats/Power Boat/Classic], [name:CaC ENFANT, value:ATV/On Road], [name:CaC SPORT, value:ATV/On Road], [name:CaC UTILITAIRE, value:ATV/On Road], [name:DIVERS, value:Divers], [name:GENERATRICE, value:Pièces/Produits mécaniques], [name:AUTOMOBILE, value:Automotive/Car], [name:LAVEUSE A PRESSION, value:Pièces/Produits mécaniques], [name:MOTO 3 ROUES, value:Motorcycle/Touring], [name:MOTO CUSTOM, value:Motorcycle/Touring], [name:MOTO DOUBLE USAGE, value:Motorcycle/Dual Purpose], [name:MOTO ENFANT, value:Motorcycle/Dirt Motocross], [name:MOTO GRAND TOURING, value:Motorcycle/Touring], [name:MOTO MOTOCROSS, value:Motorcycle/Dirt Motocross], [name:MOTO HORS ROUTE ENDURO, value:Motorcycle/Dirt Motocross], [name:MOTO RETRO/STANDARD, value:Motorcycle/Sport], [name:MOTO SPORT TOURING, value:Motorcycle/Touring], [name:MOTO SPORT, value:Motorcycle/Sport], [name:MOTO SUPERMOTARD, value:Motorcycle/Super sport], [name:MOTOMARINE SPORT, value:Personal watercrafts], [name:MOTONEIGE CROSS COUNTRY, value:Snowmobile], [name:MOTONEIGE ENFANT, value:Snowmobile], [name:MOTONEIGE FREESTYLE, value:Snowmobile], [name:MOTONEIGE GRAND SPORT, value:Snowmobile], [name:MOTONEIGE GRAND TOURING, value:Snowmobile], [name:MOTONEIGE MONTAGNE, value:Snowmobile], [name:MOTONEIGE MUSCLE, value:Snowmobile], [name:MOTONEIGE SNOWCROSS, value:Snowmobile], [name:MOTONEIGE TRAVAUX, value:Snowmobile], [name:POMPE, value:Pièces/Produits mécaniques], [name:PONTON, value:Boats/Power Boat/Pontoon Boats], [name:REMORQUE, value:Travel Trailer], [name:SCOOTER 1 PLACE, value:Motorcycle/MiniBike Moped], [name:SCOOTER 2 PLACES, value:Motorcycle/MiniBike Moped], [name:VELO ELECTRIQUE, value:Vélo/Vélos électriques], [name:VTT 2 PLACES, value:ATV/Off Road], [name:VTT 2X4, value:ATV/Off Road], [name:VTT 4X4, value:ATV/Off Road], [name:VTT ENFANT, value:ATV/Off Road], [name:VTT SPORT, value:ATV/Off Road], [name:VTT TRACK KIT, value:ATV/Off Road], [name:SOUFFLEUSE, value:Pièces/Produits mécaniques], [name:Boite Campeur, value:Automotive/VR/Caravane portée], [name:Inventaires Autres, value:Divers], [name:Roulotte, value:Automotive/VR/Caravane de voyage], [name:FW Cargo, value:Automotive/VR/Caravane à sellette cargo], [name:Roulotte, value:AutomotiveVRCaravane de voyage], [name:FW, value:Automotive/VR/Caravane à sellette], [name:Motorisé, value:Automotive/VR/Motorisé], [name:Roulotte Cargo, value:Automotive/VR/Caravane de voyage Cargo], [name:Roulotte de Parc, value:Automotive/VR/Modèle de parc], [name:Roulotte Hybride, value:Automotive/VR/Roulotte Hybride], [name:Tente-Roulotte, value:Automotive/VR/Tente-caravane], [name:2400cc, value:Divers], [name:BATEAU GONFLABLE, value:Boats/Divers Boat/Inflatable boat], [name:BATEAU H/B FIBRE, value:Boats/Divers Boat/Boat], [name:BATEAU PROPULSIO, value:Boats/Power Boat/Classic], [name:CANOT/KAYAK, value:Boats/Divers Boat/Kayak], [name:CHALOUPE ALUMINI, value:Boats/Divers Boat/Pinnace], [name:COTE A COTE ENFA, value:ATV/On Road], [name:COTE A COTE SPOR, value:ATV/On Road], [name:COTE A COTE UTIL, value:ATV/On Road], [name:DIVERS/AUTRES, value:Divers], [name:MOTO ENDURO, value:Motorcycle/Dirt Motocross], [name:MOTO GRAND SPORT, value:Motorcycle/Sport], [name:MOTO GRAND TOURI, value:Motorcycle/Touring], [name:MOTO RETRO/STD, value:Motorcycle/Sport], [name:MOTOMARINE 2 PLA, value:Personal watercrafts], [name:MOTONEIGE FREEST, value:Snowmobile], [name:MOTONEIGE GD SPO, value:Snowmobile], [name:MOTONEIGE GD TOU, value:Snowmobile], [name:MOTONEIGE MONTAG, value:Snowmobile], [name:MOTONEIGE MUSCLE, value:Snowmobile], [name:MOTONEIGE TRAVAU, value:Snowmobile], [name:MOTONEIGE X-COUN, value:Snowmobile], [name:SOUFFLEUR, value:Pièces/Produits mécaniques], [name:MOTEUR MARINE H/, value:Pièces/Moteur]]"
                                    ]
                            ]



                    ],
                    [
                            "name":"CSV_COLUMN_last_modified_date",
                            actions:[
                                    [
                                            "name":"TransformDate",
                                            "value":"dateLastBuild",
                                            "value2":"YYYY-MM-dd HH:MM"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_dealer_id",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"1"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_submodel",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_year",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"FinalYear"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_exterior_color",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"exteriorColor"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"null",
                                            "value2":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_km",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"odometer"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_price",
                            actions:[
                                    [
                                            "name":"IfGreatherthan",
                                            "value":"compiledData.barredPrice",
                                            "value2":"compiledData.displayPrice"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_sale_price",
                            actions:[
                                    [
                                            "name":"IfDifferentAndLess",
                                            "value":"compiledData.displayPrice",
                                            "value2":"compiledData.barredPrice"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_description_fr",
                            actions:[
                                    [
                                            "name":"TrimLine",
                                            "value":"compiledData.formulaDescription"
                                    ],
                                    [
                                            "name":"ReplaceAnyCRLF",
                                            "value":"<p>"
                                    ],
                                    [
                                            "name":"RemoveHTML",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_description_en",
                            actions:[
                                    [
                                            "name":"TrimLine",
                                            "value":"compiledData.formulaDescription"
                                    ],
                                    [
                                            "name":"ReplaceAnyCRLF",
                                            "value":"<p>"
                                    ],
                                    [
                                            "name":"RemoveHTML",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_vin",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"serialNumber"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_is_certified",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"False"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_is_demo",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"False"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_is_featured",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"False"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_seat_qty",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_cylinder_qty",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_warranty_text",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_drivetrain",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_fuel_type",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_horsepower",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_cooling_system",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_engine",
                            actions:[
                                    [
                                            "name":"GetAttribute",
                                            "value":"Specification",
                                            "value2":"Type de moteur"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_condition",
                            actions:[
                                    [
                                            "name":"CellReplaceText",
                                            "value":"isUsed",
                                            "value2":"true",
                                            "value3":"Used"
                                    ],
                                    [
                                            "name":"CellReplaceText",
                                            "value":"isUsed",
                                            "value2":"false",
                                            "value3":"New"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_images",
                            actions:[
                                    [
                                            "name":"Firstimages",
                                            "value":"https://xxxxx.tractiondk.com"
                                    ],
                                    [
                                            "name":"Otherimages",
                                            "value":"https://xxxxx.tractiondk.com",
                                            "value2":"|"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_is_sold",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"False"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_transmission_speeds",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_transmission",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_engine_capacity",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_video_url",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"videoUrl"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_nb_hours_used",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_in_service_date",
                            actions:[
                                    [
                                            "name":"TransformDate",
                                            "value":"dateLastBuild",
                                            "value2":"YYYY-MM-dd HH:MM"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_clearance",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"clearance"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"false",
                                            "value2":"FALSE"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"true",
                                            "value2":"TRUE"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_spec_labels_fr",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_spec_values_fr",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_spec_labels_en",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_spec_values_en",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_inorder",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"status"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"1",
                                            "value2":"TRUE"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"4",
                                            "value2":"FALSE"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"0",
                                            "value2":"FALSE"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"3",
                                            "value2":"FALSE"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":"5",
                                            "value2":"FALSE"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_location",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"location"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_length",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"length"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_weight",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"weight"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_showroom",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"showroomUrl"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_IGNORE_CHARACTER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":'"=\'\'|,= |&#039= |&amp.= |,=,'
                                    ]
                            ]
                    ]
            ]
    ]

    @Override
    public String getTitle() {
        return "vehicle.feed.template.powergo";
    }
}

