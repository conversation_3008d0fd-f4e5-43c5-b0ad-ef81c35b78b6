package traction.vehicle.feedTemplate


import groovy.util.logging.Slf4j
import traction.vehicle.dms.DmsConnector

@Slf4j
public class AutoDealer extends FeedTemplateConnector {

    static final Map rules = [
            file:"xxx_autodealer.csv",
            rules  : [
                    [
                            "name":"CSV_HEADER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"CompanyID|CategoryID|StockNumber|Status|Year|Make|Trim|Model|Kilometer|Weight|Length|ExteriorColor|Price|AdDescription|FinancingIsAvailable|FinancingType|MainPhoto|ExtraPhoto|AdModifiedDate|CaptureDate"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_HEADER_INCLUDE",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"true"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_ENCLOSURE",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":""
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_DELIMITER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"|"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_AdDescription",
                            actions:[
                                    [
                                            "name":"TrimLine",
                                            "value":"compiledData.formulaDescription"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_AdModifiedDate",
                            actions:[
                                    [
                                            "name":"GetDate",
                                            "value":"dd/MM/yyyy"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_CaptureDate",
                            actions:[
                                    [
                                            "name":"GetDate",
                                            "value":"dd/MM/yyyy"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_CategoryID",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"categoryTrader"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_CompanyID",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"1"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_ExteriorColor",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"exteriorColor"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_ExtraPhoto",
                            actions:[
                                    [
                                            "name":"Otherimages",
                                            "value":"https://xxxxx.tractiondk.com",
                                            "value2":","
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_FinancingIsAvailable",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"1"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_FinancingType",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"Crédit Bancaire"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Length",
                            actions:[
                                    [
                                            "name":"SetToInt",
                                            "value":"length"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Kilometer",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"odometer"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_MainPhoto",
                            actions:[
                                    [
                                            "name":"Firstimages",
                                            "value":"https://xxxxx.tractiondk.com"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Make",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"make"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Model",
                            actions:[
                                    [
                                            "name":"TrimLine",
                                            "value":"model"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Price",
                            actions:[
                                    [
                                            "name":"SetToInt",
                                            "value":"compiledData.displayPrice"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Status",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"isUsed"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":'false',
                                            "value2":"Neuf"
                                    ],
                                    [
                                            "name":"ReplaceAnyText",
                                            "value":'true',
                                            "value2":"Occasion"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_StockNumber",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":"T1NC-"
                                    ],
                                    [
                                            "name":"GetCellValue",
                                            "value":"stockNumber"
                                    ]

                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Trim",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"modelCode"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Weight",
                            actions:[
                                    [
                                            "name":"SetToInt",
                                            "value":"weight"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_COLUMN_Year",
                            actions:[
                                    [
                                            "name":"GetCellValue",
                                            "value":"FinalYear"
                                    ]
                            ]
                    ],
                    [
                            "name":"CSV_IGNORE_CHARACTER",
                            actions:[
                                    [
                                            "name":"SetBefore",
                                            "value":'"=\'\'|&#039= |&amp.= |"=\'\''
                                    ]
                            ]
                    ]
            ]
    ]

    @Override
    public String getTitle() {
        return "vehicle.feed.template.autodealer";
    }
}

