package traction.vehicle.error

import traction.vehicle.inheritance.InheritedVehicle

class ValidatorEmptyProperty implements VehicleStateValidator {
    String property

    ValidatorEmptyProperty(String property) {
        this.property = property
    }

    @Override
    boolean validate(InheritedVehicle inheritedVehicle, boolean magentoExists) {
        // TODO: Pour les champs selection, si pas de option selection return false. Voir ValidatorEmptySelectionnableProperty.groovy c'est pt proche
        def inheritedValue = inheritedVehicle.getProperty(property)
        boolean emptyInheritedValue = !inheritedValue || VehicleState.getNoEntry().any {
            inheritedValue == it
        }
        return emptyInheritedValue
    }
}
