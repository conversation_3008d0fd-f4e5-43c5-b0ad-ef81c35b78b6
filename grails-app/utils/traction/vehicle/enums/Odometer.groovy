package traction.vehicle.enums

import traction.I18N

class Odometer {

    enum Unit {
        KILOMETERS(true),
        MILES(true),
        HOURS(false)

        final boolean mustBeInteger

        Unit(boolean mustBeInteger) {
            this.mustBeInteger = mustBeInteger
        }

        String getMessage() {
            return "Odometer.Unit.${name()}"
        }

        Double sanitizeValue(Double value) {
            if (value == null) return null

            /*
            * STRICT VALIDATION THROW ERROR
            *
            if (mustBeInteger && value % 1 != 0) {
                throw new IllegalArgumentException("Value for $name() must be an integer.")
            }
             */

            return mustBeInteger ? value.intValue().toDouble() : value
        }

        String format(Double value) {
            if (value == null) return ""
            return mustBeInteger ? value.intValue().toString() : value.toString()
        }
    }

    Unit unit
    Double value

    void setValue(Double rawValue) {
        this.value = unit?.sanitizeValue(rawValue)
    }

    String toString() {
        def formattedValue = unit?.format(value)
        return  "${formattedValue} ${I18N.m(unit.message)}"
    }

    static Odometer fromKilometers(Double value) {
        if (!value) return null
        return new Odometer(
                unit: Unit.KILOMETERS,
                value: Unit.KILOMETERS.sanitizeValue(value)
        )
    }

    static Odometer fromHours(Double value) {
        if (!value) return null
        return new Odometer(
                unit: Unit.HOURS,
                value: Unit.HOURS.sanitizeValue(value)
        )
    }
}
