package traction.vehicle.dataTable

import grails.orm.HibernateCriteriaBuilder
import traction.ExecutionTime
import grails.web.servlet.mvc.GrailsParameterMap
import traction.datatable.*
import traction.department.Department
import traction.statistics.TopStatistics
import traction.vehicle.Vehicle
import traction.vehicle.dataTable.statistics.VehicleStatisticDataTableFilters
import traction.vehicle.error.VehicleState

import java.lang.reflect.Method

class VehicleDataTableSearch {

    private final static List<String> STRING_LIST_PROPERTIES = [
            "type",
            "affichage",
            "make",
            "iprofile",
            "catKijiji",
            "catTrader",
            "localisation",
            "couleurMagento",
            "sizeMagento",
            "materielMagento",
            "modelMagento",
            "decorMagento",
            "visibilityMagento",
            "genreMagento"
    ]

    DataTablePaging paging
    DataTableOrdering ordering
    Map<String, String> mapLikes = [:]
    List<VehicleState> stateList = []
    Map<String, Boolean> mapBooleans = [:]
    Map<String, List<String>> mapStringList = [:]
    Map<String, List<String>> mapStringListTreeBox = [:]
    Map<String, List<Long>> mapIdsList = [:]
    List<DataTableColumnFilterMinMax> minMaxList = []
    List<Vehicle.Type> globalType
    List<Department> globalDepartment
    List<String> globalDate
    Long id
    Boolean rootVehicle
    Boolean clientVehicle
    Boolean specifications
    String search
    List<Long> interests
    TopStatistics vehicleTopStatistics
    VehicleStatisticDataTableFilters vehicleStatisticDataTableFilters

    VehicleDataTableSearch(DataTableColumnFilters filter, GrailsParameterMap params, TopStatistics vehicleTopStatistics, VehicleStatisticDataTableFilters vehicleStatisticDataTableFilters) {
        this.search = params["search[value]"]
        this.paging = new DataTablePaging(params)
        this.ordering = new DataTableOrdering(params)
        this.vehicleTopStatistics = vehicleTopStatistics
        this.vehicleStatisticDataTableFilters = vehicleStatisticDataTableFilters
        this.globalType = filter.getListString("globalType").collect { it as Vehicle.Type }
        this.globalDepartment = filter.getListString("globalDepartment").collect { Department.get(it) }
        this.globalDate = filter.getListString("globalDate")
        initMapLikes(filter)
        initMapBooleans(filter)
        initMapStringList(filter)
        initMapStringListTreeBox(filter)
        initMinMaxList(filter)
        this.stateList = filter.getListString("stateList").collect { it as VehicleState }
        this.id = filter.getLong("id")
        this.rootVehicle = filter.getBoolean("rootVehicle")
        this.clientVehicle = filter.getBoolean("clientVehicle")
        this.specifications = filter.getBoolean("specifications")
        this.interests = filter.getListLong("interests")
    }

    private List<String> getLikeProperties() {
        return getColumnNamesForColumnDef("_createEditableTextColumnDef") + getColumnNamesForColumnDef("_createTextAreaInputColumnDef")
    }

    private void initMapLikes(DataTableColumnFilters filter) {
        mapLikes = getLikeProperties().collectEntries {
            [(it.replace("_", ".")): filter.getString(it)]
        }
    }

    private List<String> getBooleansProperties() {
        return getColumnNamesForColumnDef("_createBooleanColumnDef")
    }

    private void initMapBooleans(DataTableColumnFilters filter) {
        mapBooleans = getBooleansProperties().collectEntries {
            [(it): filter.getBoolean(it)]
        }
    }

    private List<String> getStringListProperties() {
        return getColumnNamesForColumnDef("_createSelectionColumnDef")
    }

    private void initMapStringList(DataTableColumnFilters filter) {
        final List<String> mapListIdsProperties = [
                "group",
                "watermark",
                "promoWatermark",
                "department"
        ]

        getStringListProperties().each {
            if (mapListIdsProperties.contains(it)) {
                mapIdsList[it.replace("_", ".")] = filter.getListLong(it)
            } else {
                mapStringList[it.replace("_", ".")] = filter.getListString(it)
            }
        }
    }

    private void initMapStringListTreeBox(DataTableColumnFilters filter) {
        getColumnNamesForColumnDef("_createTreeSelectionColumnDef").each {
            mapStringListTreeBox[it.replace("_", ".")] = filter.getListString(it)
        }
    }

    private List<String> getColumnNamesForColumnDef(String columnDef) {
        Method[] methods = VehicleDataTableRow.getDeclaredMethods().findAll { it.isAnnotationPresent(DataTableColumn) }
        List<String> list = []
        methods.each { Method method ->
            DataTableColumn column = method.getAnnotation(DataTableColumn)
            if (column.columnDef() == columnDef) {
                list.add(column.name())
            }
        }
        return list
    }

    private List<String> getMinMaxDatesProperties() {
        return getColumnNamesForColumnDef("_createEditableDateColumnDef") + getColumnNamesForColumnDef("_createDateColumnDef")
    }

    private List<String> getMinMaxDoubleProperties() {
        return getColumnNamesForColumnDef("_createCurrencyColumnDef") + getColumnNamesForColumnDef("_createCurrencyInputColumnDef")
    }

    private List<String> getMinMaxIntegerProperties() {
        return getColumnNamesForColumnDef("_createNumberInputColumnDef")
    }

    private List<String> getMinMaxIntegerChildProperties() {
        return getColumnNamesForColumnDef("_createRootChildsColumnDef")
    }

    private void initMinMaxList(DataTableColumnFilters filter) {
        minMaxList.addAll(getMinMaxIntegerProperties().collect { filter.getMinMaxInteger(it) }.findAll())
        minMaxList.addAll(getMinMaxDoubleProperties().collect { filter.getMinMaxDouble(it) }.findAll())
        minMaxList.addAll(getMinMaxDatesProperties().collect { filter.getMinMaxDate(it) }.findAll())
        minMaxList.addAll(getMinMaxIntegerChildProperties().collect { filter.getMinMaxInteger(it) }.findAll())
    }

    int countTotal(String generalImport = null) {
        return Vehicle.createCriteria().count {
            eq("deleted", false)
            'in'("type", Vehicle.Type.valuesManagerProduct(generalImport))
        }
    }

    int count(String generalImport = null) {
        return query(false, true, generalImport)
    }

    List<Vehicle> list(String generalImport = null) {
        return query(false, false, generalImport)
    }

    List<Long> listIds(String generalImport = null) {
        return query(true, false, generalImport)
    }

    private def query(boolean idsOnly, boolean isCount, String generalImport) {
        ExecutionTime executionTime = new ExecutionTime()
        List vehicles = Vehicle.createCriteria().list(isCount || idsOnly ? [:] : paging.getListParams()) {
            eq("deleted", false)
            'in'("type", Vehicle.Type.valuesManagerProduct(generalImport))

            vehicleTopStatistics.addActiveFiltersToCriteriaBuilder(delegate)

            if (globalType) 'in'("type", globalType)
            if (globalDepartment) 'in'("department", globalDepartment)
            if (globalDate) {
                or {
                    globalDate.each { dateFilter ->
                        switch (dateFilter) {
                            case "dateCreatedLast7":
                                gt("dateCreated", new Date() - 7)
                                break
                            case "dateCreatedLast30":
                                gt("dateCreated", new Date() - 30)
                                break
                            case "dateModifiedLast7":
                                gt("dateModified", new Date() - 7)
                                break
                            case "dateModifiedLast30":
                                gt("dateModified", new Date() - 30)
                                break
                            default:
                                println("globalDate filter invalid:" + dateFilter)
                                isNull("id")
                        }
                    }
                }
                println("TODO globalDate:" + globalDate)
            }

            if (this.search) {
                String[] words
                try {
                    words = this.search.split("\\s+")
                }
                catch (Exception e) {
                    words = this.search
                }
                words.each { String word ->
                    println "word:$word"
                    String s = "%$word%"
                    Integer integerValue
                    try {
                        integerValue = Integer.valueOf(it)
                    } catch (Exception e) {
                    }
                    or {
                        if (integerValue) {
                            and { eq("year", integerValue) }
                        }
                        and { ilike("stockNumber", s) }
                        and { ilike("make", s) }
                        and { ilike("model", s) }
                        and { ilike("exteriorColor", s) }
                        and { ilike("modelCode", s) }
                        and { ilike("serialNumber", s) }
                    }
                }
            }
            if (id) eq("id", id)
            mapLikes.each { likeEntry ->
                if (likeEntry.value) {
                    // For model field allow partial matches in any order
                    if (likeEntry.key == "model") {
                        likeEntry.value.split("\\s+").each { word ->
                            like(likeEntry.key, "%${word}%")
                        }
                    } else {
                        like(likeEntry.key, "%${likeEntry.value}%")
                    }
                }
            }
            mapBooleans.each { booleanEntry ->
                if (booleanEntry.value != null) eq(booleanEntry.key, booleanEntry.value)
            }
            mapStringList.each { likeEntry ->
                if (likeEntry.value) {
                    switch (likeEntry.key) {
                        case "listingStatus":
                            "in"(likeEntry.key, likeEntry.value.collect { it as Vehicle.ListingStatus })
                            break
                        case "status":
                            "in"(likeEntry.key, likeEntry.value.collect { it as Vehicle.Status })
                            break
                        case "type":
                            "in"(likeEntry.key, likeEntry.value.collect { it as Vehicle.Type })
                            break
                        default:
                            "in"(likeEntry.key, likeEntry.value)
                    }
                }
            }
            if (interests) {
                'interests' {
                    'in'("id", interests)
                }
            }
            mapIdsList.each { entry ->
                if (entry.value) {
                    if (entry.value.contains(0L)) {
                        or {
                            "in"(entry.key + ".id", entry.value)
                            isNull(entry.key)
                        }
                    } else {
                        "in"(entry.key + ".id", entry.value)
                    }
                }
            }
            if (stateList) {
                and { // TODO: voir si on veut un OR a la place?
                    stateList.each {
                        eq("compiledData.${it.compiledFieldName}", 1)
                    }
                }
            }

            if (rootVehicle != null) "${rootVehicle ? "isNotNull" : "isNull"}"("rootVehicle")
            if (clientVehicle != null) "${clientVehicle ? "isNotNull" : "isNull"}"("client")
            if (specifications != null) "${specifications ? "isNotEmpty" : "isEmpty"}"("specifications")
            mapStringListTreeBox.each { likeEntry ->
                if (likeEntry.value) {
                    or {
                        likeEntry.value.each {
                            if (it == " ") {
                                eq(likeEntry.key, "")
                            } else {
                                ilike(likeEntry.key, it)
                                ilike(likeEntry.key, it + ";%")
                                ilike(likeEntry.key, "%;" + it)
                            }
                        }
                    }
                }
            }
            addMinMaxListToCriteriaBuilder(delegate)
            if (isCount || idsOnly) {
                projections {
                    distinct("id")
                }
            } else {
                ordering.properties.each {
                    if(it.property == 'firstPicture' || it.property == 'pictures'){
                        order('compiledData.pictureCountIncludingInheritance', it.direction)
                    }
                    else{
                        order(it.property.replace("_", "."), it.direction)
                    }
                }
            }
        } as List
        println "VehicleDataTableSearch.query: ${executionTime.restart()}"
        if (isCount) {
            return vehicles.size()
        }
        return vehicles
    }

    private void addMinMaxListToCriteriaBuilder(HibernateCriteriaBuilder builder) {
        minMaxList.each { minMax ->
            minMax.property = minMax.property.replace("_", ".")
            minMax.addToCriteriaBuilder(builder)
        }
    }
}
