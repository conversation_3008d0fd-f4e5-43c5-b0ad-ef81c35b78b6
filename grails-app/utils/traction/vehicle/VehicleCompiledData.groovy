package traction.vehicle

import traction.queryBuilder.QueryBuilderField
import traction.vehicle.annotations.VehicleCustomizableLabelField
import traction.vehicle.annotations.VehicleGroupCompiledTextField
import traction.vehicle.annotations.VehicleGroupFormulaVariableField
import traction.vehicle.annotations.VehicleTextVariableField
import traction.vehicle.annotations.VehicleFeedVariableField
import traction.vehicle.annotations.VehicleXlsVariableField
import groovy.util.logging.Slf4j

@Slf4j
class VehicleCompiledData {

    static constraints = {
        formulaPrice1 nullable: true
        formulaPrice2 nullable: true
        formulaPrice3 nullable: true
        formulaPrice4 nullable: true
        formulaPrice5 nullable: true
        formulaPrice6 nullable: true
        formulaPrice7 nullable: true
        formulaPrice8 nullable: true
        formulaPdiPrice nullable: true
        formulaFreightPrice nullable: true
        formulaBarredPrice nullable: true
        barredPrice nullable: true
        weeklyPayment nullable: true
        monthlyPayment nullable: true
        minimumPrice nullable: true
        formulaDisplayPrice nullable: true
        displayPrice nullable: true
        tractionPrice nullable: true
        optionsFormulaPrice nullable: true
        costRealPrice nullable: true
        estimatedLoanAmount nullable: true
        pictureCountIncludingInheritance nullable: true
        rowData size: 0..16777215
        formulaDescription size: 0..16777215, nullable: true
        formulaShortDescription size: 0..16777215, nullable: true
        formulaDescriptionKijiji size: 0..16777215, nullable: true
        formulaDescriptionTrader size: 0..16777215, nullable: true
        formulaTitre size: 0..16777215, nullable: true
        formulaCategoryMagento size: 0..16777215, nullable: true
        dateDisplay nullable: true
        daysInStock nullable: true
        lastNewOpportunityDate nullable: true
        newOpportunityLast30 nullable: false
        newOpportunityLast90 nullable: false
        newOpportunityLast180 nullable: false
        newOpportunityAll nullable: false
    }

    static mapping = {
        formulaBarredPrice index: 'formulaBarredPrice_Idx'
        barredPrice index: 'barredPrice_Idx'
        weeklyPayment index: 'weeklyPayment_Idx'
        monthlyPayment index: 'monthlyPayment_Idx'
        minimumPrice index: 'minimumPrice_Idx'
        formulaDisplayPrice index: 'formulaDisplayPrice_Idx'
        displayPrice index: 'displayPrice_Idx'
        tractionPrice index: 'tractionPrice_Idx'
        optionsFormulaPrice index: 'optionsFormulaPrice_Idx'
        costRealPrice index: 'costRealPrice_Idx'
        estimatedLoanAmount index: 'estimatedLoanAmount_Idx'
        pictureCountIncludingInheritance index: 'pictureCountIncludingInheritance_Idx'
        dateDisplay index: 'dateDisplay_Idx'
        daysInStock index: 'daysInStock_Idx'
        lastNewOpportunityDate index: 'lastNewOpportunityDate_Idx'
        total sqlType: 'TINYINT'
        listingStatusAll sqlType: 'TINYINT'
        listingStatusInheritanceOnly sqlType: 'TINYINT'
        listingStatusWebsite sqlType: 'TINYINT'
        listingStatusDisabled sqlType: 'TINYINT'
        listingStatusTraction sqlType: 'TINYINT'
        newOpportunityLast30Count sqlType: 'TINYINT'
        newOpportunityLast90Count sqlType: 'TINYINT'
        newOpportunityLast180Count sqlType: 'TINYINT'
        newOpportunityAllCount sqlType: 'TINYINT'
        magentoNotPublished sqlType: 'TINYINT'
        magentoSynchronized sqlType: 'TINYINT'
        magentoNotSynchronized sqlType: 'TINYINT'
        buildPending sqlType: 'TINYINT'
        buildSuccess sqlType: 'TINYINT'
        buildFailed sqlType: 'TINYINT'
        updateSynchronized sqlType: 'TINYINT'
        updateNotSynchronized sqlType: 'TINYINT'
        inventoryPicturesMissing sqlType: 'TINYINT'
        picturesTwoOrLess sqlType: 'TINYINT'
        picturesMissing sqlType: 'TINYINT'
        categoryMagentoMissing sqlType: 'TINYINT'
        categoryOther sqlType: 'TINYINT'
        modelCodeMissing sqlType: 'TINYINT'
        categoryMissing sqlType: 'TINYINT'
        makeMissing sqlType: 'TINYINT'
        modelMissing sqlType: 'TINYINT'
        categoryKijijiMissing sqlType: 'TINYINT'
        categoryTraderMissing sqlType: 'TINYINT'
        interestMissing sqlType: 'TINYINT'
        rootVehicleGroupMissing sqlType: 'TINYINT'
        rootVehicleWgetPriceZero sqlType: 'TINYINT'
        kawasakiPriceZero sqlType: 'TINYINT'
        listingStatusNotAll sqlType: 'TINYINT'
        promoInternalExpired sqlType: 'TINYINT'
        promoManufacturerExpired sqlType: 'TINYINT'
        promoTextExpired sqlType: 'TINYINT'
        buildCustomRuleFailed1 sqlType: 'TINYINT'
        buildCustomRuleFailed2 sqlType: 'TINYINT'
        buildCustomRuleFailed3 sqlType: 'TINYINT'
        buildCustomRuleFailed4 sqlType: 'TINYINT'
        buildCustomRuleFailed5 sqlType: 'TINYINT'
        buildCustomRuleFailed6 sqlType: 'TINYINT'
    }

    // Formula prices
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice1
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice2
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice3
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice4
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice5
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice6
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice7
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPrice8
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaPdiPrice
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    @VehicleCustomizableLabelField
    Double formulaFreightPrice

    // Loan
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double weeklyPayment
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double monthlyPayment
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double estimatedLoanAmount

    // Other prices
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    Double formulaBarredPrice // resultat du group uniquement pour UI Group preview
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double minimumPrice
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    Double formulaDisplayPrice  // resultat du group uniquement pour UI Group preview
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double tractionPrice
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double optionsFormulaPrice
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double costRealPrice // prix reel (cost_reel_price) du group

    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double displayPrice // Prix final afficher apres heritage et toutes conditions en exterieur (based on prixspecial or formulaDisplayPrice)
    @VehicleGroupFormulaVariableField
    @VehicleTextVariableField(group="formulaPrice")
    @VehicleFeedVariableField
    @QueryBuilderField
    Double barredPrice // prix barrer final afficher apres heritage et toutes conditions en exterieur (based on formulaBarredPrice ou useMsrpAsBarredPrice et msrp)

    @VehicleFeedVariableField
    @QueryBuilderField
    Integer pictureCountIncludingInheritance = 0

    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    @VehicleFeedVariableField
    String formulaDescription
    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    @VehicleFeedVariableField
    String formulaShortDescription
    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    @VehicleFeedVariableField
    String formulaDescriptionKijiji
    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    @VehicleFeedVariableField
    String formulaDescriptionTrader
    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    @VehicleFeedVariableField
    String formulaTitre
    @VehicleGroupCompiledTextField
    @VehicleGroupFormulaVariableField
    String formulaCategoryMagento

    int total = 1

    int newOpportunityLast30 = 0
    int newOpportunityLast30Count = 0
    int newOpportunityLast90 = 0
    int newOpportunityLast90Count = 0
    int newOpportunityLast180 = 0
    int newOpportunityLast180Count = 0
    int newOpportunityAll = 0
    int newOpportunityAllCount = 0

    int listingStatusAll = 0
    int listingStatusInheritanceOnly = 0
    int listingStatusWebsite = 0
    int listingStatusDisabled = 0 // listingStatusNo?
    int listingStatusTraction = 0

    int magentoNotPublished = 0
    int magentoSynchronized = 0
    int magentoNotSynchronized = 0
    int buildPending = 0
    int buildSuccess = 0
    int buildFailed = 0
    int updateSynchronized = 0
    int updateNotSynchronized = 0
    int inventoryPicturesMissing = 0
    int picturesTwoOrLess = 0
    int picturesMissing = 0
    int categoryMagentoMissing = 0
    int categoryOther = 0
    int modelCodeMissing = 0
    int categoryMissing = 0
    int makeMissing = 0
    int modelMissing = 0
    int categoryKijijiMissing = 0
    int categoryTraderMissing = 0
    int interestMissing = 0
    int rootVehicleGroupMissing = 0
    int rootVehicleWgetPriceZero = 0
    int kawasakiPriceZero = 0
    int listingStatusNotAll = 0
    int promoInternalExpired = 0
    int promoManufacturerExpired = 0
    int promoTextExpired = 0
    int buildCustomRuleFailed1 = 0
    int buildCustomRuleFailed2 = 0
    int buildCustomRuleFailed3 = 0
    int buildCustomRuleFailed4 = 0
    int buildCustomRuleFailed5 = 0
    int buildCustomRuleFailed6 = 0

    String rowData = "{}"
    Date lastNewOpportunityDate
    Integer daysInStock
    Integer inventoryLinked = 0
    Date dateDisplay // TODO: ce champ la est pas setter encore

    static List<String> getVehicleGroupFormulaFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleGroupFormulaVariableField) }.collect { it.name }
    }

    static List<String> getVehicleGroupCompiledTextFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleGroupCompiledTextField) }.collect { it.name }
    }

    static List<String> getVehicleFeedVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it.name }
    }

    static List<String> getVehicleBuildVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it }
    }

    static List<String> getVehicleXlsVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleXlsVariableField) }.collect { it.name }
    }

    static List<String> getVehicleQueryBuilderVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(QueryBuilderField) }.collect { it.name }
    }

    String getFormulaDescriptionAsPlainText() {
        if (!formulaDescription) {
            return ''
        }
        String response = formulaDescription
                .replaceAll(/(?i)<\/?p[^>]*>/, ' ') // <p> sans fermeture
                .replaceAll(/<[^>]+>/, '')          // <HTML> autre
                .replaceAll(/\s+/, ' ')              // ferme les multi-espaces.
                .trim()

        log.debug "formulaDescription: ${formulaDescription}"
        log.debug "response: ${response}"

        return response
    }
}
