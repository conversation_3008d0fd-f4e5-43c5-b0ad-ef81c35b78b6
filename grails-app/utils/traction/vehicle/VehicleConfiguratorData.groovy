package traction.vehicle

import traction.vehicle.annotations.VehicleDmsVariableField
import traction.vehicle.annotations.VehicleInheritableField
import traction.vehicle.annotations.VehicleSelectionnableOptionField
import traction.vehicle.annotations.VehicleTextVariableField
import traction.vehicle.annotations.VehicleFeedVariableField
import traction.vehicle.annotations.VehicleXlsVariableField

class VehicleConfiguratorData {

    // TODO: check to add indexes
    static constraints = {
        style nullable: true
        colorSet nullable: true
        color nullable: true
        material nullable: true
        configurable nullable: true
        decor nullable: true
        model nullable: true
        interior nullable: true
        genre nullable: true
        size nullable: true
    }

    static mapping = {
    }

    @VehicleSelectionnableOptionField("configuratorData_style")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_style",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String style

    @VehicleSelectionnableOptionField("configuratorData_colorSet")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_colorSet",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String colorSet

    @VehicleSelectionnableOptionField("configuratorData_color")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_color",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String color

    @VehicleSelectionnableOptionField("configuratorData_material")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_material",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String material

    @VehicleSelectionnableOptionField("configuratorData_decor")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_decor",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String decor

    @VehicleSelectionnableOptionField("configuratorData_model")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_model",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String model

    @VehicleSelectionnableOptionField("configuratorData_interior")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_interior",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String interior

    @VehicleSelectionnableOptionField("configuratorData_genre")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_genre",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String genre

    @VehicleSelectionnableOptionField("configuratorData_size")
    @VehicleTextVariableField(group="configurator")
    @VehicleInheritableField(value="configuratorData_size",group="specs")
    @VehicleFeedVariableField
    @VehicleXlsVariableField
    @VehicleDmsVariableField(group="info")
    String size

    @VehicleFeedVariableField
    @VehicleXlsVariableField
    String configurable

    static List<String> getVehicleFeedVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it.name }
    }

    static List<String> getVehicleXlsVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleXlsVariableField) }.collect { it.name }
    }

    static List<String> getVehicleBuildVariableFields() {
        return this.getDeclaredFields().findAll { it.isAnnotationPresent(VehicleFeedVariableField) }.collect { it }
    }
}
