package traction.vehicle.inheritance

import groovy.util.logging.Slf4j

@Slf4j
class VehicleBuildStatusColorUtil {

    static String getColor(InheritedVehicle inheritedVehicle, boolean magentoExists) {
        if (inheritedVehicle.compiledData.displayPrice < 0 || inheritedVehicle.listingStatus.equals(InheritedVehicle.ListingStatus.INHERITANCE_ONLY) || inheritedVehicle.listingStatus.equals(InheritedVehicle.ListingStatus.NO)) {
            return ""
        }

        boolean isRootWithChild = inheritedVehicle.isRoot() && inheritedVehicle.getInventoryLinks()
        boolean isDark = isRootWithChild || inheritedVehicle.rootVehicle

        if (inheritedVehicle.validUpdated) {
            if (magentoExists) {
                return inheritedVehicle.magentoData.onWebsite ? getGreen(isDark) : getPink(isDark)
            }
            return getGreen(isDark)
        }
        return getYellow(isDark)
    }

    private static String getGreen(boolean isDark) {
        return isDark ? "#85d985" : "lightgreen"
    }

    private static String getYellow(boolean isDark) {
        return isDark ? "#dfc063" : "#fdda71"
    }

    private static String getPink(boolean isDark) {
        return isDark ? "#f3adb8" : "lightpink"
    }
/*
    static String test() {

        Vehicle vehicle
        InheritedVehicle vehicleInherited
        boolean magentoExists = true
        Filter buildFilter = Filter.findByType(traction.Filter.Type.MANAGER_PRODUCT_BUILD)
        def errorresult = []
        def allerrorstatus = ProductErrorStatus.getAll()
        allerrorstatus.each {
            boolean toadd = false
            def curcolor = it.color
            def curheader = it.header
            String dynmessage = ""
            switch(it.entry) {
            if (buildFilter && buildFilter.json){
                for (int i=1;i<20;i++){
                    if (it.entry.equals("buildconditions_"+i.toString())){
                        def obj = JSON.parse(buildFilter?.json)
                        int inhcnt = 0
                        try{
                            String operator = obj.getAt("rules")[i-1].getAt("operator")
                            String type = obj.getAt("rules")[i-1].getAt("type")
                            String field = obj.getAt("rules")[i-1].getAt("field").replace("Vehicle.","")
                            def value = obj.getAt("rules")[i-1].getAt("value")
                            switch(operator){
                                case "equal":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value.equals(vehicleInherited[field])==false){
                                                toadd = true
                                                dynmessage = ": " + field + " != " + value
                                            }
                                            break
                                    }
                                    break
                                case "not_equal":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value.equals(vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " = " + value
                                            }
                                    }
                                    break
                                case "less":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value <= (vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " >= " + value
                                            }
                                            break
                                    }
                                    break
                                case "less_or_equal":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value < (vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " > " + value
                                            }
                                            break
                                    }
                                    break
                                case "greater":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value >= (vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " <= " + value
                                            }
                                            break
                                    }
                                    break
                                case "greater_or_equal":
                                    switch(type){
                                        case ["string","integer"]:
                                            if (value > (vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " < " + value
                                            }
                                            break
                                    }
                                    break
                                case "between":
                                    def value1=value[0]
                                    def value2=value[1]
                                    switch(type){
                                        case ["string","integer"]:
                                            if ((value1 < (vehicleInherited[field]) && value2 > (vehicleInherited[field])) == false){
                                                toadd = true
                                                dynmessage = ": " + field + " ![ " + value + "]"
                                            }
                                            break
                                    }
                                    break
                                case "not_between":
                                    def value1=value[0]
                                    def value2=value[1]
                                    switch(type){
                                        case ["string","integer"]:
                                            if ((value1 < (vehicleInherited[field]) && value2 > (vehicleInherited[field]))){
                                                toadd = true
                                                dynmessage = ": " + field + " [ " + value + "]"
                                            }
                                            break
                                    }
                                    break
                                case "is_null":
                                    switch(type){
                                        case ["string","integer","date"]:
                                            if ((vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " != [null]"
                                            }
                                            break
                                    }
                                    break
                                case "is_not_null":
                                    switch(type){
                                        case ["string","integer","date"]:
                                            if (!(vehicleInherited[field])){
                                                toadd = true
                                                dynmessage = ": " + field + " = [null]"
                                            }
                                            break
                                    }
                                    break
                            }
                        }catch(Exception e){
                            System.out.println("error on:"+i.toString())
                        }
                    }
                }
            }

        }


    }


 */
}