package traction.vehicle.inheritance

import traction.vehicle.VehicleConfiguratorData
import traction.vehicle.annotations.VehicleInheritableField

class InheritedVehicleConfiguratorData extends VehicleConfiguratorData {

    private InheritedVehicle inheritedVehicle

    @Override
    @VehicleInheritableField(value="configuratorData_style",group="specs")
    String getStyle() {
        return inheritedVehicle.getFieldValue("configuratorData_style")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_colorSet",group="specs")
    String getColorSet() {
        return inheritedVehicle.getFieldValue("configuratorData_colorSet")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_color",group="specs")
    String getColor() {
        return inheritedVehicle.getFieldValue("configuratorData_color")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_material",group="specs")
    String getMaterial() {
        return inheritedVehicle.getFieldValue("configuratorData_material")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_decor",group="specs")
    String getDecor() {
        return inheritedVehicle.getFieldValue("configuratorData_decor")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_model",group="specs")
    String getModel() {
        return inheritedVehicle.getFieldValue("configuratorData_model")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_interior",group="specs")
    String getInterior() {
        return inheritedVehicle.getFieldValue("configuratorData_interior")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_genre",group="specs")
    String getGenre() {
        return inheritedVehicle.getFieldValue("configuratorData_genre")
    }

    @Override
    @VehicleInheritableField(value="configuratorData_size",group="specs")
    String getSize() {
        return inheritedVehicle.getFieldValue("configuratorData_size")
    }

}
