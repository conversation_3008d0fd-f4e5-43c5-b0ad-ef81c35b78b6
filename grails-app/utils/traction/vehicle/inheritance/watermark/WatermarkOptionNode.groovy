package traction.vehicle.inheritance.watermark

import traction.vehicle.VehicleWatermark

class WatermarkOptionNode extends WatermarkDecisionTreeNode {
    VehicleWatermark watermark
    WatermarkDecisionTreeNode child
    Type type

    @Override
    void open() {
        this.opened = true
    }

    @Override
    WatermarkDecisionTreeNode getNextChildToOpen() {
        return child
    }

    enum Type {
        PROMO_VEHICLE,
        PROMO_GROUP,
        NOIMAGE_RESERVED_DEFAULT,
        NOIMAGE_GROUP,
        NOIMAGE_DEFAULT,
        DEFAULT_VEHICLE,
        RESERVED_GROUP,
        STATUS_GROUP,
        DEFAULT_GROUP

        boolean isNoImageWatermark() {
            return this == NOIMAGE_DEFAULT || this == NOIMAGE_GROUP || this == NOIMAGE_RESERVED_DEFAULT
        }
    }

}
