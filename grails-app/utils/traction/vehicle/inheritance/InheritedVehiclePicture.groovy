package traction.vehicle.inheritance

import grails.util.Holders
import traction.ConfigService
import traction.file.FileData
import traction.vehicle.VehicleWatermark

class InheritedVehiclePicture {

    static String VEHICLE_FIRST_PICTURE_DRAFT_ID = "VEHICLE_FIRST_PICTURE"
    static String VEHICLE_FLOOR_PLAN_PICTURE_DRAFT_ID = "VEHICLE_FLOOR_PLAN_PICTURE"

    private FileData picture
    private boolean fromRoot

    InheritedVehiclePicture(FileData picture, boolean fromRoot) {
        this.picture = picture
        this.fromRoot = fromRoot
    }

    FileData asFileData() {
        return picture
    }

    long getId() {
        return picture.id
    }

    String getName() {
        return picture.name
    }

    Date getDate() {
        return picture.date
    }

    String getSize() {
        if (picture?.dataSize == null) {
            return "Unknown size"
        }
        // Convert MB to Bytes
        int sizeInBytes = picture.dataSize
        Double sizeInKb = sizeInBytes / 1024
        Double sizeInMb = sizeInKb / 1024
        Double sizeInGb = sizeInMb / 1024

        if (sizeInGb >= 1) {
            return String.format("%.2f GB", sizeInGb)
        } else if (sizeInMb >= 1) {
            return String.format("%.2f MB", sizeInMb)
        } else if (sizeInKb >= 1) {
            return String.format("%.2f KB", sizeInKb)
        } else {
            return "${sizeInBytes} Bytes"
        }
    }

    String getPublicUrl(Boolean baseURL = true,Boolean alias = true) {
        //return "C:/big_data/traction/images/document.vehiclepicture/${id}.jpg"
        ConfigService configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
        String AliasString = ""
        if (alias){
            AliasString = "/modele_images/"
        }
        int index = picture.path.lastIndexOf('.')
        String ext = picture.path.substring(index, picture.path.length())

        return "${baseURL ? configService.get("PRODUCTS", "URLROOT") : ""}${AliasString}${picture.angdeg ? picture.angdeg + "." : ""}${id}${ext}"
    }

    int getOrder() {
        return picture.ordre
    }

    boolean getFromRoot() {
        return fromRoot
    }

    boolean isFirstPicture() {
        return picture.draftId == VEHICLE_FIRST_PICTURE_DRAFT_ID && !fromRoot
    }

    boolean isFloorPlanPicture() {
        return picture.draftId == VEHICLE_FLOOR_PLAN_PICTURE_DRAFT_ID && !fromRoot
    }

    boolean isOrderable() {
        return !isFirstPicture() && !isFloorPlanPicture()
    }

    String toString() {
        return "InheritedVehiclePicture(id: $id, order: $order)"
    }
}
