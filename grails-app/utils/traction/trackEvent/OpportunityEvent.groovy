package traction.trackEvent

import org.grails.web.json.JSONObject
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityCompiledData

class OpportunityEvent {
    Long opportunityId
    boolean isNew
    Date date = new Date()
    JSONObject oldJson
    JSONObject newJson

    OpportunityEvent(Opportunity opportunity, boolean isNew, String oldJson, String newJson) {
        this.opportunityId = opportunity.id
        this.isNew = isNew
        this.oldJson = oldJson ? new JSONObject(oldJson) : new JSONObject()
        this.newJson = newJson ? new JSONObject(newJson) : new JSONObject()
    }

    OpportunityCompiledData getOpportunityCompiledData() {
        return Opportunity.createCriteria().get {
            eq("id", opportunityId)
            projections {
                property("compiledData")
            }
        }
    }

    boolean propertyHasChanged(String prop) {
        Object oldPropValue = getOldPropValue(prop)
        Object newPropValue = getNewPropValue(prop)
        return oldPropValue != newPropValue
    }

    private Object getNewPropValue(String prop) {
        return newJson.has(prop) ? newJson.get(prop) : null
    }

    private Object getOldPropValue(String prop) {
        return oldJson.has(prop) ? oldJson.get(prop) : null
    }
}
