package traction.item

import grails.web.servlet.mvc.GrailsParameterMap
import traction.datatable.*
import traction.vehicle.VehicleGeneralImportsConfig

class ItemDataTableSearch {

    DataTablePaging paging
    DataTableOrdering ordering
    Long id
    List<Item.Type> type
    String iprofile
    String search
    String stockNumber
    String supplierCode
    String name
    String description
    DataTableColumnFilterMinMax cost
    DataTableColumnFilterMinMax msrp
    DataTableColumnFilterMinMax year
    DataTableColumnFilterMinMax quantity

    ItemDataTableSearch(DataTableColumnFilters filter, DataTablePaging paging, DataTableOrdering ordering, GrailsParameterMap params) {
        this.search = params["search[value]"]
        this.paging = paging
        this.ordering = ordering
        this.id = filter.getLong("id")
        this.stockNumber = filter.getString("stockNumber")
        this.supplierCode = filter.getString("supplierCode")
        this.name = filter.getString("name")
        this.description = filter.getString("description")
        this.cost = filter.getMinMaxDouble("cost")
        this.msrp = filter.getMinMaxDouble("msrp")
        this.year = filter.getMinMaxInteger("year")
        this.quantity = filter.getMinMaxInteger("quantity")
        this.iprofile = filter.getString("iprofile")
        this.type = filter.getListString("type").collect { it as Item.Type }
    }

    int countTotal() {
        return Item.countByDeleted(false)
    }

    int count() {
        return query(true)
    }

    List<Item> list() {
        return query(false)
    }

    private def query(boolean isCount = false) {
        List items = Item.createCriteria().list(isCount ? [:] : paging.getListParams()) {
            eq("deleted", false)
            if (this.search) {
                String[] words
                try {
                    words = this.search.split("\\s+")
                }
                catch (Exception e) {
                    words = this.search
                }
                words.each { String word ->
                    println "word:$word"
                    String s = "%$word%"
                    Integer integerValue
                    try {
                        integerValue = Integer.valueOf(it)
                    } catch (Exception e) {
                    }
                    or {
                        if (integerValue) {
                            and { eq("year", integerValue) }
                        }
                        and { ilike("stockNumber", s) }
                        and { ilike("supplierCode", s) }
                        and { ilike("name", s) }
                    }
                }
            }
            if (id) eq("id", id)
            if (type) 'in'("type", type)
            if (iprofile) like("iprofile", iprofile)
            if (stockNumber) like("stockNumber", "%$stockNumber%")
            if (supplierCode) like("supplierCode", "%$supplierCode%")
            if (name) like("name", "%$name%")
            if (description) like("description", "%$description%")
            if (cost) cost.addToCriteriaBuilder(delegate)
            if (msrp) msrp.addToCriteriaBuilder(delegate)
            if (year) year.addToCriteriaBuilder(delegate)
            if (quantity) quantity.addToCriteriaBuilder(delegate)

            if (isCount) {
                projections {
                    distinct("id")
                }
            } else {
                ordering.properties.each {
                    order(it.property.replace("_", "."), it.direction)
                }
            }
        } as List
        if (isCount) {
            return items.size()
        }
        return items
    }

}
