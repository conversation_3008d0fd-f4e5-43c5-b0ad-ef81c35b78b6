package traction.cotation

import grails.gorm.DetachedCriteria
import grails.web.servlet.mvc.GrailsParameterMap
import groovy.util.logging.Slf4j
import org.hibernate.sql.JoinType
import traction.datatable.DataTableColumnFilterMinMax
import traction.datatable.DataTableColumnFilters
import traction.datatable.DataTableOrdering
import traction.datatable.DataTablePaging
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityAssigned
import traction.salesTracking.dataTable.statistics.SalesTrackingDataTableFilters
import traction.salesTracking.dataTable.statistics.SalesTrackingTopStatistics

@Slf4j
class CotationElementSearch {
    final static Map<String, String> ORDER_COLUMNS_MAPPING = [
            "dateSold"                       : "_c.dateSold",
            "date"                           : "_c.date",
            "firstname"                      : "_client.firstname",
            "lastname"                       : "_client.lastname",
            "exportedToLautopak"             : "_c.lautopakDealNumberId",
            "type"                           : "opportunity",
            "soldStatus"                     : "_c.soldStatus",
            "label"                          : "_l.name",
            "userSales"                      : "_us.firstname",
            "userBdc"                        : "_ub.firstname",
            "userFni"                        : "_uf.firstname",
            "workflowData_workflowTypeOfCard": "_workflowData.typeOfCard",
            "workflowDataDateEnd"            : "_workflowData.dateEnd",
            "workflowData_workflowBoard"     : "_workflowBoard.id",
            "workflowData_workflowMaster"    : "_workflowBoard.workflowMaster.id",
            "serialnumber"                   : "_vh.serialNumber",
            "department"                     : "_c.department",
            "lastStatusChange"               : "_c.lastStatusChange"
    ]

    String firstname
    String lastname
    Long cotationId
    Boolean isExported
    DataTableColumnFilterMinMax date
    DataTableColumnFilterMinMax lastStatusChange
    DataTableColumnFilterMinMax dateSold
    DataTableColumnFilterMinMax workflowDataDateEnd
    String type
    List<Long> departments = []
    List<Long> usersSales = []
    List<Long> usersFni = []
    List<Long> usersBdc = []
    List<Opportunity.Status> soldStatus = []
    List<String> makes = []
    List<Long> interestIds = []
    List<Long> workflowData_typeOfCard = []
    List<Long> workflowData_workflowBoard = []
    List<Long> workflowData_workflowMaster = []
    List<Long> labels
    String stocknumber
    String modelcode
    String model
    String serialnumber
    Boolean isUsed

    Integer year
    Integer daysInStock
    DataTableColumnFilterMinMax costPrice
    DataTableColumnFilterMinMax opportunityPotentialPrice
    DataTableColumnFilterMinMax sellingPrice
    DataTableColumnFilterMinMax opportunityAccessoriesMarginPercent
    DataTableColumnFilterMinMax opportunityTradePriceToAbsorb
    DataTableColumnFilterMinMax opportunityAccessories
    DataTableColumnFilterMinMax profit
    DataTableColumnFilterMinMax profitPercent
    DataTableColumnFilterMinMax opportunityOtherFee
    DataTableColumnFilterMinMax opportunityPromo
    DataTableColumnFilterMinMax opportunityMrsp
    DataTableColumnFilterMinMax commission
    DataTableColumnFilterMinMax commissionPercent
    DataTableColumnFilterMinMax fniProfitReserveFinance
    DataTableColumnFilterMinMax fniProfitLifeInsurance
    DataTableColumnFilterMinMax fniProfitCriticalIllnessInsurance
    DataTableColumnFilterMinMax fniProfitExtendedPlan
    DataTableColumnFilterMinMax fniProfitAestheticProtection
    DataTableColumnFilterMinMax fniProfitOthers
    DataTableColumnFilterMinMax fniProfitDisabilityInsurance
    DataTableColumnFilterMinMax fniProfitReplacementInsurance
    DataTableColumnFilterMinMax fniProfitRentalLeaseUsury
    DataTableColumnFilterMinMax fniProfitChemicals
    DataTableColumnFilterMinMax fniProfitTotal
    DataTableColumnFilterMinMax fniProfitTotalPercent

    DataTablePaging paging
    DataTableOrdering ordering
    SalesTrackingTopStatistics salesTrackingTopStatistics
    SalesTrackingDataTableFilters salesTrackingDataTableFilters

    // CotationElementSearch(DataTableColumnFilters filter, GrailsParameterMap params, SalesTrackingTopStatistics salesTrackingTopStatistics, SalesTrackingDataTableFilters salesTrackingDataTableFilters) {
    CotationElementSearch(DataTableColumnFilters filter, GrailsParameterMap params) {
        this.cotationId = filter.getLong("cotation")
        this.isExported = filter.getBoolean("exportedToLautopak")
        this.isUsed = filter.getBoolean("opportunityIsUsed")
        this.firstname = filter.getString("firstname")
        this.lastname = filter.getString("lastname")
        this.type = filter.getString("type")
        this.stocknumber = filter.getString("stocknumber")
        this.modelcode = filter.getString("modelcode")
        this.model = filter.getString("model")
        this.serialnumber = filter.getString("serialnumber")
        this.year = filter.getInteger("year")
        this.daysInStock = filter.getInteger("daysInStock")
        this.makes = filter.getListString("make")
        this.departments = filter.getListLong("department")
        if (filter.getString("workflowData_workflowBoard")) {
            filter.getString("workflowData_workflowBoard").split(",").each {
                if (it.isLong()) {
                    this.workflowData_workflowBoard.add(Long.valueOf(it))
                }
            }
        }
        if (filter.getString("workflowData_workflowTypeOfCard")) {
            filter.getString("workflowData_workflowTypeOfCard").split(",").each {
                if (it.isLong()) {
                    this.workflowData_typeOfCard.add(Long.valueOf(it))
                }
            }
        }
        this.workflowData_workflowMaster = filter.getListLong("workflowData_workflowMaster")
        this.labels = filter.getListLong("label")
        this.soldStatus = filter.getListInteger("soldStatus").collect { Opportunity.Status.getById(it) }
        this.interestIds = filter.getListLong("interest")
        this.usersSales.addAll(filter.getListLong("userSales"))
        this.usersFni.addAll(filter.getListLong("userFni"))
        this.usersBdc.addAll(filter.getListLong("userBdc"))
        this.costPrice = filter.getMinMaxDouble("costPrice")
        this.opportunityPotentialPrice = filter.getMinMaxDouble("opportunityPotentialPrice")
        this.sellingPrice = filter.getMinMaxDouble("sellingPrice")
        this.opportunityAccessoriesMarginPercent = filter.getMinMaxDoublePercent("opportunityAccessoriesMarginPercent")
        this.opportunityTradePriceToAbsorb = filter.getMinMaxDouble("opportunityTradePriceToAbsorb")
        this.opportunityAccessories = filter.getMinMaxDouble("opportunityAccessories")
        this.profit = filter.getMinMaxDouble("profit")
        this.profitPercent = filter.getMinMaxDoublePercent("profitPercent")
        this.opportunityOtherFee = filter.getMinMaxDouble("opportunityOtherFee")
        this.opportunityPromo = filter.getMinMaxDouble("opportunityPromo")
        this.opportunityMrsp = filter.getMinMaxDouble("opportunityMrsp")
        this.commission = filter.getMinMaxDouble("commission")
        this.commissionPercent = filter.getMinMaxDoublePercent("commissionPercent")
        this.fniProfitReserveFinance = filter.getMinMaxDouble("fniProfitReserveFinance")
        this.fniProfitLifeInsurance = filter.getMinMaxDouble("fniProfitLifeInsurance")
        this.fniProfitCriticalIllnessInsurance = filter.getMinMaxDouble("fniProfitCriticalIllnessInsurance")
        this.fniProfitExtendedPlan = filter.getMinMaxDouble("fniProfitExtendedPlan")
        this.fniProfitAestheticProtection = filter.getMinMaxDouble("fniProfitAestheticProtection")
        this.fniProfitOthers = filter.getMinMaxDouble("fniProfitOthers")
        this.fniProfitDisabilityInsurance = filter.getMinMaxDouble("fniProfitDisabilityInsurance")
        this.fniProfitReplacementInsurance = filter.getMinMaxDouble("fniProfitReplacementInsurance")
        this.fniProfitRentalLeaseUsury = filter.getMinMaxDouble("fniProfitRentalLeaseUsury")
        this.fniProfitChemicals = filter.getMinMaxDouble("fniProfitChemicals")
        this.fniProfitTotal = filter.getMinMaxDouble("fniProfitTotal")
        this.fniProfitTotalPercent = filter.getMinMaxDouble("fniProfitTotalPercent")
        this.date = filter.getMinMaxDate("date")
        this.lastStatusChange = filter.getMinMaxDate("lastStatusChange")
        this.dateSold = filter.getMinMaxDate("dateSold")
        this.workflowDataDateEnd = filter.getMinMaxDate("workflowDataDateEnd")
        this.paging = new DataTablePaging(params)
        this.ordering = new DataTableOrdering(params)
//        this.statisticDataFilters = statisticDataFilters
//        this.salesTrackingDataTableFilters = salesTrackingDataTableFilters
    }

    List<Long> listIds(){
        return query()
    }

    int countTotal(String generalImport = null) {
        return CotationElement.createCriteria().count {

        }
    }

    /**
     * TODO
     * */
    int count() {
        return CotationElement.createCriteria().count {

        }
    }

    List<CotationElement> list(){
        return query()
    }

    List query() {
        return CotationElement.createCriteria().list(paging.getListParams()) {
            createAlias('cotation', '_c', JoinType.LEFT_OUTER_JOIN)
            createAlias('_c.client', '_client', JoinType.LEFT_OUTER_JOIN)
            createAlias('opportunity', '_o', JoinType.LEFT_OUTER_JOIN)
            createAlias('_o.workflowData', '_workflowData', JoinType.LEFT_OUTER_JOIN)
            createAlias('_o.vehicleInstance', '_vh', JoinType.LEFT_OUTER_JOIN)
            createAlias('_workflowData.workflowBoard', '_workflowBoard', JoinType.LEFT_OUTER_JOIN)
            createAlias('trade', '_t', JoinType.LEFT_OUTER_JOIN)
            createAlias('userSales', '_us', JoinType.LEFT_OUTER_JOIN)
            createAlias('_o.userBdc', '_ub', JoinType.LEFT_OUTER_JOIN)
            createAlias('userFni', '_uf', JoinType.LEFT_OUTER_JOIN)
            createAlias('_o.labels', '_l', JoinType.LEFT_OUTER_JOIN)
            or {
                eq("_o.excluded", false)
                isNull("opportunity")
            }
            if (salesTrackingTopStatistics) salesTrackingTopStatistics.addActiveFiltersToCriteriaBuilder(delegate)

            if(salesTrackingDataTableFilters) salesTrackingDataTableFilters.addFilteredCellToCriteriaBuilder(delegate)
            if (this.cotationId) eq("_c.id", this.cotationId)
            if (this.isUsed != null) eq("opportunityIsUsed", this.isUsed)
            if (this.type == "trade" || this.type == "opportunity") isNotNull(this.type)
            if (this.costPrice) this.costPrice.addToCriteriaBuilder(delegate)
            if (this.opportunityPotentialPrice) this.opportunityPotentialPrice.addToCriteriaBuilder(delegate)
            if (this.sellingPrice) this.sellingPrice.addToCriteriaBuilder(delegate)
            if (this.opportunityTradePriceToAbsorb) this.opportunityTradePriceToAbsorb.addToCriteriaBuilder(delegate)
            if (this.opportunityAccessories) this.opportunityAccessories.addToCriteriaBuilder(delegate)
            if (this.profit) this.profit.addToCriteriaBuilder(delegate)
            if (this.profitPercent) this.profitPercent.addToCriteriaBuilder(delegate)
            if (this.opportunityOtherFee) this.opportunityOtherFee.addToCriteriaBuilder(delegate)
            if (this.opportunityAccessoriesMarginPercent) this.opportunityAccessoriesMarginPercent.addToCriteriaBuilder(delegate)
            if (this.opportunityPromo) this.opportunityPromo.addToCriteriaBuilder(delegate)
            if (this.opportunityMrsp) this.opportunityMrsp.addToCriteriaBuilder(delegate)
            if (this.commission) this.commission.addToCriteriaBuilder(delegate)
            if (this.commissionPercent) this.commissionPercent.addToCriteriaBuilder(delegate)
            if (this.fniProfitReserveFinance) this.fniProfitReserveFinance.addToCriteriaBuilder(delegate)
            if (this.fniProfitLifeInsurance) this.fniProfitLifeInsurance.addToCriteriaBuilder(delegate)
            if (this.fniProfitCriticalIllnessInsurance) this.fniProfitCriticalIllnessInsurance.addToCriteriaBuilder(delegate)
            if (this.fniProfitExtendedPlan) this.fniProfitExtendedPlan.addToCriteriaBuilder(delegate)
            if (this.fniProfitAestheticProtection) this.fniProfitAestheticProtection.addToCriteriaBuilder(delegate)
            if (this.fniProfitOthers) this.fniProfitOthers.addToCriteriaBuilder(delegate)
            if (this.fniProfitDisabilityInsurance) this.fniProfitDisabilityInsurance.addToCriteriaBuilder(delegate)
            if (this.fniProfitReplacementInsurance) this.fniProfitReplacementInsurance.addToCriteriaBuilder(delegate)
            if (this.fniProfitRentalLeaseUsury) this.fniProfitRentalLeaseUsury.addToCriteriaBuilder(delegate)
            if (this.fniProfitChemicals) this.fniProfitChemicals.addToCriteriaBuilder(delegate)
            if (this.fniProfitTotal) this.fniProfitTotal.addToCriteriaBuilder(delegate)
            if (this.fniProfitTotalPercent) this.fniProfitTotalPercent.addToCriteriaBuilder(delegate)
            if (this.firstname) ilike("_client.firstname", "%${this.firstname}%")
            if (this.lastname) ilike("_client.lastname", "%${this.lastname}%")
            if (this.dateSold) this.dateSold.addToCriteriaBuilder(delegate, "_c.dateSold")
            if (this.lastStatusChange) this.lastStatusChange.addToCriteriaBuilder(delegate, "_c.lastStatusChange")
            if (this.date) this.date.addToCriteriaBuilder(delegate, "_c.date")
            if (this.departments) 'in'("_c.department.id", this.departments)
            if (this.labels) 'in'("_l.id", this.labels)
            if (this.usersSales) {
                exists(new DetachedCriteria(OpportunityAssigned, "_opportunityAssigned").where {
                    eqProperty("_opportunityAssigned.opportunity.id", "_o.id")
                    'in'("_opportunityAssigned.user.id", this.usersSales)
                    projections {
                        property("_opportunityAssigned.id")
                    }
                })
            }
            if (this.usersFni) {
                exists(new DetachedCriteria(OpportunityAssigned, "_opportunityAssigned").where {
                    eqProperty("_opportunityAssigned.opportunity.id", "_o.id")
                    'in'("_opportunityAssigned.user.id", this.usersFni)
                    projections {
                        property("_opportunityAssigned.id")
                    }
                })
            }
            if (this.usersBdc) {
                exists(new DetachedCriteria(OpportunityAssigned, "_opportunityAssigned").where {
                    eqProperty("_opportunityAssigned.opportunity.id", "_o.id")
                    'in'("_opportunityAssigned.user.id", this.usersBdc)
                    projections {
                        property("_opportunityAssigned.id")
                    }
                })
            }
            if (this.isExported != null) {
                if (this.isExported) {
                    isNotNull("_c.lautopakDealNumberId")
                } else {
                    isNull("_c.lautopakDealNumberId")
                }
            }
            if (this.workflowDataDateEnd) this.workflowDataDateEnd.addToCriteriaBuilder(delegate, "_workflowData.dateEnd")
            if (this.workflowData_typeOfCard) 'in'("_workflowData.typeOfCard", this.workflowData_typeOfCard)
            if (this.workflowData_workflowMaster) 'in'("_workflowBoard.workflowMaster.id", this.workflowData_workflowMaster)
            if (this.workflowData_workflowBoard) 'in'("_workflowBoard.id", this.workflowData_workflowBoard)
            if (this.makes) 'in'("make", this.makes)
            if (this.model) ilike("model", "%${this.model}%")
            if (this.year) eq("year", this.year)
            if (this.interestIds) 'in'("interests.id", this.interestIds)
            if (this.daysInStock) eq("daysInStock", this.daysInStock)
            if (this.serialnumber) eq("_vh.serialNumber", this.serialnumber)
            if (this.soldStatus) 'in'("_c.soldStatus", this.soldStatus)
            if (this.stocknumber) ilike("stocknumber", "%${this.stocknumber}%")
            if (this.modelcode) ilike("modelcode", "%${this.modelcode}%")
            if (this.ordering) {
                this.ordering.addToCriteriaBuilder(delegate, ORDER_COLUMNS_MAPPING)
            }
        }
    }
}
