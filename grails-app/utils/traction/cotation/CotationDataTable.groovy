package traction.cotation

import grails.converters.JSON
import grails.util.Holders
import grails.web.servlet.mvc.GrailsParameterMap
import org.hibernate.sql.JoinType
import traction.CardTagLib
import traction.I18N
import traction.client.Client
import traction.communication.CommunicationTagLib
import traction.datatable.DataTableColumnFilterMinMax
import traction.datatable.DataTableColumnFilters
import traction.datatable.DataTableOrdering
import traction.datatable.DataTablePaging
import traction.vehicle.Vehicle

class CotationDataTable {
    DataTablePaging dataTablePaging
    DataTableOrdering dataTableOrdering

    // Filters
    Client client
    Vehicle vehicle
    String name
    DataTableColumnFilterMinMax date
    List<Cotation.Category> category
    String search
    String draw
    private CardTagLib cardTagLib
    private CommunicationTagLib communicationTagLib


    CotationDataTable(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.dataTableOrdering = new DataTableOrdering(params)
        this.search = params["search[value]"]
        this.draw = params.draw

        this.client = Client.get(params.long("clientId"))
        this.vehicle = Vehicle.get(params.long("vehicleId"))
        this.cardTagLib = Holders.grailsApplication.mainContext.getBean("traction.CardTagLib")
        this.communicationTagLib = Holders.grailsApplication.mainContext.getBean("traction.communication.CommunicationTagLib")
        DataTableColumnFilters filters = new DataTableColumnFilters(params.filter)
        this.name = filters.getString("name")
        this.date = filters.getMinMaxDate("date")
        this.category = filters.getListInteger("category").collect { Cotation.Category.getById(it) }
    }

    JSON render() {
        List<Cotation> cotations = list()
        Map json = [
                draw           : draw,
                recordsTotal   : countTotalRecords(),
                recordsFiltered: countTotalRecords(),
                data           : cotations.collect { format(it) }
        ]

        return json as JSON
    }

    private Map format(Cotation cotation) {
        def opportunities = cotation.opportunities.collect { it ->
            [
                    firstPictureId : it.vehicle ? it.vehicle.asInheritedVehicle().getInheritedPictures().getFirstPicture()?.id ?: 0 : 0,
                    opportunityName: it.name,
                    id             : it.id,
                    category       : ["message": it.category.message, "color": it.category.color],
                    vehicleId      : it.vehicle?.id,
                    cardDatatable  : cardTagLib.card2(opportunity: it),
                    assignedUsers  : it.assigned.collect { assignation ->
                        [
                                user  : assignation.user,
                                title : "${assignation.user.fullName} - ${I18N.m(assignation.user.userGroup?.message)}${assignation.isPrimary() ? '' : '2'}",
                                avatar: communicationTagLib.avatar(user: assignation.user, title: "${assignation.user.fullName} - ${I18N.m(assignation.user.userGroup?.message)}${assignation.isPrimary() ? '' : '2'}")
                        ]
                    }
            ]
        }

        def trades = cotation.trades.collect { it ->
            [
                    firstPictureId: it.vehicle ? it.vehicle.asInheritedVehicle().getInheritedPictures().getFirstPicture()?.id ?: 0 : 0,
                    vehicleName   : it.vehicle ? it.vehicle.toString() : "",
                    id            : it.id,
                    vehicleId     : it.vehicle?.id
            ]
        }

        return [
                id                                : cotation.id,
                name                              : cotation.name,
                status                            : cotation.soldStatus ? cotation.soldStatus.message : 'cotation.nosoldstatus',
                client                            : cotation.client,
                category                          : [name: cotation.category.toString(), icon: cotation.category.icon, color: cotation.category.color],
                date                              : cotation.date.format('MM-dd-yyyy'),
                dateSold                          : cotation.dateSold?.format('MM-dd-yyyy'),
                dateAprroved                      : cotation.dateApproved?.format('MM-dd-yyyy'),
                dateExpire                        : cotation.dateExpire?.format('MM-dd-yyyy'),
                dateModified                      : cotation.dateMod?.format('MM-dd-yyyy'),
                opportunities                     : opportunities,
                trades                            : trades,
                firstOpportunityAssignedUserAvatars: opportunities && opportunities[0].assignedUsers ? opportunities[0].assignedUsers.collect { it.avatar } : []        ]
    }

    private List<Cotation> list() {
        return Cotation.createCriteria().list(dataTablePaging.getListParams()) {
            eq("deleted", false)
            if (vehicle) {
                createAlias("cotationElements", "_cotationElements", JoinType.LEFT_OUTER_JOIN)
                createAlias("_cotationElements.trade", "_trade", JoinType.LEFT_OUTER_JOIN)
                createAlias("_cotationElements.opportunity", "_opportunity", JoinType.LEFT_OUTER_JOIN)
                or {
                    eq("_trade.vehicle", vehicle)
                    eq("_opportunity.vehicleInstance", vehicle)
                }
            }
            if (client) eq("client", client)

            if (name) ilike('name', '%' + name + '%')
            if (date) date.addToCriteriaBuilder(delegate)
            if (category) "in"("category", category)

            // TODO: CotationDataTable filtres sur les colonnes opportunity, trade et category DV1-T706

            dataTableOrdering.addToCriteriaBuilder(delegate)
        }
    }

    int countTotalRecords() {
        return Cotation.createCriteria().count {
            eq("deleted", false)
            if (vehicle) {
                createAlias("cotationElements", "_cotationElements", JoinType.LEFT_OUTER_JOIN)
                createAlias("_cotationElements.trade", "_trade", JoinType.LEFT_OUTER_JOIN)
                createAlias("_cotationElements.opportunity", "_opportunity", JoinType.LEFT_OUTER_JOIN)
                or {
                    eq("_trade.vehicle", vehicle)
                    eq("_opportunity.vehicleInstance", vehicle)
                }
            }
            if (client) eq("client", client)
        }
    }

}
