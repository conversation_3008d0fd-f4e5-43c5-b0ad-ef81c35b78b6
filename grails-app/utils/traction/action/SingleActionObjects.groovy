package traction.action

import traction.client.Client
import traction.opportunity.Opportunity
import traction.service.CalendarEvent
import traction.service.LautopakPartsInvoice
import traction.service.WorkOrder
import traction.workflow.WorkflowData

class SingleActionObjects {
    Client client
    Opportunity opportunity
    WorkflowData workflowData
    LautopakPartsInvoice lautopakPartsInvoice
    WorkOrder workOrder
    CalendarEvent calendarEvent

    SingleActionObjects(def obj) {
        switch (obj.class) {
            case Client:
                this.client = (Client) obj
                break
            case Opportunity:
                this.opportunity = (Opportunity) obj
                this.client = this.opportunity?.client
                this.workflowData = this.opportunity?.workflowData
                break
            case WorkflowData:
                this.workflowData = (WorkflowData) obj
                this.opportunity = this.workflowData?.opportunity
                this.client = this.opportunity?.client
                break
            case CalendarEvent:
                this.calendarEvent = (CalendarEvent) obj
                this.client = this.calendarEvent?.client
                this.workflowData = this.calendarEvent?.workflowData
                this.opportunity = this.workflowData?.opportunity
                break
            case LautopakPartsInvoice:
                this.lautopakPartsInvoice = (LautopakPartsInvoice) obj
                this.client = this.lautopakPartsInvoice?.client
                break
            case WorkOrder:
                this.workOrder = (WorkOrder) obj
                this.client = this.workOrder?.client
                break
        }
    }
}
