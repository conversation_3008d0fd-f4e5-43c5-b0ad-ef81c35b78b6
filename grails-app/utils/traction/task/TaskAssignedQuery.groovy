package traction.task

import traction.DateUtils
import traction.client.Client
import traction.security.User

class TaskAssignedQuery {

    Client client
    User creator
    User user
    List<User> users
    TaskAssigned.Type type
    Task.Status status
    String className
    Date fromExpectedDate
    Date toExpectedDate
    Task.Status taskStatus
    Task.Category taskCategory

    static TaskAssignedQuery searchLate(User user, TaskAssigned.Type type, Client client = null) {
        return new TaskAssignedQuery(
                client: client,
                user: user,
                type: type,
                status: Task.Status.TODO,
                toExpectedDate: new Date()
        )
    }

    static TaskAssignedQuery searchSoon(User user, TaskAssigned.Type type, Client client = null) {
        return new TaskAssignedQuery(
                client: client,
                user: user,
                type: type,
                status: Task.Status.TODO,
                fromExpectedDate: new Date()
        )
    }

    static TaskAssignedQuery searchToday(User user, TaskAssigned.Type type, Client client = null) {
        return new TaskAssignedQuery(
                client: client,
                user: user,
                type: type,
                status: Task.Status.TODO,
                fromExpectedDate: new Date(),
                toExpectedDate: DateUtils.getTomorrow()
        )
    }

    List<TaskAssigned> list() {
        return this.search(ProjectionEnum.NONE)
    }

    int count() {
        return this.search(ProjectionEnum.COUNT)
    }

    Date minExpectedDate() {
        return search(ProjectionEnum.MIN_EXPECTED_DATE)
    }

    Date maxExpectedDate() {
        return search(ProjectionEnum.MAX_EXPECTED_DATE)
    }

    private def search(ProjectionEnum projection) {
        boolean hasNoProjections = projection == ProjectionEnum.NONE
        List tasks = TaskAssigned.createCriteria().list {
            createAlias('task', '_task')
            if (users) 'in'("user", users)
            if (user) eq("user", user)
            if (type) eq("type", type)
            if (status) eq("status", status)
            if (className) eq("_task.class", className)
            if (creator) eq("_task.creator", creator)
            if (client) eq("_task.client", client)
            if (fromExpectedDate) ge("_task.expectedDate", fromExpectedDate)
            if (toExpectedDate) lt("_task.expectedDate", toExpectedDate)
            if (taskStatus) eq("_task.status", taskStatus)
            if (taskCategory) eq("_task.category", taskCategory)
            order("_task.expectedDate")
            if (!hasNoProjections) {
                projections {
                    switch (projection) {
                        case ProjectionEnum.COUNT:
                            rowCount()
                            break
                        case ProjectionEnum.MAX_EXPECTED_DATE:
                            max("_task.expectedDate")
                            break
                        case ProjectionEnum.MIN_EXPECTED_DATE:
                            min("_task.expectedDate")
                            break
                    }
                }
            }
        }
        if (hasNoProjections) return tasks
        return tasks.first()
    }

    private enum ProjectionEnum {
        NONE, COUNT, MAX_EXPECTED_DATE, MIN_EXPECTED_DATE
    }
}
