package traction.communication.email

import com.google.api.client.util.StringUtils
import grails.converters.JSON
import grails.util.Holders
import groovy.json.JsonSlurper
import groovy.util.logging.Slf4j
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import org.jsoup.select.Elements

@Slf4j
class GmailUtil {

    static String replaceInRaw(String raw, String oldString, String newString) {
        Map rawJson = new JsonSlurper().parseText(raw)
        replaceInPart(rawJson, oldString, newString)
        return new JSON(rawJson).toString()
    }

    private static String replaceInPart(Map part, String oldString, String newString) {
        if (part.body.data) {
            String text = decode(part.body.data)
            text = text.replace(oldString, newString)
            part.body.data = encode(text)
        }
        if (part.parts) {
            part.parts.each { p ->
                replaceInPart(p, oldString, newString)
            }
        }
        return part
    }

    private static String encode(String value) {
        byte[] data = StringUtils.getBytesUtf8(value)
        return java.util.Base64.getEncoder().encodeToString(data).replace("+", "-").replace("/", "_")
    }

    private static String decode(String value) {
        byte[] data = java.util.Base64.getDecoder().decode(value.replace("-", "+").replace("_", "/"));
        return StringUtils.newStringUtf8(data)
    }

    static String getBodyAsHTML(String raw) {
        if (raw) {
            String html = getText(new JsonSlurper().parseText(raw))
            if (html) {
                Document doc = Jsoup.parseBodyFragment(html.replace("=\"cid:traction-mail-src-", "=\"" + Holders.grailsApplication.mainContext.getBean('configService').get("SERVER", "server.traction.base") + "/web/getFile/"))
                doc.outputSettings().prettyPrint(false)
                Elements images = doc.select('img')
                for (Element image : images) {
                    String imageCss = image.attr('style')
                    imageCss = "max-width:100%;padding-top:0.5em;padding-bottom:0.5em;" + imageCss
                    image.attr('style', imageCss)
                }
                return doc.html()
            }
        }
        return ""
    }

    private static String getText(Map p) {
        if (p.body.data && p.mimeType.startsWith("text/")) {
            return StringUtils.newStringUtf8(Base64.getDecoder().decode(p.body.data.replace("-", "+").replace("_", "/"))).trim()
        }
        if (p.mimeType == "multipart/alternative") {
            // prefer html text over plain text
            String text
            for (int i = 0; i < p.parts.size(); i++) {
                Map bp = p.parts[i]
                if (bp.mimeType == "text/plain") {
                    if (!text) {
                        text = this.getText(bp)
                    }
                } else if (bp.mimeType == "text/html") {
                    String s = this.getText(bp)
                    if (s) {
                        return s
                    }
                } else {
                    return this.getText(bp)
                }
            }
            return text
        } else if (p.mimeType.startsWith("multipart/")) {
            String s = ""
            for (int i = 0; i < p.parts.size(); i++) {
                String add = this.getText(p.parts[i])
                if (add) {
                    if (s) {
                        s += "<br><br>" + add
                    }
                    else {
                        s = add
                    }
                }
            }
            return s
        }
        return ""
    }
}
