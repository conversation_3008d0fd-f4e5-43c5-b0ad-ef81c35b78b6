package traction.communication.email

import com.google.api.services.gmail.model.MessagePart
import com.google.api.services.gmail.model.MessagePartBody
import com.google.api.services.gmail.model.MessagePartHeader
import com.mailgun.api.v3.MailgunEventsApi
import com.mailgun.api.v3.MailgunStoreMessagesApi
import com.mailgun.client.MailgunClient
import com.mailgun.model.events.EventItem
import com.mailgun.util.ObjectMapperUtil
import feign.Response
import grails.util.Holders
import groovy.json.JsonOutput
import groovy.util.logging.Slf4j
import org.apache.commons.text.StringEscapeUtils
import traction.WordUtils
import traction.communication.MailMessage
import traction.vendor.MailgunService

@Slf4j
class MailgunMessage extends AbstractEmailMessage {

    private FullStoreMessageResponse storeMessageResponse

    MailgunMessage(FullStoreMessageResponse storeMessageResponse) {
        this.storeMessageResponse = storeMessageResponse
    }

    static MailgunMessage fetch(String messageUrl, String apiKey) {
        try {
            MailgunStoreMessagesApi mailgunStoreMessagesApi = MailgunClient.config(messageUrl, apiKey).createApiWithAbsoluteUrl(MailgunStoreMessagesApi.class)
            Response response = mailgunStoreMessagesApi.retrieveMessageFeignResponse()
            FullStoreMessageResponse storeMessageResponse = ObjectMapperUtil.decode(response, FullStoreMessageResponse.class)
            if (storeMessageResponse) {
                return new MailgunMessage(storeMessageResponse)
            }
        } catch (Exception ex) {
            ex.printStackTrace()
            log.warn "MailgunMessage.fetch: Exception: ${ex}"
        }
        return null
    }


    @Override
    String getTo() {
        return getValueFromHeaders("To") ?: getValueFromHeaders("Delivered-To") ?: this.storeMessageResponse.recipients
    }

    @Override
    MailMessage.EmailProvider getEmailProvider() {
        return MailMessage.EmailProvider.MAILGUN
    }

    @Override
    List<AbstractMessageAttachment> getAttachments() {
        return storeMessageResponse.getAttachments().collect { new MailgunMessageAttachment(it, storeMessageResponse) }
    }

    @Override
    String getEmailWatch() {
        return null
    }

    @Override
    String getId() {
        return EmailUtils.cleanEmail(storeMessageResponse.getMessageId())
    }

    @Override
    Date getDate() {
        MailgunService mailgunService = Holders.grailsApplication.mainContext.getBean('mailgunService')
        EventItem eventItem = mailgunService.getStoredEvent(this.getId())
        return eventItem ? Date.from(eventItem.timestamp.toInstant()) : new Date()
    }

    @Override
    String getRawString() {
        MessagePart mp2 = new MessagePart()
        def hs = ["Delivered-To":to,"From":from,"To":to]
        def mphs = []
        hs.eachWithIndex () { item, i ->
            MessagePartHeader mph = new MessagePartHeader()
            mph.setName(item.key)
            mph.setValue(item.value)
            mphs.add(mph)
        }
        mp2.setHeaders(mphs)
        mp2.setMimeType("text/plain")
        MessagePartBody messagePartBody = new MessagePartBody()

        String plainTextEscaped = StringEscapeUtils.escapeHtml4(storeMessageResponse.getBodyPlain() ?: storeMessageResponse.getStrippedHtml()).replaceAll("\r\n", "<br>")
        String content = storeMessageResponse.getBodyHtml() ?: plainTextEscaped
        content = WordUtils.keepAdf(content)

        messagePartBody.encodeData(content.getBytes("UTF8"))
        messagePartBody.setSize(content.size())
        mp2.setBody(messagePartBody)
        return JsonOutput.toJson(mp2)
    }

    @Override
    Integer getSizeEstimate() {
        int sumSizeAttachments = getAttachments().collect {it.getSize() }.sum() ?: 0
        return (getRawString().getBytes().length) + sumSizeAttachments
    }

    @Override
    String getSnippet() {
        return storeMessageResponse.getStrippedText()
    }

    @Override
    String getThreadId() {
        return null
    }

    @Override
    long getHistoryId() {
        return 0
    }

    @Override
    boolean isOutbound() {
        return false
    }

    @Override
    String getValueFromHeaders(String headerName) {
        return storeMessageResponse.getHeaders().findAll { it[0].equalsIgnoreCase(headerName) }.collect { it[1] }.join(", ")
    }
}
