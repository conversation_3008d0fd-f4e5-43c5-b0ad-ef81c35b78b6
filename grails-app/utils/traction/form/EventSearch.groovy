package traction.form

import groovy.util.logging.Slf4j

@Slf4j
class EventSearch {

    int max = 10
    int offset = 0
    Date fromDate
    Date toDate
    String name
    FormProfile profile

    def setDateFrom(String from) {
        try {
            fromDate = new Date().parse('yyyy-MM-dd', from)
        } catch (Exception ex) {
            log.warn "Cannot convert Date: ${ex}"
        }
    }

    def setDateTo(String to) {
        try {
            toDate = new Date().parse('yyyy-MM-dd', to)
        } catch (Exception ex) {
            log.warn "Cannot convert Date: ${ex}"
        }
    }
}
