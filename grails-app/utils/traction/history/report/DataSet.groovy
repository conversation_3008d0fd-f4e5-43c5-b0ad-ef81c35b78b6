package traction.history.report

import traction.I18N
import traction.history.History

class DataSet {
    History.Status historyStatus
    String backgroundColor
    String borderColor
    int borderWidth = 1
    List<Integer> data = []

    DataSet(History.Status historyStatus, String hexColor) {
        this.historyStatus = historyStatus
        this.backgroundColor = hexColor + "33"
        this.borderColor = hexColor
    }

    String getLabel() {
        return I18N.m(historyStatus.message)
    }
}