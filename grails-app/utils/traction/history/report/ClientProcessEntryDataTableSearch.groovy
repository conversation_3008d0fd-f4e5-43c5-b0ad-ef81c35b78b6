package traction.history.report

import grails.gorm.PagedResultList
import grails.web.servlet.mvc.GrailsParameterMap
import traction.client.ClientProcessEntry
import traction.datatable.*
import traction.history.History

class ClientProcessEntryDataTableSearch {
    DataTablePaging dataTablePaging
    DataTableOrdering dataTableOrdering

    DataTableColumnFilterMinMax date
    DataTableColumnFilterMinMax priority
    List<Long> clientProcess
    List<ClientProcessEntry.Status> status

    ClientProcessEntryDataTableSearch(GrailsParameterMap params) {
        this.dataTablePaging = new DataTablePaging(params)
        this.dataTableOrdering = new DataTableOrdering(params)
        DataTableColumnFilters filter = new DataTableColumnFilters(params.filter)
        this.date = filter.getMinMaxDate("date")
        this.priority = filter.getMinMaxInteger("priority")
        this.clientProcess = filter.getListLong("clientProcess")
        this.status = filter.getListInteger("status").collect { ClientProcessEntry.Status.getById(it) }
    }

    PagedResultList<ClientProcessEntry> list() {
        return this.get(false)
    }

    int count() {
        return this.get(true)
    }

    private def get(boolean count) {
        def res = ClientProcessEntry.createCriteria().list(dataTablePaging.getListParams()) {
            eq("deleted", false)
            if (status) "in"("status", status)
            if (date) date.addToCriteriaBuilder(delegate)
            'clientProcess' {
                if (clientProcess) "in"("id", clientProcess)
                if (priority) priority.addToCriteriaBuilder(delegate)
            }
            if (count) {
                projections {
                    rowCount()
                }
            } else {
                dataTableOrdering.addToCriteriaBuilder(delegate)
            }
        }
        return count ? res[0] : res
    }
}
