package traction.history.report

import traction.DateUtils
import traction.history.History

class SoldBoardStatCards {

    private static Map<String, Stat> stats = [:]

    static void updateStats() {
        println("updateStats : ${stats.size()}")
        stats.clear()
        println("updateStats clear : ${stats.size()}")
        List dayData = getProjectionsData("TODAY")
        processData("day", dayData)
        List monthData = getProjectionsData("STARTMONTH")
        processData("month", monthData)
        List yearData = getProjectionsData("STARTYEAR")
        processData("year", yearData)
        println("updateStats end: ${stats.size()}")
    }

    private static List<List> getProjectionsData(String method) {
        Date from = DateUtils.getDate(method, 0, 0, 0)
        List data = History.createCriteria().list {
            ge("date", from)
            "in"("status", History.Status.getSoldboards())
            projections {
                groupProperty("user.id")
                groupProperty("status")
                rowCount()
            }
        }
        return data
    }

    private static List<List> processData(String statProperty, List<List> data) {
        data.each {
            Long userId = it[0]
            History.Status status = it[1]
            int count = it[2]
            Stat stat = getStatistic(userId, status)
            stat.setProperty(statProperty, count)
        }
    }

    static Stat getStatistic(Long userId, History.Status status) {
        String key = "${userId}-${status.id}"
        Stat stat = stats[key]
        if (stat) return stat

        stat = new Stat(userId, status)
        stats[key] = stat
        return stat
    }
}

class Stat {
    Long userId
    History.Status status
    int year = 0
    int month = 0
    int day = 0

    Stat(Long userId, History.Status status) {
        this.userId = userId
        this.status = status
    }
}

