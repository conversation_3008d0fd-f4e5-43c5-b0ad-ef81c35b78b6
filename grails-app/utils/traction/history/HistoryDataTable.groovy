package traction.history

import grails.converters.JSON
import grails.web.servlet.mvc.GrailsParameterMap
import traction.ExecutionTime
import traction.I18N
import traction.datatable.DataTableColumnFilters
import traction.datatable.DataTableOrdering
import traction.datatable.DataTablePaging
import traction.item.Item
import traction.security.User

class HistoryDataTable {

    private HistoryDataTableSearch historyDataTableSearch
    private String draw

    HistoryDataTable(GrailsParameterMap params) {
        this.draw = params.draw
        this.historyDataTableSearch = new HistoryDataTableSearch(params)
    }

    JSON getData() {
        ExecutionTime executionTime = new ExecutionTime()
        int recordsTotal = this.historyDataTableSearch.countTotal()
        println "recordsTotal done: ${executionTime.executionTime()}ms"
        int recordsFiltered = this.historyDataTableSearch.count()
        println "recordsFiltered done : ${executionTime.executionTime()}ms"
        List<History> histories = this.historyDataTableSearch.list()
        println "List Item done : ${executionTime.executionTime()}ms"
        Map map = [
                draw           : draw,
                recordsTotal   : recordsTotal,
                recordsFiltered: recordsFiltered,
                data           : histories.collect {
                    [
                            id: it.id,
                            expand: "",
                            date: it.date.format("yyyy-MM-dd HH:mm"),
                            vehicle: it.vehicle?.toString(),
                            domainClassId: it.category.aClass.get(it.domainClassId)?.toString() ?: it.domainClassId,
                            user: it.user?.fullName,
                            category: I18N.m(it.category.message),
                            action: I18N.m(it.action?.message),
                            status: I18N.m(it.status.message)
                    ]
                }
        ]
        println "map render done : ${executionTime.executionTime()}ms"
        return map as JSON
    }

    static Map<String, List> getDataTableOptions(boolean vehicleOnly) {
        List<History.Category> categories = vehicleOnly ? History.Category.getRelatedToVehicle() : History.Category.values()
        return [
                category: categories.collect {
                    [
                            label: I18N.m(it.getMessage()),
                            value: it.name()
                    ]
                },
                action: History.Action.values().collect {
                    [
                            label: I18N.m(it.getMessage()),
                            value: it.name()
                    ]
                },
                user: User.getAll().collect {
                    [
                            label: it.fullName,
                            value: it.id
                    ]
                }
        ]
    }

}
