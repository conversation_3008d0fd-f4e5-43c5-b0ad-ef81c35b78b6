package traction.queryBuilder

import grails.gorm.PagedResultList
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.Filter

class QueryBuilderCriteria extends QueryBuilder {

    QueryBuilderCriteria(JSONObject json, Filter.Type filterType, Object objectForSameOperator = null) {
        this.filterType = filterType
        this.objectForSameOperator = objectForSameOperator
        if (json) {
            if (json.containsKey("condition") && json.containsKey("rules")) {
                this.condition = json.getString("condition").toLowerCase()
                if (this.condition != "and" && this.condition != "or") {
                    throw new Exception("Unknown QueryBuilder.condition : " + this.condition)
                }
                JSONArray rules = json.getJSONArray("rules")
                for (int i = 0; i < rules.length(); i++) {
                    JSONObject rule = rules.getJSONObject(i)
                    this.filters.add(new QueryBuilderCriteria(rule, this.filterType, objectForSameOperator))
                }
            } else if (json.containsKey("id")) {
                this.id = json.getString("id")
                this.field = json.getString("field")
                this.type = json.getString("type")
                if (json.containsKey("data")) {
                    JSONObject data = json.getJSONObject("data")
                    if (data.containsKey("realType")) {
                        this.type = data.getString("realType")
                    }
                    if (data.containsKey("enumType")) {
                        this.enumType = data.getString("enumType")
                    }
                }
                Map operatorMapping = [
                        "same"            : "same",
                        "equal"           : "eq",
                        "not_equal"       : "ne",
                        "in"              : "in",
                        "not_in"          : "not in",
                        "less"            : "lt",
                        "less_or_equal"   : "le",
                        "greater"         : "gt",
                        "greater_or_equal": "ge",
                        "between"         : "between",
                        "not_between"     : "not between",
                        "begins_with"     : "startswith",
                        "not_begins_with" : "not startswith",
                        "contains"        : "contains",
                        "not_contains"    : "not contains",
                        "ends_with"       : "endswith",
                        "not_ends_with"   : "not endwith",
                        "is_null"         : "isNull",
                        "is_not_null"     : "isNotNull",
                        "is_empty"        : "eq",
                        "is_not_empty"    : "ne",
                        "size_gt"         : "sizeGt",
                        "size_ge"         : "sizeGe",
                        "size_lt"         : "sizeLt",
                        "size_le"         : "sizeLe",
                        "size_ne"         : "sizeNe",
                        "size_eq"         : "sizeEq"
                ]
                this.value = json.get("value")
                if (json.containsKey("data")) {
                    this.data = json.get("data")
                }
                String o = json.getString("operator")
                if (operatorMapping.containsKey(o)) {
                    this.operator = operatorMapping[o]
                    this.addNotOperator = this.operator.startsWith("not ")
                    if (this.addNotOperator) {
                        this.operator = this.operator.substring(4)
                    }
                    if (this.operator == "contains") {
                        this.value = "%${this.value}%"
                        this.operator = "like"
                    } else if (this.operator == "startswith") {
                        this.value = "${this.value}%"
                        this.operator = "like"
                    } else if (this.operator == "endswith") {
                        this.value = "%${this.value}"
                        this.operator = "like"
                    } else if (this.operator == "same") {
                        if (!this.objectForSameOperator) {
                            throw new Exception("QueryBuilder with 'SAME' operator has no 'objectForSameOperator'. ${json}")
                        }
                        this.value = getValueForSameOperatorEq()
                        if (this.value != null) {
                            this.operator = this.value instanceof List ? "in" : "eq"
                        } else {
                            this.operator = "isNull"
                        }
                    }
                    if (this.type == "date" && this.operator == "between") {
                        try {
                            Date date = new Date().parse(DATE_FORMAT, this.value.get(1)) + 1
                            this.value.set(1, date.format(DATE_FORMAT))
                        } catch (Exception ex) {
                        }
                    }

                    this.dontNeedValue = (this.operator == "isNull" || this.operator == "isNotNull")
                } else {
                    println "WARNING!!!! : new operator found : " + o
                    this.operator = o
                }
            }
            if (this.id && this.condition) {
                throw new Exception("WARNING!!!! : id and condition found : " + json.toString())
            }
        }
    }

    @Override
    List<Long> getIdsList(int offset, int max, String orderCol, String orderDir) {
        println "this.getClosure(Projection.IDS, offset, max, orderCol, orderDir):" + this.getClosure(Projection.IDS, offset, max, orderCol, orderDir)
        PagedResultList<Long> ids = new GroovyShell().evaluate(this.getClosure(Projection.IDS, offset, max, orderCol, orderDir))
        return ids
    }

    @Override
    List<Object> getList(int offset, int max, String orderCol, String orderDir) {
        PagedResultList<Object> objects = new GroovyShell().evaluate(this.getClosure(Projection.NONE, offset, max, orderCol, orderDir))
        return objects
    }

    @Override
    List<Long> getIdsList() {
        return this.getIdsList(0, -1, null, null)
    }

    @Override
    int getCount() {
        List result = new GroovyShell().evaluate(this.getClosure(Projection.COUNT, 0, -1, null, null))
        if (result) {
            return result.first()
        }
        return result
    }

    @Override
    String getDebugQueryString() {
        return getClosureCode("", 0,0, null, null)
    }

    String getClosure(Projection projection, int offset, int max, String orderCol, String orderDir) {
        String projectionCode = ""
        switch (projection) {
            case Projection.IDS:
                projectionCode = "projections { groupProperty(\"id\") }"
                break
            case Projection.COUNT:
                projectionCode = "projections { countDistinct(\"id\") }"
                break
        }
        return this.getClosureCode(projectionCode, offset, max, orderCol, orderDir)
    }

    private String getClosureCode(String projectionCode, int offset, int max, String orderCol, String orderDir) {
        String s = "${this.filterType.aClass.name}.createCriteria().list(offset: ${offset}, max: ${max}) {\n${this.addedQueryCode}\n${this.closureString2()}\n${(orderCol && orderDir) ? "order(\"${orderCol}\", \"${orderDir}\")\n" : ""}${projectionCode}}.unique()"
        println s
        return s
    }

    private String closureString2() {
        StringBuilder builder = new StringBuilder()
        if (this.condition) {
            builder.append("'${this.condition}' {\n")
            List<String> currentLevel = []
            this.filters.sort { it.id }
            this.filters.each { f ->
                if (f.id) {
                    List<String> levels = f.id.split("[.]")
                    if (f.id != "id") {
                        levels.remove(0)
                    }
                    levels.remove(levels.size() - 1)
                    boolean differentLevels = false
                    int levelsToRemove = 0
                    for (int i = 0; i < currentLevel.size(); i++) {
                        if (!differentLevels && levels.size() > i) {
                            if (levels.get(i) != currentLevel.get(i)) {
                                differentLevels = true
                            }
                        }
                        if (differentLevels) {
                            levelsToRemove++
                        }
                    }
                    levelsToRemove.times {
                        currentLevel.remove(currentLevel.size() - 1)
                        builder.append("}\n")
                    }
                    for (int i = 0; i < levels.size(); i++) {
                        if (currentLevel.size() <= i) {
                            builder.append("${levels.get(i)} {\n")
                        }
                    }
                    if (currentLevel.size() > levels.size()) {
                        (currentLevel.size() - levels.size()).times {
                            builder.append("}\n")
                        }
                    }
                    currentLevel = levels

                }
                builder.append((f as QueryBuilderCriteria).closureString2())
            }
            currentLevel.each {
                builder.append("}\n")
            }
            builder.append("}\n")
        } else if (this.id) {
            List split = this.field.split("[.]")
            String prop = split[split.size() - 1]
            String not = ""
            String notend = ""
            if (this.addNotOperator) {
                not = "not { "
                notend = " }"
            }
            if (this.id == "id") {
                Filter filter = Filter.get(Long.valueOf(this.value))
                String subCriteria = (filter.getQueryBuilder(null) as QueryBuilderCriteria).getClosure(Projection.IDS, 0, -1, null, null)
                builder.append("${not}'${this.operator}'(\"id\", ${subCriteria} + [0L]\n)${notend}\n")
            } else if (this.dontNeedValue) {
                builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)})${notend}\n")
            } else {
                switch (this.type) {
                    case "long":
                        List valueList = (this.value instanceof List) ? this.value : [this.value]
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${valueList.collect { "${Long.valueOf(it)}L" }.join(", ")})${notend}\n")
                        break
                    case "integer":
                        List valueList = (this.value instanceof List) ? this.value : [this.value]
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${valueList.collect { "${Integer.valueOf(it)}" }.join(", ")})${notend}\n")
                        break
                    case "double":
                        List valueList = (this.value instanceof List) ? this.value : [this.value]
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${valueList.collect { "${Double.valueOf(it)}D" }.join(", ")})${notend}\n")
                        break
                    case "date":
                        List<String> valueList = this.value
//                        println "valueList:" + valueList
                        String valueString = valueList.collect { String value -> value.startsWith("new Date() - ") ? "new Date() - " + Integer.valueOf(value.substring(13)) : "Date.parse(\"${DATE_FORMAT}\", \"${Date.parse(DATE_FORMAT, value).format()}\")" }.join(", ")
  //                      println "valueString: " + valueString
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${valueString})${notend}\n")
                        break
                    case "enum":
                        def values = this.value
                        String finalValue
                        if (this.enumType == "string") {
                            finalValue = '"' + values + '"'
//                            finalValue = "[" + values.collect { this.safeCriteriaString(it) }.join(",") + "]" // old ?
                        } else {
                            Class enumClass = Class.forName(String.valueOf(this.enumType))
                            finalValue = "[" + values.collect { "${enumClass.name}.getById(${Integer.valueOf(it)})" }.join(",") + "]"
                        }
      //                  println("finalValue:" + finalValue)
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${finalValue})${notend}\n")
                        break
                    case "object":
        //                println "this.value:" + this.value
                        List values = this.value
                        String finalValue = "[" + values.collect { "${Long.valueOf(it)}L" }.join(", ") + "]"
          //              println("finalValue:" + finalValue)
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop + ".id")}, ${finalValue})${notend}\n")
                        break
                    case "hasmanyobject":
            //            println "this.value:" + this.value
                        List values = this.value
                        String finalValue = "[" + values.collect { "${Long.valueOf(it)}L" }.join(", ") + "]"
              //          println("finalValue:" + finalValue)
                        builder.append("${prop} {\n${not}'${this.operator}'(\"id\", ${finalValue})${notend}\n}\n")
                        break
                    case "string":
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${this.safeCriteriaString(this.value)})${notend}\n")
                        break
                    case "boolean":
                        Boolean b = Boolean.valueOf(this.value)
                        builder.append("${not}'${this.operator}'(${this.safeCriteriaString(prop)}, ${b})${notend}\n")
                        break
                    default:
                        throw new Exception("WARNING!!! \"${not}'${this.operator}'(\\\"${prop}\\\", ${this.value})${notend}\"")
                }
            }
        }
        return builder.toString()
    }

    String safeCriteriaString(String value) {
        return '"' + (value ? value.replace("\\", "\\\\").replace("\"", "\\\"") : "") + '"'
    }

}
