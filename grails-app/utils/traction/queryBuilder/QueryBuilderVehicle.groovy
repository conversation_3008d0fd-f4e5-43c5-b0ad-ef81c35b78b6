package traction.queryBuilder

import groovy.sql.Sql
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.Filter
import traction.vehicle.Vehicle
import traction.vehicle.group.VehicleFormulaExpressionUtil

class QueryBuilderVehicle extends QueryBuilder {

    Sql sql
    List<QueryBuilderVehicle> filters = []
    private String vehicleTableAlias = "v"
    private Long idEqCondition
    private Long idNeCondition
    private Boolean deletedEqCondition
    private Boolean validateEqCondition
    private List<Vehicle.Type> typeInCondition
    private List<Vehicle.ListingStatus> listingStatusInCondition

    QueryBuilderVehicle(Sql sql, JSONObject json, Filter.Type filterType, Object objectForSameOperator, String vehicleTableAlias) {
        this.sql = sql
        this.filterType = filterType
        this.objectForSameOperator = objectForSameOperator
        if (vehicleTableAlias) {
            this.vehicleTableAlias = vehicleTableAlias
        }
        if (json) {
            if (json.containsKey("condition") && json.containsKey("rules")) {
                this.condition = json.getString("condition").toUpperCase()
                if (this.condition != "AND" && this.condition != "OR") {
                    throw new Exception("Unknown QueryBuilder.condition : " + this.condition)
                }
                JSONArray rules = json.getJSONArray("rules")
                for (int i = 0; i < rules.length(); i++) {
                    JSONObject rule = rules.getJSONObject(i)
                    this.filters.add(new QueryBuilderVehicle(sql, rule, this.filterType, objectForSameOperator, vehicleTableAlias))
                }
            } else if (json.containsKey("id")) {
                this.id = json.getString("id")
                this.field = json.getString("field")
                this.type = json.getString("type")
                if (json.containsKey("data")) {
                    JSONObject data = json.getJSONObject("data")
                    if (data.containsKey("realType")) {
                        this.type = data.getString("realType")
                    }
                    if (data.containsKey("enumType")) {
                        this.enumType = data.getString("enumType")
                    }
                }
                Map operatorMapping = [
                        "same"            : "same",
                        "equal"           : "=",
                        "not_equal"       : "!=",
                        "in"              : "IN",
                        "not_in"          : "NOT IN",
                        "less"            : "<",
                        "less_or_equal"   : "<=",
                        "greater"         : ">",
                        "greater_or_equal": ">=",
                        "between"         : "BETWEEN",
                        "not_between"     : "NOT BETWEEN",
                        "begins_with"     : "startswith",
                        "not_begins_with" : "NOT startswith",
                        "contains"        : "contains",
                        "not_contains"    : "NOT contains",
                        "ends_with"       : "endswith",
                        "not_ends_with"   : "NOT endwith",
                        "is_null"         : "IS NULL",
                        "is_not_null"     : "IS NOT NULL"
                ]
                this.value = json.get("value")
                if (json.containsKey("data")) {
                    this.data = json.get("data")
                }
                String o = json.getString("operator")
                if (operatorMapping.containsKey(o)) {
                    this.operator = operatorMapping[o]
                    this.addNotOperator = this.operator.startsWith("NOT ")
                    if (this.addNotOperator) {
                        this.operator = this.operator.substring(4)
                    }
                    if (this.operator == "contains") {
                        this.value = "%${this.value}%".toString()
                        this.operator = "like"
                    } else if (this.operator == "startswith") {
                        this.value = "${this.value}%".toString()
                        this.operator = "like"
                    } else if (this.operator == "endswith") {
                        this.value = "%${this.value}".toString()
                        this.operator = "like"
                    } else if (this.operator == "same") {
                        if (!this.objectForSameOperator) {
                            throw new Exception("QueryBuilderVehicle with 'SAME' operator has no 'objectForSameOperator'. ${json}")
                        }
                        this.value = getValueForSameOperatorEq()
                        if (this.value != null) {
                            this.operator = this.value instanceof List ? "IN" : "="
                        } else {
                            this.operator = "IS NULL"
                        }
                    }
                    if (this.type == "date" && this.operator == "between") {
                        try {
                            Date date = new Date().parse(DATE_FORMAT, this.value.get(1)) + 1
                            this.value.set(1, date.format(DATE_FORMAT))
                        } catch (Exception ex) {
                        }
                    }
                } else {
                    println "WARNING!!!! : new operator found : " + o
                    this.operator = o
                }
            }
            if (this.id && this.condition) {
                throw new Exception("WARNING!!!! : id and condition found : " + json.toString())
            }
        }
    }

    void idEq(Long id) {
        idEqCondition = id
    }

    void idNe(Long id) {
        idNeCondition = id
    }

    void deletedEq(boolean deleted) {
        deletedEqCondition = deleted
    }

    void validateEq(boolean validUpdated) {
        validateEqCondition = validUpdated
    }

    void typeIn(List<Vehicle.Type> types) {
        typeInCondition = types
    }

    void listingStatusIn(List<Vehicle.ListingStatus> listingStatuses) {
        listingStatusInCondition = listingStatuses
    }

    @Override
    List<Long> getIdsList(int offset, int max, String orderCol, String orderDir) {
        QueryBuilderVehicleSqlQueryBuilder query = this.generateSqlCode(Projection.IDS, offset, max, orderCol, orderDir)
        List<Long> ids = sql.rows(query.getSqlCode(), query.getParams()).collect { it.id as Long }
        return ids
    }

    // Groovy grails code, fix this to make getAll method recognised
    @Override
    List<Object> getList(int offset, int max, String orderCol, String orderDir) {
        return filterType.aClass.getAll(getIdsList(offset, max, orderCol, orderDir)) // check order
    }

    @Override
    List<Long> getIdsList() {
        return this.getIdsList(0, -1, null, null)
    }

    @Override
    int getCount() {
        QueryBuilderVehicleSqlQueryBuilder query = this.generateSqlCode(Projection.COUNT, 0, -1, null, null)
        Map result
        if (query.getParams()) {
            result = sql.firstRow(query.getSqlCode(), query.getParams())
        } else {
            result = sql.firstRow(query.getSqlCode())
        }
        if (result) {
            return result.count
        }
        return 0
    }

    @Override
    String getDebugQueryString() {
        return generateSqlCode(QueryBuilderVehicle.Projection.COUNT, 0,-1, null, null)
    }

    QueryBuilderVehicleSqlQueryBuilder generateSqlCode(Projection projection, int offset, int max, String orderCol, String orderDir) {
        return generateSqlCode(projection, offset, max, orderCol, orderDir, new QueryBuilderVehicleSqlQueryBuilder(vehicleTableAlias))
    }

    QueryBuilderVehicleSqlQueryBuilder generateSqlCode(Projection projection, int offset, int max, String orderCol, String orderDir, QueryBuilderVehicleSqlQueryBuilder sqlQuery) {
        String projectionCode = ""
        switch (projection) {
            case Projection.IDS:
                projectionCode = "id"
                break
            case Projection.COUNT:
                projectionCode = "COUNT(id) AS 'count'"
                break
        }
        sqlQuery.appendLine("SELECT ${projectionCode}")
        sqlQuery.appendLine("FROM vehicle ${vehicleTableAlias}")
        sqlQuery.appendLine("WHERE ")
        appendWhereClause(sqlQuery)
        appendAdditionnalConditions(sqlQuery)
        sqlQuery.appendLine(sqlOrderBy(orderCol, orderDir))
        if (max >= 0) {
            sqlQuery.appendLine("LIMIT ${max} OFFSET ${offset}")
        }
        return sqlQuery
    }

    private String sqlOrderBy(String orderCol, String orderDir) {
        if (!orderCol || !orderDir) return ""
        return "ORDER BY ${VehicleFormulaExpressionUtil.camelCaseToSnakeCase(orderCol)} ${orderDir.toUpperCase()}\n"
    }

    private QueryBuilderVehicleSqlQueryBuilder appendWhereClause(QueryBuilderVehicleSqlQueryBuilder query) {
        if (this.condition) {
            appendConditionClause(query)
        } else if (this.id) {
            appendIdClause(query)
        }
        return query
    }

    private QueryBuilderVehicleSqlQueryBuilder appendAdditionnalConditions(QueryBuilderVehicleSqlQueryBuilder query) {
        if (idEqCondition) {
            query.appendLine("AND")
            appendClause(query, "id", "=", false, idEqCondition.toString())
        }
        if (idNeCondition) {
            query.appendLine("AND")
            appendClause(query, "id", "!=", false, idNeCondition.toString())
        }
        if (deletedEqCondition != null) {
            query.appendLine("AND")
            appendClause(query, "deleted", "=", false, deletedEqCondition.toString())
        }
        if (validateEqCondition != null) {
            query.appendLine("AND")
            appendClause(query, "validUpdated", "=", false, validateEqCondition.toString())
        }
        if (typeInCondition != null) {
            query.appendLine("AND")
            appendClause(query, "type", "IN", false, "(" + typeInCondition.collect { "'${it.name()}'" }.join(", ") + ")")
        }
        if (listingStatusInCondition != null) {
            query.appendLine("AND")
            appendClause(query, "listingStatus", "IN", false, "(" + listingStatusInCondition.collect { "'${it.name()}'" }.join(", ") + ")")
        }
    }

    private void appendConditionClause(QueryBuilderVehicleSqlQueryBuilder query) {
        query.appendLine("(")
        query.indent()
        if (this.filters) {
            this.filters.sort { it.id }

            this.filters.eachWithIndex { f, index ->
                f.appendWhereClause(query)
                if (index < this.filters.size() - 1) {
                    query.appendLine(this.condition)
                }
            }
            query.unindent()
        } else {
            query.appendLine("true")
        }
        query.appendLine(")")
    }

    private void appendIdClause(QueryBuilderVehicleSqlQueryBuilder query) {
        List split = this.field.split("[.]")
        String prop = split.last()

        if (this.id == "id") {
            appendFilterByIdClause(query)
        } else if (!needsValue()) {
            appendClause(query, prop, this.operator, this.addNotOperator)
        } else {
            appendTypeSpecificClause(query, prop)
        }
    }

    private boolean needsValue() {
        return (this.operator != "IS NULL" && this.operator != "IS NOT NULL")
    }

    private boolean isObjectType() {
        return (this.type == "object" || this.type == "hasmanyobject")
    }

    private void appendFilterByIdClause(QueryBuilderVehicleSqlQueryBuilder query) {
        Filter filter = Filter.get(Long.valueOf(this.value))
        QueryBuilderVehicleSqlQueryBuilder subQuery = (filter.getQueryBuilder(sql) as QueryBuilderVehicle).generateSqlCode(Projection.IDS, 0, -1, null, null) // TODO: Ne fonctionnera pas vu qui vas avoir 2 vehicle AS v dans le SQL?
        query.params.putAll(subQuery.params)
        appendClause(query, "id", "IN", addNotOperator, "(${subQuery.getSqlCode()})")
    }

    private void appendClause(QueryBuilderVehicleSqlQueryBuilder query, String prop, String operator, boolean notOperator, String value = "") {
        String snakeCaseProp = VehicleFormulaExpressionUtil.camelCaseToSnakeCase(prop)
        if (isObjectType()) {
            snakeCaseProp = snakeCaseProp + "_id"
        }
        String notClause = notOperator ? "NOT (" : ""
        String notClauseEnd = notOperator ? ")" : ""

        if (usesInheritanceValues()) {
            query.appendLine("${notClause}${VehicleFormulaExpressionUtil.buildSqlInheritanceValueExpression(vehicleTableAlias, prop, snakeCaseProp)} ${operator} ${value}${notClauseEnd}")
        } else {
            query.appendLine("${notClause}${vehicleTableAlias}.${snakeCaseProp} ${operator} ${value}${notClauseEnd}\n")
        }
    }

    private boolean usesInheritanceValues() {
        return filterType != Filter.Type.VEHICLE_INHERITANCE
    }

    private void appendTypeSpecificClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        if (prop == "interests") {
            appendInterestsClause(query)
            return
        }
        switch (this.type) {
            case "long":
            case "integer":
            case "double":
                appendNumericClause(query, prop)
                break
            case "date":
                appendDateClause(query, prop)
                break
            case "enum":
                appendEnumClause(query, prop)
                break
            case "object":
            case "hasmanyobject":
                appendObjectClause(query, prop)
                break
            case "string":
                appendStringClause(query, prop)
                break
            case "boolean":
                appendBooleanClause(query, prop)
                break
            default:
                throw new Exception("WARNING!!! Unknown type for SQL generation")
        }
    }

    private void appendNumericClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        String paramNames = (this.value instanceof List) ? this.value.collect { query.addParam(prop, it) }.join(" AND ") : query.addParam(prop, this.value)
        appendClause(query, prop, this.operator, this.addNotOperator, paramNames)
    }

    private void appendDateClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        List<String> paramsNames = this.value.collect { String value ->
            query.addParam(prop, value.startsWith("new Date() - ") ? new Date() - Integer.valueOf(value.substring(13)) : Date.parse(DATE_FORMAT, value))
        }
        appendClause(query, prop, this.operator, this.addNotOperator, paramsNames.join(" AND "))
    }

    private void appendEnumClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        String paramNames = (this.value instanceof List) ? this.value.collect { query.addParam(prop, it) }.join(", ") : query.addParam(prop, this.value)
        appendClause(query, prop, this.operator, this.addNotOperator, "($paramNames)")
    }

    private void appendObjectClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        List<Long> values = this.value.collect { it as Long }
        appendClause(query, prop, this.operator, this.addNotOperator, "(" + values.join(",") + ")")
    }

    private void appendInterestsClause(QueryBuilderVehicleSqlQueryBuilder query) {
        List<Long> values = this.value.collect { it as Long }

        String notClause = this.addNotOperator ? "NOT (" : ""
        String notClauseEnd = this.addNotOperator ? ")" : ""

        query.appendLine("""
${notClause}
EXISTS (
    SELECT 1 
    FROM vehicle_client_interest 
    WHERE 
        vehicle_client_interest.vehicle_interests_id = IF(
            ${usesInheritanceValues()} && ${VehicleFormulaExpressionUtil.buildSqlHasInheritanceOnField(vehicleTableAlias, "interests")},
            ${vehicleTableAlias}.root_vehicle_id,
            ${vehicleTableAlias}.id
        ) AND
        vehicle_client_interest.client_interest_id IN (${values.join(",")})
)
${notClauseEnd}\n""")
    }

    private void appendStringClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        String params
        if (this.value instanceof JSONArray) {
            params = "(" + this.value.collect { query.addParam(prop, it) }.join(",") + ")"
        } else {
            params = query.addParam(prop, this.value)
        }
        appendClause(query, prop, this.operator, this.addNotOperator, params)
    }

    private void appendBooleanClause(QueryBuilderVehicleSqlQueryBuilder query, String prop) {
        Boolean b = Boolean.valueOf(this.value)
        appendClause(query, prop, this.operator, this.addNotOperator, query.addParam(prop, b))
    }

}
