package traction.pdf

class DefaultSalesPosterPdf {

    final static Map mapping = [
            "Marque": "\${productMake}",
            "Modèle": "\${productModel}",
            "Année": "\${productYear}",
            "Options": "\${String.format('%.2f', productOptionsPrice)}",
            "Options Texte": "\${productOptions ? '• ' + productOptions.replace(',','\\\\n• ') : ''}",
            "Prix Par Semaine": "\${productPricePerWeek ? productPricePerWeek.round() + '\$/sem' : ''}",
            "Stock": "\${productStockNumber}",
            "Numéro de Série": "\${productSerialNumber}",
            "Kilomètres": "\${productKilometers}",
            "productName": "\${productMake} \${productModel} \${productYear}",
            "productSpec": "<% if(product.specifications.size() > 0) { product.specifications.sort{it.id}.each { %>\${'•' + it.name + ': ' + it.value + '\\n'}<% } } %>",
            "productSpecial": "\${Math.round(productPriceSpecial)}",
            "Description": "\${productDescription ? productDescription.replaceAll('<br>','\\r\\n').replaceAll('<br/>',' ').replaceAll('<p>','\\r\\n').replaceAll('</h.?>','\\r\\n').replaceAll('</.{0,6}>','').replaceAll(' ',' ').size() >= 750 ? productDescription.replaceAll('<br>','\\r\\n').replaceAll('<br/>',' ').replaceAll('<p>','\\r\\n').replaceAll('</h.?>','\\r\\n').replaceAll('</.{0,6}>','').replaceAll(' ',' ').substring(0, 750) + '...' : productDescription.replaceAll('<br>','\\r\\n').replaceAll('<br/>',' ').replaceAll('<p>','\\r\\n').replaceAll('</h.?>','\\r\\n').replaceAll('</.{0,6}>','').replaceAll(' ',' ') : ''}",
            "NbSemaine" : "\${'Sur ' + productTermWeek + ' semaines. Taux de financement: ' + productRate + '%'}",
            "Taux" : "\${productRate}",
            "PromoDate": "\${productPromoValidity ? productPromoValidity.format(\"yyyy-MM-dd\") : ''}",
    ]

    // Check out /assets/images/pdf/*.pdf for pdf files
    // Activated or not in "DEFAULT_SALES_POSTER_PDF" config
    final static String sales_poster_small_regular = "sales_poster_small_regular"
    final static String sales_poster_small_regular_by_week = "sales_poster_small_regular_by_week"
    final static String sales_poster_small_promo = "sales_poster_small_promo"
    final static String sales_poster_small_promo_by_week = "sales_poster_small_promo_by_week"
    final static String sales_poster_big_regular = "sales_poster_big_regular"
    final static String sales_poster_big_regular_by_week = "sales_poster_big_regular_by_week"
    final static String sales_poster_big_promo = "sales_poster_big_promo"
    final static String sales_poster_big_promo_by_week = "sales_poster_big_promo_by_week"
    final static String sales_poster_big_regular_options = "sales_poster_big_regular_options"
    final static String sales_poster_big_regular_options_by_week = "sales_poster_big_regular_options_by_week"
    final static String sales_poster_big_promo_options = "sales_poster_big_promo_options"
    final static String sales_poster_big_promo_options_by_week = "sales_poster_big_promo_options_by_week"

    // See configService.getDefaultSalesPosterPdfActive for only active pdfs
    static List<String> getAll() {
        return [
                sales_poster_small_regular,
                sales_poster_small_regular_by_week,
                sales_poster_small_promo,
                sales_poster_small_promo_by_week,
                sales_poster_big_regular,
                sales_poster_big_regular_by_week,
                sales_poster_big_promo,
                sales_poster_big_promo_by_week,
                sales_poster_big_regular_options,
                sales_poster_big_regular_options_by_week,
                sales_poster_big_promo_options,
                sales_poster_big_promo_options_by_week
        ]
    }
}
