package traction.file

import grails.util.Holders
import groovy.transform.AutoClone
import product.ManagerProductConfig
import traction.ConfigService
import traction.MailTemplate
import traction.category.CategoryFile
import traction.client.Client
import traction.communication.CallNotes
import traction.communication.Communication
import traction.communication.VideoRoom
import traction.form.FormProfile
import traction.history.HistoryAuditable
import traction.history.HistoryExcludedField
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityOption
import traction.security.User
import traction.service.WorkOrderJob
import traction.status.StatusFile
import traction.stripe.StripeSession
import traction.task.Task
import traction.vehicle.Vehicle
import traction.vehicle.VehicleInspectionItem
import traction.vehicle.VehicleWatermark
import traction.vehicle.inheritance.InheritedVehiclePicture
import traction.workflow.WorkflowBoard

import java.security.MessageDigest

@AutoClone
class FileData implements HistoryAuditable {

    static constraints = {
        description size: 0..16777215
        author nullable: true
        client nullable: true
        communication nullable: true
        workflowBoard nullable: true
        cnf nullable: true
        task nullable: true
        callNotes nullable: true
        videoRoom nullable: true
        profile nullable: true
        opportunity nullable: true
        opportunityOption nullable: true
        clientWork<PERSON>rderJob nullable: true
        stripeSession nullable: true
        mailTemplate nullable: true
        draftId nullable: true
        vehicle nullable: true
        vehicleWatermark nullable: true
        vehicleInspectionItem nullable: true
    }

    static mapping = {
        version false
        date index: 'date_Idx'
        category index: 'category_Idx'
        status index: 'status_Idx'
        pixelguru sqlType: "int", length: 1
        client cascade: 'none'
        communication cascade: 'none'
        profile cascade: 'none'
        opportunity cascade: 'none'
        mailTemplate cascade: 'none'
        opportunityOption cascade: 'none'
        workflowBoard cascade: 'none'
        clientWorkOrderJob cascade: 'none'
        task cascade: 'none'
        callNotes cascade: 'none'
        videoRoom cascade: 'none'
        vehicleWatermark cascade: 'none', unique: true
        cnf cascade: 'none'
        stripeSession cascade: 'none'
        vehicle cascade: 'none'
        vehicleInspectionItem cascade: 'none'
    }

    static belongsTo = [
            client            : Client,
            communication     : Communication,
            mailTemplate      : MailTemplate,
            stripeSession     : StripeSession,
            profile           : FormProfile,
            opportunity       : Opportunity,
            opportunityOption : OpportunityOption,
            workflowBoard     : WorkflowBoard,
            clientWorkOrderJob: WorkOrderJob,
            task              : Task,
            callNotes         : CallNotes,
            videoRoom         : VideoRoom
    ]

    @HistoryExcludedField
    Date date = new Date()
    String name = ""
    /*** In bytes */
    @HistoryExcludedField
    int dataSize = 0
    /*** In pixels */
    @HistoryExcludedField
    int height = 0
    /*** In pixels */
    @HistoryExcludedField
    int width = 0
    @HistoryExcludedField
    int angdeg = 0
    @HistoryExcludedField
    int ordre = 0
    @HistoryExcludedField
    boolean pixelguru = false
    User author
    @HistoryExcludedField
    String draftId
    @HistoryExcludedField
    String path = ""
    @HistoryExcludedField
    String previewPath = ""
    String description = ""
    // Todo change the name for contentType?
    @HistoryExcludedField
    String status = StatusFile.NOFORMAT.message
    String category = CategoryFile.SYSTEM.message
    ManagerProductConfig cnf
    VehicleWatermark vehicleWatermark
    Vehicle vehicle
    VehicleInspectionItem vehicleInspectionItem

    String getIcon() {
        return StatusFile.getIcon(this.status)
    }

    boolean isDangerous() {
        return StatusFile.DANGEROUS.message.equals(this.status)
    }

    @Override
    boolean preventHistories() {
        return draftId == InheritedVehiclePicture.VEHICLE_FIRST_PICTURE_DRAFT_ID || draftId == InheritedVehiclePicture.VEHICLE_FLOOR_PLAN_PICTURE_DRAFT_ID || category == CategoryFile.VEHICLE_OPTION_PICTURE.message
    }

    String toString() {
        return "File: ${this.id}: ${this.name}: ${this.path}"
    }

    BlobData asBlobData() {
        return this instanceof BlobData ? this : null
    }

    String getFormattedDataSize() {
        if (dataSize < 0) {
            return "N/A"
        }

        String[] units = ["B", "KB", "MB", "GB", "TB"]
        int unitIndex = 0
        double size = dataSize

        while (size >= 1024 && unitIndex < units.size() - 1) {
            size /= 1024
            unitIndex++
        }

        return String.format("%.1f %s", size, units[unitIndex])
    }

    String getAccess() {
        MessageDigest md = MessageDigest.getInstance("MD5")
        byte[] messageDigest = md.digest(this.name.getBytes())
        StringBuffer hashtext = new StringBuffer()
        for (byte b : messageDigest) {
            hashtext.append(String.format("%02x", b & 0xff))
        }
        return hashtext.toString()
    }


    String getUrl() {
        ConfigService configService = (ConfigService) Holders.grailsApplication.mainContext.getBean("configService")
        return configService.get("SERVER", "server.traction.base") + "/web/getFile/" + this.id
    }


    /**
     * Return a list of properties from FileData that create a history if they change
     *
     * *** Make sure that the property is set in messages.properties (filedata.propertyName e.g. filedata.description) ***
     *
     * @return List of FileData properties
     */
    static List<String> getHistoryProperties() {
        return [
                "id",
                "date",
                "name",
                "description",
                "path",
                "status",
                "category",
                "author",
                "ordre",
                "dataSize",
                "height",
                "width",
                "angdeg",
                "pixelguru"
        ]
    }
}