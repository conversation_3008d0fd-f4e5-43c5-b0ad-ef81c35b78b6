package traction

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.springframework.context.i18n.LocaleContextHolder
import traction.category.CategoryFile
import traction.department.Department
import traction.file.FileData
import traction.goal.UserPointTarget
import traction.goal.UserSalesTarget
import traction.permissions.PermissionLevel
import traction.opportunity.OpportunityAssigned
import traction.security.Role
import traction.security.User
import traction.security.UserRole
import traction.service.ServiceResource
import traction.status.StatusBusy
import traction.status.StatusUser
import traction.render.RenderUtils
import org.springframework.web.multipart.MultipartFile

@Secured("@securityService.secured('PERM_USER_ADMIN')")
class UserController {

    def userService
    def fileService
    def springSecurityService
    def configService
    def userGoalService
    def userPointService
    def departmentService
    def opportunityService
    def workflowDataService
    def cacheService
    def callService
    def securityService

    def index() {
        render(view: "index", model: [message: params.message, success: params.success])
    }

    def deleted() {
        render(view: "deleted")
    }

    def userTable() {
        boolean deleted = params.boolean("deleted")
        List<User> users = deleted ? userService.getAllDeleted() : userService.getAll()
        render(view: "userTable", model: [users: users, deleted: deleted])
    }

    /**
     * Edit User.
     *
     * @param id Id of the user.
     */
    def edit() {
        User user
        if (params.id) {
            user = userService.get(params.id as long)
        }
        boolean allowMultipleDepartment = configService.get("CONFIG", "department.allowmultiple").equals("1")

        // TODO rework view system sans profiles
        def profilesViews = [:]
        def productProfilesViews

        if (productProfilesViews) {
            productProfilesViews.each { pro ->
                List<String> views
                profilesViews.put(pro, views)
            }
        }

        boolean enableServiceRoles = configService.get("SERVICE", "enable.service.roles") == "1"
        render(view: "edit", model: [
                enableServiceRoles     : enableServiceRoles,
                profilesViews          : profilesViews,
                user                   : user,
                randomColor            : RandomColorUtils.getRandomColor(),
                genders                : StatusUser.getAll(),
                groups                 : UserGroup.values(),
                departments            : Department.getAll(),
                allowMultipleDepartment: allowMultipleDepartment
        ])
    }

    @Secured('ROLE_ADMIN')
    def userGroupModal() {
        User user = User.get(params.long("userId", 0))
        UserGroup oldUserGroup = UserGroup.getById(params.int("oldUserGroup",0))
        UserGroup newUserGroup = UserGroup.getById(params.int("newUserGroup",0))
        Set<Long> oppIdList = OpportunityAssigned.createCriteria().list {
            eq("user", user)
            projections {
                property("opportunity.id")
            }
        }
        Long conflictCount = OpportunityAssigned.createCriteria().get {
            createAlias("user", "_user")
            'in'('opportunity.id', oppIdList)
            eq('_user.userGroup', newUserGroup)
            projections {
                countDistinct("opportunity.id")
            }
        }
        render(template: "modalUserGroup",
                model: [
                        oppCount     : oppIdList.size(),
                        conflictCount: conflictCount,
                        oldUserGroup : oldUserGroup,
                        newUserGroup : newUserGroup
                ])
    }

    @Secured('ROLE_USER')
    def getAllUserStatus() {
        Map data = [:]
        List list = User.createCriteria().list {
            projections {
                property("id")
                property("status")
            }
        }
        list.each {
            StatusBusy statusBusy = ((StatusBusy) it[1])
            data.put(it[0], [icon: '<span data-toggle="tooltip" title="' + g.message(code: statusBusy.message) + '">' + statusBusy.html() + '</span>', bgClass: statusBusy.bgClass])
        }
        render data as JSON
    }

    def save() {
        log.debug "UserController.save ${params}"
        User user = new User()
        if (params.id) {
            user = userService.get(params.id as long)
        }
        if (params.username) {
            user.username = params.username
        }
        if (params.motdepasse) {
            user.password = params.motdepasse
        }
        user.firstname = params.firstname ? params.firstname : ""
        user.lastname = params.lastname ? params.lastname : ""
        user.email = params.email ? params.email : ""
        user.color = params.color ? "#" + params.color : null
        if (params.authCode) {
            user.authCode = params.authCode
        }

        user.permissionLevel = PermissionLevel.valueOf(params.permissionLevel)

        List<Long> departmentIds = []
        try {
            departmentIds.add(Long.valueOf(params.department))
        }
        catch (Exception e) {
            params.department.each {
                departmentIds.add(Long.valueOf(it))
            }
        }
        Department depTemp
        Department.getAll().each { department ->
            if (departmentIds.contains(department.id)) {
                if (departmentIds.first()) {
                    log.debug("DEPARMENT....${departmentIds.first()}")
                    depTemp = department
                }
                user.addToDepartments(department)
            } else {
                user.removeFromDepartments(department)
            }
        }

        user.enabled = params.enabled ? true : false
        user.allManagerProductProfiles = params.allManagerProductProfiles ? true : false
        user.allManagerProductView = params.allManagerProductView ? true : false
        user.allManagerProductFilter = params.allManagerProductFilter ? true : false
        user.notificationDepartmentService = params.notificationDepartmentService ? true : false
        user.notificationService = params.notificationService ? true : false
        user.notifications = params.notifications ? true : false
        user.visibleInSoldboard = params.visibleInSoldboard ? true : false
        user.signSmsWithName = params.signSmsWithName ? true : false
        user.allPrivateCalls = params.allPrivateCalls ? true : false
        user.allPrivateCommunications = params.allPrivateCommunications ? true : false

        UserGroup oldUserGroup = user.userGroup
        user.userGroup = null
        UserGroup.values().each { userGroup ->
            if (params[userGroup.message]) {
                user.userGroup = userGroup
            }
        }
        boolean userGroupChanged = (oldUserGroup != user.userGroup)
        user.defaultFromEmail = params.defaultFromEmail ?: null

        if (!user.userGroup && user.id) {
            User userToReplace
            if (params.userToReplace) {
                userToReplace = User.get(params.userToReplace as long)
                if (userToReplace?.userGroup) {
                    opportunityService.replaceUserInAssignations(user, userToReplace)
                }
            }
        }

        log.debug "user.currentBusinessUnit : " + user.currentBusinessUnit

        def ret = [success: "error", message: "Error."]
        log.debug "Business Unit params: ${params.businessUnit}"
        List<Long> buIds = []
        try {
            buIds.add(Long.valueOf(params.businessUnit))
        }
        catch (Exception e) {
            params.businessUnit.each {
                log.debug "ITL ${it}"
                buIds.add(Long.valueOf(it))
            }
        }
        log.debug "buIds: ${buIds}"

        BusinessUnit.getAll().each { BusinessUnit bu ->
            log.debug "BU2: ${bu}"
            if (buIds.contains(bu.id)) {
                user.addToBusinessUnits(bu)
            } else {
                user.removeFromBusinessUnits(bu)
            }
        }

        if (user.businessUnits) {
            if (user.departments) {
                if (!user.currentBusinessUnit) {
                    user.currentBusinessUnit = user.businessUnits.first()
                }
                if (!user.departments.any { it == user.currentDepartment }) {
                    user.currentDepartment = user.departments.first()
                }

                User sameUsername = User.findByUsername(user.username)
                User sameDefaultEmail = user.defaultFromEmail ? User.findByDefaultFromEmail(user.defaultFromEmail) : null

                if (sameUsername && sameUsername.id != user.id) {
                    ret = [success: "error", message: g.message(code: "user.saveError.username.exists")]
                } else if (sameDefaultEmail && sameDefaultEmail.id != user.id) {
                    ret = [success: "error", message: g.message(code: "user.saveError.email.exists")]
                } else {
                    // Gérer l'upload d'image AVANT la sauvegarde principale
                    if (params.file_data) {
                        MultipartFile file = null

                        // Handle both single file and multiple files (when form has multiple inputs with same name)
                        if (params.file_data.getClass() == ArrayList) {
                            // Multiple files - take the first non-empty one
                            List<MultipartFile> files = params.file_data as List<MultipartFile>
                            file = files.find { it && !it.isEmpty() }
                        } else {
                            // Single file
                            file = params.file_data as MultipartFile
                        }

                        if (file && !file.isEmpty()) {
                            log.debug "Processing user image upload: ${file.originalFilename}"
                            // Supprimer l'ancienne image
                            if (user.image) {
                                FileData oldImage = user.image
                                user.image = null
                                fileService.delete(oldImage)
                            }
                            // Ajouter la nouvelle image avec la même logique que ClientController
                            user.image = fileService.addFile([
                                    user            : user,
                                    filecontent     : file,
                                    storageType     : "BlobData",
                                    filecontenttype : file.getContentType(),
                                    filecontentname : file.getOriginalFilename(),
                                    filecontentbytes: file.getBytes(),
                                    category        : CategoryFile.USER.message
                            ])
                            log.debug "User image uploaded: ${user.image?.id}"
                        }
                    }

                    boolean doSave = true
                    if (doSave) {
                        ret = userService.save(user)
                    }
                }
            } else {
                ret = [success: "error", message: g.message(code: "user.save.error.department")]
            }
        } else {
            ret = [success: "error", message: g.message(code: "user.save.error.businessunit")]
        }

        log.debug "user:departments:" + user.departments
        if (ret.success != "error") {
            Department.getAll().each { // To place users in great DepartmentGroup
                departmentService.save(it)
            }
            if (params.company) {
                userService.setUserAttribute(user, "company", params.company)
            } else {
                userService.removeUserAttribute(user, "company")
            }
            if (params.phone) {
                userService.setUserAttribute(user, "phone", params.phone)
            } else {
                userService.removeUserAttribute(user, "phone")
            }
            if (params.cell) {
                userService.setUserAttribute(user, "cell", params.cell)
            } else {
                userService.removeUserAttribute(user, "cell")
            }
            if (params.country) {
                userService.setUserAttribute(user, "country", params.country)
            } else {
                userService.removeUserAttribute(user, "country")
            }
            if (params.region) {
                userService.setUserAttribute(user, "region", params.region)
            } else {
                userService.removeUserAttribute(user, "region")
            }
            if (params.province) {
                userService.setUserAttribute(user, "province", params.province)
            } else {
                userService.removeUserAttribute(user, "province")
            }
            if (params.address) {
                userService.setUserAttribute(user, "address", params.address)
            } else {
                userService.removeUserAttribute(user, "address")
            }
            if (params.birthday) {
                userService.setUserAttribute(user, "birthday", params.birthday)
            } else {
                userService.removeUserAttribute(user, "birthday")
            }
            if (params.hireDate) {
                userService.setUserAttribute(user, "hireDate", params.hireDate)
            } else {
                userService.removeUserAttribute(user, "hireDate")
            }
            if (params.zipCode) {
                userService.setUserAttribute(user, "zipCode", params.zipCode)
            } else {
                userService.removeUserAttribute(user, "zipCode")
            }
            if (params.title) {
                userService.setUserAttribute(user, "title", params.title)
            } else {
                userService.removeUserAttribute(user, "title")
            }
            if (params.gender) {
                userService.setUserAttribute(user, "gender", params.gender)
            }
            if (params.language) {
                userService.setUserAttribute(user, "language", params.language)
            }

            if (userGroupChanged) {
                userService.cleanUserAssigned(user)
            }


            def roles = Role.findAll()
            Role dev = Role.findByAuthority('ROLE_DEV')
            roles.each() { role ->
                if (params[role.authority]) {
                    log.debug "User got Role: ${role.authority}"

                    if (!UserRole.exists(user.id, role.id)) {
                        log.debug "adding ${user} to role ${role}"
                        UserRole.withTransaction {
                            UserRole.create(user, role, true)
                        }
                    }
                } else {
                    if (UserRole.exists(user.id, role.id) && dev.id != role.id) {
                        log.debug "remove ${user} to role ${role}"
                        UserRole.withTransaction {
                            UserRole.remove(user, role)
                        }
                    }
                }
            }
            log.debug("Current user ${springSecurityService.getCurrentUser()}")
            log.debug("User ${user.username}")
            springSecurityService.clearCachedRequestmaps()
            if (springSecurityService.getCurrentUser().username == user.username) {
                log.debug "Re-Authenticating"
                springSecurityService.reauthenticate user.username
            }
        }
        render ret as JSON
    }

    @Secured("@securityService.secured('PERM_USER_POINT_ADMIN')")
    def modaluserpoints() {
        User user = User.get(params.long("id"))
        UserPointTarget target = userPointService.getUserPointTarget(user)
        UserSalesTarget sold = userGoalService.getUserSalesTarget(user)
        render(template: "modaluserpoints", model: [
                user     : user,
                year     : target?.year ?: 0,
                month    : target?.month ?: 0,
                day      : target?.day ?: 0,
                soldyear : sold?.year ?: 0,
                soldmonth: sold?.month ?: 0,
                soldday  : sold?.day ?: 0,
        ])
    }

    @Secured('ROLE_USER')
    def saveUserCommunicationTransfer() {
        Map ret = [success: false, message: "error.occurred"]
        User user = User.get(params.long("userId"))
        User redirectedUser = User.findByUsername(params.redirectedUserUsername)

        user.redirectedUser = redirectedUser
        if (userService.save(user))
            ret = [success: true, message: I18N.m("save.user.communication.transfer.success")]
        render ret as JSON
    }

    @Secured("@securityService.secured('PERM_USER_POINT_ADMIN')")
    def saveUserPointTarget() {
        log.debug 'saveUserPointTarget' + params
        if (params.userId && params.day && params.month && params.year && params.day.isInteger() && params.month.isInteger() && params.year.isInteger() && params.soldday && params.soldmonth && params.soldyear && params.soldday.isInteger() && params.soldmonth.isInteger() && params.soldyear.isInteger()) {
            User user = User.get(params.userId)
            if (user) {
                UserPointTarget lastTargetSet = userPointService.getUserPointTarget(user)
                if (lastTargetSet &&
                        lastTargetSet.day == params.day.toInteger() &&
                        lastTargetSet.month == params.month.toInteger() &&
                        lastTargetSet.year == params.year.toInteger()) {
                    log.debug "Target points not changed"
                } else {
                    UserPointTarget target = new UserPointTarget()
                    target.user = user
                    target.day = params.day.toInteger()
                    target.month = params.month.toInteger()
                    target.year = params.year.toInteger()
                    userPointService.saveTarget(target)
                }
                UserSalesTarget lastTargetGoalSet = userGoalService.getUserSalesTarget(user)
                if (lastTargetGoalSet &&
                        lastTargetGoalSet.day == params.soldday.toInteger() &&
                        lastTargetGoalSet.month == params.soldmonth.toInteger() &&
                        lastTargetGoalSet.year == params.soldyear.toInteger()) {
                    log.debug "Target sold not changed"
                } else {
                    UserSalesTarget target = new UserSalesTarget()
                    target.user = user
                    target.day = params.soldday.toInteger()
                    target.month = params.soldmonth.toInteger()
                    target.year = params.soldyear.toInteger()
                    userGoalService.saveTarget(target)
                }
            }
        }
        render "OK"
    }

    def delete() {
        def ret = [success: false, message: g.message(code: "user.notdeleted")]
        log.debug "delete:" + params
        if (params.id) {
            User user = User.get(Long.valueOf(params.id))
            if (user && user != springSecurityService.currentUser) {
                ServiceResource serviceResource = ServiceResource.findByUser(user)
                if (serviceResource?.deleted == false) {
                    ret.message = g.message(code: "user.notdeleted.servicescheduleerror")
                } else if (user.isInAssignmentRuleUsersField()) {
                    ret.message = g.message(code: "user.notdeleted.inAssignmentRule")
                } else if (user.isInEvent()) {
                    ret.message = g.message(code: "user.notdeleted.inEvent")
                } else if (userService.delete(user)) {
                    if (params.userToReplace) {
                        if (params.userToReplace) {
                            User userToReplace = User.get(params.userToReplace as long)
                            if (userToReplace) {
                                opportunityService.replaceUserInAssignations(user, userToReplace)
                            }
                        }
                    }
                    ret.success = true
                }
            }
        }
        render ret as JSON
    }

    def unDelete() {
        def ret = [success: false, message: g.message(code: "user.notrecovered")]
        log.debug "delete:" + params
        if (params.id) {
            User.withDeleted {
                User user = User.get(Long.valueOf(params.id))

                if (user && user != springSecurityService.currentUser) {
                    if (userService.unDelete(user)) {
                        ret.success = true
                        ret.message = g.message(code: "user.recovered")
                    } else {
                        log.debug("An error occured in unDelete")
                    }
                }
            }
        }
        render ret as JSON
    }

    /**
     * Upload user profile image - Même logique que ClientController.uploadImage()
     */
    @Secured("@securityService.secured('PERM_USER_ADMIN')")
    def uploadUserImage() {
        log.debug "uploadUserImage called with params: ${params}"

        User user = User.get(params.long("id"))
        if (!user) {
            log.debug "User not found with id: ${params.id}"
            return render(RenderUtils.error())
        }

        log.debug "Found user: ${user.username}"

        // Supprimer l'ancienne image (même logique que ClientController)
        FileData oldFileData = user.image
        if (oldFileData) {
            log.debug "Deleting old image: ${oldFileData.id}"
            user.image = null
            fileService.delete(oldFileData)
        }

        // Ajouter la nouvelle image (même logique que ClientController)
        MultipartFile file = null

        // Handle both single file and multiple files (when form has multiple inputs with same name)
        if (params.file_data) {
            if (params.file_data.getClass() == ArrayList) {
                // Multiple files - take the first non-empty one
                List<MultipartFile> files = params.file_data as List<MultipartFile>
                file = files.find { it && !it.isEmpty() }
            } else {
                // Single file
                file = params.file_data as MultipartFile
            }
        }

        if (file && !file.isEmpty()) {
            log.debug "Processing new file: ${file.originalFilename}"
            user.image = fileService.addFile([
                    user            : user,
                    filecontent     : file,
                    storageType     : "BlobData",
                    filecontenttype : file.getContentType(),
                    filecontentname : file.getOriginalFilename(),
                    filecontentbytes: file.getBytes(),
                    category        : CategoryFile.USER.message
            ])
            if (user.image) {
                log.debug "New image created with id: ${user.image.id}"
                userService.save(user)
                log.debug "User saved successfully"
            } else {
                log.error "Failed to create image file"
            }
        } else {
            log.debug "No file provided in params.file_data"
        }
        render(RenderUtils.success())
    }

    /**
     * Test method to verify the route is working
     */
    @Secured("@securityService.secured('PERM_USER_ADMIN')")
    def testUpload() {
        log.debug "testUpload called with params: ${params}"
        render([success: true, message: "Route is working", params: params] as JSON)
    }

    @Secured('ROLE_USER')
    def search() {
        log.debug "search: ${params}"
        def search = ""
        if (params.search) {
            search = params.search
        }
        List users = userService.searchUsers(search, 10)
        respond(users: users)
    }

    @Secured('ROLE_USER')
    def maintenance() {
        render(view: "maintenance")
    }

    @Secured('ROLE_USER')
    def getUserSelect() {
        log.debug "getUserSelect: " + params
        List<Department> departmentList = params.list("departments[]").collect { Department.get(it) }.findAll()
        List<UserGroup> userGroups = params.list('userGroups[]').collect {
            UserGroup.getById(Integer.valueOf(it))
        }.findAll()
        log.debug "departmentList:" + departmentList
        log.debug "userGroups:" + userGroups
        List data = User.createCriteria().list {
            eq("deleted", false)
            eq("enabled", true)
            if (departmentList) {
                departments {
                    or {
                        departmentList.each { d ->
                            eq("id", d.id)
                        }
                    }
                }
            }
            if (userGroups) {
                or {
                    userGroups.each { g ->
                        eq("userGroup", g)
                    }
                }
            }
        }.collect { [value: it.id, text: it.fullName] }
        render data as JSON
    }

    @Secured('ROLE_USER')
    def flushCache() {
        cacheService.flush("cards_${LocaleContextHolder.getLocale().getLanguage().toUpperCase()}")
        cacheService.flush("cardTask_${LocaleContextHolder.getLocale().getLanguage().toUpperCase()}")
        render ""
    }
}

