accessories.profit=Profit on accessories
according.to.vehicle.year=According to the year of the vehicle
according.to.year.calculation=According to the year of calculation
action.active=Active
action.category=Category
action.create=Create action
action.description=Description
action.edit=Edit an action
action.execute.now=Execute action now
action.execute=Execute action
action.executed=Action executed for {0}
action.executeOnAllWorkflowCardMove=Execute on all workflow card moves
action.executioncount=Execution count
action.executionDelay=Execution delay in hours
action.fallbackAction.hint=Action to execute if this action fails
action.fallbackAction=Fallback action
action.list=List of actions
action.manualTrigger.hint=In client view action log or in clients, opportunities, work order or parts invoices lists
action.manualTrigger=Manual trigger
action.multipleEntriesInterval=Interval in days to allow a second execution of the action
action.name=Name
action.not.deleted.clientprocess=Unable to delete an action used in a client process
action.not.deleted.fallback=Unable to delete an action used as a fallback action
action.not.saved.unique.name=Action name must be unique.
action.not.saved=Action not saved. Please make sure the fallback action is not recursive.
action.saved=Action saved.
action.status.blocked.tooltip=Action blocked by the minimum delay for the repetition of the same action
action.status.blocked=Blocked
action.status.canceled.tooltip=Action canceled manually
action.status.canceled=Canceled
action.status.executed.tooltip=Action executed
action.status.executed=Executed
action.status.inactive.tooltip=Action deactivated not executed
action.status.inactive=Inactive
action.status.missinginfo.tooltip=Action not executed because some information is missing
action.status.missinginfo=Missing information
action.status.pending.tooltip=Action pending execution date
action.status.pending=Pending
action.trigger.again=Trigger again
action.triggers=Triggers
action.type.calendarevent.config=Configuration of calendar event confirmation
action.type.emailtoclient.config=Configuration of email sent to client
action.type.emailtoclient=Send email to client
action.type.emailtouser.config.sendToCurrentUser=Send to current user
action.type.emailtouser.config.sendToOpportunityAssignedUsers=Send to users assigned to the opportunity
action.type.emailtouser.config.sendToUserIds=Send to users
action.type.emailtouser.config.sendToWorkflowBoardSubscribedUsers=Send to users subscribed to the workflow board
action.type.emailtouser.config.sendToWorkflowDataAssignedUsers=Send to users assigned to workflow data
action.type.emailtouser.config.sendToWorkflowDataSubscribedUsers=Send to users subscribed to the workflow data
action.type.emailtouser.config=Configuration of email sent to user
action.type.emailtouser=Send email to user
action.type.smstoclient.config=Configuration of sms sent to client
action.type.smstoclient=Send sms to client
action.type.task.config=Configuration of the created task
action.type.task=Creation of a task
action.type=Type
action.workflow.card.move=Card move
actionlog.cancel=Cancel
actionlog.executionDate=Execution date
actionlog.last=Last action
actionlog.obj=Object
actionlog.onlyThisOpportunity=Display only the actions of the current opportunity
actionlog.status=Status
actionlog=Action log
actions=Actions
active.calls=Active Calls
active.filters=Active filters
activix=Activix
add.assignation.status=Add assignment status
add.confirm.product=Product stock number
add.confirm=Please type :
add.group=Create a group
add.line.state=Add the state of the line
add.option.default.categorykijiji=Add default categories
add.option.default.categorytrader=Add default categories
add.option.default.magentodata_visibility=Add default magentodata_visibility categories
add.option.distinct.category=Add distinct category from inventory
add.option.distinct.make=Add distinct make from inventory
add.option=Add an option
add.salesmanager.action.error=Please make sure that you select a sales manager and that the opportunity has an assigned seller
add.skill.level=Add skill level
add.skill=Add skill
add.takeover.role.error=Only a sales manager can add a take over
add.variable.to.formula=Add a variable to your formula
add=Add
address.city=City
address.country=Country
address.line1=Address line 1
address.line2=Address line 2
address.postalCode=Postal code
address.state=State/Province
advanced.filters=Advanced filters
ago=ago
all.communication=Communications
all.departments=Departments (all)
all.remarks=All remarks
all.task=Tasks
all=All
alldates=All dates
amount=Amount
and=And
apply=Apply
appointment.count=Number of appointments
appointment.visitlog=Visit associated with this meeting
april=April
arctic_cat=Arctic Cat
are.you.sure=Are you sure ?
ask.payment=Request a payment
assign.tech=Assign technician
assigned.to.you=Assigned to you
assigned=Assignments
assignmentrule.active=Assignment rule is enabled
assignmentrule.cannot.edit.default=The default assignment rule cannot be modified
assignmentrule.condition.clientInterests=Client interest
assignmentrule.condition.isUsed=Used
assignmentrule.condition.makes=Make
assignmentrule.condition.origins=Source
assignmentrule.condition.time=Current time
assignmentrule.condition.weekdays=Current weekday
assignmentrule.create=Create an assignment rule
assignmentrule.defaultuser=Default user
assignmentrule.deleted=Assignment rule deleted
assignmentrule.edit=Edit an assignment rule
assignmentrule.enabled=Enabled
assignmentrule.method=Method
assignmentrule.nodefaultuser=No default user as been assigned
assignmentrule.notdeleted=Unable to delete this assignment rule
assignmentrule.notsaved=Assignment rule unable to be saved
assignmentrule.opportunity.notfound=Opportunity not found
assignmentrule.priority=Priority
assignmentrule.rules=Assignment rules
assignmentrule.saved=Assignment rule saved
assignmentrule.success=Default user saved
assignmentrule.test.findFirstMatchingRule=Find the first matching assignment rule for the opportunity
assignmentrule.test.resultrules=This assignment rule corresponds to:\nPriority: {0}
assignmentrule.test.userdisplay=This user corresponds to: {0}
assignmentrule.test=Test assignment rules
assignmentrule.users.empty=Unselected users 
assignmentrule.users=Users
assignmentrule=Assignment rule
associated.origin=Associated source
august=August
auth.confirmpassword=Confirm password
auth.forgotpassword=Forgot password
auth.forgotyourpassword=Forgot your password ?
auth.login=Login
auth.logout=Logout
auth.reset.success=Your password has been reset successfully !
auth.reset=Reset
auth.resetmypassword=Reset my password
auth.sendmail=Send email
auth.signin=Sign in
avg=Average
back.step=Previous step
bank.bmo=Montreal bank
bank.cibc=CIBC bank
bank.deleted=Bank deleted
bank.desjardins=Desjardins bank
bank.hsbc=HSBC bank
bank.yamaha=Yamaha finance
bank.lmg=LMG
bank.santander=Santander bank
bank.prefera=Prefera bank
bank.lendcare=Lendcare financial
bank.laurentian=Laurentian bank
bank.manulife=Manulife bank
bank.national=National bank
bank.royal=Royal bank
bank.saved=Bank saved
bank.scotia=Scotia Bank
bank.td=TD Bank
bank.vehicle.type=Compatible vehicle type
bank=Bank
basic.filters=Basic filters
best=best
bookmarks=Bookmarks
brp.copyright=\u00a9 2024 BRP.
brp=BRP
Buildrule.error=Unable to save these compilation rules...
Buildrule.success=Compilation rules saved !
bulk.question=What action would you like to carry out?
bump.in.process=In process
bump.to.validate=Waiting for validation
bump.valid=Valid
bump.validate=Validate the BUMP value
bump.validated=Validated BUMP value
bump.value=BUMP value
bump=BUMP
bumpandto.report=BUMP and TO report
button=Button
by.default=By default
by= by
cadealer.sync.status=Ca Dealer file synchronization
cadealer=Ca Dealer
calculationmethod.association=Calculate by associtation (AND)
calculationmethod.volume=Calculate by volume (OR)
calendar.dayGridDay=Day (list)
calendar.dayGridMonth=Month
calendar.dayGridWeek=Week (list)
calendar.google=Google
calendar.note.edit=Edit the calendar note
calendar.online=Online
calendar.other=Other calendar
calendar.outlook=Outlook
calendar.timeGridDay=Day (grid)
calendar.timeGridWeek=Week (grid)
calendar.view=Calendar View
calendar.yahoo=Yahoo
calendar=Calendar
calendarevent.add.to.your.calendar=Add to your calendar
calendarevent.confirmation.thanks=Thank you for your appointment confirmation. Contact us for any changes to your appointment. Don't forget to add the event to your calendar so you don't forget!
calendarevent.confirmation.workflowData=Confirmation of the {0} appointment for {1}.
calendarevent.confirmation=Appointment confirmation
calendarevent.status.accepted.label=Accept
calendarevent.status.accepted=Accepted
calendarevent.status.confirmationsent=Confirmation sent
calendarevent.status.missingcontactinfo=Missing contact information
calendarevent.status.new=New
calendarevent.status.refused.label=Refused
calendarevent.status.refused=Refused
calendarevent.status=Status
calendarevent.type.workflowdatadateend=End date
calendarevent.type.workflowdatadatestart=Start date
calendarevent.workflowdata.dateend=Workflow data end date
calendarevent.workflowdata.datestart=Workflow data start date
calendarevent.your.answer=Your answer
calendarevent=Calendar event
calendareventconfig.attendees=Attendees
calendareventconfig.description=Description
calendareventconfig.location=Location
calendareventconfig.title=Title
calendarEventDeleteConfig=Action configuration for deleting the calendar event
calendarEventModifyConfig=Action configuration for subsequent calendar event changes
call.completemanually=Are you sure you want to complete this call manually? This operation could affect statistics if the call is still in progress.
call.created=Created
call.delai=Delay
call.duration=Duration
call.event.duration=Duration
call.event.ext=EXT
call.event.type=Type
call.events=Call events
call.inbound=Inbound call of {0}
call.listen.not.allowed= Request access
call.listen.not.allowed.subtitle=You are not authorized to listen to this call.
call.loadRecording=Load recording
call.note.see.all=See all call notes
call.out.notconnected=Outbound call not connected with client
call.out.toshort=Call duration too short
call.outbound=Outbound call of {0}
call.private=You are not authorized to listen to this private call.
call.queue=Queue
call.rating=Rating
call.redirect.direct=Direct
call.redirect.queue=Queue
call.redirect.type=Redirection type
call.report.averagedelaireception=Average wait time at reception
call.report.averagewaitingduration.tooltip=Average wait time when the call is queued or forwarded
call.report.averagewaitingduration=Average waiting time
call.report.from.sources=Calls from sources
call.report.missedcalls.queue=Reception returns from queue
call.report.missedcalls.tooltip=Calls returned to reception
call.report.missedcalls.user=Reception returns from users
call.report.missedcalls=Returns to Reception
call.today=Calls today
call.type=Type
call.unidentified.department=Unidentified branch
call.user.report.user-chart-for-delai=Queue response time
call.with=Call with
call.withclient=With client only
call.withoutclient=Without client only
call.withwithoutclient=With or without client
calldata.generate.transcript=Generate summary and transcript
calldata.plaintext=Call from {0} to {1} ({2} -> {3}) with a duration of {4}.
calldata.source=Source
calldata.status.directexttransfert=Direct transfer to an extension
calldata.status.neverconnected=Never connected with an agent
calldata.status.receptionreturn=Contains a return to the reception
calldata.status.storewasclosed=Done when the store was closed
calldata.status.voicemail=Client left a voicemail
calldata.transcript=Transcript
calldata=Call
callmanager.agent.status=Agent Status
callmanager.agent=Agent
callmanager.alldepts=All branches
callmanager.anonymous=Anonymous
callmanager.call.in.progress=In progress
callmanager.call.list=Call list
callmanager.comment=Comment
callmanager.completed=Completed
callmanager.connected.reception=Connected reception
callmanager.connected=Connected
callmanager.delay=Delay
callmanager.ext=EXT
callmanager.extension.list=Extension list and individual numbers
callmanager.extension=Extension
callmanager.extensions=Extensions
callmanager.freepbx=Freepbx
callmanager.group=Group
callmanager.hide.connected=Hide connected calls
callmanager.inbound.tooltip=Number of inbound calls with in brackets the quantity that made a direct transfer to an extension by dialing it.
callmanager.inbound=Inbound
callmanager.info.call.live=Information on call in progress
callmanager.lead=Client
callmanager.live.call.notes=Notes in progress
callmanager.manager=Manager
callmanager.me=me
callmanager.message=Message
callmanager.send=Send
callmanager.name=Name
callmanager.notes=My call notes
callmanager.number=Number
callmanager.ok=OK
callmanager.onlymydepts=My branches
callmanager.outbound=Outbound
callmanager.parked=Parked
callmanager.phone=Phone
callmanager.pot=POT
callmanager.queue.ext=Direct transfer
callmanager.queue.reception.return=Return queue reception
callmanager.queue.reception=Queue reception
callmanager.queue=Queue
callmanager.reception.redirect=Redirect
callmanager.reception=Reception
callmanager.record=Record
callmanager.reloadwithallnotes=Refresh to see all notes
callmanager.show.connected=Display connected calls
callmanager.spd=SPD
callmanager.stats=Statistics
callmanager.status=Status
callmanager.supervisor=Supervisor
callmanager.target=Number
callmanager.tools=Call manager tools
callmanager.tot=TOT
callmanager.visits=Visits
callmanager.voicemail=Voicemail
callnote.add.placeholder=Write a comment
callnote.add=Add a call note
callnote.alluser.warning=Call note without an assigned user!
callnote.category=Category
callnote.client=Client
callnote.comment=Comment
callNote.confirm.delete=Are you sure you want to delete this call note ?
callnote.create=Create a call note
callnote.creator=Creator
callnote.incomplete.callnotes.associated.number=Incomplete call notes are already associated with this number
callnote.late.warning=Late call note!
callnote.open.existing.callnote.new.tab=Open existing call notes in a new tab
callnote.phone=Phone
callnote.soon.warning=A call note has been assigned to you!
callnote.toCalldata=See recording
callnote.todo.thisclient=Notes to do for this client
callnote.user=Assigned employee
callnotes.deleted=Call note deleted
callnotes.department.select=Select a branch
callnotes.departmentGroup.select=Select a department
callnotes.recall=Call back
callnotes.save=Save
callnotes.saved=Call note saved
callnotes.temporary.being.typed=Call note being typed...
callnotes.voice=New voicemail
calls.title=Calls
cancel=Cancel
card.4calendars.blue=Blue Calendar: Workflow card parts date
card.4calendars.green=Green calendar: Workflow card start date
card.4calendars.red=Red calendar: Workflow card end date
card.4calendars.yellow=Yellow calendar: Next task scheduled with the client for the opportunity
card.4calendars=4 calendars displayed on the card
card.calendar.explanation=Opportunity card calendar explanations
card.calendar.noworkflow.explanation=If the opportunity does not belong to a workflow; the 3 workflow calendar icons will display the letter N. In the case where the opportunity belongs to a workflow; the calendars will display points instead (for indefinite dates) or the day of the date.
card.calendar.purple.explanation=Today's dates are in purple.
card.calendar.red.explanation=Dates that surpass today's date are in red. The start date of the workflow card will always be white.
card.calendar.white.explanation=Dates that are later than tomorrow are white. (The start date is always in white)
card.calendar.yellow.explanation=Tomorrow's date are in yellow.
card.client.icons=Contact information icons (email and phones)
card.lastactivityicon=Information from the last activity
card.skuline=Info. card, SKU, serial number and model code
card.workflow.date.not.applicable=Not applicable for this opportunity (does not belong to a workflow)
cards.cards=Cards
cards.matching.filter=Number of cards matching these filters
cardtype.create.in.workflow=Create new card type in this workflow
cardtype.create.reservation.color=Create new reservation color
cardtype.create=Create new card type
cardtype.deleted=Card type deleted
cardtype.notfound=Card type not found
cardtype.notsaved=Card type not saved
cardtype.saved=Card type saved
cardtype.title=Types of cards
cardtype=Card type
categories=Categories
category.client.donotcontact=Do not contact
category.cotation.active=Active
category.cotation.approved=Approved
category.cotation.deleted=Deleted
category.cotation.expired=Expired
category.cotation.modified=Modified purchase offer
category.cotation.pending=Pending approval
category.cotation.project=Project
category.feed.status.disabled=DISABLED
category.feed.status.enabled=ENABLED
category.feed.status.progress=PROGRESS
category.feed.type.boatdealerxml=BOATDEALER XML
category.feed.type.downloadfeed=Download feed
category.feed.type.downloadzii=Download External API
category.feed.type.googlexml=GOOGLE XML
category.feed.type.granddesignxml=GRAND DESIGN XML
category.feed.type.kijijicsv=CSV
category.feed.type.recreatifxml=RECREATIF XML
category.feed.type.traderxml=TRADER XML
category.files.client=Client
category.files.department=Branch
category.files.lautopak=Lautopak
category.files.login=Login page background
category.files.logo=Logo
category.files.stripesession=Stripe session
category.files.system=System
category.files.training=Training
category.files.user=User
category.files.workflowboard=Workflow board
category.history.callnote=Call note
category.history.chat=Chat
category.history.client=Client
category.history.clientmessage=Customer portal message
category.history.comment=Comments
category.history.communication=Communications
category.history.config=Configuration
category.history.cotation=Quotation
category.history.files=Files
category.history.goal=Goal
category.history.hook=Hook
category.history.managerproduct=Product manager
category.history.managerproductconfig=Product manager configuration
category.history.managerproductgroup=Vehicle group
category.history.meeting=Meeting
category.history.opportunity=Opportunity
category.history.product=Product
category.history.task=Task
category.history.trade=Trade
category.history.vehicle=Vehicle
category.history.vehicleinheritancerule=Vehicle inheritance rules
category.history.vehicleselectionoption=Vehicle selection options
category.history.vehiclespecification=Vehicle specifications
category.history.vehicletext=Vehicle text
category.history.vehiclewatermark=Vehicle watermark
category.history.workflow=Workflow
category.history.workflowdata=Workflow data
category.message.comment=Message
category.opportunity.active=Active
category.opportunity.close=Closed
category.opportunity.lost=Lost
category.opportunity.other=Excluded
category.opportunity.pending=Pending
category.opportunity.sold=Sold
category.opportunity.soldmonth=Sold
category.product.inventory=Inventory
category.product.root=Root
category.product.template=Template
chat.altText.en=Alternative Text - English
chat.altText.fr=Alternative Text - French
chat.available=Available
chat.close.confirm=Are you sure you want to close this chat?
chat.complete.vendeur.fail=Error: you cannot close a chat that has already been assigned to another user
chat.complete.vendeur.sucess=Chat successfully closed
chat.completed=The chat has been closed.
chat.continue=Continue chatting
chat.information=Information
chat.inProgress=Chat in progress
chat.list.users=Users
chat.new=New Chats
chat.not.available=Not available
chat.notAvailable=Sorry, no one is online to communicate with you. We will get back to you as soon as possible. Have a nice day!
chat.now=Chat now
chat.transfer.no.channel=No channel to transfer from
chat.transfer.no.user=No user to transfer to
chat.users.status=User Chat Status
chat.widget.call=Call
chat.widget.code=Code
chat.widget.contact=How would you like to be contacted if no one is available to assist you?
chat.widget.delete=You cannot delete a chat widget that is associated to a chat message
chat.widget.department=Choose a branch to contact
chat.widget.email=Email
chat.widget.error.create.exists=Chat already open
chat.widget.error.create=You cannot have more than 3 chats open
chat.widget.message.placeholder=Ask your question!
chat.widget.message=Message
chat.widget.notsaved=Chat Widget not saved
chat.widget.one.moment.please=Sorry, there are no current agents available. You can leave us message and we will get back to you as soon as possible!
chat.widget.questions=For all questions about service, parts and the boutique, please contact us by phone: ************
chat.widget.saved=Chat Widget Saved
chat.widget.say=Send a message...
chat.widget.send=Sent
chat.widget.start=Start chatting
chat.widget.text=Text
chat.widget.welcome.client=Please ask your question, and our sales agent will contact you as soon as possible!
chat.widget.welcome=Please fill in your contact information, and our sales agent will contact you as soon as possible!
chat.with=Chat with
chat.without.client=Anonymous chat
chat=Chat
chatbox.close=Are you sure you want to close this conversation?
chatmessage.sent.error.user=Error: a user has already been assigned to this chat
chatmessage.sent.error=Chat message not sent.
chatmessage.sent=Message sent
chatquerybuilder.anyPageUrl=Any URL page visited
chatquerybuilder.clientBrowsingTime=Client browsing time
chatquerybuilder.clientViewedPages=Client viewed pages
chatquerybuilder.currentPageUrl=Current page URL
chatquerybuilder.referringWebsiteAddress=Website address reference
chatquerybuilder.userConnectedCount=Connected user number
chats=Chats
chatwidget.allowed.domains.hint=List separated by coma. EX: www.youtube.com,www.google.com,www.facebook.com
chatwidget.allowed.domains=Allowed domains
chatwidget.color=Color
chatwidget.confirm.delete=Are you sure you want to delete this chat widget?
chatwidget.copy.code=Copy embed code
chatwidget.create=Create chat widget
chatwidget.displayRules=Display rules
chatwidget.edit=Edit chat widget
chatwidget.installed.false=Not installed
chatwidget.installed.true=Installed
chatwidget.list=Chat widget list
chatwidget.name=Name
chatwidget=Chat widget
chatwidgetgreeting.active=Active
chatwidgetgreeting.chats=Chats
chatwidgetgreeting.chatWidgetGroup=Group
chatwidgetgreeting.confirm.delete=Are you sure you want to delete this greeting?
chatwidgetgreeting.conversionRate=Conversion rate
chatwidgetgreeting.create=Create a chat greeting
chatwidgetgreeting.displayDelay.hint=Time on page is more than X seconds.
chatwidgetgreeting.displayDelay=Display delay
chatwidgetgreeting.displayRules=Display rules
chatwidgetgreeting.displays=Displays
chatwidgetgreeting.edit=Edit a chat greeting
chatwidgetgreeting.list=List of chat greetings
chatwidgetgreeting.name=Name
chatwidgetgreeting.showOnlyOnce=Show only once
chatwidgetgreeting.text=Text
chatwidgetgroup.available.false=Offline
chatwidgetgroup.available.true=Online
chatwidgetgroup.cannot.move=Cannot move further
chatwidgetgroup.confirm.delete=Are you sure you want to delete this chat widget group?
chatwidgetgroup.create=Create chat group
chatwidgetgroup.createPotentialOpportunity=Create potential opportunities
chatwidgetgroup.deleted=Chat group deleted !
chatwidgetgroup.displayRules=Rules displayed
chatwidgetgroup.drag=Drag and drop to sort
chatwidgetgroup.edit=Edit chat group
chatwidgetgroup.list=List of chat groups
chatwidgetgroup.move.down=Move down
chatwidgetgroup.move.up=Move up
chatwidgetgroup.name=Name
chatwidgetgroup.notdeleted=Chat group not deleted !
chatwidgetgroup.origin=Source
chatwidgetgroup.priority=Priority
chatwidgetgroup.saved=Save widget chat group
chatwidgetgroup.select=Department
chatwidgetgroup.users.empty=The group does not have any users
chatwidgetgroup.users=Users
checkbox.off=Off
checkbox.on=On
choose.file=Choose files
client.access=Access denied
client.activity=Activities
client.add.comment.fail=Error when creating a comment
client.add.comment.ok=Comment added
client.add.task=Add a task
client.add.taskoraction=Add a task or action
client.address.city.input=Enter a city
client.address.city=City
client.address.country.input=Enter a country
client.address.country=Country
client.address.input=Enter the address
client.address.line2=Address 2
client.address.postalCode.input=Enter a postal code
client.address.postalCode=Postal Code
client.address.state.input=Enter a state
client.address.state=State
client.address=Address
client.addtolinks_partnotcompatible=Add selected parts
client.addtolinks_partrequired=Add selected parts
client.addtolinks_partsubs=Add selected parts
client.addtolinks_prdsubs=Add selected products
client.admin=Administration
client.advancedsearch=Advanced research
client.altEmail=Alternative Email
client.altemail=Alternative Email
client.appointment.today.confirm=Did you meet {0} today?
client.assignation=Assigned to
client.attachments.attachment=From client attachments
client.attachments.delete.confirm=Do you really want to delete this file?
client.attachments.device=From your device
client.attachments.id=ID
client.attachments.name=Name
client.attachments.preview=Preview
client.attachments=Attachments
client.attributes=Attributes
client.balance=Balance
client.banned=Banned
client.birthday.error=The birthday entered is not valid.
client.birthday=Birthday
client.call.history.empty=No call history
client.call.history=Call history
client.call.notes=Call notes
client.callperformance=Call Performance
client.calls=Calls
client.chat=Chat
client.chats=Chats
client.clientattachments=Client Attachments
client.clientnotfound=unable to find a client that matches your search
client.cloneproducts=Clone products
client.cloneyearplusun=Clone products year + 1
client.comment=Comment
client.comments=Comments
client.communicationLanguage=Communication language
client.company=Company Name
client.consent=Consentment
client.contact.details=Contact details
client.contact.principal=Main contact
client.contact.sumarry=Summary
client.contactInformations.add=Add a contact
client.contactInformations=Additional Contacts
client.coproprio=Co-owner
client.cotation.empty=No quotation
client.cotation=Quotations
client.create=Create a new client
client.date=Creation date
client.dateMOD=Modification date
client.deleteall=Delete all images
client.deleteallnoproduct=No products selected
client.deleteallproduct=Delete All selected products
client.delivery=Delivery
client.documents.none=No documents
client.documents=Documents
client.donotcontact.warning=Unable to contact a client with a "do not contact" status. Please change the status to be able to contact the customer.
client.downloadall=Download all images
client.draftattachments=Mail draft attachments
client.draftBulkAttachments=Mass email draft attachments
client.driverlicence.input=Enter the driver's license number
client.driverlicence=Driver's Licence
client.duplicatedPhone=Phone number duplicate
client.duplicatedPhonecell=Mobile phone number duplicate
client.duplicategroup=Duplicate all selected groups
client.email.all=All
client.email.allgroups=All department emails
client.email.archived=Archived
client.email.cc=CC
client.email.compose=New message
client.email.deliverable=Deliverable
client.email.do_not_send=Do not send
client.email.drafts=Drafts
client.email.empty=No email corresponding to your search
client.email.error.notvalidated=The client does not have a valid email.
client.email.from=From
client.email.input=Enter an email
client.email.nomail=Email generated by Traction
client.email.notvalidated=Not validated
client.email.read=Read
client.email.receivedDate=Received date
client.email.refresh=Refresh
client.email.reply=Reply
client.email.send.default.subject=Email sent by {0}
client.email.send.success=Email has been sent.
client.email.send=Sent
client.email.sendmail.error=Error when sending email
client.email.sendmail=Emails sent
client.email.to=To
client.email.typeto=Enter recipient
client.email.undeliverable=Undeliverable
client.email.unknown=Unknown
client.email.verified.failed=Email not valid
client.email.verified.success=Email verified
client.email=Email
client.emails=Emails
client.emailValidationResult=Email validation
client.emergencyname=Emergency contact
client.emergencyphone=Emergency phone number
client.event.empty=No event
client.event.upcoming=Upcoming
client.externalId=External ID
client.facebookId=Facebook ID
client.fax=Fax
client.files=Files
client.finances=Finances
client.firstname=First name
client.flash.closequicktasks.nok=Task cannot be closed
client.flash.closequicktasks.ok=Task has been closed
client.flash.created=Client created
client.flash.error=Error when creating a client
client.flash.modify.emailexist=Another client with this email already exists. Click here to merge this client with the existing one.
client.flash.modify.extidexist={0} already has this ID. Click here to merge this client with the existing one.
client.flash.new.emailexist=A client with this email already exists. Click here to go to the client.
client.flash.new.extidexist=A client with this ID already exists. Click here to go to the client.
client.flash.newfile.nok=New File failed
client.flash.newfile.ok=New File created
client.flash.newoppor.nok=Opportunity failed
client.flash.newoppor.ok=Opportunity saved
client.flash.newoppornointerest.nok=New opportunity failed: The customer must have at least one interest!
client.flash.newoppornoname.nok=New opportunity failed: No Name!
client.flash.newopporsold.nok=New opportunity failed: The category can not be changed if the opportunity is sold!
client.flash.nofollow.ok=No Follow-up history created
client.flash.saved=Client information successfully registered
client.flash.video.template.nok=No configuration template, conference created but not sent to client!
client.follownotrequired=Follow up not required
client.fromdate=From
client.fullname=Full name
client.gender=Gender
client.history=History
client.homeaddress=Home address
client.homecity=Home City
client.homecountry=Home Country
client.homepostal=Home Postal Code
client.homeprovince=Home State
client.id=Traction ID
client.image.current=Current avatar
client.image.delete=Delete the current image to return to the default avatar
client.image.modify=Modify client avatar
client.image.upload=Upload an avatar image
client.info=Client information
client.instagramLink.placeholder=Enter or paste the Instagram link
client.instagramLink=Instagram link
client.interest.deleted=Client interest deleted
client.interest.dmsName=DMS interest name
client.interest.existing=Existing interests
client.interest.name=Interest name
client.interest.needed=Client interest required to save an opportunity
client.interest.new=New client interest
client.interest.notsaved=Please make sure that a client interest does not already exist and the name is not empty.
client.interest.rule=Interest name
client.interest.save=Create / Save client interest
client.interest.saved=Client interest saved
client.interests=Interests
client.invalid.phone=Please make sure the phone number is valid
client.lastday=Last day
client.lastmonth=Last month
client.lastname=Last name
client.lastweek=Last week
client.lastyear=Last year
client.mail.cancel=Cancel
client.media=Media
client.merge.btn=Merge
client.merge.confirm.post=in the client
client.merge.confirm.pre=Are you sure you want to merge this client
client.merge.confirm=Are you sure you want to merge this client {0} with client {1}
client.merge.first.client=First client
client.merge.message.merged=(will be updated)
client.merge.message.specification=(will be deleted)
client.merge.name=Name
client.merge.second.client=Second client
client.merge.small=Merge
client.merge.value=Value
client.merge.warn.missing=A client is missing
client.merge.warn.same=You cannot merge two clients that are identical
client.merge=Merge two Clients
client.messages=Comments
client.missing.assigned.user=Missing assigned user
client.missing.date=Missing date
client.missing.info=Missing information
client.missing.opportunities=Missing opportunities
client.missing.status=Missing status
client.modify=Modify this client
client.moredata=Extra data for this client
client.moreinfo=More information
client.name=Name
client.navigationhistory=Client navigation history
client.nb.call.today=Number of calls today
client.newclient=New Client
client.newopportunity.tooltip=New Opportunity
client.newopportunity=New Opportunity
client.newtodonotes=Personal notes
client.no.call.notes=No call notes
client.nomail.confirm=Are you sure you want to save this client without an email and/or a phone number?
client.nomail.list.empty=No clients
client.nomail.list=List of clients without an email
client.opportunities=Opportunities
client.opportunity.empty=No opportunity
client.opportunity=Opportunity
client.origin=Source
client.parts=Invoices
client.partsInvoice.empty=No invoice
client.pdf=Client PDFs
client.phone=Phone
client.phonecell.verified.failed=Phone not valid
client.phonecell=Cellphone
client.phonework=Work phone
client.pointofsales=Point of Sales
client.posstatus.all2=All
client.posstatus.all=All
client.posstatus.bdc=BDC
client.posstatus.delivery=Delivery
client.posstatus.fni=F&I
client.posstatus.lautopacl=Lautopak
client.posstatus.lautopak=Lautopak
client.posstatus.parts=Parts
client.posstatus.sales2=Sales 2
client.posstatus.sales=Sales
client.posstatus.service=Service
client.posstatus.shop=Shop
client.posstatus.tech=Technician
client.posstatus.traction=Traction
client.printForms=Print PDFs
client.product.add=Add a product
client.product.code=Stock Number
client.product.description=Description
client.product.details=Details
client.product.modelcode=Model code
client.product.name=Product Name
client.product.new=New client product
client.product.nodetails=No details...
client.product.price=Price
client.product.prixmsrp=MSRP price
client.product.prixspecial=Special price
client.product.search.txt=name,year,make,model code,color or stock number
client.product.search=Product search
client.product.value=Value
client.product=Client product
client.productnotfound=unable to find any product that matches your inquiry
client.purchase.invoice.empty=No parts invoice
client.relatedClients=Associated clients
client.relation.add=Associate a client
client.relation.search=Search a client to add a relation
client.relation=Relationship
client.ro=RO Number
client.roopen=Info. card
client.sales.history=Sales history
client.sales=Sales
client.same.phone=Clients with the same phone number
client.save.duplicate.phone=Are you sure you want to save this customer knowing that other customers with the same phone number already exist ?
client.save=Save
client.saveandcontinue=Save & Continue
client.search.phone=Search client by phone number
client.search=Search client
client.sendallproduct=Export all selected products
client.sendForms=Send PDFs to client via email
client.server.error=Server error when saving the client.
client.service=Service
client.signature=Client signature
client.smsMessages=SMS
client.status=Status
client.summary=Summary
client.task.priority=Priority
client.tasks.ask.create.followup=You do not seem to have a task scheduled with this client for a follow up. Do you want to create one?
client.tasks=Tasks
client.tasktodo.empty=No tasks
client.time=Time spent on this client's opportunities (calls and appointments)
client.todate=To
client.total.cash=Cash
client.total=Total
client.trade.empty=No trade
client.trade=Trades
client.tradein=Trade-in
client.transfer=Transferring chat
client.units.add=Add a vehicle
client.units.remove.confirm=Are you sure wou want to remove this vehicle from the client ?
client.units.remove=Remove vehicle from this client
client.units=Vehicles
client.user=User
client.vehicle.empty=No vehicles
client.vehicle=Vehicles
client.verifyaddattributes=Are you sure you want to add this attribute to all {0} selected products ?
client.verifyaddattributesfromdefaultlocal=Are you sure you want to add all attributes from the default localization to another localization for the {0} selected products?
client.verifyaddtolinks_partsubs=Are you sure you want to add all {0} selected parts ?
client.verifyaddtolinks_prdsubs=Are you sure you want to add all {0} selected products ?
client.verifycloneyearplusun=Are you sure you want to clone all {0} selected products ?
client.verifyconvertconfigurable=Are you sure you want to convert all {0} selected products ?
client.verifydeleteallproduct=Are you sure you want to delete all {0} selected products ?
client.verifyduplicategroup=Are you sure you want to add all {0} selected groups ?
client.verifyremovemagento=Are you sure you want to remove all {0} selected products from magento ?
client.verifysendmagento=Are you sure you want to export all {0} selected products to magento ?
client.view=Client file
client.warning.others.exists=The following clients have similar information
client.with.email.exists=Another client with this email already exists.
client.workaddress=Work address
client.workcity=Work city
client.workcountry=Work country
client.workflow.empty=No workflow
client.workflow=Workflow cards
client.workOrders.empty=No work order
client.workOrders=Work orders
client.workpostal=Work postal code
client.workprovince=Work state
client=Client
clientDashboard.creationChart.short=CD
clientDashboard.creationChart.tooltip=Average days of creation dates for all opportunities
clientDashboard.lastActChart.short=AD
clientDashboard.lastActChart.tooltip=Average days of last activities dates for all opportunities
clientmessage.sent.error=Message not sent.
clientmessage.sent=Message sent
clientprocess.category=Category
clientprocess.create=Create client process
clientprocess.description=Description
clientprocess.edit=Edit client process
clientprocess.email=Email
clientprocess.end=End date
clientprocess.execute=Execute process
clientprocess.executionInterval=Execution interval in hours (0 for manual execution only)
clientprocess.filter=Filter
clientprocess.lastExecution=Last execution
clientprocess.list=Client process list
clientprocess.multipleEntriesInterval=Interval in days to allow a second entry for the same object
clientprocess.name=Name
clientprocess.not.saved.unique.name=Client process not saved. The name {0} is already used.
clientprocess.not.saved=Client process not saved
clientprocess.priority=Priority
clientprocess.saved=Client process saved successfully
clientprocess.start=Start date
clientprocess.status.not.updated=Error while updating the status
clientprocess.status.updated=Status updated successfully
clientprocess.taskconfig=Task configuration automatically created
clientprocess.type.action=Action
clientprocess.type.call=Call
clientprocess.type.manual=Manual
clientprocess.type=Type
clientprocess=Client process
clientprocessentry.count=Number of entries within a client process
clientprocessentry.date=Date
clientprocessentry.list=Client process entry list
clientprocessentry.status.automatic=Automatic
clientprocessentry.status.cancel=Cancel
clientprocessentry.status.done=Done
clientprocessentry.status.pending=Pending
clientprocessentry.status.todo=To do
clientprocessentry.status=Status
clientprocessentry=Client process entry
clientproduct.interest.confirm=Are you sure you want to modify the interest of this client product? The inspection sheet will reset according to the new interest selected.
clientproduct.interest.role.error=You do not have access to modify the interest of the client product.
clientproduct.search=Client product search
clientworkorderschedule.calendar=Work order schedule
close=Close
column.that.concerns=Column that concerns
com.all=All communications
comment.add.comment.placeholder=Write a comment
comment.add.comment=Add a comment
comment.add=Add
comment.alert=The comment cannot be empty
comment.author=Author
comment.cancel=Cancel
comment.client=Client
comment.comment.placeholder=Write a comment
comment.date=Date
comment.new=New comment
comment.section=List of comments
comment.subject=Subject
comment.task=Task
comment.text.placeholder=Write a message
comment.text=Text
comment.type.here=Type your comment here...
comments=Comments
communication.assignedTo=Assigned to
Communication.calls=Calls
communication.cannot.send.email=You are not authorized to use {0} as an email to send a communication.
communication.cannot.send.facebook=You are not authorized to send a communication via Facebook.
communication.cannot.send.number=You are not authorized to use {0} as the phone number to send a communication.
communication.category=Category
communication.client.typing=Client is typing
communication.completed=Call note completed
communication.completion.completed=Completed
communication.completion.frozen=Frozen
communication.completion.late=Late
communication.completion.todo=To do
communication.completion.voicemail=Voicemail
communication.datatable.all=Communications list
communication.datatable.mine=My communications list
communication.days.ago={0} days ago
communication.details=Communication sent from {0} to {1}
communication.direction=Direction
communication.drop.images.only=You can only upload image files.
communication.emails=Emails
communication.evaluation.comment=Feedback on assessment for training purposes
communication.evaluation.saved=Feedback saved
communication.evaluation=Feedback
communication.forward.to.user.done=Assignment made
communication.forward.to.user.notdone=Assignment not made
communication.forward.to.user=Assign communication to another user
communication.forward=Forward
communication.goto.client=Open communications in client file
communication.hours.ago={0} hours ago
communication.in.queue={0} communications in queue
communication.isImportant.false=Not important
communication.isImportant.markas.false=Mark as not important
communication.isImportant.markas.true=Mark as important
communication.isImportant.true=Important
communication.isImportant.warning=Warning! You can only edit important marks for your own communications.
communication.isImportant=Important
communication.isPrivate.content=This communication is private. Please refer to {0} to view the content.
communication.isPrivate.markas.false=Mark as public
communication.isPrivate.markas.true=Mark as private
communication.isUnread.false=Read
communication.isUnread.markas.false=Mark as read
communication.isUnread.markas.true=Mark as unread
communication.isUnread.true=Unread
communication.isUnread.warning=Warning! You can only edit unread marks for your own communications.
communication.isUnread=Unread
communication.lock=This communication is private
Communication.Messages=Messages
communication.minutes.ago={0} minutes ago
communication.missinginfo=Client information is missing for this communication channel!
communication.modal.check.messages.sent.by=Sent by
communication.modal.client.important=Conversation status with client
communication.modal.conversation.call=Phone conversation
communication.modal.conversation.filter.general=General Filter
communication.modal.conversation.filter.reset=Reset filters
communication.modal.conversation.filter.specific=Filter communication types
communication.modal.conversation.filter.users=Users
communication.modal.conversation.filter=Avanced filters
communication.modal.conversation.messages.direction.all=All
communication.modal.conversation.messages.direction.incoming=Incoming
communication.modal.conversation.messages.direction.outgoing=Outgoing
communication.modal.conversation.messages.status.all=All
communication.modal.conversation.messages.status.important=Favorite
communication.modal.conversation.messages.status.notified=Notified
communication.modal.conversation.messages.status.private=Private
communication.modal.departments.and.groups=Branches and departments
communication.modal.departments=Branches
communication.modal.mail.status=Mailing status
communication.modal.remove.all=Remove all
communication.modal.search.key.word=Search a keyword
communication.modal.search=Search by
communication.modal.select.time.period.30days=Since last 30 days
communication.modal.select.time.period.7days=Since last 7 days
communication.modal.select.time.period.90days=Since last 90 days
communication.modal.select.time.period.from.the.begining=From the begining
communication.modal.select.time.period=Select time period
communication.modal.select.visible.message.direction=Select the direction of visible messages
communication.modal.select.visible.message.readability.all=All
communication.modal.select.visible.message.readability.read=Read
communication.modal.select.visible.message.readability.unread=Unread
communication.modal.select.visible.message.readability=Select the eligibility of visible messages
communication.modal.select.visible.message.state=Select the state of visible messages
communication.modal.userGroups=Departments
communication.modal.view=View
communication.new.Facebook=New Facebook message
communication.new.MailMessage=New email
communication.new.SmsMessage=New SMS
communication.new=NEW
communication.not.matching.filter={0} communications do not match the filters
communication.outbound.false=Inbound
communication.outbound.true=Outbound
communication.private=You are not authorized to read this communication
communication.queue.client=Client
communication.queue.from=From
communication.queue.method=Method
communication.queue.opportunity=Opportunity
communication.queue.text=Text
communication.queue.user=User
communication.queue=Communication queue
communication.rating.comment=Comment
communication.rating.communication=Communication
communication.rating.evaluator=Evaluator
communication.rating.negative=Evaluation to verify
communication.rating.rating.eq=Equals
communication.rating.rating.max=Maximum
communication.rating.rating.min=Minimum
communication.rating.rating=Rating
communication.rating.searchClient=Client search
communication.rating.user=User
communication.rating=Training Evaluation
communication.read=Read
communication.received.via=Received via
communication.search=Search in contacts
communication.seconds.ago={0} seconds ago
communication.send.infoError=Please enter all information before sending
communication.send.limit=Bulk communication limit {0} reached
communication.send.mass=Mass mailing
communication.send.statusError=Unable to contact a customer with "Do not send" or "SPAM" status.
communication.send.via=Send via
communication.send=Send
communication.sender.bcc.placeholder=Enter a BCC email
communication.sender.cc.placeholder=Enter a CC email
communication.sender.join.files=Join files
communication.sender.mode=Mode
communication.sender.more.options=More options
communication.sender.opportunity.placeholder=Select an opportunity
communication.sender.select.mode=Select mode
communication.sender.template.new=New Template
communication.sender.template.placeholder=Select a template
communication.sender.template.send=Save as new template after sending
communication.sending=Sending
communication.sent.by.system=Sent by the system
communication.sent.via=Sent via
communication.status.accepted=Accepted
communication.status.clicked=Clicked
communication.status.complained=Complained
communication.status.delivered=Delivered
communication.status.opened=Opened
communication.status.permfail=Permanent fail
communication.status.queued=Queued
communication.status.received=Received
communication.status.receiving=Received
communication.status.rejected=Rejected
communication.status.sending=Sending
communication.status.sent=Sent
communication.status.stored=Stored
communication.status.tempfail=Temporary fail
communication.status.undelivered=Undelivered
communication.status.unknown=Unknown
communication.status.unsubscribe=Unsubscribed
communication.status.unsubscribed=Unsubscribed
communication.status=Status
communication.summary=Summary of communications
communication.text.placeholder=Write a message...
communication.text=Text
communication.transfer.modal.title=Communication transfer
communication.type=Type
communication.unread=Unread
communication.userGroup=Department
communication.years.ago={0} years ago
communication=Communications
communicationmeeting.plaintext={0} - {1} (Duration of {2} minutes)
communications=My communications
community.announcements=Announcements
community.faq=Q&A
community.featurerequests=New feature requests
community=Community
compilant=Compliant
config.activatiomodule=Module activation
config.add=Add a configuration
config.addLine=Add line
config.archive.title=Archive configuration
config.archive=Archive
config.autoarchive.boardnames.hint=Separated by commas
config.autoarchive.boardnames=Name of workflow boards that are archived
config.autoarchive=Autoarchive
config.back=Back
config.businessunit=Business unit
config.cache.disable=Disable cache
config.cache.enable=Enable cache
config.cache.status=Cache
config.callmanager.groupe.description=Department filter configuration within Call Manager statistics
config.callmanager.groupe=Department configuration
config.callmanager.title=Call Manager configuration
config.callmanager=Call Manager
config.category=Category
config.client.interest=Client interest configuration
config.configtable=Configuration table
config.default.user=Default user
config.default=Default
config.delay=Delay
config.delete=Delete
config.deleted=Configuration successfully deleted
config.edit=Edit a configuration
config.email.ignore=Ignore emails
config.email=Email
config.emailParser=Email Parser
config.emailTemplate=Email Templates
config.financing.banks=Financing banks configuration
config.freepbx=FreePBX configuration
config.friday=Friday
config.general=General
config.google.storage.bucket.name.invalide=Bucket name must have only lowercase alphabetic characters, numbers, hyphen or underscore (no spaces allowed)
config.google.storage.bucket.name=Bucket name
config.google.storage.credential=Identification information
config.google.storage.enabled=Cloud Storage enabled
config.google.storage.has.bucket=Bucket is created
config.google.storage.no.bucket=Bucket has not been created
config.google.storage.storage.credential=Credentials
config.google.storage=Cloud Storage Configuartion
config.gsuite=GSuite Email
config.history.score.title=Points history configuration
config.icon.legend=Icon legend configuration
config.image=Image
config.lang=Language
config.language.delete=Delete
config.language.update=Update
config.langue.english=English
config.langue.french=French
config.langue=Language
config.lautopak=Lautopak configuration
config.location=Location
config.magento.status=Magento Interface
config.monday=Monday
config.name=Name
config.notsaved=Configuration not saved
config.opportunity.description=Opportunity description
config.opportunity.title=Opportunity configuration
config.pdf.title=PDF template configuration
config.product=Product
config.providers.financing=Export to a DMS
config.providers.job.aimbase=Sync Aimbase Leads Job
config.providers.job.arcticcat=Sync Arctic Cat Leads Job
config.providers.job.bmw=Sync BMW Leads Job
config.providers.job.brp=Sync BRP Leads Job
config.providers.job.ducati=Sync Ducati Leads Job
config.providers.job.groupeBeteneau=Sync Groupe Beneteau Leads Job
config.providers.job.polaris=Sync Polaris Leads Job
config.providers.job.smokercraft=Sync Smokercraft Leads Job
config.providers.job.yamaha=Sync Yamaha Leads Job
config.providers.job.zeroMotorcycle=Sync Zero Motorcycle Leads Job
config.saturday=Saturday
config.save=Save
config.saved=Configuartion saved
config.select.bank=Add a bank
config.select=Choose a configuration
config.sizeLimit=Size limit
config.sold.board=Sold board configuration
config.sunday=Sunday
config.supportlanguage=Supported language
config.thursday=Thursday
config.title=Configuration
config.tuesday=Tuesday
config.update=Update
config.value=Value
config.wednesday=Wednesday
config.widthLimit=Width limit
config.workflow.hooks.label.board=Table
config.workflow.hooks.label.destination=Destination
config.workflow.hooks.label.name=Name
config.workflow.hooks.label.type=Type
config.workflow.hooks.label.workflow=Workflow
config.workflow.hooks.title=Hooks configuration
config.workflow=Workflow
config=Configuration
configurator.add.parts.success=Parts have been added
configurator.addRootLnk.success=Configurable has been added to the configurator link
configurator.linkedVariable=Variable linked to products
configurator.parts.incompatible=Incompatible parts
configurator.parts.link=Add parts
configurator.parts.linked=Parts
configurator.parts.required=Required parts
configurator.parts.unlink=Delete parts
configurator.select=Select a menu
configurator=Configurator
confirm.action=Action confirmation
confirm.delete=Delete confirmation
contact.create.question=Select the type of contact:
contact.create=Create a new contact
contact.information.cancel=Cancel
contact.information.delete=Delete
contact.information.edit=Modify
contact.information.instructions=Instructions
contact.information.label=Label
contact.information.name=Contact
contact.information.phone.showExt=Has extension ?
contact.information.phone=Phone
contact.information.save=Save
contact.information.title=Title
contact.preference=Contact preference
contact.search=Client file search
contacts=Contacts
copied.to.clipboard=Copied to clipboard
copy.to.clipboard=Copy to clipboard
copy.embed.code=Copy embed code
costPrices=Cost prices
cotation.action.list=List of actions
cotation.add.opportunity=Add an opportunity
cotation.add.trade=Add a trade
cotation.approbation.error=Expiration date required
cotation.approbation.min.expiration=The expiration date must be in the future
cotation.approved.on=Approved on
cotation.btn.approve=Approve
cotation.btn.askApprobation=Request approval
cotation.btn.cancel=Cancel
cotation.btn.changed=Changes
cotation.btn.pendingApprobation=Approval in progress
cotation.btn.refuse=Refuse
cotation.btn.save=Save
cotation.btn.sendPurchaseOffer=Send purchase offer to client
cotation.cancel.confirm=Are you sure you want to cancel this edition of this cotation ?
cotation.cancel=Cancel quotation
cotation.cashdown=Cash down
cotation.category=Category
cotation.choice=Choose a quote
cotation.cotationElements=Quotation elements
cotation.cotationScenario=Quotation scenario
cotation.create.opportunity=Create an opportunity
cotation.create.trade.vehicle=Create a trade unit
cotation.create=Create a quote
cotation.created.on=Created on
cotation.date.list=List of dates
cotation.date=Creation date
cotation.dateSold.confirm=Are you sure you want to modify the date of sale?
cotation.dateSold=Sold
cotation.delete.confirm=Are you sure to delete this quote: {0}?
cotation.delete=Delete quote
cotation.deleted.error=Quote not found.
cotation.deleted=Quote deleted
cotation.department.choose=Which branch do you want to export the quote with?
cotation.department.error=Missing a sales user 
cotation.department=Branch
cotation.dmsDealerId=DMS ID
cotation.duplicate=Duplicate quote
cotation.duplicated.error=No quote found with the specified ID.
cotation.duplicated=Quote duplicated
cotation.edit=Edit quote
cotation.expirationDate=Expiration date
cotation.expired.on=Expired on
cotation.export.client.error=Client cannot be created in the DMS
cotation.export.error.api=The Lautopak API is unavailable
cotation.export.error.approved=The quote must be approved
cotation.export.error.client=The client cannot be created/updated
cotation.export.error.coClient=The co-owner cannot be created/updated
cotation.export.error.connect=Impossible to connect to Zii
cotation.export.error.dms=Your branch is not linked to a DMS. Please contact your administrator.
cotation.export.error.root=The opportunity contains a root product. A root product cannot be exported.
cotation.export.error.sold=The quotation must be sold
cotation.export.error.trades=A trade cannot be created/updated
cotation.export.error.usersales=The quote is missing a sales user
cotation.export.error2=Unable to add, modify or delete. The quote has already been exported.
cotation.export.error=An error occured while exporting to your DMS
cotation.export.exists.extId=DMS ID already associated with client {0}
cotation.export.inprogress=Export in progress...
cotation.export.lars.connexion=Connection...
cotation.export.lars.cotation=Creating quote...
cotation.export.lars.creationClient=Creating client...
cotation.export.lars.creationCotation=Creating quote...
cotation.export.lars.fees=Adding fees...
cotation.export.lars.fni=Adding F&I...
cotation.export.lars.foundClient=Client found...
cotation.export.lars.options=Adding accessories...
cotation.export.lars.product=Adding products...
cotation.export.lars.searchClient=Searching client...
cotation.export.lars.trades=Adding trades...
cotation.export.missing.dateEnd=Missing opportunity delivery date
cotation.export.missing.email=Missing client email
cotation.export.missing.financing=The quote needs to be financed
cotation.export.missing.product=All opportunities must be linked to a product (root or inventory)
cotation.export.missing.scenario=Financing scenario missing
cotation.export.missing.userFniExtId=F&I user missing and/or assign DMS ID to the user
cotation.export.missing.userSalesExtId=Sales user missing and/or assign DMS ID to the user
cotation.export.read.error=DMS took more than 10 seconds to respond and generated an error.  Please contact Traction support for further assistance.
cotation.export.read.password=Missing password to access DMS. Please contact Traction support for further assistance.
cotation.export.success=The quote exported successfully
cotation.export.warning=The quotation related to this opportunity has been exported. All modifications have been made in the DMS as well.
cotation.export=Export quotation
cotation.exported=Exported to DMS
cotation.financing=Financing scenarios
cotation.lautopakDealNumberId=Lautopak deal number ID
cotation.lautopakStatus=Lautopak status
cotation.locked=Lock quotation
cotation.modal.approbation=Expiration
cotation.modal.deny=Reason for quote refusal
cotation.modify.dateSold=Modify sold date
cotation.name.name=Name the quote
cotation.name=Name of quotation
cotation.nosoldstatus=In quotation
cotation.not.saved=An error occured while saving the quote
cotation.opportunities.added=Opportunities added
cotation.opportunities.removed=Opportunities removed
cotation.opportunities=Opportunities
cotation.pdf.contract=Sales agreement
cotation.pdf.purchaseoffer.en=Purchase Offer in English
cotation.pdf.purchaseoffer.fr=Purchase Offer in French
cotation.pdf.quote.en=Quote in English
cotation.pdf.quote.fr=Quote in French
cotation.pdf.quote=Quote
cotation.pdf.sendPdf=Error: Contact support for further details.
cotation.print=Print quote
cotation.reasonDenied=Reason for refusal
cotation.refundableDeposit=Refundable deposit
cotation.saved.error=Quote must contain a name.
cotation.saved=Quote saved
cotation.scenario.active=Active financing scenario
cotation.scenario.bank=Bank
cotation.scenario.calculate=Calculate financing scenario
cotation.scenario.detail=Financing details
cotation.scenario.error=Financing scenario not saved
cotation.scenario.fees=Fees
cotation.scenario.financedamount=Net financed amount
cotation.scenario.insurance.coverages=Coverages
cotation.scenario.insurance.nontaxablefee=Non-taxable fee
cotation.scenario.insurance.planname=Plan name
cotation.scenario.insurance.premium=Premium
cotation.scenario.insurance.premiums=Premiums
cotation.scenario.insurance.tax=Taxes
cotation.scenario.insurance.taxablefee=Taxable fee
cotation.scenario.insurance.term=Term
cotation.scenario.insurances=Insurances
cotation.scenario.interestrate=Interest rate
cotation.scenario.monthlyterm=Monthly term
cotation.scenario.netFinanced=Net financed
cotation.scenario.payment=Payment
cotation.scenario.price=Prices
cotation.scenario.protection=Protection
cotation.scenario.protectionWarrantly.deductible=Deductible
cotation.scenario.protectionWarrantly.kilometersterm=Kilometers term
cotation.scenario.protectionWarrantly.monthlyterm=Monthly term
cotation.scenario.protectionWarrantly.planname=Plan name
cotation.scenario.protectionWarrantly.price=Price
cotation.scenario.protectionWarrantly.stocknumber=Stock number
cotation.scenario.protectionWarrantly=Protections/Warranties
cotation.scenario.subfee=Insurances, protections, warranties and other fees
cotation.scenario.success=Quotation financing scenario saved
cotation.scenario.termtype.bimonthly=Bimonthly
cotation.scenario.termtype.biweekly=Biweekly
cotation.scenario.termtype.cash=Cash
cotation.scenario.termtype.monthly=Monthly
cotation.scenario.termtype.weekly=Weekly
cotation.scenario.termtype=Term type
cotation.scenario.total=Total of quotation
cotation.scenario.valid=Valid according to conditions:
cotation.scenario.vehicle.make=Make
cotation.scenario.vehicle.state=Vehicle state
cotation.scenario.vehicle.type=Vehicle type
cotation.scenario.warranty=Warranty
cotation.scenario.year=Year
cotation.scenario=Financing scenarios
cotation.send.pdf.confirm=Are you sure you want to send {0} by email to the client?
cotation.sendSalesAgreement.confirm=Are you sure you want to send this quote {0} by email to the client?
cotation.sendSalesAgreement=Send quote by email to the client
cotation.soldStatus=Status of sale
cotation.status=Status
cotation.summary.accessories.margin.salesprice.tooltip== Accessories selling price / sales price
cotation.summary.accessories.margin.salesprice=Accessories penetration rate on sales price
cotation.summary.accessories.margin.tooltip== Accessories selling price / MSRP
cotation.summary.accessories.margin=Accessories penetration rate on MSRP
cotation.summary.banque=Bank
cotation.summary.cash=Cash
cotation.summary.costAccessories=Cost of accessories
cotation.summary.costPreparation=Preparation Cost
cotation.summary.costPromotion=Promotions Received
cotation.summary.costReal=Real cost
cotation.summary.costSubtotal=Cost Subtotal
cotation.summary.costTransport=Cost of transportation
cotation.summary.displayTrade=Display Trade
cotation.summary.insuranceTerm=Insurance/Term
cotation.summary.interestRate=Interest Rate
cotation.summary.objectiveUpSales=Upsell objective
cotation.summary.opportunityAccessories.tooltip=Cost of accessories billed to the client of the opportunity
cotation.summary.opportunityAccessories=Accessories
cotation.summary.opportunityAdditionalCost=Aditional costs applied to the opportunity
cotation.summary.opportunityDiscount.tooltip=Discounts applied on the opportunity
cotation.summary.opportunityDiscount=Discounts
cotation.summary.opportunityFreight=Freight
cotation.summary.opportunityLabour=Labour time
cotation.summary.opportunityMinimumPrice.tooltip=Minimum product price of the opportunity
cotation.summary.opportunityMinimumPrice=Minimum price
cotation.summary.opportunityMrsp.tooltip=Product opportunity MSRP
cotation.summary.opportunityMrsp=Price
cotation.summary.opportunityNonTaxableFee=Non-taxable fees
cotation.summary.opportunityOptions.tooltip=Product option pricing of the opportunity
cotation.summary.opportunityOptions=Options
cotation.summary.opportunityOtherFee.tooltip=Other fees applied to the opportunity
cotation.summary.opportunityOtherFee=Other fees
cotation.summary.opportunityPDI=PDI
cotation.summary.opportunityPrice=Price
cotation.summary.opportunityPriceSpecial=Special Price
cotation.summary.opportunityPriceSubtotal.tooltip== MSRP + Options + Freight + PDI - Discounts - Promotions + Accessories + Other Fees
cotation.summary.opportunityPriceSubtotal=Subtotal
cotation.summary.opportunityPromo.tooltip=Product promotions of the opportunity
cotation.summary.opportunityPromo=Promotions
cotation.summary.opportunityTractionOptionsPrice=Options
cotation.summary.opportunityVarious=Miscellaneous/Tire taxes
cotation.summary.payment=Payment
cotation.summary.priceAtDelivery=Price at Delivery
cotation.summary.priceSpecial=Special Price
cotation.summary.profitAfterAccessories=Profit after accessories
cotation.summary.profitBeforeAccessories=Profit before accessories
cotation.summary.protectionTerm=Protection/Term
cotation.summary.refundableDeposit=Refundable deposit
cotation.summary.sum.list=Sum list
cotation.summary.taxes.federal=Federal taxes
cotation.summary.taxes.provincial=Provincial taxes
cotation.summary.taxes=Taxes
cotation.summary.term=Term
cotation.summary.totalObligation=Total obligation
cotation.summary.tradeGross=Gross Trades
cotation.summary.tradeLink=Trade lien
cotation.summary.tradeNetTrade=Net Trades
cotation.summary.tradePriceToAbsord=Trade absorption price on profit
cotation.summary.tradeRealPrice=Real Trade price
cotation.summary.typeTerm=Term Type
cotation.summary.upSales=Upsell
cotation.summary.warantlyTerm=Warranty/Term
cotation.summary=Quote Summary
cotation.table=Quotation dashboard
cotation.toggleCollapse=Show/Hide quotation sidebar
cotation.trades=Trades
cotation.unlocked=Unlock quote
cotation.update.api.error.same=This DMS deal number already exists
cotation.update.api.missing=Missing quote ID and/or DMS deal number ID settings
cotation.update.api.success=Quote updated successfully
cotation.update=Update quote
cotation.view=Quotation file
cotation.with=With quote only
cotation.without=Without quote
cotation=Quotation
cotationelement.category.all=All quotes
cotationelement.category.notsold=Only unsold quotes
cotationelement.category.sold=Only sold quotes
cotationelement.comment.updated=Comment saved
cotationelement.commission.updated=Commission saved
cotationelement.fniProfit=F&I profit
cotationelement.fniProfitAestheticProtection=Aesthetic protection
cotationelement.fniProfitChemicals=Chemicals
cotationelement.fniProfitCriticalIllnessInsurance=Critical illness insurance
cotationelement.fniProfitDisabilityInsurance=Disability insurance
cotationelement.fniProfitExtendedPlan=Extended plan
cotationelement.fniProfitLifeInsurance=Life insurance
cotationelement.fniProfitOthers=Other
cotationelement.fniProfitRentalLeaseUsury=Rental lease / Usury
cotationelement.fniProfitReplacementInsurance=Replacement insurance
cotationelement.fniProfitReserveFinance=Reserve finance
cotationelement.fniProfitTotal=Total F&I profit
cotationelement.fniProfitTotalPercent=Total F&I percentage profit
cotationelement.fniProfitTotalPerOpp=Average per vehicle
cotationelement.followup.approvedsalesmanager=Approved by sales manager
cotationelement.followup.cleardiff=Clear difference
cotationelement.followup.default=----
cotationelement.followup.slightdiff=Slight difference
cotationelement.followup.updated=Follow-up saved
cotationelement.followup=Follow-up
cotationelement.notupdated=Error when saving
cotationelement.promo.updated=Promotion saved
cotationelement.type.ratio=Trade ratio = Number of trades / Number of opportunities
cotationelement.type=Type
cotations=Quotations
create.cotation=Create a quote
create.new.line=Create a new line or select a line and modify it
create.opportunity=Create an opportunity
create.or.modify.clients.interests=Creation and modification of client interests
create=Create
creator=Creator
current.value=Current value
custom.scenario=Custom
custom=Custom
customcolvis=Edit columns
dailyUserData.lateTask=Late tasks
dailyUserData.scheduledTask=Scheduled Tasks
dashboard.alluser=All user's
dashboard.appointment.tooltip=Opportunities with an appointment task
dashboard.appointment=Appointment
dashboard.averagedelai=Average delay
dashboard.averageduration.tooltip=Average duration of calls
dashboard.averageduration=Average duration
dashboard.averagepotentiel=Average efficiency
dashboard.date.default=Date
dashboard.date.from=From
dashboard.date.month=Month
dashboard.date.to=To
dashboard.date.week=Week
dashboard.date=Duration of statistics
dashboard.delivery.tooltip=Opportunities with a delivery task
dashboard.delivery=Delivery
dashboard.interests=Interests
dashboard.late=Late
dashboard.mycallnotes=My call notes
dashboard.mycommunications.call=Call
dashboard.mycommunications.chat=Chat
dashboard.mycommunications.email=Email
dashboard.mycommunications.facebook=Facebook
dashboard.mycommunications.sms=SMS
dashboard.mycommunications=My communications
dashboard.newclient.tooltip=New customers today
dashboard.newclient=New lead
dashboard.newopportunity.tooltip=New opportunities today
dashboard.newopportunity=New opportunity
dashboard.notconnected.tooltip=Calls that have not connected with an agent and in parentheses calls whose customer hangs up during a transfer
dashboard.notconnected=Call lost
dashboard.noteretard=Late note
dashboard.notetodo=Notes to complete
dashboard.origins=Sources
dashboard.rdv.tooltip=Appointment scheduled in the next 7 days
dashboard.rdv=Appointment (7 days)
dashboard.subscribed=Subscribed
dashboard.takeover.tooltip=Take over's today
dashboard.takeover=TO today
dashboard.taskdelivery=Delivery task
dashboard.tasklate.tooltip=Opportunities with a late task
dashboard.tasklate=Late task
dashboard.tasknotfinish.tooltip=Opportunity with incomplete task sent to another user
dashboard.tasknotfinish=Incomplete task sent
dashboard.taskprovider=Task provider
dashboard.tasksoon.tooltip=Opportunities with a task to be completed soon
dashboard.tasksoon=Tasks to do
dashboard.todo=To do
dashboard.totalcall=Total number of calls
dashboard.unsubscribed=Unsubscribed
dashboard.usernamenotfound=Unable to find a user that corresponds to your search
dashboard.view.complete=Complete view
dashboard.view.simple=Simple view
dashboard.view=View
dashboard.waiting.contacted.tooltip=Opportunities with a task pending when the client has already been contacted
dashboard.waiting.contacted=Waiting to be contacted
dashboard.waiting.notcontacted.tooltip=Opportunities waiting for a response with an email, task or form 
dashboard.waiting.notcontacted=Waiting for response - not contacted
dashboard.waiting.tooltip=Opportunities waiting for a response with an email, task or form 
dashboard.waiting=Waiting
dashboard=Dashboard
dataauthor.copy.url=Copy url
dataauthor.delete=Delete this entry
dataauthor.deletealltxt=Are you sure you want to delete all these entries ?
dataauthor.deletetxt=Are you sure you want to delete this entry ?
dataauthor.drag=Drag and drop to sort
dataauthor.modify=Modify Name
dataauthor.modifymetadata=Modify name, description and metadata
dataauthor.numberofentry=Number of entries
dataauthor.rotateleft=Rotate image to the left
dataauthor.rotateright=Rotate image to the right
dataauthor.save=Apply a new name
dataauthor.writesomething=Write something.....
datatable.addcallnote=Add a new call note
datatable.adddefault=Add default values
datatable.addentry=Add a new line
datatable.addentrygroup=Edit a group of lines
datatable.addNote=Add a note
datatable.addtext=Add a new text
datatable.addView=Add a view
datatable.colvis.month=Monthly visibility
datatable.colvis=Columns visibility
datatable.colvisEdit=Columns
datatable.colvisRestore=Restore visibility
datatable.deleteView.confirm=Are you sure you want to delete this view?
dataTable.edit.selected=Edit selected rows
datatable.export.csv.price=Export prices to CSV
datatable.export.csv=Export to CSV
datatable.export.excel.price=Export prices to excel
datatable.export.excel=Export to excel
datatable.export.pdf=Export to PDF
datatable.export=Export data
datatable.exportData=Export data
datatable.grid.view=Grid view
datatable.grouped.view=Grouped view
datatable.hide.columns=Hide / Show columns
datatable.hideAll=Hide all
datatable.importfromexcel=Import from an excel file
datatable.makeGeneralView=Share view with everyone
datatable.modifyView=Modify view
datatable.no.view=No view selected
datatable.noteClient=Client
datatable.noView=No view
dataTable.phone.no=Without number
dataTable.phone.yes=With number
datatable.refresh=Refresh
datatable.resetState=Reset
datatable.rows.all=All
datatable.rows=rows
datatable.search=Search
datatable.selectAll.title=All
datatable.selectAll=Select all
dataTable.selection.allSelected.clear=Clear selection
dataTable.selection.allSelected=All {0} elements are selected.
dataTable.selection.error.selected=Select at least one element in the table
dataTable.selection.selected.button=Select all {0} elements
dataTable.selection.selected={0} selected elements
datatable.selectNone=Deselect all
datatable.selectPage.title=Page
datatable.selectPage=Select this page
datatable.selectView=Select view
datatable.showAll=Show all
datatable.statistics.filter=Statistics filter
datatable.statistics.view=Statistics view
datatable.table.view=Table view
datatable.toggleGridMode=Toggle grid/table
datatable.validateimportproduct=Validate all selected products
datatable.view.confirm.delete=Are you sure you want to remove this view?
datatable.view.cookieName=Cookie name
datatable.view.deleted=View successfully deleted 
datatable.view.filter=Filter
datatable.view.name=Name
datatable.view.profile=Profile
datatable.view.set.default=Set default views
datatable.view.state=State
datatable.view.useable=Useable
datatable.view.user=Owner
datatable.view=Table view
datatable.viewName=Name
datatable.viewPublic=Everyone can view
datatable.views.add.default=Create all views bydefault 
datatable.views.add=Create a view
datatables.page.first=First
datatables.page.info.filtered=(filtered from {0})
datatables.page.info={0} - {1} of {2}
datatables.page.last=Last
datatables.page.next=Next
datatables.page.previous=Previous
date.approved=Approved
date.converted.to.cotation=Converted to quotation
date.creation=Created
date.day=Day
date.dayoftheweek=Day of the week
date.expired=Expired
date.last.modified=Last modification
date.month.10=October
date.month.11=November
date.month.12=December
date.month.1=January
date.month.2=February
date.month.3=March
date.month.4=April
date.month.5=May
date.month.6=June
date.month.7=July
date.month.8=August
date.month.9=September
date.month=Month
date.sold=Sold
date.traded=Traded
date.week=Week
date.year=Year
date=Date
dateFormat=yyyy-MM-dd
dateFormatjs=YYYY-MM-DD
dateFormatWithTime=yyyy-MM-dd hh:mm a
dateFormatWithTimeForFiles=yyyy-MM-dd_hh-mm_a
day.first.letter=d
day=day
days.after.now=days after today
days.ago.short={0}d ago
days.before.now=days before today
days=days
dealervu=DealerVu
debug.complete=Completed
debug.create.event=Create Event
debug.create.resource=Create Resource
debug.date=Date
debug.delete.event=Delete Event
debug.delete.resource=Delete Resource
debug.email.list=Email List
debug.fix.parser.error.default=An error occured while parsing the email.
debug.fix.parser.error.noMessageFound=No message found for this ext ID.
debug.fix.parser.error.parser=The email analysis failed. Please add the necessary conditions to the parser.
debug.fix.parser.success=The message has been parsed.
debug.get.resource=Get Resource
debug.migration=Migration
debug.sync.workflow=Synchronize workflow calendar
debug.title=Test tags for client search
debug.update.calendar=Update calendar
debugVehicle.index=Debug Import Vehicule
december=December
default.add.label=Add {0}
default.blank.message=Property [{0}] of class [{1}] cannot be blank
default.boolean.false=False
default.boolean.true=True
default.button.add.label=Add
default.button.cancel.label=Cancel
default.button.close.label=Close
default.button.continue.label=Continue
default.button.create.label=Create
default.button.delete.confirm.message=Are you sure?
default.button.delete.label=Delete
default.button.edit.label=Edit
default.button.join.label=Join
default.button.no=No
default.button.ok=Ok
default.button.save.label=Save
default.button.update.label=Update
default.button.yes=Yes
default.create.label=Create {0}
default.created.message={0} {1} created
default.date.format=yyyy-MM-dd HH:mm:ss z
default.deleted.message={0} {1} deleted
default.doesnt.match.message=Property [{0}] of class [{1}] with value [{2}] does not match the required pattern [{3}]
default.edit.label=Edit {0}
default.error=An unexpected error occurred, please contact support.
default.home.label=Home
default.invalid.creditCard.message=Property [{0}] of class [{1}] with value [{2}] is not a valid credit card number
default.invalid.email.message=Property [{0}] of class [{1}] with value [{2}] is not a valid e-mail address
default.invalid.max.message=Property [{0}] of class [{1}] with value [{2}] exceeds maximum value [{3}]
default.invalid.max.size.message=Property [{0}] of class [{1}] with value [{2}] exceeds the maximum size of [{3}]
default.invalid.min.message=Property [{0}] of class [{1}] with value [{2}] is less than minimum value [{3}]
default.invalid.min.size.message=Property [{0}] of class [{1}] with value [{2}] is less than the minimum size of [{3}]
default.invalid.range.message=Property [{0}] of class [{1}] with value [{2}] does not fall within the valid range from [{3}] to [{4}]
default.invalid.size.message=Property [{0}] of class [{1}] with value [{2}] does not fall within the valid size range from [{3}] to [{4}]
default.invalid.url.message=Property [{0}] of class [{1}] with value [{2}] is not a valid URL
default.invalid.validator.message=Property [{0}] of class [{1}] with value [{2}] does not pass custom validation
default.list.label={0} List
default.new.label=New {0}
default.not.deleted.message={0} {1} could not be deleted
default.not.equal.message=Property [{0}] of class [{1}] with value [{2}] cannot equal [{3}]
default.not.found.message={0} not found with id {1}
default.not.inlist.message=Property [{0}] of class [{1}] with value [{2}] is not contained within the list [{3}]
default.not.unique.message=Property [{0}] of class [{1}] with value [{2}] must be unique
default.null.message=Property [{0}] of class [{1}] cannot be null
default.number.format=0
default.optimistic.locking.failure=Another user has updated this {0} while you were editing
default.paginate.next=Next
default.paginate.prev=Previous
default.show.label=Show {0}
default.updated.message={0} {1} updated
define.general.config=Define general configurations
define.workflow.config=Define workflow configurations
delay=Delay
delete.confirm=Are you sure?
delete.this.page.data=Delete this page data
delete=Delete
deleted.ressource=Deleted ressource
denied.gain.access=To obtain access; please contact an administrator of your company.
department.companyName=Company name
department.confirm.delete=Do you really want to delete this branch?
department.create=Create a branch
department.dmsDealer.lars=Lars
department.dmsDealer.lautopak=Lautopak
department.edit=Edit branch
department.federalTax=Federal Taxes
department.groups=Departments
department.label=Branch
department.lautopakStoreId=Lautopak Store ID
department.provincialTax=Provincial taxes
department.stripeAccount=Stripe Account
department=Branch
departmentgroup.defaultFromEmail.invalid=Traction Email invalid : {0}
departmentgroup.defaultFromEmail=Traction Email
departmentgroup.defaultUserFromEmail.hint=Build a dynamic email address with the username of the users of this department. For example service_[username]@moto.ca will give <NAME_EMAIL> for a user with phil as username.
departmentgroup.defaultUserFromEmail.invalid.char=Email user Traction invalid : {0}. Cannot include %.
departmentgroup.defaultUserFromEmail.invalid.localpart=Email Invalid User Traction: {0}. The local part of the email has a limit of 30 characters excluding the user number.
departmentgroup.defaultUserFromEmail.invalid.username=Email user Traction invalid : {0}. [username] missing in the email.
departmentgroup.defaultUserFromEmail.invalid=Email user Traction invalid : {0}
departmentgroup.defaultUserFromEmail.localpart=Email user Traction invalid : {0}. The local part has a 30 characters limit excluding the username.
departmentgroup.defaultUserFromEmail=User's Traction email
departmentGroup=Department
departmentgroups.goal.tooltip=Departments goal:
departments=Branches
dept.dealer=Dealer's DMS
dept.dealerId=DMS Dealer ID
dept.default.from=default email sender
dept.deleted=Branch deleted
dept.existing=Existing branches
dept.goal.day=Daily sales goal (for sales dashboard)
dept.goal.month=Monthly sales goal (for sales dashboard)
dept.groups=Departments
dept.name=Branch name
dept.new=Create a new branch
dept.notdeleted=There must be at least one other branch and no user in the branch to be able to delete it.
dept.notsaved=Branch not saved
dept.saved=Branch saved
dept.title=Branches
description.add.remark=Add a remark
description=Description
details.expand=Show / Hide details
details=Details
detected=detected
digital.storage.config=Digital storage configuration
display.on=Display on
display=Display
Distance.Unit.FEET=ft
Distance.Unit.INCHES=in
Distance.Unit.METERS=m
Distance.Unit.MILLIMETERS=mm
dms.add=Add a DMS
dms.added=DMS added
dms.config.color=Zii status color
dms.config.element=Element
dms.config.status=Status/State
dms.config.title=Dms global configuration
dms.config.value=Value
dms.deleted=DMS deleted
dms.dmsId=dmsID
dms.edit=Edit DMS
dms.extIds.notEmpty=DMS is linked to external IDs, it cannot be deleted
dms.name=DMS Name
dms.none=No DMS
dms.source=Source
dms=DMS
document.addFile=Add
document.edit=Edit File name
document.price=Price List
document.promotion=Promotion
document.warranties=Warranties
documents.opportunity=Opportunity pictures
documents.opportunityOption=Accessorie images
documents.trade=Trade pictures
done=Done
download.attached.files=Download attached files:
download.image=Download image
download=Download
ducati=Ducati
duration=Duration
dutchmen=Dutchmen
edit.bulk.apply.with.old.values=Add to current values
edit.bulk.empty.value=Remove current value
edit.goals=Edit goals
edit.saved=Modification saved
edit=Edit
email.not.received=Email not received?
email.placeholder=<EMAIL>
empty=Empty
endDate=End date
ensure.forward.motion.with=Ensure forward motion with
enter.client.firstname=Enter the client's first name
enter.client.lastname=Enter the client's last name
enter.client.name=Enter the client's name
enter.entreprise.name=Enter the entreprise name
enter.labor.info=Enter labor information
enter.part.info=Enter parts information
enter.status=Opportunities entered in this status
enter.vehicle.name=Enter the vehicle name
enterprise.create=Create an enterprise
enterprise.email.nullable.error=Email is required. If the client does not have an email, enter "nomail"
Enterprise.list=Enterprise list
enterprise.name=Company Name
Enterprise.relatedClients.associate=Associate an enterprise
Enterprise.relatedClients=Related enterprises
enterprise.search=Search an enterprise
enterprise=Enterprise
enterpriseContact.email.email.invalid=Email is invalid
enterpriseContact.name.nullable=Name is required
enterprisecontacts=Enterprise contacts
entreprise.search=Search for an entreprise
Entreprise=Enterprise
erase=Erase
error.exception=Exception
error.grail=Grails Runtime Exception
error.message=Message
error.notfound.message=If this is unexpected behavior please contact us
error.notfound.title=Page Not Found
error.notfound=Page not found with this URL
error.occurred.message=Please try your last actions again. If the issue persists, please contact us
error.occurred=An error has occurred
error.option.distinct.category=All distinct categories from inventory already exist
error.option.distinct.categorykijiji=All Kijiji categories already exist
error.option.distinct.categorytrader=All Trader categories already exist
error.option.distinct.magentodata_visibility=All magentodata visibility categories already exist
error.option.distinct.make=All vehicle makes from inventory already exist
error.path=Path
error.role=You do not have the permissions to perform this action.
error.title=Error
error.unsupportedBrowser=<b>Unsupported Browser!</b> This form will offer limited functionality and may not work properly in this browser. We highly suggest to use the most recent version of Google Chrome.
estimate=Estimate
event.destination.magento=MAGENTO
event.hook.workflow=WORKFLOW
events.eventprofile=Events form
events.events=Events
events.registration=Registration
events.surveyprofile=Survey profile
example=Example
exit.status=Opportunities comming out of this status without completing a sale
export.client.clearfilter=Clear filters
export.client.columns=Columns
export.client.filter.reset=Reset
export.client.filter.selectAll=Select All
export.client.filter=Filter
export.client.views=Views
export.copy=Copy
export.csv=CSV
export.data=Export Data
export.edit.error=There is no row selected
export.excel=Excel
export.pdf=PDF
export.print=Print
extension.create=Create extension
extension.delete=Delete extension
extension.edit=Edit extension
extension=Extension
extensions=Extensions
external.identifier=External identifier
extID.add=Add an external ID
extID.added=External ID added
extID.removed=External ID removed
extID=External ID
facebook.ask.email.label=Request the client's email
facebook.ask.email=May I have your email?
facebook.ask.phonecell.label=Request the client's phone number
facebook.ask.phonecell=May I have your phone number?
facebook.contact.delai.expired=The 7 day deadline for contacting the customer via this Facebook page has expired.
facebook.contact.delai=You have until {0} to contact the customer via this Facebook page.
facebookmessage.file.too.big=The file {0} is too large.\nThe maximum size must be under 25 Mo.
facebookmessage.sent.error=Facebook message not sent. Verify the selected facebook page.
facebookmessage.sent=Facebook message sent
facebookPage.accessToken=Access token
facebookpage.add=Add a facebook page
facebookpage.cannot.contact.client=You cannot contact the customer with this Facebook page. The customer must first have contacted you on this page so that you can reply to them.
facebookpage.confirm.delete=Do you really want to delete this facebook page?
facebookpage.edit=Edit a Facebook page
facebookPage.pageId=Page ID
facebookPage=Facebook page
false=False
feature.comingsoon=Feature coming soon
february=February
feed.config.action.delete=Are you sure you want to delete this feed configuration action
feed.config.action.error=Cannot save this feed configuration action...
feed.config.action.success=Feed configuration action saved !
feed.config.action=action
feed.config.delete=Are you sure you want to delete this feed configuration
feed.config.error=Cannot save this feed configuration...
feed.config.preview=preview
feed.config.rule.delete=Are you sure you want to delete this feed configuration rule
feed.config.rule.error=Cannot save this feed configuration rule...
feed.config.rule.success=Feed configuration rule saved !
feed.config.success=Feed configuartion saved !
feed.delete.config.error=Cannot delete this feed configuration...
feed.delete.config.success=Feed configuration deleted !
feed.duplicate=Duplicate
feed.edit=Modify
feed.filename=Feed file name
file.browse=Browse
file.download.blocked=File download not allowed by the current configuration
file.upload=Upload
filedata.author=Author
filedata.category=Category
filedata.create.error=File can not be added
filedata.create.success=File added
filedata.dataSize=Size
filedata.date=Date
filedata.delete.confirm=Do you really want to delete this file
filedata.delete.error=File cannot be deleted
filedata.delete.success=File deleted
filedata.description=Description
filedata.dimensions=Dimensions
filedata.id=ID
filedata.metadata.byLine=Author
filedata.metadata.byLineTitle=Author title
filedata.metadata.captionAbstract=Caption description
filedata.metadata.category=Category
filedata.metadata.city=City
filedata.metadata.copyrightNotice=Copyright
filedata.metadata.countryPrimaryLocationName=Country Location Name
filedata.metadata.credit=Credit
filedata.metadata.dateCreated=Date created
filedata.metadata.headline=Headline
filedata.metadata.keywords=Keywords
filedata.metadata.objectName=Document title
filedata.metadata.originalTransmissionReference=Original, Reference
filedata.metadata.provinceState=Province/State
filedata.metadata.source=Source
filedata.metadata.specialInstructions=Special instructions
filedata.metadata.subLocation=Sublocation
filedata.metadata.supplementalCategory=Supplemental category
filedata.metadata.urgency=Urgency
filedata.metadata.writerEditor=Caption writer
filedata.name=Name
filedata.path=Path
filedata.rename.error=File can not be renamed
filedata.rename.success=File renamed
filedata.status=Type
filter.add.confition=Add a condition
filter.apply=Apply filters
filter.count.result={0} {1} match this filter
filter.create=Create a filter
filter.delete=Delete
filter.deleted=Filter successfully deleted
filter.department=Filter by branches
filter.duplicate=Duplicate filter
filter.edit=Edit filter
filter.get.count=Get result count
filter.get.criteria=Get generated criteria code
filter.history.category=Category
filter.history.status=Status
filter.id.count.result={0} {1} match {2}
filter.list.client=List of filters: Client
filter.list.data=See the list
filter.list.lautopakpartsinvoice=List of filters: Parts invoice
filter.list.opportunity=List of filters: Opportunity
filter.list.workorder=List of filters: Work order
filter.list=List of filters
filter.name=Filter name
filter.not.deleted.used.clientProcess=The filter cannot be deleted since it is used in the following client process: {0}
filter.not.deleted.used=The filter cannot be deleted since it is used in the following filters: {0}
filter.not.deleted=Error removing filter
filter.not.saved=Filter not saved
filter.opportunity.category=Category
filter.opportunity.status=Status
filter.origins=Sources
filter.queryBuilder=Query builder
filter.recursive.error=Cannot save a recursive filter
filter.select.to.load=Select a filter to load
filter.task.status=Status
filter.user=User
filter.usergroup=Department
filter.usergroups=Departments
filter=Filter
filtermenu.actions.noSelected=You need to select rows first
filtermenu.actions=Bulk actions
filtermenu.apply=Apply filters / Refresh content
filtermenu.communication=Send bulk communication
filtermenu.expand=Expand/Collapse advanced send
filtermenu.filtre=Filter
filtermenu.refresh=Refresh data
filtermenu.reset=Clear filters
filters=Filters
financing.banks=Financing banks
financing.scenario.apply.conditions=Apply scenario according to those conditioned to:
financing.scenario.bank=Bank
financing.scenario.base.data=Base datas of the scenario
financing.scenario.calculate=Calculate financing scenarios
financing.scenario.create=Create a scenario
financing.scenario.define=Define the financing scenarios
financing.scenario.edit=Edit scenario
financing.scenario.garranty=Garranty
financing.scenario.insurance=Insurance
financing.scenario.interestRate=Interest rate
financing.scenario.name.placeholder=Name the scenario
financing.scenario.name=Scenario name
financing.scenario.price=Price
financing.scenario.protection=Protection
financing.scenario.select=Select the financing scenario
financing.scenario.term.type=Term type
financing.scenario.term=Term
financing.scenario.value=Value
financing.scenario.vehicle.state=Vehicle state
financing.scenario.vehicle.type=Vehicle type
financingScenario.termType.biweekly=Bi-weekly
financingScenario.termType.monthly=Monthly
financingScenario.termType.semimonthly=Bi-monthly
financingScenario.termType.weekly=Weekly
first.opportunity.only=First opportunity only
focus.enable=Enable focus
followup.potential.opportunity=Follow-up of potential opportunity categories
form.actions=Actions
form.assigned=Assigned to
form.auth=Authorization required
form.authorization=Authorization
form.authorized=Authorized
form.authorizedBy=Authorized by
form.awaiting=Create a pending notification
form.cancel=Cancel
form.clear=Clear
form.client.cellphone=Client Cellphone
form.client.email=Client Email
form.client.name=Client Name
form.client.phone=Client Phone
form.client=Client
form.comment=Comment
form.consent.accept=I accept
form.consent.data=Consent Text
form.consent.refuse=I refuse
form.consent=Enable consentment
form.consented.no=No
form.consented.yes=Yes
form.consented=Consented
form.consentmentDefault=You are about to send this form. Are you sure you want to send it now?
form.consentmentTitle=Consent
form.create=Create new page
form.css=CSS
form.date=Date
form.delete.confirm=Are you sure you want to delete this form and all its elements?
form.edit=Edit page
form.element.actions=Actions
form.element.assignList=User assignment list
form.element.attachments=Attachment
form.element.auth=Authorization
form.element.authorization=Authorization
form.element.backgroundColor=Background color
form.element.checkbox=Checkbox
form.element.chooseProduct=Choose product label
form.element.clientProduct=Client Product
form.element.clientWorkOrder.addButton=Add button text
form.element.clientWorkOrder.description=Job description
form.element.clientWorkOrder.removeButton=Remove button text
form.element.clientWorkOrder.subtitle=Job subtitle
form.element.clientWorkOrder=Client Work Order
form.element.color=Text color
form.element.configurator=Configurator
form.element.consent=Consentment
form.element.css=CSS code
form.element.date=Date
form.element.defaultOn=Selected by default
form.element.defaultSelected=Default Selected
form.element.delete.confirm=Are you sure you want to delete the following element: {0}?
form.element.deparmtentalselect=Branch List
form.element.departmentToShow=Branches to display
form.element.disabled=Disable this element from the form 
form.element.edit=Edit Form Element
form.element.error=Value of {0} is not valid.
form.element.hidden=Hide the form element
form.element.hide=Hidden field
form.element.html=Custom text
form.element.htmlSource=HTML code
form.element.inputBrowseButton=Input attachments button text
form.element.inputTitle=Input attachments title
form.element.interest=Interest label
form.element.interestsElement=Interests
form.element.label.email=Email
form.element.label.firstname=First name
form.element.label.lastname=Last name
form.element.label.submit=Submit
form.element.label.title=Field Title
form.element.label=Field Label
form.element.library.cancel=Cancel
form.element.library.error=There is no profile found.
form.element.library.fileLink=Add file
form.element.library.title=Adding Files
form.element.limitSize=Limit Upload Size (Mo)
form.element.make=Make label
form.element.meta=Meta Data Code
form.element.model=Model label
form.element.multiselect=Multiple selection
form.element.name=Element Name
form.element.numLine=Number of lines
form.element.options=Select options
form.element.optionsrule=One option per line
form.element.order=Element Order
form.element.ordre=Element Order
form.element.pick=Pick a profile then an event from the list
form.element.position.bottomBody=Bottom of the page body
form.element.position.bottomHead=Bottom of the page header
form.element.position.inline=In-line
form.element.position.topBody=Top of the page body
form.element.position.topHead=Top of the page header
form.element.position=Position
form.element.primary=Make the element primary
form.element.radio=Radio
form.element.rating.defaultError=There was a problem trying to retrieve the main elements already defined
form.element.rating.formNotFound=No form has been found.
form.element.rating.primaryActiveQuestion=You will override the primary rating already defined :
form.element.rating=Rating
form.element.required=Require an element
form.element.script=Javascript Code
form.element.select=Select
form.element.sendOnly=Send information via email only
form.element.serialnumber=Serial number label
form.element.signature.header=Electronic signature authorization
form.element.signature=Signature
form.element.size=Bootstrap field size (1-12)
form.element.source=Source
form.element.starsNumber=Number of stars
form.element.submit=Submit
form.element.textBox=Text Box
form.element.textEditor=Text Editor
form.element.textField=Text Field
form.element.title=Elements
form.element.type=Element Type
form.element.usergroups=Department List
form.element.usergroupsToShow=Departments to display
form.element.validator.checked=Checked
form.element.validator.date=Valid date
form.element.validator.driverlicense=Driver's License
form.element.validator.email=Email
form.element.validator.none=None
form.element.validator.notempty=Not empty
form.element.validator.phone=Phone
form.element.validator.ramq=RAMQ
form.element.validator.selected=Selected
form.element.validator.zipcode.canada=Canadian Postal Code 
form.element.validator.zipcode.usa=USA Zip Code
form.element.year=Year label
form.elements=Elements
form.email.customer=Send email to client
form.email.data=Email Text
form.email.thanks=Customer thank you page
form.email.user=Sales rep email notification
form.error.authorization=The authorization code is not valid.
form.error.field=is not valid.
form.error.required=The following element is required :
form.error=An error occured while saving. The fields are not correctly filled in or the name (and type) is already used in the form.
form.event.create=Create new event
form.event.delete.confirm=Are you sure you want to delete the following event: {0}?
form.event.displayOldEvents=Display old events
form.event.edit=Edit Event
form.event.fromdate=From
form.event.images=File Library
form.event.lead=Leads
form.event.name=Name
form.event.noClient=Client not found
form.event.noOpportunity=Opportunity not found
form.event.notificationEmail.error=There is no template available for email notifications. Please, add the template through configuration.
form.event.notificationEmail.lang=Email language for notified user's
form.event.notificationUsers=User's notified
form.event.nouser=No assigned user
form.event.profile=Form
form.event.sendworkflow=Send to workflow
form.event.sendworkflowdep=Send to workflow by branch
form.event.table.title=List of filled out forms
form.event.todate=To
form.event.users=User's assigned
form.event.widget.copy.confirm=Widget copied
form.event.widget.copy=Copy widget
form.event.widget=Widget
form.event.workflow.select=Select a workflow to use
form.event.workflow=Workflow
form.event.workflowdefault=Workflow by default
form.event=Event
form.formFilled=New form filled out
form.id=ID
form.javascript=Javascript
form.mailchimp.apikey=Mailchimp API Key
form.mailchimp.listid=Mailchimp list ID
form.mailchimp=Enable mailchimp
form.maketask=Create a task
form.menu.data=Form data
form.menu.event=Events
form.menu.profile=Forms
form.menu.review=Product review
form.menu.title=Forms
form.name=Name
form.newForm=Regular
form.nocontent=No content.
form.opportunity=Opportunity
form.opportunityType=Opportunity type
form.ordre=Order
form.other=Other
form.preview=Preview
form.profile.actions=Actions
form.profile.backgroundimage=Background Image
form.profile.copy.confirm=URL Copied
form.profile.copy=Copy URL
form.profile.create=Create a form
form.profile.dailysummary.description=Enable daily summary
form.profile.dailysummary.error=There is currently no template for the daily summary. Please add the template through configuration.
form.profile.dailysummary.lang=Daily summary email language
form.profile.dailysummary=Daily summary
form.profile.date=Creation date
form.profile.decoration=Decoration
form.profile.delete.confirm=Are you sure you want to delete this profile, {0} events and {1} forms?
form.profile.edit=Edit a form
form.profile.events=Events
form.profile.font=Font (.TTF or .WOFF only)
form.profile.forms=Pages
form.profile.fromdate=From
form.profile.image.list=Image list
form.profile.naked.no=No
form.profile.naked.yes=Yes
form.profile.naked=No menu
form.profile.name=Name
form.profile.notifyAssignedUser=Send email notification to notified user's
form.profile.police=Font (.TTF or .WOFF only)
form.profile.profile=Form
form.profile.redirectAssignedUser=Automatic redirection to the first page after 10 seconds
form.profile.table.title=List of events
form.profile.todate=To
form.profile.url=URL
form.public.event=Events
form.public.lang=Lang
form.public.profile=Profile
form.review.question=Question
form.review.rating=Rating
form.satisfaction=Review
form.select.event=Event selection
form.subForm=Add information to the previous form
form.success=Success
form.template.delete.confirm=Are you sure you want to delete the following template: {0}?
form.thanks.data=Edit the customized thank you page
form.thanks.enabled=Enable customized thank you page
form.thanks=Customized thank you page
form.title=Form
form.type=Page type
form.unauthorized=Not authorized
form.validator=Validator
form.waiver=Waiver
formatToDayAbbreviatedMonth=d MMM
fr=Fr
freepbx.base=Telephone base
freepbx.list.hint=as a comma-separated list
freepbx.privateCallNumbers=Phone numbers with private calls only
freepbx.reception.queue=Reception queue
freepbx.reception.return=Return to reception queue
freepbx.reception.user.queue=Reception user extensions
freepbx.recording.server.password=Recording server password
freepbx.recording.server.url=Recording server URL
freepbx.recording.server.user=Recording server user
freepbx.user.interface.password=User interface password
freepbx.user.interface=User interface
freepbx.user.read.password=User read password
freepbx.user.read=User read
freepbx.user.write.password=User password for writing
freepbx.user.write=User writing
freepbx.voicemailEmailFrom=Voicemail email address
from=From
Fullscreen=Fullscreen
gender.men=Male
gender.na=N/A
gender.notsay=Rather not say
gender.women=Female
general.config.table=Define general configurations
general.import.config.action.error=Unable to save this importation action...
general.import.config.action.success=Importation action saved !
general.import.config.delete=Are you sure you want to delete this importation profile
general.import.config.error=Unable to save this importation profile...
general.import.config.rule.error=Unable to save this importation rule...
general.import.config.rule.success=Importation rule saved !
general.import.config.success=Importation profile saved !
general.import.delete.config.error=Unable to delete this importation profile...
general.import.delete.config.success=Importation profile deleted !
goal.appointment=Appointment
goal.calldone=Call done
goal.createclient=Create client
goal.emailsent=Email sent
goal.newopportunity=New opportunity
goal.notes=Notes
goal.opportunitysold=Opportunities sold
goal.smssent=Sms sent
goal.taskcreated=Task created
GoogleGmailWatchJob.status=Google Gmail Watch Job
goto.inbox=Go to inbox
goto.serviceschedule=Go to workflow schedule
goto.task.calendar=Go to task calendar
goto.workschedule=Go to work schedule
groupe_beneteau=Groupe Beneteau
groupedstatistics.groupProperty.category.null=No category
groupedstatistics.groupProperty.category=Category
groupedstatistics.groupProperty.categoryKijiji.null=No Kijiji category
groupedstatistics.groupProperty.categoryTrader.null=No Trader category
groupedstatistics.groupProperty.dateDayOfWeek=Day of the week (creation date)
groupedstatistics.groupProperty.dateSoldDayOfWeek.null=No sold date
groupedstatistics.groupProperty.dateSoldDayOfWeek=Day of the week (date sold)
groupedstatistics.groupProperty.dateSoldYear.null=No sold date
groupedstatistics.groupProperty.dateSoldYear=Year (date sold)
groupedstatistics.groupProperty.dateSoldYearMonth.null=No sold date
groupedstatistics.groupProperty.dateSoldYearMonth=Month (date sold)
groupedstatistics.groupProperty.dateYear=Year (creation date)
groupedstatistics.groupProperty.dateYearMonth=Month (creation date)
groupedstatistics.groupProperty.line.null=No line
groupedstatistics.groupProperty.make.null=No make
groupedstatistics.groupProperty.media=Media
groupedstatistics.groupProperty.model.null=No model
groupedstatistics.groupProperty.origin.null=No source
groupedstatistics.groupProperty.origin=Source
groupedstatistics.groupProperty.product.isUsed.null=No data
groupedstatistics.groupProperty.salesUsed=Vehicle type New/Used
groupedstatistics.groupProperty.status=Status
groupedstatistics.groupProperty.subCategory.null=No sub-category
groupedstatistics.groupProperty.userBdc.null=No BDC user assigned
groupedstatistics.groupProperty.userBdc=BDC
groupedstatistics.groupProperty.userSales.null=No sales user assigned
groupedstatistics.groupProperty.userSales=Sales user
groupedstatistics.groupProperty.vehicle.category.null=No category
groupedstatistics.groupProperty.vehicle.category=Vehicle category
groupedstatistics.groupProperty.vehicle.interest.null=No vehicle interest
groupedstatistics.groupProperty.vehicle.interest=Vehicle interest
groupedstatistics.groupProperty.vehicle.isUsed.null=No vehicle linked
groupedstatistics.groupProperty.vehicle.make.null=No vehicle make
groupedstatistics.groupProperty.vehicle.make=Vehicle make
groupedstatistics.groupProperty.vehicle.subCategory.null=No sub-category
groupedstatistics.groupProperty.vehicle.subCategory=Vehicle sub-category
groupedstatistics.groupProperty.year.null=No year
gsuite.api.attachment=Attachment path
gsuite.api.domain=Domain
gsuite.api.user=API User
gsuite.api.watcher.topicname=Watcher Topic Name
gsuite.config=Configure the gsuite
gsuite.credentials=Credentials
gsuite.from.email=From emails
gsuite.save=Save
gsuite.scope=Scope
gsuite.system.user=System user
gsuite.watch.email=Watch emails
headers.colors=Column header colors
here=here
hide=Hide
histories=Histories
History.Action.CREATE=Create
History.Action.DELETE=Delete
History.Action.OTHER=Other
History.Action.UPDATE=Update
history.action=Action
history.aftervalue=Value changed
history.beforevalue=Previous value
history.category=Category
history.date=Date
history.details=Details
history.domainClassId=Object
history.location=location
history.modify.by=Modified by
history.points.month=Points this month
history.points.today=Points today
history.points.total=Total points
history.points=Points history
history.scores=Score history
history.status.isActivity=Activity
history.status.isClientAction=Client action
history.status=Status
history.target=User concerned
history.user=User
history.userGroup=Department
history.userId=User ID
history.userName=Username
history=History
host=Host
hour.first.letter=h
hour=Hour
hours.ago.short={0}h ago
icon.category=Type
icon.class=Class
icon.classofpart=Parts class
icon.classofsale=Sales class
icon.classofservice=Service class
icon.classofshipping=Shipping class
icon.color=Color
icon.create=Create a new icon
icon.deleted=Icon deleted.
icon.existings=Existing icons
icon.html=Html
icon.icon=Icon
icon.notfound=Icon not found.
icon.notsaved=Icon not saved, please make sure you specify all required information.
icon.page.title=System icons management
icon.preview=Preview
icon.saved=Icon saved.
icon.title=Title
icon.workorder=Work order
icon.workorderjob=Work order job
id=ID
image.type.not.supported=Format not supported by browser.
important=Important
in.minutes=In minutes
in1day=In a day
in1hour=In an hour
in1month=In a month
in1week=In a week
inbox.isImportant=Filter important only
inbox.isUnread=Filter unread only
individualClient.email.nullable.error=Email is required. If the client does not have an email, enter "nomail"
individualClient.email.unique.error=A client or an Enterprise with this email already exists
IndividualClient.list=Client list
IndividualClient.relatedClients.associate=Associate a client
IndividualClient.relatedClients=Related clients
Inheritancerule.active.error=Unable to deactivate this inheritance rule...
Inheritancerule.active.success=Inheritance rule activated !
Inheritancerule.already.exists=This inheritance rule already exists !
Inheritancerule.delete.error=Unable to delete this inheritance rule...
Inheritancerule.delete.success=Inheritance rule deleted !
Inheritancerule.delete=Are you sure you want to delete this inheritance rule
Inheritancerule.error=Unable to save this inheritance rule...
Inheritancerule.reorder.error=Unable to reorder inheritance rule...
Inheritancerule.reorder.success=Inheritance rule reordered !
Inheritancerule.success=Inheritance rule saved !
insert.variables=Insert variables
interest=Interests
invalid.filter=Invalid filter
inventory=Inventory
item.cost=Cost
item.created=New item created: {0}
item.delete.confirm=Are you sure you want to delete this item? This action is irreversible.
item.description=Description
item.iprofile=Iprofile
item.list=Item list
item.msrp=MSRP
item.name=Name
item.quantity=Quantity
item.stockNumber=Stock number
item.supplierCode=Supplier code
Item.Type.OTHER=Other
Item.Type.VEHICLE_PART=Part
item.type=Type
item.year=Year
january=January
job.retention.pool=Job retention pool
jobs.in.workorder=Jobs in the work order #{0}
jobs=Jobs
july=July
june=June
keystone=KeyStone
knowledge.base=Knowledge base
label.name=Label name
label.user.can.edit=Editor
label=Label
labels=Labels
labors=Labors
language.english=English
language.french=French
language.spanish=Spanish
lars.sync.status=Lars file synchronization
lars=LARS
last.five.years=Last 5 years
last.x.months=Last {0} months
last180days=Last 180 Days
last30days=Last 30 Days
last365days=Last 365 Days
last7days=Last 7 Days
last90days=Last 90 Days
lastMonth=Last Month
lastmonth=Last month
lautopak.assignUser=Assign a user
lautopak.createOpportunity=Create an opportunity
lautopak.createWorkflowData=Create data in a workflow
lautopak.exceeding.view=Exceeding view
lautopak.forEveryone=For everyone
lautopak.status.accounted=Accounted
lautopak.status.financing_approved=Financing Approved
lautopak.status.fni_in_charge=F&I In Charge
lautopak.status.lost_fni=Lost F&I
lautopak.status.lost_showroom=Lost Showroom
lautopak.status.open=Open
lautopak.status.open_showroom=Open Showroom
lautopak.status.sold_and_delivered=Sold And Delivered
lautopak.status.sold_not_delivered=Sold Not Delivered
lautopak.sync.status=Lautopak DB synchronization (RO, jobs, customers, vehicles, orders, parts reservations and parts invoice)
lautopak.view.in.other=See this data in another page
lautopak.viewer=Viewer
lautopak.workshop.dashboard.chart=Workshop dashboard chart
lautopak.workshop.dashboard=Workshop dashboard
lautopak=Lautopak
lautopakcodemenu.add=Add menu code
lautopakcodemenu.list=List of menu codes
lautopakcodemenu.notconfigured=Menu code not configured
lautopakcodemenu.priority=Priority
lautopakcodemenu.save.unique.error=The menu code must be unique
lautopakcodemenu=Menu code
lautopakorder.deliveryDate=Delivery date
lautopakorder.estimatedReception=Estimated delivery
lautopakorder=Order
lautopakpartreservation.bookingDate=Reservation date
lautopakpartreservation.checked=Checked
lautopakpartreservation.comment=Comment
lautopakpartreservation.department=Branch
lautopakpartreservation.expirationDate=Expiration date
lautopakpartreservation.extId=ID
lautopakpartreservation.link=Invoice
lautopakpartreservation.list=Parts reservation list
lautopakpartreservation.partName=Part name
lautopakpartreservation.partSerialNumber=Part number
lautopakpartreservation.qty=Quantity
lautopakpartreservation.qtyExp=Quantity exp.
lautopakpartreservation.status.available=Available
lautopakpartreservation.status.completereception=Reception complete 
lautopakpartreservation.status.correction=Cancelled
lautopakpartreservation.status.noparts=No parts
lautopakpartreservation.status.notordered=Not ordered
lautopakpartreservation.status.ordered=Ordered
lautopakpartreservation.status.partialreception=Partial reception
lautopakpartreservation.status=Status
lautopakpartreservation.statusLastChange=Last status change
lautopakpartreservation.type=Type
lautopakpartreservation=Part reservation
lautopakpartreservations=Parts reservations
lautopakpartsinvoice.clerkExtId=Clerk
lautopakpartsinvoice.closingDate=Closing
lautopakpartsinvoice.department=Branch
lautopakpartsinvoice.employeeExtId=Employee
lautopakpartsinvoice.lautopakPartReservationStatus=Parts reservation status
lautopakpartsinvoice.lautopakPartReservationStatusLastChange=Last parts reservation status change
lautopakpartsinvoice.list=Parts invoice list
lautopakpartsinvoice.openingDate=Opening
lautopakpartsinvoice.partsTotal=Parts total
lautopakpartsinvoice.roID=#Invoice
lautopakpartsinvoice.serialnumber=Serialnumber
lautopakpartsinvoice.short=PI
lautopakpartsinvoice.status.canceled=Cancelled
lautopakpartsinvoice.status.estimated=Estimated
lautopakpartsinvoice.status.opened=Opened
lautopakpartsinvoice.status.printed=Printed
lautopakpartsinvoice.status=Status
lautopakpartsinvoice.statusLastChange=Last status change
lautopakpartsinvoice.type=Type
lautopakpartsinvoice=Parts invoice
lautopakprovider.create=Create lautopak provider
lautopakprovider.daysForReception=Average number of days for receiving orders
lautopakprovider.dms=DMS
lautopakprovider.edit=Edit lautopak provider
lautopakprovider.extId=Lautopak ID
lautopakprovider.list=Lautopak provider list
lautopakprovider.name=Name
lautopakprovider.order.count30=Number of orders (last 30 days)
lautopakprovider.order.count=Number of orders
lautopakprovider=Lautopak provider
lautopakskill.add=Add a skill
lautopakskill.list=Skills list
lautopakskill.not.deleted=Make sure that no menu code is linked to this skill before deleting it
lautopakskill.root1=Root 1
lautopakskill.root2=Root 2
lautopakskill.root3=Root 3
lautopakskill.title=Create a skill
lautopakskill=Skill
lautopakviewer.add.level=Add a level
lautopakviewer.add=Add a viewer
lautopakviewer.levels=Levels
lautopakviewer.name=Name
lautopakviewer=Viewer
lbool.false=False
lbool.true=True
lead.added=Lead added to Traction
lead.error.client=Client cannot be created, must provide a valid email
lead.error.opportunity.exist=Duplicate Prospect
lead.error.required=Unknown Dealer
leaders.points=Leaders ( in points)
leaders.salesratio=Leaders
leads.advisors=Advisors
leads.all=All opportunities
leads.appointmentsCreated=Appointment task created
leads.appointmentsExpected=Appointment task expected
leads.average=Average
leads.averageDuration=Average duration
leads.averageResponseTime=Average response time
leads.byAdvisor=By advisor
leads.delivered=Delivered
leads.filters.date=Filter by date
leads.filters.origin=Filter by source
leads.filters.userGroups=Filter by department
leads.groupings=Groupings
leads.groupProperty1=Level 1
leads.groupProperty2=Level 2
leads.initDateEnabled=Filter by dates:
leads.instore=In-store opportunities
leads.noViewName=The name of the view is missing.
leads.phoneup=Telephone opportunities
leads.property=Property
leads.renewalLoyalty=Renewal & Loyalty
leads.sales=Sales
leads.salesDeliveries=Sales & Deliveries
leads.salesman=Salesman
leads.showGroups=Show/Hide group
leads.showHideStats=Show/Hide statistics
leads.showHideTable=Show/Hide table
leads.stats=Statistics
leads.takeOver.inStore=In-store take-over 
leads.takeOver.phone=Telephone take-over
leads.takeOver=Take-over
leads.testDrives=Test drives
leads.topStat.active.tooltip=Active opportunities
leads.topStat.active=Active
leads.topStat.activeNoTasks.tooltip=Active opportunities without tasks
leads.topStat.activeNoTasks=Active without tasks
leads.topStat.activeWithoutContactLast30.tooltip=Opportunities with an active category and without any contact from a sales user or BDC in the last 30 days.
leads.topStat.activeWithoutContactLast30=No contact 30d.
leads.topStat.activeWithoutTasks.tooltip=Opportunities with an active category and without any tasks pending
leads.topStat.activeWithoutTasks=Without task
leads.topStat.attempts.tooltip=User attempted to contact the client
leads.topStat.attempts=Attempts
leads.topStat.averageDuration.tooltip=Average duration of calls
leads.topStat.averageDuration=Average duration
leads.topStat.averageFirstResponse.tooltip=Average first response
leads.topStat.averageFirstResponse=Average first response
leads.topStat.delivered.tooltip=Delivered vehicles according to the workflow end date
leads.topStat.delivered=Delivered vehicles
leads.topStat.deliveriesThisMonth.tooltip=Deliveries completed this month based on the workflow data end date
leads.topStat.deliveriesThisMonth=Delivered this month
leads.topStat.firstMeetingAppointment.nowWithSecondMeeting=now within two meetings
leads.topStat.firstMeetingAppointment.tooltip=Opportunities with only one appointment completed and no walk-ins
leads.topStat.firstMeetingAppointment=With an appointment
leads.topStat.firstMeetingAppointmentSales.tooltip=Sales with only one appointment completed and no walk-ins
leads.topStat.firstMeetingAppointmentSales=Sales with an appointment
leads.topStat.firstMeetingWalkin.nowWithSecondMeeting=now in two meetings
leads.topStat.firstMeetingWalkin.tooltip=Opportunities with only one walk-in and no appointment completed
leads.topStat.firstMeetingWalkin=Walk-in
leads.topStat.firstMeetingWalkinSales.tooltip=Sales with only one walk-in and no appointment completed
leads.topStat.firstMeetingWalkinSales=Walk-in sales
leads.topStat.instore.tooltip=In-store opportunities
leads.topStat.instore=In-store
leads.topStat.instoreSales.day.short=Today
leads.topStat.instoreSales.day.tooltip=Today's in-store sales
leads.topStat.instoreSales.day=In-store sales - Today
leads.topStat.instoreSales.month.short=This month
leads.topStat.instoreSales.month.tooltip=In-store sales this month
leads.topStat.instoreSales.month=In-store sales - This month
leads.topStat.instoreSales.short=Date filtered
leads.topStat.instoreSales.tooltip=In-store sales
leads.topStat.instoreSales.week.short=This week
leads.topStat.instoreSales.week.tooltip=In-store sales this week
leads.topStat.instoreSales.week=In-store sales - This week
leads.topStat.instoreSales=In-store sales
leads.topStat.last7Days.tooltip=Opportunities created in the last 7 days
leads.topStat.last7Days=New 7d.
leads.topStat.leads.day.short=Today
leads.topStat.leads.day.tooltip=All opportunities today
leads.topStat.leads.day=All opportunities - Today
leads.topStat.leads.month.short=This month
leads.topStat.leads.month.tooltip=All opportunities this month
leads.topStat.leads.month=All opportunities - This month
leads.topStat.leads.short=Date filtered
leads.topStat.leads.tooltip=All opportunities
leads.topStat.leads.week.short=This week
leads.topStat.leads.week.tooltip=All opportunities this week
leads.topStat.leads.week=All opportunities - This week
leads.topStat.leads=All opportunities
leads.topStat.leadsSales.tooltip=Sales
leads.topStat.leadsSales=Sales
leads.topStat.noContact.tooltip=User has not attempted to contact the client
leads.topStat.noContact=Not contacted
leads.topStat.noContactWeb.tooltip=User has not attempted to contact the client for web opportunities only
leads.topStat.noContactWeb=Not contacted (Web)
leads.topStat.pending.noTasks.short=Pending without tasks
leads.topStat.pending.noTasks.tooltip=Client is awaiting a response from the user and no task has been created
leads.topStat.pending.noTasks=Pending without tasks
leads.topStat.pending.short=Pending
leads.topStat.pending.tooltip=Client is awaiting a response from the user
leads.topStat.pending=Pending
leads.topStat.phoneup.tooltip=Telephone opportunities
leads.topStat.phoneup=Call
leads.topStat.phoneupSales.day.short=Today
leads.topStat.phoneupSales.day.tooltip=Telephone sales today
leads.topStat.phoneupSales.day=Telephone sales - Today
leads.topStat.phoneupSales.month.short=This month
leads.topStat.phoneupSales.month.tooltip=Telephone sales this month
leads.topStat.phoneupSales.month=Telephone sales - This month
leads.topStat.phoneupSales.short=Date filtered
leads.topStat.phoneupSales.tooltip=Telephone sales
leads.topStat.phoneupSales.week.short=This week
leads.topStat.phoneupSales.week.tooltip=Telephone sales this week
leads.topStat.phoneupSales.week=Telephone sales - This week
leads.topStat.phoneupSales=Telephone sales
leads.topStat.points.day.short=Today
leads.topStat.points.day.tooltip=Sum of today's points
leads.topStat.points.day=Points - Today
leads.topStat.points.month.short=This month
leads.topStat.points.month.tooltip=Sum of today's points this month
leads.topStat.points.month=Points - This month
leads.topStat.points.short=Date filtered
leads.topStat.points.tooltip=Sum of points
leads.topStat.points.week.short=This week
leads.topStat.points.week.tooltip=Sum of this week's points
leads.topStat.points.week=Points - This week
leads.topStat.points=Points
leads.topStat.pointsToday.tooltip=History points points today
leads.topStat.pointsToday=Today's points
leads.topStat.pool.tooltip=All opportunities in the pool
leads.topStat.pool=Pool
leads.topStat.reached.tooltip=User successfully contacted the client
leads.topStat.reached=Reached
leads.topStat.scheduledDelivery.tooltip=Opportunities with a delivery scheduled
leads.topStat.scheduledDelivery=Scheduled
leads.topStat.takeOver.tooltip=Take-over
leads.topStat.takeOver=Take-over
leads.topStat.unscheduledDelivery.tooltip=Opportunities sold without a delivery scheduled 
leads.topStat.unscheduledDelivery=Unscheduled
leads.topStat.visitLog.mydepartmentgroups=for all user's in your departments
leads.topStat.visitLog.tooltip=Your non-associated visits and those of your departments
leads.topStat.visitLog=Unassociated visits
leads.topStat.web.tooltip=Web opportunities
leads.topStat.web=Web
leads.topStat.webSales.day.short=Today
leads.topStat.webSales.day.tooltip=Web sales today
leads.topStat.webSales.day=Web sales - Today
leads.topStat.webSales.month.short=This month
leads.topStat.webSales.month.tooltip=Web sales this month
leads.topStat.webSales.month=Web sales - This month
leads.topStat.webSales.short=Date filtered
leads.topStat.webSales.tooltip=Web sales
leads.topStat.webSales.week.short=This week
leads.topStat.webSales.week.tooltip=Web sales this week
leads.topStat.webSales.week=Web sales - This week
leads.topStat.webSales=Web sales
leads.topStat.withappointment.1day.short=Today
leads.topStat.withappointment.1day.tooltip=Opportunities with at least one appointment (to do, done, cancelled or not presented)
leads.topStat.withappointment.1day=With appointment - Today
leads.topStat.withappointment.7day.short=Past 7 days
leads.topStat.withappointment.7day.tooltip=Opportunities with at least one appointment (to do, done, cancelled or not presented)
leads.topStat.withappointment.7day=With appointment - Past 7 days
leads.topStat.withappointment.cancelnoshow.label=cancelled or not presented
leads.topStat.withappointment.late.short=Late
leads.topStat.withappointment.late.tooltip=Opportunities with at least one appointment (to do, done, cancelled or not presented)
leads.topStat.withappointment.late=With appointment - Late
leads.topStat.withappointment.short=Date filtered
leads.topStat.withappointment.tooltip=Opportunities with at least one appointment (to do, done, cancelled or not presented)
leads.topStat.withappointment=With appointment
leads.topStat.withAppointmentCancelOrNoShow.tooltip=Opportunities with at least one appointment cancelled or not presented
leads.topStat.withAppointmentCancelOrNoShow=Cancelled
leads.topStat.withappointmentlate.tooltip=Opportunities with one late appointment
leads.topStat.withappointmentlate=Late appointment
leads.topStat.withoutFirstMeeting.tooltip=In-store opportunities created without any meetings completed (appointment and/or walk-in). You should create a meeting or identify a different media than in-store.
leads.topStat.withoutFirstMeeting=Unidentified
leads.topStat.withoutFirstMeetingInStore=Unidentified (In-store)
leads.topStat.withSecondMeeting.tooltip=Opportunities with at least two meetings completed (appointment and/or walk-in)
leads.topStat.withSecondMeeting=Two meetings
leads.topStat.withSecondMeetingSales.tooltip=Sales with at least two meetings completed (appointment and/or walk-in)
leads.topStat.withSecondMeetingSales=Sales with two meetings
leads.topStat.withtask.tooltip=Opportunities with at least one task to complete for a sales user or BDC
leads.topStat.withtask=With a task to complete
leads.topStat.withtasklate.tooltip=Opportunities with at least one late task for a sales user or BDC
leads.topStat.withtasklate=Witha late task
leads.userGroupMode.Sales=All opportunities - Sales
leads.visits=Visits
leads.web=Web opportunities
leads.withFirstMeeting=With a first meeting
leads=Opportunities
least.recent=Least recent
lightspeed=Lightspeed
load.more=Load more
log.appointment=Walk-in
log.salesmanager.action=Log sales manager action
mail.active=Active
mail.body.selectvar=Select a variable to insert in body
mail.body=Body
mail.bodytext=Body in text
mail.define.configuration=Define email configurations
mail.emailrouting=Email routing
mail.errors.error.noId=No message found.
mail.errors.success=The error state has changed.
mail.errors.title=Unrecognized emails
mail.html=Enable HTML source code 
mail.receiver.default=Default receiver
mail.selectvar=Select variable
mail.sender.default=Default Sender
mail.sender.error.default=An error occured while testing email.
mail.sender.error.noContent=No content found to send email.
mail.sender.error.noFrom=Email missing in order to send email from.
mail.sender.error.noSubject=Subject missing in order to send email.
mail.sender.error.noTo=Email missing in order to send the email.
mail.sender.mailgun.api=Mailgun API Key
mail.sender.mailgun.domain=Mailgun Domain
mail.sender.mailgun.failslimit=Mailgun fail limit (in percentage)
mail.sender.mailgun.validation=Enable email validation
mail.sender.success=Test email has been sent.
mail.sender.test.title=Email test sent
mail.sender.test=Send test
mail.sender=Email sender
mail.snippet=Snippet
mail.subject.selectvar=Select a variable to insert in subject
mail.subject=Subject
mail.template.dailySummary.template=Daily summary form template
mail.template.deleted=Mail template deleted
mail.template.email.template.create=Create new email template
mail.template.email.template.preview=Preview email template
mail.template.email.template=Email template configuration
mail.template.form.template=Form template
mail.template.lang=Language
mail.template.notdeleted=Cannot delete model template
mail.template.notsaved=Content not saved.
mail.template.report=Email daily report
mail.template.salesagreement=Email sales agreement
mail.template.saved=Saved successfully
mail.template.title=Template Type
mail.template.user.signature.preview=User signature preview
mail.template.user.signature=User's signature
mail.template=Template
mail.testsource.placeholder=Email address
mail.testsource.test=Obtain source
mail.testsource=Test source of an email address
mail.title=Emails
mail.user.template.create=Create a new template
mail.user.template.deleted=Template {0} deleted
mail.user.template.preview=Email preview for client
mail.user.template.select=Select template
mail.user.template=User mail templates
mailmessage.forward.to.email=Forward to emails
mailmessage.nosubject=(no subject)
mailmessage.quickview=Email quick view
mailmessage.send.bulk=Are you sure you want to send this email to all listed recipients?
mailmessage.send.emailValidationResultError=Unable to send an email to this address. Please check the customer's email.
mailmessage.send.emailValidationResultNotValidated=The email address has not been validated. Close this message to continue.
mailmessage.send.emailValidationResultWarning=The email has been indicated as "Do Not Send". Do you wish to continue and send the email?
mailmessage.subject=Subject
mailmessage.view.original=View original
MailMessage=Email
mailtemplate.active=Activate this template
mailtemplate.actived=Active
mailtemplate.new=New template
mailtemplate.title=Title
mailtemplate.users=User's shared
main.admin=Administration
main.backTraction=Back to Traction
main.callmanager=Call Manager
main.callnotes=Call Notes
main.chat.inbox=Chat Inbox
main.Chat=Chat
main.client=Client
main.customers=Customers
main.dashboard=Sales representative dashboard 
main.development=Developement
main.document=Documentation
main.events=Events
main.goals=Goals
main.management=Management
main.marketing=Marketing
main.pinmenu=My tagged menu's
main.preferences=My preferences
main.productmanager=Product Manager
main.products=Products
main.sales=Sales
main.salesClosingRate.table=Sales conversion rate table
main.salesClosingRate=Sales conversion rate
main.salesdashboard=Sales director's dashboard
main.salesObjectiveReport=Sales objectives
main.service=Service
main.soldboard=Sold board
main.team=Team
main.tools=Tools
main.workflow=Workflow
mainmenu.assist=Assistance
mainmenu.communication=Communications
mainmenu.darkmode=Dark mode
mainmenu.general=General
mainmenu.lightmode=Light mode
mainmenu.marketing=Marketing
mainmenu.mytaggedmenu=My tagged menu's
mainmenu.new=New
mainmenu.report=Reports
mainmenu.sale=Sales
mainmenu.search=Search
mainmenu.service=Service
mainmenu.serviceandpart=Service and parts
mainmenu.team=Team
mainmenu.tools=Tools
mainmenu.workflows=Workflow
maintenance.explanations=The system may not be available for a few minutes. Thank you for your understanding!
maintenance.title=We will be performing maintenance or updates
make.noProfile=No profile
make=Makes
managerproductattribute.value=Value
managerproductconfig.categoryTraction=Traction category 
managerproductconfig.ignoreCategory=Ignore category
managerproductfeed.configs=Feed configuration
managerproductfeed.duration=Duration
managerproductfeed.file=File
managerproductfeed.filter=Feed filters
managerproductfeed.last.generated=Last generated
managerproductfeed.nbproducts=Product count
managerproductgroups.barred_price=Barred price
managerproductgroups.categoryMagento=Magento category 
managerproductgroups.cost_reel=Real cost
managerproductgroups.default_watermark=Default watermark
managerproductgroups.description_kijiji=Kijiji description 
managerproductgroups.description_trader=Trader description 
managerproductgroups.formule_price_1=Formula price 1
managerproductgroups.formule_price_2=Formula price 2
managerproductgroups.formule_price_3=Formula price 3
managerproductgroups.noimage_watermark=No watermark image
managerproductgroups.options_price=Options price
managerproductgroups.pdi_price=PDI price
managerproductgroups.potentialPrice=Potential price
managerproductgroups.pr=PR
managerproductgroups.prixminimum=Minimum price
managerproductgroups.profit_reel=Real profit
managerproductgroups.promo_watermark=Promotional watermark 
managerproductgroups.reserved_watermark=Reserved watermark
managerproductgroups.shortdesc=Short description
managerproductgroups.special_price_1=Special price 1
managerproductgroups.special_price_2=Special price 2
managerproductgroups.statut_watermark=Status watermark
managerproductgroups.titre=Title of model
managerproductgroups.traction_price=Traction price
managerproductgroups.transport_price=Transport price
manitou=Manitou
march=March
marketing.productExport=Vehicles exportation
marketing.productmanager=Product manager
Marketing.products=Vehicles
max=Maximum
maximize=Maximize
maximum=Maximum
may=May
media.instore=In-store opportunities
media.phoneup=Telephone opportunities
media.web=Web opportunities
media=Media
meeting.complete=Meeting completed
meeting.done.modify.date=Modify the date, duration, note and status of the meeting
meeting.duration=Duration
meeting.emptynote.error=Please enter a note about the meeting
meeting.modify.history.error=Please ensure that the date entered is not in the future.
meeting.next=Next meeting
meeting.note=Meeting note
meeting.owner=Meeting owner
meeting=Meeting
meetingcategory.isAppointment=Appointment
meetingcategory.isNotNull=With meeting (walk-in and/or appointment)
meetingcategory.isNull=Without meeting (never seen)
meetingcategory.isWalkIn=Walk-in
merge.client.warning.extID=Warning! Both clients have identical external ID's. Make sure you also merge clients in your DMS.
message.add=Write a message
message.author=Author
message.category=Category
message.client=Client
message.date=Date
message.opportunity=Opportunity
message.saved.error=The message can not be empty.
message.saved=Message saved
message.status=Status
message.text=Contents
min=Minimum
minimize=Minimize
minimum.should.be.greater=Maximum year should be greater than the minimum year
minimum=Minimum
minute.first.letter=m
minutes.ago.short={0}m ago
minutes=minutes
missing.role.error=You do not have authorization to complete this action.
mitsubishi=Mitsubishi
mo=Mo
modal.cotation.create.name.cotation=Name the quote
modify.code.menu=Modify menu code 
modify.viewer=Modify viewer
modify=Modify
month=month
most.recent=Most recent
multilocal.language=Language
multiselect.allSelected=All selected
multiselect.nonSelected=None selected
multiselect.nSelected=selected
multiselect.origins.nonSelected=Sources filter
multiselect.selectAll=Select all
my.chat=My chat
my.communications=My communications
my.notif.param=Notification settings
my.notif=Notifications
my.subscribed.tasks=My task subscriptions
my.task.assigned=My tasks
my.task.subscriptions=My task subscriptions
my.tasks=My tasks
name=Name
Name=Name
navigationhistory.empty=Navigation history is empty
navigationhistory.type.all=All
navigationhistory.type.searched=Searched
navigationtab.bo=Bo
navigationtab.client=Client
navigationtab.entreprise=Company
navigationtab.product=Product
new.opportunity.from.potential=Opportunity created from potential opportunities
new.payment=New payment
new.service.opportunity=New service opportunity
new.value=New value
new=New
next.availability=Next availability
next.maintenance.time=Maintenance at
next.step=Next step
next.task.todo=Next task to complete
next14days=Next 14 Days
next7days=Next 7 Days
no.assignement.only=No assignment only
no.chatwidget=Live chat widget not configured
no.data=No data
no.default.view=No view selected
no.department.assigned=No department assigned
no.facebookpage=No facebook page configured
no.group=No group
no.name=No name
no.opportunities.selected=No opportunities selected
no.recording.subtitle=Click to refresh recording.
no.recording.video=Recording not available
no.recording=Recording not loading
no.resource.assigned=No resource is currently assigned
no.result.for=No result for
no.service.schedule=No service schedule yet
no.serviceschedule=No workflow schedule
no.user=No user
no.usergroup=No department
no.value=No value
no.workflowdata=No workflow data
no=No
no.date=No date
no.department=No department
non.compilant=Non-compilant
none=NONE
not.assigned.to.you=Not assigned to you
note.late=Late
notes.client.search=Search a client to add a note
notif.comment=Comment
notif.note=Note
notification.end=No more notifications
notification.event.callnotes.title=New call note
notification.event.newcallnotes.text={0} has assigned you to the call note {1} linked to the \n client {2}
notification.event.newcomment.text={0} commented {1}
notification.event.newcomment.title=Comment
notification.event.newcommentmention.text={0} mentioned you in a comment on {1}
notification.event.newcommentmention.title=@Mention
notification.event.newcommunication.text={0} sent you a message
notification.event.newcommunication.title=Communications
notification.event.newpoolopportunity.text={0} just arrived in your opportunity pool
notification.event.newvisitlog.text={0} has logged a visit. Client Description: {1}
notification.event.opportunity.title=Opportunity
notification.event.opportunity.assigned=Opportunity assigned to you
notification.event.opportunity.assigned.text={0} has been assigned to you.
notification.event.subscribedtask.title=Task Subscriptions
notification.event.subscribedtaskdone.text={0} assigned to {1} is completed.
notification.event.subscribedtasklate.text={0} assigned to {1} is late.
notification.event.task.title=Tasks
notification.event.taskassigned.text={0} created by {1} has been assigned to you
notification.event.visitlog.title=In-store visits
notification.filter.all=All
notification.filter.important=Favourited
notification.filter.read=All
notification.filter.unread=Unread
notification.new.call.notes.body=A call note has been assigned to you
notification.new.call.notes.title=New call note
notification.new.chat.body=Chat: {0}
notification.new.chat.title.from=New Chat message from {0}
notification.new.chat.title=New chat message
notification.new.email.body=Email: {0}
notification.new.email.title.from=New email from {0}
notification.new.email.title=New email
notification.new.facebook.body=Facebook message: {0}
notification.new.facebook.title.from=New Facebook message from {0}
notification.new.facebook.title=New Facebook message
notification.new.pool.body=An opportunity has been added to the pool
notification.new.pool.title=New opportunity in pool
notification.new.sms.body=Sms: {0}
notification.new.sms.title.from=New sms from {0}
notification.new.sms.title=New sms
notification.new.task.body=A task has been assigned to you
notification.new.task.title=New task
notification.new.visitlog.body=A visit has been assigned to you
notification.new.visitlog.title=New visit
notification.pool.taken=Pool already taken, you were a little late
notification.set.all.read=Mark all as read
notifications.actions=Actions
notifications.load.more=Load more notifications
notifications.read.all=Mark all as read
notifications=Notifications
november=November
number.of.times=Number of times
october=October
Odometer.Unit.HOURS=h
Odometer.Unit.KILOMETERS=km
Odometer.Unit.MILES=mi
only=only
operator.be=between
operator.co=contains
operator.eq=equal to
operator.ge=greater than or equal to
operator.gt=greater than
operator.in=in
operator.le=less than or equal to
operator.lt=less than
operator.nb=not between
operator.nc=does not contain
operator.ne=not equal to
operator.ni=not in
operator.size_eq=size equal to
operator.size_ge=size greater than or equal to
operator.size_gt=size greater than
operator.size_le=size less than or equal to
operator.size_lt=size less than
operator.size_ne=size not equal to
oppBar.displayAll=Display all opportunities
oppBar.displayCloseArchived=Display all closed and archived opportunities
oppBar.displayRelated=Display all co-owner related opportunities
oppBar.displaySales=Display all sales opportunities
oppBar.displaySalesActive=Display all active sales opportunities
oppBar.displayService=Display all service opportunities
opportunities.attempted=Attempted
opportunities.followup=Opportunities follow up according to the month of creation
opportunities.reached=Reached
opportunities.sales=Sales opportunities
opportunities.saved=Opportunities have been saved
opportunities.service=Service opportunities
opportunities.short=Opp.
opportunities.table=Opportunities table
opportunities.with.vehicle=Opportunities with the vehicle {0}
opportunities=Opportunities
opportunity.accessories.add=Add an accessory
opportunity.accessories.adds=Add accessories
opportunity.accessories.delete=Are you sure you want to remove this accessory ?
opportunity.accessories.search=Search accessories
opportunity.accessories=Accessories
opportunity.accessoriesTab=Accessories
opportunity.accessory=Accessory description
opportunity.actDate=Last activity date
opportunity.activities=Activities
opportunity.activitiesSize=Number of activities
opportunity.add.remark=Add a memo
opportunity.add.walkin=Add a walk-in
opportunity.add=Add an opportunity
opportunity.additionalCost=Additional cost
opportunity.addorigin=Identify source
opportunity.already.bump=Unable to add a BUMP for an opportunity that already contains a BUMP and is sold.
opportunity.appointment=Appointment
opportunity.archive=Archive
opportunity.archived=Archived
opportunity.assign.user.search=Assign another user to the opportunity
opportunity.assign.user=Assigned user
opportunity.assigned.empty=No user's assigned
opportunity.assigned.type.primary=Primary
opportunity.assigned.type.secondary=Secondary
opportunity.assigned.users=User's assigned to the opportunity
opportunity.assigned=Assignments
opportunity.assignUser.error=You do not have authorization to modify the sales user's.
opportunity.assignUser.success=The user's assigned to the opportunity have been modified.
opportunity.autoassign.on=Automatically assign a user when a new opportunity is created
opportunity.autoassign.usersales=Automatically assign the seller to the opportunity of the customer who has the most recent activity when creating an opportunity
opportunity.card=Card
opportunity.category.status.error=This status cannot be changed if the opportunity is sold
opportunity.category=Category
opportunity.changemedia=Change media
opportunity.client.create.no-vehicle=Create opportunity without vehicle
opportunity.client.create.vehicle=Create opportunity with vehicle
opportunity.client.input=Enter opportunity name
opportunity.client=Client
opportunity.clientPrice=Client price
opportunity.close.btn.tooltip=Select opportunities
opportunity.close.btn.txt=Close tasks
opportunity.close.confirmation.post=tasks associated to the selected opportunities. Are you sure you want to close them?
opportunity.close.confirmation.pre=There is
opportunity.communication.status=Communication status
opportunity.complete.appointment=Complete an appointment
opportunity.configurator=Configurator
opportunity.confirmRemove=Are you sure to remove this opportunity from the quote?
opportunity.contactUserBdc=BDC contact 
opportunity.contactUserSales=Sales contact 
opportunity.cotation=Quotation
opportunity.cotationElements=Quote elements
opportunity.count=Number of opportunities
opportunity.create=Create an opportunity
opportunity.created.on=Created on
opportunity.creation=Creation
opportunity.data=Data
opportunity.date.short=Creation
opportunity.date=Creation date
opportunity.dateMeetingDone=Meeting date completed
opportunity.dateMeetingTodo=Meeting date to-do
opportunity.dateMOD.short=Modification
opportunity.dateMOD=Last modification date
opportunity.dateSOLD.short=Sold
opportunity.dateSOLD=Date Sold
opportunity.delete.confirm=Are you sure you want to delete this opportunity: {0}?
opportunity.delivered=Delivered
opportunity.department=Branch
opportunity.description.copy.product=Copy the product description in the opportunity description when the opportunity description is empty
opportunity.description=Description 
opportunity.descriptionTab=Product description
opportunity.detailSource=Source details
opportunity.displayTab=Display
opportunity.documents=Documents
opportunity.edit.bulk=Edit opportunities
opportunity.edit.remark.cancel=Cancel the modification
opportunity.edit.remark=Modify the remark
opportunity.edit.user=Modify the assigned user 
opportunity.empty=All opportunity information will be deleted. Are you sure you want to continue?
opportunity.error.id=No opportunity found
opportunity.error.name=The opportunity must have a name.
opportunity.exceedDateStopCompileData=Not calculated for an archived, closed, lost, or excluded opportunity.
opportunity.exclude.tooltip=Exclude from statistics
opportunity.exclude=Exclude from statistics
opportunity.excluded=Excluded
opportunity.fee.add=Add a fee/charge
opportunity.fee.autoAddToOpportunity.false=Do not add to new opportunities
opportunity.fee.autoAddToOpportunity.true=Add to new opportunities
opportunity.fee.autoAddToOpportunity=Add...
opportunity.fee.cost=Cost
opportunity.fee.delete.confirm=Are you sure you want to delete this fee/charge?
opportunity.fee.delete=Delete fee
opportunity.fee.deleted=Fee/Charge deleted
opportunity.fee.dms=DMS
opportunity.fee.edit=Edit fee
opportunity.fee.error=No fee found
opportunity.fee.name=Name
opportunity.fee.nontaxable=Non-taxable
opportunity.fee.notsaved=Fee saved
opportunity.fee.save=Save fee
opportunity.fee.saved=Fee/Charge saved
opportunity.fee.sell=Selling price
opportunity.fee.sync.button=Sync DMS fees
opportunity.fee.sync.success=Synchronization of DMS fees is finished
opportunity.fee.taxable=Taxable
opportunity.fee.type.fee=Fee
opportunity.fee.type.right=Charge
opportunity.fee.type=Type
opportunity.feeTab=Fees/Charges
opportunity.files=Files
opportunity.finalprice=Final price
opportunity.firstMeetingCategory=First meeting
opportunity.forms.satisfaction=Satisfaction Forms
opportunity.go.to=Go to opportunity
opportunity.histories=History
opportunity.history.details=Modifications details
opportunity.history.new=New value
opportunity.history.previous=Previous value
opportunity.historypoints=Points associated to this opportunity
opportunity.identify.media.source=Identify the opportunities media/source
opportunity.images=Images
opportunity.infoCard=Card information
opportunity.infoTab=Information
opportunity.isInStoreNotIdentified.already.fixed=Opportunity has been identified as "{0}" correctly.
opportunity.isInStoreNotIdentified=Unidentified
opportunity.labels=Opportunity labels
opportunity.lastActivityOfAssignedUser=Last activity with the client of the assigned user
opportunity.lastBriefing=Briefing
opportunity.lastBump=BUMP
opportunity.lastcompletedclientprocess=Date of last client process completed 
opportunity.lastStatusChange=Date of last status change 
opportunity.lastTakeOver=Take-over
opportunity.lastWalkin=Walk-in
opportunity.list=List of opportunities
opportunity.lost.month=Lost this month
opportunity.mailMessages=Emails
opportunity.manage.btn.txt=Manage tasks
opportunity.media=Media
opportunity.meetingAppointment=Appointment
opportunity.meetingAppointmentPhone=Telephone appointment 
opportunity.messages=Messages
opportunity.modified.on=Modified on
opportunity.name=Name
opportunity.new.remark=Add a remark to the list
opportunity.new=New opportunity
opportunity.newtrade=Add a new trade
opportunity.nextTaskUserBdc=Next BDC task 
opportunity.nextTaskUserSales=Next sales task 
opportunity.no.labels=No label
opportunity.none=No filter
opportunity.not.archived=Not archived
opportunity.not.delivered=Not delivered
opportunity.not.excluded=Not excluded
opportunity.notified=Notified
opportunity.options=Opportunity options
opportunity.optionsTab=Options
opportunity.origin=Source
opportunity.otherfee=Other fees
opportunity.otherInfo=Other information
opportunity.otherUserHasCommunicatedAfter=An unassigned user has more recent communications with this client
opportunity.placeholder.name=Search a product or create an opportunity
opportunity.pool.empty=Pool is empty
opportunity.pool=Pool
opportunity.priceOption=Options
opportunity.priceTab=Prices
opportunity.product.differences=Differences with the new version
opportunity.product.modified=New version of this product with this stock number available. Click here to see the changes.
opportunity.product.options.included=Included
opportunity.product.reserved=This product is reserved.
opportunity.product=Product
opportunity.promo=Manage promotions
opportunity.remark.list=List of remarks
opportunity.remark.write=Write a remark
opportunity.remark=Remark
opportunity.remarks=Remarks
opportunity.remove=Remove this opportunity from the quote
opportunity.removed.error=Opportunity not found
opportunity.removed=Opportunity removed from quotation
opportunity.replaceVehicle=Replace vehicle of the opportunity
opportunity.salePrice=Sales price
opportunity.salesmanageronly.status.error=This status can only be set by user's with "Administrate opportunities" permissions
opportunity.salesmanageronly.status.list=The opportunity status can only be set by user's with "Administrate opportunities" permissions
opportunity.salesprice=Sales price
opportunity.save.error.name=The opportunity name is missing
opportunity.save.error.product.reserved=The product is reserved
opportunity.save.error.vehicle.reserved=The vehicle is reserved
opportunity.saved=Opportunity saved
opportunity.savelabel=Save label
opportunity.search.text=Text search
opportunity.search=Search opportunity
opportunity.secondMeetingCategory=Second meeting
opportunity.select=Select opportunity
opportunity.show.labels.legend=Labels legend
opportunity.sold.on=Sold on
opportunity.sold.ratio={0} opportunity sold from this section compared to {1} new entry in section ({2}%)
opportunity.stats=Opportunity statistics
opportunity.status.icon=Opportunity statistics icon
opportunity.status=Status
opportunity.stocknumber=Stock
opportunity.substatus=Sub-status
opportunity.switchclient.search=Search the new client of this opportunity
opportunity.switchclient=Change client's opportunity
opportunity.tasks.close.confirm=Do you want to close all tasks associated to this opportunity?
opportunity.tasks.closed.error=There was an error when closing tasks.
opportunity.tasks.closed=The opportunity tasks have been closed successfully!
opportunity.tasks=Tasks
opportunity.time=Time spent for this opportunity (calls and appointments)
opportunity.title=Opportunities
opportunity.trades=Trades
opportunity.type.sale=Sale
opportunity.type.service=Service
opportunity.type=Type
opportunity.unarchive=Unarchive
opportunity.unexclude.tooltip=Unexclude from statistics
opportunity.unexclude=Unexclude
opportunity.unexcluded=Unexclude
opportunity.update.product=Update product with new values
opportunity.userBdc=BDC user 
opportunity.userFni=F&I user
opportunity.userParts=Parts user 
opportunity.userSales2=Second sales user
opportunity.userSales=Sales user 
opportunity.userService=Service user 
opportunity.userShipping=Shipping user 
opportunity.userShop=Boutique user
opportunity.userTech=Technician user 
opportunity.value=Value
opportunity.vehicle.null=No vehicle linked to this opportunity
opportunity.vehicle=Vehicle
opportunity.vehiculeIdentification2=Vehicle Identification 2
opportunity.vehiculeIdentification=Identification
opportunity.view=Opportunity view
opportunity.visit=Visit
opportunity.visitWithAppointment=With appt.
opportunity.visitWithoutAppointment=Without appt.
opportunity.workflowDataTab=Workflow data
opportunity=Opportunity
opportunityassigned.type=Type
opportunityassigned.user=User
opportunityassigned=Opportunity assignment
opportunityby.origin.null=Without source
opportunityby.source.null=Without media
opportunityby.userSales.null=Without seller
OpportunityCompiledData.top.categoryActive.tooltip=Active opportunities
OpportunityCompiledData.top.categoryActive=Active
OpportunityCompiledData.top.categoryActiveNoTasks.tooltip=Active opportunities without tasks
OpportunityCompiledData.top.categoryActiveNoTasks=Active without tasks
OpportunityCompiledData.top.categoryActiveWithoutContactLast30.tooltip=Opportunities with category active and without any contact with user sales or BDC in the last 30 days.
OpportunityCompiledData.top.categoryActiveWithoutContactLast30=No contact 30d.
OpportunityCompiledData.top.categoryActiveWithoutTasks.tooltip=Opportunities with category active and without any task to do
OpportunityCompiledData.top.categoryActiveWithoutTasks=Without task
OpportunityCompiledData.top.categorySold.tooltip=Sales
OpportunityCompiledData.top.categorySold=Sales
OpportunityCompiledData.top.categorySoldWithoutDeliveryDate.tooltip=Opportunities sold without workflow end date
OpportunityCompiledData.top.categorySoldWithoutDeliveryDate=Unscheduled
OpportunityCompiledData.top.communicationStatusClientPending.noTasks.short=Pending without tasks
OpportunityCompiledData.top.communicationStatusClientPending.noTasks.tooltip=Client is pending a response from the user and no tasks has been added
OpportunityCompiledData.top.communicationStatusClientPending.noTasks=Pending without tasks
OpportunityCompiledData.top.communicationStatusClientPending.short=Pending
OpportunityCompiledData.top.communicationStatusClientPending.tooltip=Client is pending a response from the user
OpportunityCompiledData.top.communicationStatusClientPending=Pending
OpportunityCompiledData.top.communicationStatusClientReached.tooltip=User successfully contacted the client
OpportunityCompiledData.top.communicationStatusClientReached=Reached
OpportunityCompiledData.top.communicationStatusUserAttempt.tooltip=User attempted to contact the client
OpportunityCompiledData.top.communicationStatusUserAttempt=Attempts
OpportunityCompiledData.top.communicationStatusUserNoContact.tooltip=User has not attempted to contact the client
OpportunityCompiledData.top.communicationStatusUserNoContact=Not contacted
OpportunityCompiledData.top.communicationStatusUserNoContactWeb.tooltip=User has not attempted to contact the client for web opportunities only
OpportunityCompiledData.top.communicationStatusUserNoContactWeb=Not contacted (Web)
OpportunityCompiledData.top.createdLast7Days.tooltip=Opportunities created in the last 7 days
OpportunityCompiledData.top.createdLast7Days=New 7d.
OpportunityCompiledData.top.delivered.tooltip=Delivered vehicles according to the workflow date end
OpportunityCompiledData.top.delivered=Delivered vehicles
OpportunityCompiledData.top.deliveryScheduled.tooltip=Opportunities with workflow date end
OpportunityCompiledData.top.deliveryScheduled=Delivery scheduled
OpportunityCompiledData.top.deliveryThisMonth.tooltip=Delivery this month according to the workflow date end
OpportunityCompiledData.top.deliveryThisMonth=Delivery this month
OpportunityCompiledData.top.inStore.tooltip=In-store opportunities
OpportunityCompiledData.top.inStore=In-store
OpportunityCompiledData.top.inStoreCategorySold.day.short=Today
OpportunityCompiledData.top.inStoreCategorySold.day.tooltip=In-store sales today
OpportunityCompiledData.top.inStoreCategorySold.day=In-store sales - Today
OpportunityCompiledData.top.inStoreCategorySold.month.short=This month
OpportunityCompiledData.top.inStoreCategorySold.month.tooltip=In-store sales this month
OpportunityCompiledData.top.inStoreCategorySold.month=In-store sales - This month
OpportunityCompiledData.top.inStoreCategorySold.short=Date filtered
OpportunityCompiledData.top.inStoreCategorySold.tooltip=In-store sales
OpportunityCompiledData.top.inStoreCategorySold.week.short=This week
OpportunityCompiledData.top.inStoreCategorySold.week.tooltip=In-store sales this week
OpportunityCompiledData.top.inStoreCategorySold.week=In-store sales - This week
OpportunityCompiledData.top.inStoreCategorySold=In-store sales
OpportunityCompiledData.top.inStoreOrWithMeeting.tooltip=In-store opportunities
OpportunityCompiledData.top.inStoreOrWithMeeting=In-store
OpportunityCompiledData.top.phoneUp.tooltip=Phone opportunities
OpportunityCompiledData.top.phoneUp=Phone
OpportunityCompiledData.top.phoneUpCategorySold.day.short=Today
OpportunityCompiledData.top.phoneUpCategorySold.day.tooltip=Phone sales today
OpportunityCompiledData.top.phoneUpCategorySold.day=Phone sales - Today
OpportunityCompiledData.top.phoneUpCategorySold.month.short=This month
OpportunityCompiledData.top.phoneUpCategorySold.month.tooltip=Phone sales this month
OpportunityCompiledData.top.phoneUpCategorySold.month=Phone sales - This month
OpportunityCompiledData.top.phoneUpCategorySold.short=Date filtered
OpportunityCompiledData.top.phoneUpCategorySold.tooltip=Phone sales
OpportunityCompiledData.top.phoneUpCategorySold.week.short=This week
OpportunityCompiledData.top.phoneUpCategorySold.week.tooltip=Phone sales this week
OpportunityCompiledData.top.phoneUpCategorySold.week=Phone sales - This week
OpportunityCompiledData.top.phoneUpCategorySold=Phone sales
OpportunityCompiledData.top.total.day.short=Today
OpportunityCompiledData.top.total.day.tooltip=All opportunities today
OpportunityCompiledData.top.total.day=All opportunities - Today
OpportunityCompiledData.top.total.month.short=This month
OpportunityCompiledData.top.total.month.tooltip=All opportunities this month
OpportunityCompiledData.top.total.month=All opportunities - This month
OpportunityCompiledData.top.total.short=Date filtered
OpportunityCompiledData.top.total.tooltip=All opportunities
OpportunityCompiledData.top.total.week.short=This week
OpportunityCompiledData.top.total.week.tooltip=All opportunities this week
OpportunityCompiledData.top.total.week=All opportunities - This week
OpportunityCompiledData.top.total=All opportunities
OpportunityCompiledData.top.web.tooltip=Web opportunities
OpportunityCompiledData.top.web=Web
OpportunityCompiledData.top.webCategorySold.day.short=Today
OpportunityCompiledData.top.webCategorySold.day.tooltip=Web sales today
OpportunityCompiledData.top.webCategorySold.day=Web sales - Today
OpportunityCompiledData.top.webCategorySold.month.short=This month
OpportunityCompiledData.top.webCategorySold.month.tooltip=Web sales this month
OpportunityCompiledData.top.webCategorySold.month=Web sales - This month
OpportunityCompiledData.top.webCategorySold.short=Date filtered
OpportunityCompiledData.top.webCategorySold.tooltip=Web sales
OpportunityCompiledData.top.webCategorySold.week.short=This week
OpportunityCompiledData.top.webCategorySold.week.tooltip=Web sales this week
OpportunityCompiledData.top.webCategorySold.week=Web sales - This week
OpportunityCompiledData.top.webCategorySold=Web sales
OpportunityCompiledData.top.webOnlyCommunicationStatusUserNoContact.tooltip=Web opportunities without contact with BDC or sales user
OpportunityCompiledData.top.webOnlyCommunicationStatusUserNoContact=No contact (Web)
OpportunityCompiledData.top.withAppointment.tooltip=Opportunities with at least one appointment (to do, done, canceled or not presented)
OpportunityCompiledData.top.withAppointment=With appointment
OpportunityCompiledData.top.withAppointmentCancelOrNoShow.tooltip=Opportunities with at least one appointment canceled or not presented
OpportunityCompiledData.top.withAppointmentCancelOrNoShow=Canceled
OpportunityCompiledData.top.withAppointmentLate.tooltip=Opportunities one appointment late
OpportunityCompiledData.top.withAppointmentLate=Appointment late
OpportunityCompiledData.top.withAppointmentTodo.tooltip=Opportunities with at least one appointment to-do
OpportunityCompiledData.top.withAppointmentTodo=With appointment to-do
OpportunityCompiledData.top.withFirstMeetingAppointment.nowWithSecondMeeting=now in 2 meetings
OpportunityCompiledData.top.withFirstMeetingAppointment.tooltip=Opportunities with only one appointment done and no walk-in
OpportunityCompiledData.top.withFirstMeetingAppointment=Appointment
OpportunityCompiledData.top.withFirstMeetingAppointmentCategorySold.tooltip=Sales with only one appointment done and no walk-in
OpportunityCompiledData.top.withFirstMeetingAppointmentCategorySold=Appointment sales
OpportunityCompiledData.top.withFirstMeetingWalkin.nowWithSecondMeeting=now in 2 meetings
OpportunityCompiledData.top.withFirstMeetingWalkin.tooltip=Opportunities with only one walk-in and no appointment done
OpportunityCompiledData.top.withFirstMeetingWalkin=Walk-in
OpportunityCompiledData.top.withFirstMeetingWalkinCategorySold.tooltip=Sales with only one walk-in and no appointment done
OpportunityCompiledData.top.withFirstMeetingWalkinCategorySold=Walk-in sales
OpportunityCompiledData.top.withoutMeeting.tooltip=Opportunities created in store without any meetings done (appointment and/or walk-in). You should add a meeting or identify a different media than In store.
OpportunityCompiledData.top.withoutMeeting=Not identified (In store)
OpportunityCompiledData.top.withSecondMeeting.tooltip=Opportunities with at least 2 meetings done (appointment and/or walk-in)
OpportunityCompiledData.top.withSecondMeeting=2 meetings
OpportunityCompiledData.top.withSecondMeetingCategorySold.tooltip=Sales with at least 2 meetings done (appointment and/or walk-in)
OpportunityCompiledData.top.withSecondMeetingCategorySold=2 meeting sales
OpportunityCompiledData.top.withTakeOver.tooltip=Take over
OpportunityCompiledData.top.withTakeOver=Take over
OpportunityCompiledData.top.withTaskLate.tooltip=Opportunities with at least one task late for user sales or BDC
OpportunityCompiledData.top.withTaskLate=With task late
OpportunityCompiledData.top.withTaskTodo.tooltip=Opportunities with at least one task todo for user sales or BDC
OpportunityCompiledData.top.withTaskTodo=With task todo
opportunityoption.add.error.limit.char=If the name of the accessory has more than 39 characters, it will be cut off when exporting to your DMS
opportunityoption.add.error=Please ensure that the option name is unique
opportunityoption.add.success=Accessories have been added successfully
opportunityoption.add=Add option
opportunityoption.costingPrice=Cost
opportunityoption.delete=Delete option
opportunityoption.description=Description
opportunityoption.discount=Discount
opportunityoption.hour=hrs
opportunityoption.labourPrice=Cost of labour
opportunityoption.name=Name
opportunityoption.optionNumber=Option number
opportunityoption.placeholder=Search an accessory
opportunityoption.providerId=Manufacturer ID
opportunityoption.quantity=Quantity
opportunityoption.sellingPrice=Selling Price
opportunityoption.time=Time
opportunityPrices=Opportunity prices
option.add=Add an option or an accessory
option.description=Description
option.images=Image
option.name=Name
option.optional=Optional
option.order=Order
option.price=Price
option.title=Options and accessories
options=Options
or=or
order.asc=Ascending
order.by.col=Order by
order.by.dir=Direction
order.desc=Descending
origin.create=Create source
origin.dealers.placeholder=Enter a new dealer number
origin.dealers=Dealer numbers
origin.delete.confirm=Are you sure you want to delete this source?
origin.edit=Edit source
origin.emails.placeholder=Enter a new email
origin.emails=Sending emails
origin.name=Name
origin.phones.placeholder=Enter a new phone number
origin.phones=Telephone numbers
origin.receptionEmail.hint=Enter the receiving email only if the source cannot be identified with the sending email. If the sending email is sufficient, leave this field blank.
origin.receptionEmail=Receiving email
origin.text=Searched text
origin.timer.info=The minimum is 15 minutes
origin.timer=Reattribution delay (in minutes)
origin.timerFromTo=Reattribution from/to
origin.title=Sources
origin.unique.name=Another source already exists with this name
origin=Source
origins=Sources
other.emails=Other emails
other=Other
others=Others
parts.list=Parts list
pay=Pay
payment.cancel.body=Payment cancelled! You can make the payment later or contact {0} for more information about this payment request.
payment.cancel.title=Payment cancelled!
payment.success.body=Thank you for your payment! {0} will be notified that you fulfilled the payment request.
payment.success.title=Payment received!
paymentintent.isUnread.warning=Warning! You can only edit unread marks for your own payments.
payments.empty=No transactions yet
payments=Payments
pdf.template.active=Active
pdf.template.add=Add a PDF
pdf.template.chooseVar=Choose a variable...
pdf.template.display.client=Client
pdf.template.display.product=Sales display
pdf.template.display.salesagreement=Sales agreement
pdf.template.display.trade=Print trade
pdf.template.display=PDF displayed
pdf.template.edit=Edit PDF
pdf.template.error.delete=An error occured while deleting.
pdf.template.error.get=An error occured while receiving the PDF.
pdf.template.error.no.salesagreement=Add a sales agreement PDF in the configuration.
pdf.template.error.salesagreementortrade=Only one PDF can be submitted for the sales agreement and printing of the trade
pdf.template.error.sameName=PDF name already exists
pdf.template.error.save=An error occured while saving. There is no opportunity or file.
pdf.template.error.send.client=An error occured while sending {0} to client
pdf.template.error.send=An error occured while sending the sales agreement to the client
pdf.template.field.name=Name
pdf.template.field.value=Value
pdf.template.image.height=Height
pdf.template.image.width=Width
pdf.template.image.x=Position from left
pdf.template.image.y=Position from top
pdf.template.image=Enable adding the first image to the PDF
pdf.template.list=List of PDFs
pdf.template.name=Name
pdf.template.success.delete=The PDF has been deleted.
pdf.template.success.error=At least one PDF has to be selected
pdf.template.success.save=The PDF has been saved.
pdf.template.success.send=Sales agreement as been sent to the client by email.
pdf.template.text=PDF texts
pdf.template.update=Update PDF
pdf.template.warning=Do not forget to accept pop-ups on the top right of the browser.
pdf.template=PDF
pending.color=Color reservations
pendingcolor.create=Create a new color reservation
pendingcolor.deleted=Reservation color deleted
pendingcolor.edit=Change reservation color
pendingcolor.notfound=Reservation color not found
PERM_ACTION=Manage actions
PERM_ASSIGNMENT_RULE=Manage assignment rules
PERM_BULK_SMS=Send bulk SMS
PERM_BUMP_TO_WRITE=Manage BUMP and TO
PERM_CALL_LISTENING_ALL=Call Listening - All
PERM_CALL_LISTENING_DEPARTMENTS=Call Listening - My branches
PERM_CALL_LISTENING_GROUP=Call Listening - My group
PERM_CALL_NOTES=Access call notes
PERM_CALL_READ=Access call manager
PERM_CALL_REPORT_READ=Access call statistics
PERM_CALL_WRITE=Change the user status in the call manager
PERM_CHAT_USERS_STATUSES=Manage user chat statuses
PERM_CHAT_WIDGET=Manage chat widgets
PERM_CHAT_WIDGET_GREETING=Manage chat widget greetings
PERM_CHAT_WIDGET_GROUP=Manage chat groups
PERM_CLIENT_LIST=Access client list
PERM_CLIENT_PROCESS_ADMIN=Manage client processes
PERM_CLIENT_PROCESS_ENTRIES=Access client process entry list
PERM_COMMUNICATION_RATING_WRITE=Manage communication ratings
PERM_CONFIG=Manage configurations
PERM_CONFIGURATOR=Manage vehicle configurator
PERM_COTATION=Access quotations
PERM_COTATION_ADMIN=Manage quotations
PERM_DEPARTMENT=Manage branches
PERM_DMS=Manage the DMS
PERM_DOCUMENT_READ=Access to documents
PERM_DOCUMENT_WRITE=Manage documents
PERM_EMAIL_ERROR_ADMIN=Manage email errors
PERM_EXTENSION=Manage extensions
PERM_FACEBOOK_PAGE=Manage facebook pages
PERM_FILTER=Manage filters
PERM_FORM_DATA=Access to form data
PERM_FORM_EVENT=Manage form events
PERM_FORM_PROFILE=Manage form profiles
PERM_ITEM_LIST_READ=Access the item list
PERM_ITEM_LIST_WRITE=Manage item list
PERM_LAUTOPAKPARTRESERVATION=Access to parts reservations
PERM_LAUTOPAKPARTSINVOICE=Access to parts invoices
PERM_MAIL_TEMPLATE_ADMIN=Manage email templates
PERM_MANAGER_PRODUCT_CONFIG=Manage vehicle configurations
PERM_MANAGER_PRODUCT_GROUP=Manage vehicle groups
PERM_MEETING_ADMIN=Modify meeting history
PERM_OPPORTUNITY_ADMIN=Manage opportunities
PERM_OPPORTUNITY_ASSIGN_ADMIN=Manage user assignment for sales opportunities
PERM_OPPORTUNITY_LIST=Access opportunity list
PERM_ORIGIN=Manage sources
PERM_PAYMENT_ADMIN=Manage payments
PERM_PIXEL_GURU=Pixel guru
PERM_POINT_REPORT=Access to point reports
PERM_REVIEW=Manage reviews
PERM_ROLE_ADMIN=Manage user roles
PERM_SALES_CLOSING_RATE_REPORT=Access  to sales conversion rate report
PERM_SALES_REPRESENTATIVE_DASHBOARD=Access to sales representative dashboard
PERM_SOLD_DASHBOARD=Access to sold board
PERM_SALES_MANAGER_DASHBOARD=Access to sales director's dashboard
PERM_SALES_OBJECTIVE_REPORT=Access to sales objective reports
PERM_SALES_TRACKING_ADMIN=Edit in the sales tracking dashboard
PERM_SALES_TRACKING_READ=Access to sales tracking dashboard
PERM_SERVICE_CALENDAR=Manage service calendars
PERM_SERVICE_RESOURCE=Manage service resources
PERM_SERVICE_SCHEDULE_DISPATCH=Dispatch in service schedules
PERM_SERVICE_SCHEDULE_READ=Access to service schedules
PERM_SOURCE_REPORT=Access to source reporting
PERM_STRIPE_ACCOUNT=Manage stripe accounts
PERM_TAKETURNS_ADMIN=Manage round robin
PERM_TASK_ADMIN=Manage the task list
PERM_TAX=Manage taxes
PERM_TRAINING_READ=Access to training
PERM_TRAINING_WRITE=Manage training
PERM_TWILIO_NUMBER=Manage twilio numbers
PERM_USER_ADMIN=Manage users
PERM_USER_POINT_ADMIN=Manage users point objectives
PERM_USER_SUPERVISOR_REPORT=Access to user's supervisor
PERM_VEHICLE=Vehicles
PERM_VEHICLE_DMS_IMPORT=Manage DMS vehicle import
PERM_VEHICLE_FEED=Manage vehicle feeds
PERM_VEHICLE_GENERAL_IMPORT=Manage vehicle imports
PERM_VEHICLE_INHERITANCE_RULE=Manage vehicle inheritance rules
PERM_VEHICLE_INSPECTION=Manage vehicle inspection
PERM_VEHICLE_LIST=Access to vehicle list
PERM_VEHICLE_READ=Access to vehicles
PERM_VEHICLE_SELECTION_OPTION=Manage vehicle selection options
PERM_VEHICLE_SPECIFICATION_ADMIN=Manage vehicle specifications
PERM_VEHICLE_TEXT=Manage vehicle texts
PERM_VEHICLE_TRANSLATED_TEXT=Manage vehicle translated texts
PERM_VEHICLE_WATERMARK=Manage vehicle watermarks
PERM_VEHICLE_WRITE=Manage vehicles
PERM_VISIT_LOG_ADMIN=Manage irrelevant visit logs
PERM_WEB_SESSION=Access to chat website traffic
PERM_WORK_ORDER=Access to work orders
PERM_WORK_SCHEDULE=Manage work schedules
PERM_WORKFLOW_ADMIN=Manage workflows
PERM_WORKFLOW_CALENDAR_NOTE_WRITE=Edit notes within a workflow calendar
PERM_WORKFLOW_READ=Access to workflows
PERM_WORKFLOW_WRITE=Edit workflows
permission.allowedLevels=For permission levels
permission.group=Group
permission.list=Permissions list
permission.name=Name
permission.related.pages=Related pages
PermissionGroup.BUMP_TO=Bump and Take Overs
PermissionGroup.CALL=Calls
PermissionGroup.CHAT=Chat
PermissionGroup.CLIENT=Clients
PermissionGroup.CLIENT_PROCESS=Client processes
PermissionGroup.COMMUNICATION=Communications
PermissionGroup.CONFIG=Configurations
PermissionGroup.COTATION=Quotations
PermissionGroup.DOCUMENT=Documents
PermissionGroup.FORM=Forms
PermissionGroup.ITEM=Items
PermissionGroup.OPPORTUNITY=Opportunities
PermissionGroup.PAYMENT=Payments
PermissionGroup.REPORT_DASHBOARD=Reports and Dashboards
PermissionGroup.SERVICE=Service
PermissionGroup.TASK=Tasks
PermissionGroup.TRAINING=Training
PermissionGroup.USER=Users
PermissionGroup.VEHICLE=Vehicles
PermissionGroup.WORKFLOW=Workflow
PermissionLevel.ADMIN=Administration
PermissionLevel.TELEPHONY=Telephony
PermissionLevel.USER=User
permissions=Permissions
phone.type.home=Home
phone.type.mobile=Mobile
phone.type.work=Work
po.client.call.count=Calls with the client
po.confirm.delete=Are you sure you want to delete this potential opportunity?
po.create.opportunity=Create this opportunity
po.report.assigned=Opportunity created from a potential opportunity
po.report.not.treated=Potential opportunity created by a user phonecall and untreated
po.report.responder=Potential opportunity created by a user phonecall and marked as potential
po.type.new=New
po.type.with.potential=With potential
po.type.without.potential=Without potential
points.report=Points reporting
points.splitting=Points splitting
polaris=Polaris
port=Port
potential.opportunities=Potential Opportunities
potentialopportunity.category=Category
potentialopportunity.client=Client
potentialopportunity.date=Creation date
potentialopportunity.responder=Responder
potentialopportunity.type=Type
potentialopportunity.user=User assigned
potentialOpportunity=Potential opportunity
powered.by=Powered by
preference.title=Preferences
preview.user.signature=Preview your user signature
previous.step=Previous step
prices=Prices
print.pdf=Print a PDF
Print=Print
print=Print
product.added.to.selection=Product added to selection
product.affichage.all=All
product.affichage.inheritance_only=Inheritance only
product.affichage.location=Location
product.affichage.no=No
product.affichage.obsolete=Obsolete
product.affichage.siteweb=Website
product.affichage.traction=Traction
product.affichage=Display
product.attributes.add=Add
product.attributes.list=Product list linked to attributes
product.attributes.modify=Modify
product.attributes=Attributes
product.availabilityDate.short=Availability
product.availabilityDate=Availability date
product.availableInStock=Available in stock
product.categories=Product categories
product.category.add=Add a Product category
product.category.delete.confirm=Are you sure you want to delete the product category?
product.category.deleted=Product category deleted
product.category.edit=Edit Product category
product.category.interest=Client interest
product.category.kijiji=Kijiji category
product.category.manufacture=Manufacturer category
product.category.name=Product category Name
product.category.names=Product categories Name
product.category.notdeleted=Product category not deleted
product.category.notsaved=Category not saved. Please make sure to enter a name.
product.category.saved=Category saved.
product.category.trader=Trader Category
product.category=Category
product.categoryManufacture=Manufacturing category
product.categoryManufature=Manufacture category 
product.catkijiji=Kijiji Category
product.cattrader=Trader Category
product.client2=Previous client
product.client3=Previous client 2
product.clientInterest=Interest
product.clientorinventory.search=Search inventory or client product
product.comments=Comments
product.config=Vehicle configuration
product.costGL=GL cost 
product.costModel=Model cost
product.costReal=Real cost
product.data=Data
product.daysInStock=Days in stock
product.dealernet=Dealer net
product.deleted=Deleted
product.deliveredDate=Delivery date
product.department=Branch
product.description=Product description
product.disclaimer=Product disclaimer
product.discount.tooltip=((MSRP + Options + Freight + PDI - Sales price) + Discount) / (MSRP + Options + Freight + PDI)
product.discount=Discount
product.display.download=Download this page
product.display.url.added=Product display URL added at the end of your message. The customer will be able to see the product display by clicking on this link.
product.disponibilityStatus.nostock=Out of stock
product.disponibilityStatus.stock=In stock
product.disponibilityStatus=Availability status
product.dueDate=Due date
product.duplicate.serialnumber=Another product already exists with this serial number. Are you sure you want to save a product with the same serial number?
product.edit.traction.localisation=Locate product
product.engine=Engine
product.enginename=Engine name
product.export.choose=Choose PDF Template
product.export.error=No PDF template selected
product.export.print=Print
product.export.title=Print PDF template
product.exteriorColor=Exterior color
product.freight=Freight
product.fueltype=Fuel type
product.groupcode=Group
product.guarantee=Guarantee
product.hours=Hours
product.hp=HP
product.id=ID
product.identifier=Identifier
product.images.view=View images
product.images=Images
product.inspectionStatus=Inspection status
product.interest=Interest
product.interiorColor=Interior color
product.invlink=Inv. link
product.isStock=In Stock
product.isUsed=Is used
product.keyLocation=Key location
product.kilometers=Kilometers
product.lautopak.status.addedoptions=Added options
product.lautopak.status.brokerage=Brokerage
product.lautopak.status.confirmed=Confirmed
product.lautopak.status.consignment=Consignment
product.lautopak.status.crate=Crate
product.lautopak.status.instock=In stock
product.lautopak.status.location=Location
product.lautopak.status.modifiedstock=Modified stock
product.lautopak.status.mounted=Mounted
product.lautopak.status.ordered=Ordered
product.lautopak.status.packedup=Packed up
product.lautopak.status.packeduppdicompleted=Packed up PDI completed
product.lautopak.status.pdicompleted=PDI completed
product.lautopak.status.solddelivered=Sold delivered
product.lautopak.status.soldnondelivered=Sold not delivered
product.lautopak.status.trade=Trade
product.lautopak.status.transit=Transit
product.lautopakStatus=DMS status
product.length=Length
product.licenseNumber=Licence number
product.list=Product list
product.localisations=DMS Location
product.location=Location
product.magentocat=Magento category
product.make.add=Add a product make
product.make.delete.confirm=Are you sure to delete this make?
product.make.deleted=The product make has been deleted!
product.make.edit=Edit product make
product.make.missing.profile=Missing profile
product.make.notsaved=An error occured while saving the product make
product.make.saved=The product make has been added!
product.make.sync.success=Makes have been synchronized.
product.make.sync=Sync makes
product.make=Make
product.makes=Product makes
product.modDate=Modification date
product.model=Model
product.modelCode=Model code
product.name=Product name
product.navCategory=Compatible category
product.navMake=Compatible make
product.navModel=Compatible model
product.navYear=Compatible year
product.nbstockday2=Days
product.nbstockday=Days in stock
product.new=New
product.nodetails=No Details...
product.notStock=Not stock
product.notUsed=New
product.option.clientChoice.absorb=Absorbs the fee
product.option.clientChoice.client=Client
product.option.clientChoice.contract=Contract
product.option.clientChoice.default=----
product.option.clientChoice.finance=Finance
product.option.delete=Delete option
product.option.inspection.average=Average
product.option.inspection.conform=Conform
product.option.inspection.default=----
product.option.inspection.notapplicable=N/A
product.option.inspection.notConform=Non-conformance
product.option.salesManagerChoice.billClient=Client bill of sale
product.option.salesManagerChoice.default=----
product.option.salesManagerChoice.repairAtSale=Repair at sale
product.option.salesManagerChoice.repairNow=Repair now
product.option.salesManagerChoice.sellAsIs=Sell as is
product.option.status.final=Final
product.option.status.inspection=Inspection
product.option.status.salesManager=Sales Manager
product.option.status.technicalAdviser=Technical Adviser
product.option.type.default=
product.option.type.other=Manually added
product.options.before_showroom=Before showroom
product.options.declaration_client_usersales=Declaration client and sales
product.options.estimated=Estimate
product.options.inspection_done=Inspection done
product.options.inspection_inprogress=Inspection in progress
product.options.no_conform_client=Presentation of non-conformance to the client
product.options.none=None
product.options.showroom=Showroom
product.options.waiting_inspection=Awaiting inspection
product.options.waiting_product=Awaiting product
product.options.work_accepted=Work accepted
product.options.work_done=Work done
product.options.work_inprogress=Work in progress
product.options.work_waiting=Awaiting work
product.options=Options
product.options_price=Options price
product.orderDate=Order date
product.paidDate=Date paid
product.parts=Parts
product.payMonth=Price per month
product.payWeek=Price per week
product.PDI=PDI
product.percentageSales=off
product.potentialPrice=Potential price
product.prepcost=Preparation cost
product.prepdetail=Preparation
product.price=Display price
product.priceAccepted=Price accepted
product.priceAsked=Price requested
product.priceDisplay=Trade display value
product.priceMinimum=Minimum price
product.priceMsrp=MSRP
product.priceReal=Real value
product.priceSpecial=Special price
product.priceTotal=Price with options
product.prixMsrp=MSRP price
product.prixSpecial=Special price
product.profile=Profile
product.promo2=Promotion 2
product.promo3=Promotion 3
product.promo=Promotion
product.promotion2=Promotion 2
product.promotion3=Promotion 3
product.promotion=Promotion
product.promovalid2=Promotion 2 valid
product.promovalid3=Promotion 3 valid
product.promovalid=Promotion  valid
product.qty=Quantity in stock
product.rate=Rate
product.reserved=Reserved
product.search.txt=Search vehicle by name, year, make, model code or stock number
product.search=Search product
product.sellingOptions2=Selling price options
product.sellingOptions=Options
product.serialnumber2=Serial number 2
product.serialnumber=Serial number
product.serie=Serial Number
product.source=Source
product.specialPrice1=Price 1
product.specialPrice2=Price 2
product.state=State
product.status=Status
product.stockNumber=Stock number
product.stocknumberMagento=Magento stock number 
product.stocktype.inventory=Inventory
product.stocktype.location=Location
product.stocktype.root=Root
product.stockType=Stock type
product.suffix=suffix
product.surcharge=Surcharge
product.termMonth=Monthly term
product.termWeek=Weekly term
product.traction.add.localisation=Add a location
product.traction.localisation.empty=Product location must be filled out
product.traction.localisation.saved=Product located
product.traction.localisation=Location
product.traction_price=Price
product.trcost=Transport cost
product.trdetail=Transport
product.type.all=All
product.type.clientproduct=Client product
product.type.new=New inventory
product.type.parts=Parts
product.type.product=Product
product.type.used=Used inventory
product.type=Type
product.used=Used
product.validpromotion=Promotion validity
product.vehiculeNavs=Compatibility
product.warrantyTerm=Warranty
product.weight=Weight
product.year=Year
product=Product
productbox.workflow=Choose a workflow to use
productboxlinktoworkflow.error=You don't have authorization to complete this action
productdatatablecolumn.delete.confirm=Are you sure you want to delete this product's additional column?
productdatatablecolumn.label=Column title
productdatatablecolumn.list=Additional columns within the product list
sales_poster_big_promo_by_week=8x11 promotion with payment
sales_poster_big_promo_options_by_week=8x11 promotion with options and payment
sales_poster_big_promo_options=8x11 promotion with options
sales_poster_big_promo=8x11 promotion 
sales_poster_big_regular_by_week=8x11 regular with payment
sales_poster_big_regular_options_by_week=8x11 regular with options and payment
sales_poster_big_regular_options=8x11 regular with options
sales_poster_big_regular=8x11 regular
sales_poster_small_promo_by_week=4x11 promotion with payment
sales_poster_small_promo=4x11 promotion
sales_poster_small_regular_by_week=4x11 regular with payment
sales_poster_small_regular=4x11 regular
vehicleExport=Sales display
productfilter.addelement=Add an element to the filter
productfilter.delete.confirm=Are you sure you want to delete this product filter?
productfilter.list=Product filter list
productfilter.name=Name
productfilter.productType=Product type
productfilterelement.attribute=Attribute
productfilterelement.attributeCategory=Attribute category
productfilterelement.attributeName=Attribute name
productfilterelement.booleanFalseLabel=False value label
productfilterelement.booleanTrueLabel=True value label
productfilterelement.default=Default values
productfilterelement.filter.on=Filter on
productfilterelement.invalid.type=Type [{0}] is not valid with the filter [{1}]
productfilterelement.label=Label
productfilterelement.name=Name
productfilterelement.ordre=Order
productfilterelement.property=Property
productfilterelement.type.boolean=Boolean
productfilterelement.type.multipleselection=Multiple selection
productfilterelement.type.number=Number (Minimum/Maximum)
productfilterelement.type.numberrange=Number range (Minimum/Maximum)
productfilterelement.type.radio=Radio buttons
productfilterelement.type.selection=Simple selection
productfilterelement.type.text=Text search
productfilterelement.type.textselection=Text search with selection
productfilterelement.type=Type
productmanager.active.filters=Active filters
productmanager.add.configurator=Add a new configurator
productmanager.add.configurator_filter=Add a new filter
productmanager.add.configurator_filter_config=Add a new filter configuration
productmanager.add.configurator_filter_config_dataprice=Add a new filter price
productmanager.add.error=Add a new error
productmanager.add.inherit=Add a new inheritance
productmanager.add=Products
productmanager.addattributes=Add attributes to products
productmanager.addattributesfromdefaultlocal=Add attributes to another location
productmanager.addimagestoall=Add image to products
productmanager.addimagestoselected=Add image to products selected
productmanager.addspecstoselected.error=Missing category, name or value
productmanager.addspecstoselected=Add specifications to products selected
productmanager.addtolinks_partnotcompatible=Add selected incompatible parts to the main current
productmanager.addtolinks_partrequired=Add selected required parts to the main current
productmanager.addtolinks_partsubs=Add selected parts to the main current
productmanager.addtolinks_prdmain=Replace main products with those selected
productmanager.addtolinks_prdsubs=Add selected products to the main current
productmanager.attribute.category=Category attribute
productmanager.attribute.filter=Add a new attribute filter
productmanager.attribute.name=Category name
productmanager.attribute.save=Save
productmanager.attribute.unlinknotcompatibleparts=Delink all incompatible parts
productmanager.attribute.unlinknotcompatibleselparts=Delink uncompatible parts selected
productmanager.attribute.unlinkreqparts=Delink all parts required
productmanager.attribute.unlinkreqselparts=Delink required parts selected
productmanager.attribute.value=Category value
productmanager.attributes.allspecs=All category attributes
productmanager.attributes.allspecsmodify=Modify all attributes
productmanager.attributes.allspecsname=All attribute names
productmanager.attributes.allspecsvalue=All attribute values
productmanager.attributes=Attributes
productmanager.bldstatus.noconfigurablespecification=No configurable specifications
productmanager.build=Product manager build
productmanager.cloneproducts=Clone products
productmanager.conditions=Conditions
productmanager.config.addcategorycat=Add a new category
productmanager.config.addconfig=Add a new configuration
productmanager.config.addkijijicat=Add a new Kijiji category
productmanager.config.addmagentocat=Add a new Magento category
productmanager.config.addmagentointeret=Add a new Magento interet
productmanager.config.addmake=Add a new make
productmanager.config.addsecondarylinks=Add a new secondary link
productmanager.config.addtradercat=Add a new Trader category
productmanager.config.defaultaffichage=Default inventory displayed
productmanager.config.defaultgroup=Default group
productmanager.config.defaultimagepath=Default image path
productmanager.config.defaultmagentosurls=Magento Urls
productmanager.config.defaultmagentourl=Magento Settings
productmanager.config.managecat=Manage category:
productmanager.config=Configuration
productmanager.configurator.cellsconf=Configurable variable
productmanager.configurator.cellsconf_linker=Linked variables configuration
productmanager.configurator.cellsconf_links=Final links configuration
productmanager.configurator.config.actions=Actions
productmanager.configurator.config.added=Configuration has been added
productmanager.configurator.config.attributes=Attributes
productmanager.configurator.config.category=Category
productmanager.configurator.config.exist=Configuration already exists
productmanager.configurator.config.id=ID
productmanager.configurator.config.name=Name
productmanager.configurator.config.options=Options
productmanager.configurator.config.parts=Parts
productmanager.configurator.config.price=Price
productmanager.configurator.config.showroom=Showroom
productmanager.configurator.config.title=Configuration
productmanager.configurator.config.urlVideo=Video URL
productmanager.configurator.config.value=Value
productmanager.configurator.deleted.confirm=Are you sure you want to delete this configurator?
productmanager.configurator.deleted=Configurator has been deleted
productmanager.configurator.duplicate=Duplicate configuration
productmanager.configurator.editors.Main=Editor : Body
productmanager.configurator.editors.Product=Editor : Products
productmanager.configurator.exist=The configurator name already exists
productmanager.configurator.feature.added=Feature has been added
productmanager.configurator.feature.deleted.confirm=Are you sure you want to delete this feature?
productmanager.configurator.feature.deleted=Feature has been deleted
productmanager.configurator.features.addfeatures=Add new features
productmanager.configurator.features.name=Name
productmanager.configurator.features.page.addattributes=Add a new attribute filter
productmanager.configurator.features.page.addfeatures=Add a new feature filter 
productmanager.configurator.features.rename=Display
productmanager.configurator.group.deleted.confirm=Are you sure you want to delete this product group, all its product sublinks and associated configurators?
productmanager.configurator.group.deleted=Product group has been deleted
productmanager.configurator.group.exist=The product group already exists.
productmanager.configurator.group.id=ID
productmanager.configurator.group.name.placeholder=Enter the name of products group
productmanager.configurator.group.name=Product group name
productmanager.configurator.group.success=The product group has been added.
productmanager.configurator.links.deleted.confirm=Are you sure to delete this link: {0}?
productmanager.configurator.links.deleted.success=The trim has been deleted
productmanager.configurator.links.model=Trim names
productmanager.configurator.links.modelMagento.exists=Select from existing trims
productmanager.configurator.links.modelMagento.name=Trim name
productmanager.configurator.links.modelMagento.success=The trim has been added successfully
productmanager.configurator.links.modelMagento.value=Trim value
productmanager.configurator.links=Products trims
productmanager.configurator.links_main=Main
productmanager.configurator.links_subs=Subs
productmanager.configurator.mapping.options=Options mapping
productmanager.configurator.mapping.parts=Parts mapping
productmanager.configurator.mapping.ui=Products mapping
productmanager.configurator.mapping.variable=Products mapping by variables
productmanager.configurator.name.placeholder=Enter the name of configurator
productmanager.configurator.name=Configurator name
productmanager.configurator.option.category=Category
productmanager.configurator.option.description=Description
productmanager.configurator.option.id=ID
productmanager.configurator.option.image=Image
productmanager.configurator.option.name=Name
productmanager.configurator.option.optional=Optional
productmanager.configurator.option.order=Order
productmanager.configurator.option.price=Price
productmanager.configurator.parts.btn=Add parts
productmanager.configurator.parts.incompatibleBtn=Add to incompatible parts
productmanager.configurator.parts.link=Link parts to products
productmanager.configurator.parts.requiredBtn=Add to required parts
productmanager.configurator.pricecummulative=Add the price to the configurable parent item
productmanager.configurator.pricesum=Add price with inherited configurable
productmanager.configurator.sublink.deleted=The product link has been deleted
productmanager.configurator.success=The configurator has been added
productmanager.configurator=Configurator
productmanager.configuratorpreviewtab=Preview configurator
productmanager.configurators.features=Features and filters
productmanager.configurators.filters=Filters
productmanager.configurators=Configurators
productmanager.configuratorsettingstab=Configurator settings
productmanager.configuratortab=Configurator
productmanager.convertconfigurable=Convert to configurable
productmanager.datatable.next=Next
productmanager.datatable.previous=Previous
productmanager.deleteallnoproduct=No product selected 
productmanager.deleteallproduct=Delete selected products
productmanager.dev=Dev
productmanager.duplicategroup=Duplicate selected groups
productmanager.edit.group.formulas=Edit formulas of
productmanager.error.name=Error name
productmanager.error=Error: Fix to be displayed on website
productmanager.errorstatus.buildnok=NOK
productmanager.errorstatus.buildok=OK
productmanager.errorstatus.categoryautre=Category various/other
productmanager.errorstatus.categorymagento=No magento category 
productmanager.errorstatus.conditions_1=Build condition #1
productmanager.errorstatus.conditions_2=Build condition #2
productmanager.errorstatus.conditions_3=Build condition #3
productmanager.errorstatus.conditions_4=Build condition #4
productmanager.errorstatus.conditions_5=Build condition #5
productmanager.errorstatus.conditions_6=Build condition #6
productmanager.errorstatus.conditions_7=Build condition #7
productmanager.errorstatus.conditions_8=Build condition #8
productmanager.errorstatus.conditions_9=Build condition #9
productmanager.errorstatus.conditions_10=Build condition #10
productmanager.errorstatus.magento.nok=Not on website
productmanager.errorstatus.magento.nok2=Website not updated
productmanager.errorstatus.magento.ok2=Updated on website
productmanager.errorstatus.magento.ok=On website
productmanager.errorstatus.no.category=No category
productmanager.errorstatus.no.groupecode=No group code
productmanager.errorstatus.no.interetcat=No magento interest 
productmanager.errorstatus.no.kijijicat=No Kijiji category
productmanager.errorstatus.no.make=No make
productmanager.errorstatus.no.model=No model
productmanager.errorstatus.no.modelcode=No model code
productmanager.errorstatus.no.tradercat=No Trader category
productmanager.errorstatus.nobuild=No Build
productmanager.errorstatus.noimages=No Images
productmanager.errorstatus.noinventoryimages=No inventory images
productmanager.errorstatus.not.accepted=Not accepted
productmanager.errorstatus.not.affichage.all=Do not display ALL
productmanager.errorstatus.noupdate_required=UP TO DATE
productmanager.errorstatus.prix.kawasaki0=Kawasaki at price 0
productmanager.errorstatus.prix.wget0=WGET at price 0
productmanager.errorstatus.promoInternalAmountfinished=Internal promotion expired!
productmanager.errorstatus.promoManufacturerAmountfinished=Manufacturer promotion expired!
productmanager.errorstatus.promoTextfinished=Promotional text expired!
productmanager.errorstatus.update_required=UPDATE REQUIRED!!!
productmanager.errorstatus.vehicle.error=Vehicle error
productmanager.errortab=Error: Resolve in order to display on website
productmanager.feed.action=Action
productmanager.feed.cancel=Delete feed now
productmanager.feed.cantdel=You cannot delete this configuration because it is used in the feed:
productmanager.feed.cantdelfilter=You cannot delete this filter because it is used in the feed:
productmanager.feed.config.name=Configuration name
productmanager.feed.confignew=Add a new configuration feed
productmanager.feed.filter.name=Filter name
productmanager.feed.filter=Feed filters
productmanager.feed.filternew=Add a new feed filter
productmanager.feed.name=Name
productmanager.feed.progress=Progress
productmanager.feed.queued=QUEUED
productmanager.feed.run=Run feed now
productmanager.feed.runall=Run all today's tasks
productmanager.feed.select=Select a feed
productmanager.feed.task.name=Feed name
productmanager.feed.time=Time
productmanager.feed=Feed
productmanager.feeds.listadd=Feed configuration lists to add
productmanager.feeds.listfilteradd=Feed filter lists to add
productmanager.feeds=Feeds
productmanager.filter.colums=Columns
productmanager.filter.views.1=View 1
productmanager.filter.views.2=View 2
productmanager.filter.views.3=View 3
productmanager.filter.views.defaut=Default
productmanager.filter.views=Views
productmanager.group.cancel=Delete group now
productmanager.groups.create.formula=Modify the formula
productmanager.groups.preview=Preview formula
productmanager.groups=Groups
productmanager.history=History
productmanager.igncachefilter=Show cache
productmanager.imagepixelguru.status.deleted=Deleted
productmanager.imagepixelguru.status.finished=Finished
productmanager.imagepixelguru.status.ignored=Ignored
productmanager.imagepixelguru.status.inprogress=In progress
productmanager.imagepixelguru.status.inverification=In verification
productmanager.imagepixelguru.status.ready=Ready
productmanager.imagepixelguru.status.verified=Verified
productmanager.imagepixelguru.status.waiting=Waiting
productmanager.images.metadata=IPTC Metadata
productmanager.images.organize=Organize
productmanager.images.organizesave=Save order
productmanager.images=Images
productmanager.import.action=Add an action to
productmanager.import.add=Add
productmanager.import.addprofile=Add import profile
productmanager.import.autoupdateimport=Auto update modification import
productmanager.import.build.acceptnoimages=Accept products without images
productmanager.import.build.acceptnoimagesused=Accept used products without images
productmanager.import.build.magentoasync=Use magento async
productmanager.import.build.magentostag=Use magento stag server
productmanager.import.build.noconfigurable=Unable to export configurables
productmanager.import.build.nodoublons=No duplicates
productmanager.import.build.nomergeproduct=No merged products
productmanager.import.build.nooverrideaffichage=Do not override display column
productmanager.import.build.nooverridekilometer=Do not override kilometers
productmanager.import.build.permitkawa0=Accept Kawasaki prices at 0
productmanager.import.build.roundprice=Round price up to 99/75/49/25
productmanager.import.build.sendmagentoallstore=Export magento data to all boutiques
productmanager.import.build.useaccessoirestatut=Use accessories status
productmanager.import.build.usecolkilometrage=Use kilometers column
productmanager.import.build.usedisplayprice=Use the display price with the special price
productmanager.import.build.usehtml=Use HTML instead of JSON for attributes
productmanager.import.build.usetaxecol=Use tax column
productmanager.import.categoryid=Category ID
productmanager.import.categorylinkid=Category link ID
productmanager.import.categorylinkname=Category link name
productmanager.import.categoryname=Category name
productmanager.import.config=Import Configuration
productmanager.import.configurable=Configurable
productmanager.import.debug.checkmagentocategory=Verify if Magento category exists in Magento website 
productmanager.import.debug.copyimagetoroot=Copy root images to a zip file
productmanager.import.debug.deleteoldmagentosku=Delete old magento skus
productmanager.import.debug.detectfalsepng=Detect and copy false PNGs
productmanager.import.debug.fixconfigurator=Fix configurator duplicate images
productmanager.import.debug.magento.downloadfeed=Download Magento feed
productmanager.import.debug.magento.exportfeed=Export Magento feed
productmanager.import.debug.magentoparams=Debug Magento parameters
productmanager.import.debug.profileinventory=Enable this profile for inventory updates in Traction
productmanager.import.debug.resetimages=Reset Magento product images
productmanager.import.debug.resetproducts=Reset Magento products
productmanager.import.debug.resyncmagentocat=Resync Magento categories
productmanager.import.debug.resyncmagentocouleur=Resync Magento color table
productmanager.import.debug.setaffichageno=Apply NO display to avoid classification
productmanager.import.debug.stacktrace=Debug error messages
productmanager.import.filelocation=File import name
productmanager.import.importall=Import all entries
productmanager.import.importprofilename=Import profile name
productmanager.import.inheritancename=Inheritance name
productmanager.import.linkheader=Link header
productmanager.import.modify=Modify
productmanager.import.override=Override
productmanager.import.profilename=Profile name
productmanager.import.resynccell=Resync cell order
productmanager.import.resyncmagento.attributes=Resync Magento attributes
productmanager.import.resyncmagento.attributesattrset=Setup Magento attributes/attrset
productmanager.import.resyncmagento.attrset=Resync Magento attrset
productmanager.import.resyncmagento.category=Resync Magento website categories
productmanager.import.resyncmagento.syncin=Synchronize Magento variables to the product manager
productmanager.import.resyncmagentoc.export=Export Traction categories to Magento
productmanager.import.skipimport100000=Ignore & import 100 000 entries
productmanager.import.skipimport10000=Ignore & import 10 000 entries
productmanager.import.templateoverride=Template override
productmanager.import.thread.magentopassword=Magento password
productmanager.import.thread.magentousername=Magento username
productmanager.import.thread.priceupdate=Number of Magento processes for price updates
productmanager.import.thread.productget=Number of Magento processes to retrieve the product list
productmanager.import.thread.productset=Number of Magento processes for adding products
productmanager.import.thread.productupdate=Number of Magento processes for product updates
productmanager.import.validate10000=Authorize 10 000 entries
productmanager.import.validate50000=Authorize 50 000 entries
productmanager.import.viewname=View name
productmanager.import.youtubekey=Youtube key
productmanager.import=Import status
productmanager.importexcelfile=Please select an excel file with at least a stock number as a header
productmanager.importfromexcel=Import from excel file
productmanager.inherit=Inherit
productmanager.menu=Product manager
productmanager.metadata.author=Author
productmanager.metadata.authortitle=Author title
productmanager.metadata.captiondesc=Description caption 
productmanager.metadata.captionwriter=Writer caption 
productmanager.metadata.category=Category
productmanager.metadata.city=City
productmanager.metadata.copyright=Copyright
productmanager.metadata.countrylocation=Country location name
productmanager.metadata.credit=Credit
productmanager.metadata.datecreate=Date created
productmanager.metadata.documenttitle=Document title
productmanager.metadata.filedesc=File description
productmanager.metadata.filename=File name
productmanager.metadata.headline=Headline
productmanager.metadata.keywords=Keywords
productmanager.metadata.province=Province/State
productmanager.metadata.reference=Original, Reference
productmanager.metadata.source=Source
productmanager.metadata.specialinstruction=Special instructions
productmanager.metadata.sublocation=Sublocation
productmanager.metadata.supcategory=Supplementary category
productmanager.metadata.urgency=Urgency
productmanager.migration.features.affichage=Display
productmanager.migration.features.assembly_file=Assembly file
productmanager.migration.features.assembly_part=Assembly part
productmanager.migration.features.assembly_position=Assembly position
productmanager.migration.features.attrset=Attrset
productmanager.migration.features.baselabel=Base label
productmanager.migration.features.boatdealercategory=Boatdealer category
productmanager.migration.features.boatdealerclass=Boatdealer CLASS
productmanager.migration.features.boatdealerenginename=Boatdealer engine
productmanager.migration.features.boatdealerhp=Boatdealer HP
productmanager.migration.features.boatdealerhpint=Boatdealer HP INT
productmanager.migration.features.boatdealertype=Boatdealer Type
productmanager.migration.features.boatdealerweight=Boatdealer Weight
productmanager.migration.features.carburant=Fuel
productmanager.migration.features.category=Category
productmanager.migration.features.chambre=Room
productmanager.migration.features.classid=Class ID
productmanager.migration.features.color_setmagento=ColorSet Magento
productmanager.migration.features.cost=Cost
productmanager.migration.features.couleurmagento=Magento color
productmanager.migration.features.csv.delimiter=CSV DELIMITER
productmanager.migration.features.csv.enclosure=CSV ENCLOSURE
productmanager.migration.features.csv.header.include=CSV HEADER INCLUDE
productmanager.migration.features.csv.header=CSV HEADER
productmanager.migration.features.cuisine_exterieure=Exterior kitchen
productmanager.migration.features.cuisine_exterieure_option=Exterior kitchen options
productmanager.migration.features.decormagento=Magento decor
productmanager.migration.features.description=Description
productmanager.migration.features.description_kijiji=Kijiji description
productmanager.migration.features.description_trader=Trader description
productmanager.migration.features.emplacement_cuisine=Kitchen location
productmanager.migration.features.engine=Engine
productmanager.migration.features.Extensions=Extensions
productmanager.migration.features.exteriorcolour=Exterior color
productmanager.migration.features.filelist=File list
productmanager.migration.features.fueltype=Fuel type
productmanager.migration.features.genremagento=Magento genre
productmanager.migration.features.ilot_de_cuisine=Kitchen
productmanager.migration.features.ilot_de_cuisine_option=Kitchen options
productmanager.migration.features.interiormagento=Magento interior
productmanager.migration.features.iprofile=iProfile
productmanager.migration.features.is_featured=Is featured
productmanager.migration.features.is_used=Is Used
productmanager.migration.features.kijijiengine=Kijiji engine
productmanager.migration.features.kijijiid=Kijiji ID
productmanager.migration.features.kijijimodel=Kijiji model
productmanager.migration.features.kilometer=Kilometers
productmanager.migration.features.length=Length
productmanager.migration.features.lengthint=Length INT
productmanager.migration.features.liquidation=Liquidation
productmanager.migration.features.lit_principal=Main bed
productmanager.migration.features.lit_principal_option=Main bed options
productmanager.migration.features.lit_superpose=Bunk bed
productmanager.migration.features.litsupplementaire=Additional beds
productmanager.migration.features.livraisonMagento=Magento shipping
productmanager.migration.features.mainurl=Main URL
productmanager.migration.features.managestock=Manage stock
productmanager.migration.features.manufacturer=Manufacturer
productmanager.migration.features.materielmagento=Magento material
productmanager.migration.features.metadesc=Meta description
productmanager.migration.features.metakey=Meta keywords
productmanager.migration.features.metatitre=Meta title
productmanager.migration.features.modelmagento=Magento model
productmanager.migration.features.name=Name
productmanager.migration.features.paysemaine=Weekly payments
productmanager.migration.features.price=Price
productmanager.migration.features.qty=Qty
productmanager.migration.features.salleamanger_option=Dining room options
productmanager.migration.features.section=Section
productmanager.migration.features.shortdescription=Short description
productmanager.migration.features.showroom=Showroom
productmanager.migration.features.sizemagento=Magento size
productmanager.migration.features.sku=Sku
productmanager.migration.features.sku_delete=Sku deleted
productmanager.migration.features.sku_supplier=Sku reference
productmanager.migration.features.skuprofile=Sku profile
productmanager.migration.features.status=Status
productmanager.migration.features.stylemagento=Magento style
productmanager.migration.features.succursales=Branch
productmanager.migration.features.supplier_sku=Supplier sku
productmanager.migration.features.tagsMagento=Magento tags
productmanager.migration.features.traderid=Trader ID
productmanager.migration.features.tradermake=Trader Make
productmanager.migration.features.tradermodel=Trader Model
productmanager.migration.features.url_key=URL Key
productmanager.migration.features.url_video=URL Video
productmanager.migration.features.vehicule_navigator=Vehicle navigator
productmanager.migration.features.visibility=Visibility
productmanager.migration.features.weight=Weight
productmanager.migration.features.year=Year
productmanager.migration.features.year_num=Number of years
productmanager.migration=Migration
productmanager.Modify.group=Modifying of
productmanager.multilocale.addmultilocale=Add multi locations
productmanager.multilocale.error=Multi locations already exist
productmanager.multilocale.name=Name of multi locations
productmanager.multilocale.save=Multi locations have been saved
productmanager.multilocale=Multi location
productmanager.options.name=Name
productmanager.options.optional=Optional
productmanager.options.order=Order
productmanager.options.price=Price
productmanager.options=Options
productmanager.partsnotcompatible=Parts are not compatible with product
productmanager.partsrequired=Parts are linked to product
productmanager.pixelguru.add=Add
productmanager.pixelguru.date.created=Date created
productmanager.pixelguru.ignore=Ignore
productmanager.pixelguru.new.image=New image
productmanager.pixelguru.only.treated=Only treated
productmanager.pixelguru.product.image=Image
productmanager.pixelguru.product.stocknumber=Stock number
productmanager.pixelguru.product=Product
productmanager.pixelguru.replace=Replace
productmanager.pixelguru.resync=Resync images
productmanager.pixelguru.status=Status
productmanager.pixelguru.verification=Verify
productmanager.pixelguru=Pixelguru
productmanager.preview=Preview
productmanager.products.delete=Are you sure you want to delete
productmanager.products.generate=Generate feed
productmanager.products=Products
productmanager.profile.addprofile=Add a new profile
productmanager.profile.cells=Cells in the profile
productmanager.profile.error.cells=Cells must be selected
productmanager.profile.error.name=A profile name is mandatory
productmanager.profile.name=Profile name
productmanager.profile=Profile
productmanager.profiles=Profiles
productmanager.removefrommagento=Remove from Magento
productmanager.search.filter=Product search
productmanager.sendtomagento=Export to Magento
productmanager.surcharge=Surcharge
productmanager.task.assemblyupdate_kawasaki=Assembly Update KAWASAKI
productmanager.task.assemblyupdate_ktm=Assembly Update KTM
productmanager.task.assemblyupdate_polaris=Assembly Update Polaris
productmanager.task.build=Build
productmanager.task.diffbuild=Differential build
productmanager.task.differential_delete=Delete differential
productmanager.task.difffastbuild=Fast build differential 
productmanager.task.diffpriceupdate=Update differential price 
productmanager.task.dms=DMS
productmanager.task.downloadfeed=Download feed
productmanager.task.downloadzii=Download external API
productmanager.task.exportbuild=Export build
productmanager.task.exportdiffbuild=Export differential build
productmanager.task.fastbuild=Fast build
productmanager.task.inventoryupdate=Inventory update
productmanager.task.lautopak=Lautopak task
productmanager.task.lespacsfeed=LesPacs feed
productmanager.task.magentopartapi=Magento API
productmanager.task.magentopartapi_delete=Delete Magento API
productmanager.task.magentopartapi_downloadfeed=Download Magento API feed
productmanager.task.magentopartapi_nostock=No stock Magento API
productmanager.task.magentopartapi_uploadfeed=Upload Magento API feed
productmanager.task.priceupdate=Price update
productmanager.task.qtyupdate_fox=QTY Update FOX
productmanager.task.qtyupdate_fxr=QTY Update FXR
productmanager.task.qtyupdate_husqvarna=QTY Update HUSQVARNA
productmanager.task.qtyupdate_indian=QTY Update INDIAN
productmanager.task.qtyupdate_kimpex=QTY Update KIMPEX
productmanager.task.qtyupdate_klim=QTY Update KLIM
productmanager.task.qtyupdate_ktm=QTY Update KTM
productmanager.task.qtyupdate_motovan=QTY Update Motovan
productmanager.task.qtyupdate_polaris=QTY Update POLARIS
productmanager.task.validateimport=Validate import
productmanager.tasks=Tasks
productmanager.text.cancel=Delete text now
productmanager.text=Texts
productmanager.type.importmod=Import module
productmanager.type.importmodconfigurable=Import configurable module
productmanager.type.importnew=Import new
productmanager.type.importnewconfigurable=Import new configurable
productmanager.type.importwarning=Importation warning
productmanager.type.inventory=Inventory
productmanager.type.location=Location
productmanager.type.root=Root
productmanager.type.template=Template
productmanager.validate=Validate selected groups
productmanager.verifyaddattributes=Are you sure you want to add this attribute to all {0} selected products ?
productmanager.verifyaddattributesfromdefaultlocal=Are you sure you want to add all attributes from default localisation to another localisation for these {0} selected product ?
productmanager.verifyaddimagestoselected=Are you sure you want to add an image to all {0} selected product ?
productmanager.verifyaddtolinks_partsubs=Are you sure you want to add all {0} selected part ?
productmanager.verifyaddtolinks_prdmain=Are you sure you want to replace all product Main with {0} selected product ?
productmanager.verifyaddtolinks_prdsubs=Are you sure you want to add all {0} selected product ?
productmanager.verifycloneyearplusun=Are you sure you want to clone all {0} selected product ?
productmanager.verifyconvertconfigurable=Are you sure you want to convert all {0} selected product ?
productmanager.verifydeleteallproduct=Are you sure you want to delete all {0} selected product ?
productmanager.verifyduplicategroup=Are you sure you want to add all {0} selected group ?
productmanager.verifyremovemagento=Are you sure you want to remove from magento all {0} selected product ?
productmanager.verifysendmagento=Are you sure you want to export to magento all {0} selected product ?
productmanager.warningtab=Warning: Fix for more accurate ads
productmanager.watermark.list=List of Watermark
productmanager.watermark.listadd=List of Watermark to add
productmanager.watermark.noimage.replace=Watermark to use when no image on product
productmanager.watermark.noimagereserved.replace=Watermark to use when no image and product reserved
productmanager.watermark.position=POSITION ON IMAGE
productmanager.watermark.preview=Watermark Preview
productmanager.watermark=Watermark
productmanager.watermarkaddnew=Add a new watermark
productmanager.watermarkalpha=Transparency
productmanager.watermarkbottom=BOTTOM
productmanager.watermarkbottomleft=BOTTOM LEFT
productmanager.watermarkbottomright=BOTTOM RIGHT
productmanager.watermarkcenter=CENTER
productmanager.watermarkchangeposition=Change position of a watermark image
productmanager.watermarkdelete=Delete watermark
productmanager.watermarkdeletea=Delete a watermark
productmanager.watermarkhelp=Help information
productmanager.watermarkhowtoadd=How to add a new watermark?
productmanager.watermarkhowtodelete=How to delete a watermark?
productmanager.watermarkhowtomove=How to move a watermark?
productmanager.watermarkhowtorename=How to rename a watermark?
productmanager.watermarkhowtoupload=How to upload a watermark?
productmanager.watermarkkeepborder=keep image border
productmanager.watermarkleft=LEFT
productmanager.watermarknew=New watermark
productmanager.watermarknoimage=use watermark for products with no image
productmanager.watermarknoimagereserved=use watermark for products with no image and reserved
productmanager.watermarkposition=Position
productmanager.watermarkrename=Rename a watermark
productmanager.watermarkreporiginal=replace original image by watermark
productmanager.watermarkright=RIGHT
productmanager.watermarksave=Save watermark
productmanager.watermarkscale=Scale
productmanager.watermarksearch=search watermark by name...
productmanager.watermarksearchkeyword=search by keyword
productmanager.watermarksystemtab=System Watermark
productmanager.watermarktab=Management Watermark
productmanager.watermarktop=TOP
productmanager.watermarktopleft=TOP LEFT
productmanager.watermarktopright=TOP RIGHT
productmanager.watermarkuploadimage=Upload a watermark image
productmanager.watermarkxlimit=X Maximum
productmanager.watermarkylimit=Y Maximum
productmanager=Product Manager
productoption.images=Images
productoptioncategories=Product option categories
productoptioncategory.add=Add options category
productoptioncategory.addtype=Add an option
productoptioncategory.clientInterests=Linked client interests
productoptioncategory.delete.confirm=Are you sure to delete this product option category?
productoptioncategory.deleted=The product option category has been deleted!
productoptioncategory.description=Description
productoptioncategory.edit=Edit options category
productoptioncategory.name=Name
productoptioncategory.ordre=Order
productoptioncategory.other=Other
productoptioncategory.saved=The options category has been added!
productoptiontype.client=Show in client inspection
productoptiontype.delivery=Show in delivery inspection
productoptiontype.inspection=Show in inspection
productoptiontypes=Options
productpicture.sent.error=Product image not sent. Verify the client cellphone number and try again.
productpicture.sent=product pictures sent
products.manager=Manager
produits.create=Create product
profitability=Profitability
profitPrices=Profits & Margins
promo.active=Active
promo.amount=Amount or text
promo.end=Expiration
promo.start=Start date
promo.status=Status
promo.type.internal=Internal promotion
promo.type.manufacturer=Manufacturer promotion
promo.type.text=Text promotion
promo.type=Promotion type
PromoStatus.ACTIVATED=Activated
PromoStatus.EXPIRED=Expired
PromoStatus.UNSCHEDULED=Unscheduled
PromoStatus.UNSTARTED=Unstarted
property=Property
purchase.invoice=Purchases invoices
pushjs.allowed.body=We can now keep you updated!
pushjs.allowed.title=Thank You :)
quantity=Quantity
readed=Read
recurrence.end.count=After (number of occurences)
recurrence.end.never=Never
recurrence.end.until=The
recurrence.end=End
recurrence.freq.daily=days
recurrence.freq.monthly=months
recurrence.freq.weekly=weeks
recurrence.freq.yearly=years
recurrence.interval=Interval
recurrence=Recurrence
redirectedUser=Redirected user
refresh.stats=Refresh statistics
register.now=Register now
repeat.every=Repeat every
report.age=Age
report.amount=Amount
report.average.short=Ave.
report.average=Average
report.avgDuration.tooltip=Average duration of completed calls
report.avgDuration=Average time
report.avgRating.call=Average call rating
report.avgRating.tooltip=Average rating excluding zeros (1-5 stars)
report.avgRating=Average rating
report.awaiting=Waiting email
report.call.answeredbydeptgroup=Call answered percentage by queue
report.call.delaibydeptgroup=Response time by queue
report.call.receivedbydeptgroup=Calls Received
report.calls.tooltip=Total number of calls
report.calls=Calls
report.createnomailclient.tooltip=Opportunities with a no email client
report.createnomailclient=No email client
report.datanotsaved=Data not saved.
report.datasaved=Data saved.
report.days=Days
report.details=Details
report.filter.day=Day
report.filter.month=Month
report.filter.provider=Provider
report.filter.week=Week
report.filter.year=Year
report.goal.rating=Goal success rate
report.hideEmpty=Show/Hide empty rows
report.importsection=Import section
report.inbound.tooltip=Total number of calls inbound
report.last3year.average=Last 3 years average
report.lastyear=Last year
report.missedcall.ratio=% of calls missed
report.name=Name
report.nbvehicules=Number of vehicles
report.outbound.tooltip=Total number of calls outbound
report.percentandqttbysource=% and quantity by source
report.percentcost=% according to cost
report.potential.opportunity=Potential opportunity report
report.queueAnswerSpd.tooltip=Average anwser speed for transfer into a queue
report.queueAnswerSpd=Queue anwser speed
report.queueReturnCalls.tooltip=Number of transfer in a queue that returned to reception
report.queueReturnCalls=Queued calls returned to reception
report.queues.tooltip=Number of transfered call in a queue
report.queues=Queued calls
report.score=Score
report.seconds=Seconds
report.soldbysource=Sale by source
report.source=Sources report
report.target=Target
report.tasksoon=Task soon
report.tasktodo=Tasks to complete
report.thisyear=This year
report.time=Time
report.title=Report
report.total=Total
report.totalvalue=Total value
report.trackinggoal=Report Tracking / Goal
report.transferAnswerSpd.tooltip=Average answer speed for direct transfers to an extension
report.transferAnswerSpd=Transfer answer speed
report.transferReturnCalls.tooltip=Number of direct transfer to an extension that have returned to reception
report.transferReturnCalls=Transfered calls returned to reception
report.transfers.tooltip=Number of direct transfer to an extension
report.transfers=Calls transfered
report.used=Used
report.user.supervisor=Users supervisor
required.field=Required field
resend.email=Resend email
reservation.color=Reservations colors
reset=Reset
result=Result
return.traction=Return to Traction
review.delete.confirm=Are you sure that you do not want to send this review?
review.delete.success=The review will not be send to Magento
review.error.default=An error occured while processing
review.error.gettingreview=Review not found
review.error.param=There is an error with the parameters
review.noComment=No comments
review.noquestion=No question found
review.send.success=The review has been sent to Magento
product.vehicle.type.insert=Insert text
role.add.user.to.role=Add this user to the role : {0}
role.addnew=Add new role
role.authority=Unique identifier
role.choose=Choose a role
role.delete=Delete this role
role.edit=Edit role
role.list=Role list
role.members=Members in role
ROLE_ADMIN=Admin
ROLE_CALLMANAGER=Call Manager
ROLE_EVENT=Event
ROLE_PRD_ADMIN=Product Manager - Admin
ROLE_PRD_CONFIG=Product Manager - Config
ROLE_PRD_READ=Product Manager - Read
ROLE_PROMO_WRITE=Promotion
ROLE_SALES=Seller
ROLE_SALESMANAGER=Sales Manager
ROLE_SERVICE=Service
ROLE_SERVICE_ADMIN=Service - Admin
ROLE_SERVICE_DISPATCH=Service Dispatch
ROLE_TRAINING_WRITE=Training - Write
ROLE_USER=User
ROLE_VEHICLE_ADMIN=Vehicle - Admin
ROLE_WORKFLOW=Workflow
ROLE_WORKFLOW_WRITE=Workflow - Write
routingschedule.addTitle=Add user
routingschedule.categories=Product Categories:
routingschedule.days=Days:
routingschedule.defaultuser=Default User
routingschedule.delete.confirm=Are you sure to delete this user from the routing schedule?
routingschedule.deleteTitle=Delete user
routingschedule.endTime=End:
routingschedule.interests=Client Interests:
routingschedule.makes=Product Makes:
routingschedule.method.awaiting=Awaiting
routingschedule.method.closingrate=Closing Rate
routingschedule.method.performance=Performance
routingschedule.method.pool=Pool
routingschedule.method.quantity=Quantity
routingschedule.method.random=Random
routingschedule.origins=Sources:
routingschedule.startTime=Start:
routingschedule.states=States:
routingschedule.success=The routing schedule has been updated.
routingschedule.title=Routing Schedule
routingschedule.to=to
routingschedule.users=Users:
row.number=Row number
rows.selected=rows selected
sa=Sa
sales.by.status=Sales by status
sales.inventory=Inventory
sales.objectives.chart=Sales objective, sales ratio and sales for selected period
sales.objectives.last=Sales objective last year
sales.objectives.newinterest.chart=Sales objective new product by interest
sales.objectives.newinterest.pie=Sales objectives by interest
sales.objectives.newmake.chart=Sales objective new product by make
sales.objectives.newmake.pie=Sales objectives by make
sales.objectives.report=Sales Objective Report
sales.objectives.switch=Report calculated by
sales.objectives.tooltip=Opportunity sold / Objective of the month
sales.objectives.type.chart=Sales objective of products by type
sales.objectives.type.pie=Opportunities sold by product type
sales.objectives.update.error=An error occured while updating the objective
sales.objectives.update.success=The objective has been modified
sales.objectives=Sales objective
sales.opportunities.last=Sales opportunities last year
sales.opportunities.newinterest.pie=New product sold by interest
sales.opportunities.newmake.pie=New product sold by make
sales.opportunities=Sales opportunities
sales.opportunity=Sales opportunity
sales.ratio.last=Sales ratio last year
sales.ratio=Sales ratio
sales.salestracking=Sales tracking
sales.sold.last=Opportunities sold last year
sales.sold=Opportunities sold
salesdashboard.emergency=Emergency
salesdashboard.goal.title=Goals
salesdashboard.lastyear.tooltip=Last Year:
salesdashboard.notesandtask=Notes and overdue tasks
salesdashboard.sold.for=Sale as of
salesdashboard.sold.lost.for=Sale lost as of
salesdashboard.sold.today=Sale today
salesmanager.only=Sales manager only
salesmanager=Sales Manager
salesratio.by.status=Sales ratio by status
salesratio.by.user=Ratio of sales per user
salesratio.for.period=Sales ratio and sales for the selected period
salestracking.commission=Commission
salestracking.commissionPercent.total=Commission percentage = (total commission / total profit)
salestracking.commissionPercent=Commission percentage
salestracking.costPrice.tooltip=Cost subtotal // Trade real value
salestracking.costPrice=Cost
salestracking.costPriceNoAcc.tooltip=Cost subtotal without accessories
salestracking.costPriceNoAcc=Cost without accessories
salestracking.finance.tooltip=Percentage of files funded
salestracking.finance=Funded files
salestracking.opportunityAccessories=Sale price of accessories
salestracking.opportunityFinalMinimumPrice.tooltip=Opportunity minimum selling price
salestracking.opportunityFinalMinimumPrice=Minimum price
salestracking.opportunityPotentialPrice.tooltip=Potential price of the product of the opportunity
salestracking.opportunityPotentialPrice=Potential price
salestracking.opportunityPotentialPriceLack.tooltip== Selling - Potential price
salestracking.opportunityPotentialPriceLack=Lack
salestracking.profit.percent.total=Percentage of total profit = (total profit / total selling)
salestracking.profit=Profit
salestracking.profitNoAcc=Profit without accessories
salestracking.sellingPrice.tooltip=Opportunity subtotal // Trade display price
salestracking.sellingPrice=Selling
salestracking.sellingPriceNoAcc.tooltip== Selling - Accessories - Trade
salestracking.sellingPriceNoAcc=Selling without accessories
salestracking=Sales tracking dashboard
save.user.communication.transfer.success=success
save=Save
saved=Saved
scenario=Scenario
schedules=Schedules
schwartzmonitor.currentTime=Current Time
schwartzmonitor.editcron.button.cancel=Cancel
schwartzmonitor.editcron.button.save=save
schwartzmonitor.editcron.expression=Cron Expression
schwartzmonitor.editcron.reschedule=Reschedule
schwartzmonitor.editcron.title=Quartz Jobs - Reschedule
schwartzmonitor.headline=Quartz Jobs
schwartzmonitor.job.actions=Actions
schwartzmonitor.job.duration=Job ran in
schwartzmonitor.job.exception=Job threw exception
schwartzmonitor.job.lastRun=Last Run
schwartzmonitor.job.name=Name
schwartzmonitor.job.nextRun=Next Scheduled Run
schwartzmonitor.job.paused=Paused
schwartzmonitor.job.result=Result
schwartzmonitor.job.triggerName=Trigger Name
schwartzmonitor.title=Quartz Jobs
schwartzmonitor.tooltip.pause=Pause job schedule
schwartzmonitor.tooltip.reschedule=Reschedule
schwartzmonitor.tooltip.resume=Resume job schedule
schwartzmonitor.tooltip.run=Run now
schwartzmonitor.tooltip.start=Start job schedule
schwartzmonitor.tooltip.stop=Stop job from running again
search.advancedsearch=Advanced search
search.agent=Search an agent
search.client.vehicle=Search a client vehicle
search.column=Search column
search.for.icon=Search for an icon
search.vehicle=Vehicle
search=Search
second.first.letter=s
seconds=seconds
see.all=See all
see.full.discussion=See the full discussion
see.less=See less
sei=SEI
select.client.interest=Selection of the interest
select.client.vehicle.to.trade=Select vehicle to trade
select.none=No selected
select.opportunities=Select opportunities
select.option=Select an option
select.trade.information=Trade information
select.trade=Select trade
selected.communications=selected communications
selected.files=Files
selected.icon.add=Add new icon
selected.icon.modify=Modify the selected icon
selected.messages=selected comments
send.approbation.demand=Send approbation demand
send.jobs.to.serviceschedule=Send selected jobs to workflow schedule
send.message=Send a message...
send.pictures.add.error=addFileDataAuthor
send.pictures.file.check=check file size failed
send.pictures.file.too.big=file too big ({0} MB) not enough memory ({1} MB)
send.pictures.id.not.found=no manager product with that id
send.pictures.no.files=Received no files
send.pictures.no.jfif=jfif files not allowed
send.pictures.no.managerproduct.id=no manager product id
send.pictures.no.permission=no permission
send.pictures.save.error=saveProduct
send.pictures.too.many.file=too many files
send.to.serviceschedule=Send to schedule
send=Send
september=September
SERTI=SERTI
server.host.key=Server host key
service.opportunities=Service opportunities
service.opportunity=Service opportunity
service.ressource=Ressources
service.workorder=Workorder
service.workshop=Workshop
serviceai.assistant.ai=AI Assistant
serviceai.select.question=Select a question in the list to show the answer here
servicecalendar.color=Color
servicecalendar.complete=Full workflow schedule
servicecalendar.create=Create workflow schedule
servicecalendar.edit=Modify workflow schedule
servicecalendar.name=Name
servicecalendar.ziistatuscolor.add=Add status
servicecalendar.ziistatuscolor.orderused=Status with order {0} already exist
servicecalendar.ziistatuscolor=Color Status Zii
servicecalendar=Workflow schedule
servicecalendars=Workflow schedules
servicechedule.drag.view.error=Unable to schedule in this view.
serviceresource.active=Active
serviceresource.cannot.delete.desactivate=Cannot delete or deactivate the resource because it contains future schedules.
serviceresource.create=Creating a resource
serviceresource.edit=Editing a resource
serviceresource.external=External resource
serviceresource.name=Name
serviceresource.ordre=Order
serviceresource.reschedule.error=Error during the resheduling process
serviceresource.reschedule.execute=Launch rescheduling
serviceresource.reschedule=Reschedule resource
serviceresource.rescheduleDuration=Duration (in days)
serviceresource.rescheduleFrom=From
serviceresource.show.deleted=Show deleted resources
serviceresource.show.not.deleted=Show valid resources
serviceresource.undelete.confirm=Are you sure you want to undelete this resource?
serviceresource.undeleted=Resource undeleted successfully
serviceresource.unique.user.error.deleted=Deleted resource with this user already exist. You can recover the resource from the deleted resources list.
serviceresource.unique.user.error=Resource with this user already exist.
serviceresource=Workflow resource
serviceresources.deleted=Deleted workflow resources
serviceresources.not.deleted=Valid workflow resource
serviceresources=Workflow resources
serviceschedule.burst=Burst
serviceschedule.create.from.workflowdata=From workflow data
serviceschedule.create.from.workorder=From work order
serviceschedule.create.reservation=Reservation
serviceschedule.create=Create workflow schedule
serviceschedule.date.hint=Leave empty to schedule on the next available slot
serviceschedule.dayGridMonth=Month
serviceschedule.dayGridWeek=Day grid week
serviceschedule.delete=Delete
serviceschedule.disponibilities.exclude=Excluding external resources
serviceschedule.disponibilities=Availabilities
serviceschedule.duplicate=Duplicate
serviceschedule.duration=Duration
serviceschedule.end=End
serviceschedule.find.workflowdata=Find card
serviceschedule.for=Workflow schedule for {0}
serviceschedule.listDay=List day
serviceschedule.listMonth=List month
serviceschedule.listWeek=List week
serviceschedule.listYear=List year
serviceschedule.pendingJobNo=Job #
serviceschedule.pendingWorkOrderRoID=WO #
serviceschedule.pendingWorkOrderType=Type
serviceschedule.reserations=Your reservations
serviceschedule.reservation.notsaved=Reservation not saved
serviceschedule.reservation.saved=Reservation saved
serviceschedule.reserved.by=Reserved by {0}
serviceschedule.resize.timeLeft.for.workorder=Resize to time left for all work order jobs
serviceschedule.resize.timeLeft=Resize to time left
serviceschedule.resourceTimeGridDay=Day grid
serviceschedule.resourceTimelineDay=Day timeline
serviceschedule.resourceTimelineWeek=Week timeline
serviceschedule.schedule.error.closed.jobs=Selected jobs are closed. Nothing has been added to the schedule.
serviceschedule.schedule.saved=The schedule has been saved
serviceschedule.select.workflow=Select workflow
serviceschedule.setstatus.for.workorder=Mark all schedules for this work order
serviceschedule.settings.automation.hint=Enable automation with options below and schedule separations in working hours.
serviceschedule.settings.automation=Automation
serviceschedule.settings.fillEmpty.hint=Moves future schedules when deleting or moving a schedule leaving empty space in the schedule.
serviceschedule.settings.fillEmpty=Fill in the blanks
serviceschedule.settings.fillEmptyDays.hint=Determines the maximum number of days in which schedules will be taken to fill empty spaces.
serviceschedule.settings.fillEmptyDays=Days to fill in the blanks
serviceschedule.settings.includeClosed.hint=Show closed jobs schedules
serviceschedule.settings.includeClosed=Show closed
serviceschedule.settings.invertColors.hint=Switch background color with the left border color.
serviceschedule.settings.invertColors=Switch colors
serviceschedule.settings.pushDown.hint=Moves the overlapping schedules after the schedule that is modified.
serviceschedule.settings.pushDown=Push down
serviceschedule.settings.pushUp.hint=Moves the overlapping schedules after the schedule that is modified.
serviceschedule.settings.pushUp=Push up
serviceschedule.settings.resource.order=Modify resources order
serviceschedule.settings.showAllDepartments.hint=Show resources from all branches instead of only you current branch.
serviceschedule.settings.showAllDepartments=Show all branches
serviceschedule.settings=Workflow schedule settings
serviceschedule.start=Start
serviceschedule.status.default=Default status of workflow schedule
serviceschedule.status.fixed=Fixed
serviceschedule.status.flexible=Flexible
serviceschedule.status.markas=Mark as
serviceschedule.status.offSite=Vehicle on site
serviceschedule.status.onSite=Client on site
serviceschedule.status.rdvOffSite=Appt - Vehicle on site
serviceschedule.status.rdvOnSite=Appt - Client on site
serviceschedule.status.toschedule=To schedule
serviceschedule.status=Status
serviceschedule.timeGridDay=Day grid
serviceschedule.timeGridWeek=Day week
serviceschedule.view.expand.collapse=Expand / collapse calendar view
serviceschedule.warning.jobduplicate=Warning! The job has multiple schedules.
serviceschedule.warning.overlapping=Warning! The resource contains overlapping schedules.
serviceschedule.warning.workschedule=Warning! The schedule has been put outside of the resource's working hours.
serviceschedule=Workflow schedule
serviceScheduleEvent=Schedule
sessiontimeout=Session Time Out!
shareConvo=Share convo
show.advanced.filters=Go to Advanced filters
show.archived=Archived
show.basic.filters=Go to basic filters
show.date=Dates
show.exclude.stats=Exclude statistics
show.filtered.opportunities=Show filtered opportunities
show.null.values=Show null values
show.printPdf=Print PDF
show.remarks=Remarks
show.request.payment=Request a payment
Show=Show
sms.body=Message
sms.bulk.history.title=History of mass SMS
sms.bulk.send.title=Send Bulk SMS
sms.bulk.sent.error=Sms sending error for thoses clients :
sms.bulk.sent=Sms successfully sent to thoses clients :
sms.bulk.title=Bulk sending
sms.client.create.button=Create client with this phone number
sms.client=Client
sms.date=Date
sms.event.bulk.sent=Bulk SMS
sms.event.received=Received SMS
sms.event.sent=Sent SMS
sms.fromNumber=Sender's phone number
sms.send.bluk.notemplate=No sms template found. Add sms templates in the config and try again.
sms.send.bluk=Send bulk sms
sms.send.from.group=From department
sms.sent.error.template=Sms not sent. Select a message template.
sms.sent.error.text.size=Message exceeds 1500 characters, please reduce its length.
sms.sent.error=SMS not sent. Verify the client cellphone number and try again.
sms.sent=SMS sent
sms.template.body=SMS template body
sms.template.new=New sms template
sms.template.title=SMS template title
sms.template=Sms template
sms.title=SMS
sms.toNumber=Recipiant's phone number
sms.typemessage=Type a message
sms.typetitle=Type a title
sms.user=User
sms.usergroup=Department
sms.with=Sms with
smsmessage.errorcode.accountsuspended=Account suspended
smsmessage.errorcode.landlineorunreachablecarrier=Landline or unreachable carrier
smsmessage.errorcode.messageblocked=Message blocked
smsmessage.errorcode.messagefiltered=Message filtered
smsmessage.errorcode.contentsizeexceedscarrierlimit=Message failed because the size of the content associated with the message exceeded carrier limit
smsmessage.errorcode.messagepriceexceedsmax=Message price exceeds max price.
smsmessage.errorcode.missingsegment=Missing segment
smsmessage.errorcode.queueoverflow=Queue overflow
smsmessage.errorcode.unknowndestinationhandset=Unknown destination handset
smsmessage.errorcode.unknownerror=Unknown error
smsmessage.errorcode.unreachabledestinationhandset=Unreachable destination handset
smsmessage.send.error.empty=Cannot send an empty message.
smsmessage.send.error.from.to=`From` or `To` number is unspecified.
smsmessage.send.error.invalid.number=`To` number is not a valid mobile number.
smsmessage.send.error.invalid.region=You do not have permission to send SMS messages to this region, please contact support to gain access.
smsmessage.send.error.numberofmedia=The number of media URLs exceeds the maximum allowed. You may send up to 10 media files in a single message.
smsmessage.send.error.unsubscribed=Attempt to send to unsubscribed recipient. Client has to text `START` before you can contact him again with this number.
SmsMessage=SMS
sold.status=Opportunities coming out of this status by completing a sale
soldboard.activeclient=Active client
soldboard.blue.explain=Default color
soldboard.blue=Blue
soldboard.colors=Card colors
soldboard.cyan.explain=The opportunity status is "On order"
soldboard.cyan=Cyan
soldboard.daily.points={0} points achieved out of {1} ({2} %)
soldboard.delete.confirm=Are you sure you want to cancel this sale?
soldboard.delete=Cancel the sale
soldboard.filter.usergoals=User goals
soldboard.goals.ranks.color=Color
soldboard.goals.ranks.modal.title=Add / Modify goal achievement rank
soldboard.goals.ranks.rate=Minimum success rate
soldboard.goals.ranks=Goal Achievement Rank
soldboard.goals=Goals
soldboard.goto.client=Go to client view
soldboard.goto.workflow=Go to workflow card
soldboard.menu.actiflead=Active lead
soldboard.menu.boat=Boat
soldboard.menu.daydate=Day to date
soldboard.menu.department=Branch
soldboard.menu.filter=Filter
soldboard.menu.monthdate=Month to date
soldboard.menu.needtwoinfo=Have 2 info by
soldboard.menu.newopportunity=New opportunity
soldboard.menu.objectifs=Goals
soldboard.menu.other=Other
soldboard.menu.powersport=Power Sport
soldboard.menu.ratioemail=Email Ratio
soldboard.menu.sold=Sold
soldboard.menu.weekdate=Week to date
soldboard.menu.yeardate=Year to date
soldboard.potential.opportunity.tooltip=Number of new potential opportunities
soldboard.potential.opportunity=Potential opp.
soldboard.ratioemail=Ratio Email
soldboard.red.explain=The sale took place today
soldboard.red=Red
soldboard.sold=Sold
soldboard.yellow.explain=The sale is lost
soldboard.yellow=Yellow
solutions=Solutions
sort.card.by=Sort cards by
source.call=Call
source.chat=Live chat
source.dms=DMS
source.email=Email
source.externalprovider.opportunity=External providers
source.facebook=Facebook
source.form=Form
source.invalid.email=Please make sure this is a valid email
source.opportunity.report.bysource=Follow-up sources of opportunities by sources
source.opportunity.report=Follow-up sources of opportunities
source.potential.opportunity=Potential opportunities
source.sms=SMS
source.store=Store
stacked.line.charts=Stacked line charts
startDate=Start date
starts.in=In
StatisticRow.Column.APPOINTMENT_TODO_NEXT_7D.tooltip=Opportunities with an appointment scheduled in the next 7 days with user sales or BDC and not sold
StatisticRow.Column.APPOINTMENT_TODO_NEXT_7D=Appt. +7d
StatisticRow.Column.OUTBOUND_COMMUNICATIONS.tooltip=Outbound communications of user sales and BDC
StatisticRow.Column.OUTBOUND_COMMUNICATIONS=Outbound communications
StatisticRow.Column.RATING_COMMUNICATIONS=Rating communications
StatisticRow.Column.VERBAL_COMMUNICATIONS.tooltip=Verbal communications of user sales and BDC
StatisticRow.Column.VERBAL_COMMUNICATIONS=Verbal communications
status.client.all=All
status.client.appointments=Appointments
status.client.awaiting=Awaiting
status.client.new=Client
status.client.spam=Spam
status.client.tasksoon=Tasks coming up
status.client.tasktodo=Tasks to complete
status.history.appointment=Appointment done
status.history.appointmentphone=Telephone appointment done
status.history.callnotecompleted=Call note completed
status.history.chatend=Chat ended
status.history.clientmessagereply=Customer portal reply
status.history.clientmessagesent=Customer portal message sent
status.history.closeactivity=Activity closed
status.history.createclient=Client created
status.history.createnoemailclient=Created client without an email
status.history.delete.po=Potential opportunity deleted
status.history.deletecotation=Quotation deleted
status.history.deletefile=Delete file
status.history.deletetrade=Trade deleted
status.history.deleteworkflowdata=Workflow data deleted
status.history.delivery=Delivery
status.history.duplicatedcotation=Quote duplicated
status.history.editcallnote=Edit call note
status.history.editfile=Edit file
status.history.email=Web request email
status.history.emailreply=Email reply
status.history.emailsent=Email sent
status.history.emailsystem=Email system
status.history.facebookreply=Facebook message reply
status.history.facebooksent=Facebook message sent
status.history.formfilled=Form filled out
status.history.historyauditable=History
status.history.hook=Hook triggered
status.history.inboundcall=Inbound call
status.history.mergeclient=Merge clients
status.history.modify.po=Potential opportunity modified
status.history.modifyclient=Modify client
status.history.modifycotation=Quote modified
status.history.modifyopportunity=Opportunity modified
status.history.modifytrade=Trade modified
status.history.modifytradeprices=Trade price modified
status.history.modifyworkflowdata=Workflow data modified
status.history.new.po=New potential opportunity
status.history.newcallnote=New call note
status.history.newcomment=New comment
status.history.newcotation=New quote
status.history.newemail=New email
status.history.newopportunity=New opportunity
status.history.newtrade=New trade
status.history.newworkflowdata=New workflow data
status.history.nofollowup=No follow-up required
status.history.opportunitysold=Opportunity sold
status.history.outboundcall=Outbound call
status.history.po.has.no.potential=Potential opportunity has no potential
status.history.po.has.potential=Potential opportunity created an opportunity
status.history.salesmanager.briefing=Sales manager briefing
status.history.salesmanager.bump.to.validate=BUMP sales manager (waiting for validation)
status.history.salesmanager.bump.validated=BUMP sales manager (validated)
status.history.salesmanager.bump=BUMP sales manager
status.history.smsreply=SMS reply
status.history.smssent=SMS sent
status.history.smssystem=SMS system
status.history.takeover.phone=TO by telephone
status.history.takeover.store=TO in-store
status.history.taskcompleted=Task completed 
status.history.taskcompletedlate=Task completed late
status.history.taskcreated=Task created
status.history.uploadfile=Upload file
status.history.videochatend=Video chat ended
status.history.walk.in=Walk-in
status.message.new=New message
status.message.read=Message read
status.note.todo=To do
status.opportunity.appointment=Appointment
status.opportunity.approved=Approved
status.opportunity.april=April
status.opportunity.august=August
status.opportunity.awaiting=Awaiting
status.opportunity.badcontactinginfo=Bad contacting info
status.opportunity.badcredit=Bad credit
status.opportunity.boughtelsewhere=Bought elsewhere
status.opportunity.brokerageapproved=Brokerage approved
status.opportunity.cancel=Cancelled
status.opportunity.cashdeal=Cashdeal
status.opportunity.consignment=Consignment
status.opportunity.december=December
status.opportunity.deposit=Deposit
status.opportunity.depositcall=Deposit (Call)
status.opportunity.depositemail=Deposit (Email)
status.opportunity.depositmagasin=Deposit (Store)
status.opportunity.depositpending=Deposit (Pending)
status.opportunity.deposittraction=Deposit (Traction)
status.opportunity.duplicate=Duplicate
status.opportunity.event=Event
status.opportunity.february=February
status.opportunity.fnifinalized=FNI finalized
status.opportunity.fniincharge=FNI in charge
status.opportunity.future=Future
status.opportunity.january=January
status.opportunity.july=July
status.opportunity.june=June
status.opportunity.location=Location
status.opportunity.lost=Lost
status.opportunity.lostcancel=Lost cancelled
status.opportunity.lostfinance=Lost finance
status.opportunity.lostnoresponse=Lost no response
status.opportunity.march=March
status.opportunity.may=May
status.opportunity.negotiations=Negotiations
status.opportunity.no.appointment=Communication before meeting
status.opportunity.nonsales=Non-sales
status.opportunity.noresponse=No response
status.opportunity.notarialbrokerage=Notarial brokerage
status.opportunity.notauthorized=You are not authorized to change this status
status.opportunity.notinstock=Not in stock
status.opportunity.notserious=Not serious
status.opportunity.november=November
status.opportunity.october=October
status.opportunity.one.appointment.comm=Communication after meeting
status.opportunity.one.appointment=One meeting
status.opportunity.onorder=On order
status.opportunity.outofmarket=Out of market
status.opportunity.pending=Contact later
status.opportunity.personal=Personal
status.opportunity.potential=Potential
status.opportunity.price=Price
status.opportunity.productsearch=Product search
status.opportunity.purchaseatthedoor=Purchase at the door
status.opportunity.quoted=Quoted
status.opportunity.september=September
status.opportunity.showroom=Showroom
status.opportunity.tradevalue=Exchange price
status.opportunity.two.appointment.comm=Communication after two meetings
status.opportunity.two.appointment=Two meetings and more
status.opportunity.underage=Under age
status.task.cancel=Cancelled
status.task.done=Done
status.task.noshow=No show
status.task.todo=To do
status.user.available=Available
status.user.break=Break
status.user.inmeeting.not.planned=Walk-in
status.user.inmeeting.planned=In a meeting
status.user.long.vacation=Extended break
status.user.mr=Mr.
status.user.mrs=Mrs.
status.user.notavailable=Not Available
status.user.onphone=On the phone
status.user.vacation=Vacation
still=Still
storage=Storage
stripe.charge.received.email.subject=Receipt for your payment to {0}
stripe.charge.received.sms.body=Hello {0},\n\n{1} has received your payment.\n\nThe receipt is available at the following link: {2}\n\nThank you!
stripe.item.amount=Amount (CAD)
stripe.item.description.text=Enter description
stripe.item.description=Description
stripe.item.name=Name
stripe.payment.request.created=Payment request created
stripe.payment.request.email.subject={0} sent you an online payment request
stripe.payment.request.sms.body=Hello {0},\n\n{1} sent you an online payment request. You can make a payment here: {2}\n\nThe payment link is valid until {3}.\n\nThank you!
stripe.request.payment.not.configured=Payment requests are not configured for your branch.
stripe.request.payment.send.via=Send request via
stripe.request.payment.send=Send payment request
stripe.request.payment=Request a payment
stripeaccount.create=Create account
stripeaccount.edit=Edit account
stripeaccount.name=Account name
stripeaccount.stripeId=Stripe ID
stripeaccount=Stripe account
stripeaccounts=Stripe accounts
stripecharge.billingDetails.address.city=City
stripecharge.billingDetails.address.country=Country
stripecharge.billingDetails.address.line1=Adress line 1
stripecharge.billingDetails.address.line2=Adress line 2
stripecharge.billingDetails.address.postalCode=Postal code
stripecharge.billingDetails.address.state=State
stripecharge.billingDetails.email=Email
stripecharge.billingDetails.name=Name
stripecharge.billingDetails.phone=Phone
stripecharge.billingDetails=Billing details
stripecharge.date=Date
stripecharge.details=Transaction details
stripecharge.outcome.networkStatus.approved_by_network=Approved by network
stripecharge.outcome.networkStatus.declined_by_network=Declined by network
stripecharge.outcome.networkStatus.not_sent_to_network=Not sent to network
stripecharge.outcome.networkStatus.reversed_after_approval=Reverse after approval
stripecharge.outcome.networkStatus=Network status
stripecharge.outcome.reason.elevated_risk_level=Elevated risk level
stripecharge.outcome.reason.highest_risk_level=Highest risk level
stripecharge.outcome.reason=Reason
stripecharge.outcome.sellerMessage=Message
stripecharge.outcome.type.authorized=Authorized
stripecharge.outcome.type.blocked=Blocked
stripecharge.outcome.type.invalid=Invalid
stripecharge.outcome.type.issuer_declined=Issuer declined
stripecharge.outcome.type.manual_review=Manual review
stripecharge.outcome.type=Type
stripecharge.outcome=Outcome
stripecharge.outcomeRisk=Risk
stripecharge.outcomeRiskLevel=Risk level
stripecharge.outcomeRiskScore=Risk score
stripecharge.partial.refund=Partial refund
stripecharge.receiptUrl.click=Click here to see the receipt
stripecharge.refunded=Refunded
stripecharge.risklevel.elevated=Elevated
stripecharge.risklevel.highest=Highest
stripecharge.risklevel.normal=Normal
stripecharge.risklevel.notassessed=Not assessed
stripecharge.risklevel.unknown=Unknown
stripepaymentintent.amount=Amount
stripepaymentintent.consolidated.false=Not consolidated
stripepaymentintent.consolidated.markas.false=Unconsolidate
stripepaymentintent.consolidated.markas.true=Consolidate
stripepaymentintent.consolidated.true=Consolidated
stripepaymentintent.consolidated=Consolidated
stripepaymentintent.creator=Creator
stripepaymentintent.date=Date
stripepaymentintent.list.selected=Selected payments
stripepaymentintent.list=Payment list
stripepaymentintent.my.list=My payment list
stripepaymentintent.status.canceled=Cancelled (Expired)
stripepaymentintent.status.processing=Processing
stripepaymentintent.status.requires_action=Require's action
stripepaymentintent.status.requires_confirmation=Require's confirmation
stripepaymentintent.status.requires_payment_method=Require's payment method
stripepaymentintent.status.succeeded=Succeeded
stripepaymentintent.status=Status
stripepaymentintent.tractionId=Traction ID
stripepaymentintent.unread.false=Read
stripepaymentintent.unread.markas.false=Mark as read
stripepaymentintent.unread.markas.true=Mark as unread
stripepaymentintent.unread.true=Unread
stripepaymentintent.unread.warning=Warning! You can only edit unread marks for your own payments.
stripepaymentintent.unread=Unread
stripepaymentrequest.params.error=Invalid parameters. Please enter the client, branch, amount and payment request name.
stripepaymentrequest.recreate=Recreate the payment request
stripesession.expire=Expire the request
stripesession.fileDatas=Attachments
stripesession.status.complete=Complete
stripesession.status.expired=Expired
stripesession.status.open=Open
stripesession.url.copied=Payment URL copied successfully
stripesession.url.copy=Copy URL
stripesession.valid.until=URL expires on
su=Su
summary=Summary
sums.and.averages=Sums & Averages
support.any.agent=Get support from any agent
support.startchat=Start chatting
support=Support
system.training=System information
tableview.config.title=Personalized table view configuration
tag.add=Add tag
tag.configs=Tag configurations
Tag.ConfigType.TASK=Tasks
Tag.ConfigType.WORK_ORDER=Work orders
tag.create=Create tag
tag.delete.confirm=Are you sure you want to delete this tag? This is not reversable.
tag.edit=Edit tag
tag.remove=Remove
tag.save.reuse=Save to reuse
tag.saved=Saved tags
tag.search=Search or create a tag...
tag=Tag
TagConfigTab.taskDone=Completed tasks
TagConfigTab.taskTodo=Tasks to do
tags=Tags
takeover=TO
taketurns.add.user=Select a new user to add to this rotation
taketurns.changed=The rotation {0} changed
takeTurns.create.error=The name for the round robin cannot be empty
takeTurns.create.success=The round robin has been created
taketurns.create=Create new rotation
taketurns.delete=Delete this rotation
taketurns.deleted=Rotation has been deleted
taketurns.edit=Edit name
takeTurns.missing.meeting=You need to log a meeting to change status
taketurns.name=Rotation name
takeTurns.name=Round robin name
takeTurns.reset.success=The rotation has been saved
taketurns.reset=Reset by the end of the day
takeTurns.user.active.success=User activity for this rotation has been changed
takeTurns.user.active=Your next turn: {0}
takeTurns.user.position.success=User position changed
takeTurns.user.position=Next turn: {0}
takeTurns.user.unactive=Inactive
takeTurns.users.active=Active users
takeTurns.users.not.active=Inactive users
taketurns=Round robin
target=User concerned
task.add.file=Add files
task.add=New task
task.additional.users=Additional users assigned
task.all=See all tasks
task.assigned=Assignments
task.assignedTo=Assigned to
task.attachments.delete.confirm=Do you really want to delete that file?
task.attachments=Task attachments
task.awaiting=Awaiting notification
task.calendar=Task calendar
task.cannot.edit.delivery=You cannot edit a delivery meeting
task.category.appointment=Appointment
task.category.appointmentphone=Telephone appointment
task.category.call=Call
task.category.delivery=Delivery appointment
task.category.email=Email
task.category.facebook=Facebook
task.category.form=Form
task.category.internalcommunication=Internal communication
task.category.other=Other
task.category.provider=Manufacturer
task.category.service=Service
task.category.signature=Signature
task.category.sms=SMS
task.category.walkin=Walk-in
task.category.withoutclient=Without client
task.category=Category
task.close.error=An error occured while closing this task.
task.close.success=The task has been closed.
task.close=Close task
task.comment.section.hide=Hide comments
task.comment.section=Display comments
task.completed=Completed
task.completedDate=Date completed 
task.conflicting=Conflicting task(s)
task.conflictSubTitle=You can modify them here or skip this modal
task.conflictTitle=The added task's due date conflicts with other tasks
task.count=Number of tasks
task.create=Create a task
task.creator=Creator
task.date.description=Creation date and description
task.date=Creation date
task.days.todo=Number of days to complete the task
task.delivery=Delivery task
task.description.date=Creation date and description
task.description=Description
task.draft.file=Draft file that needs to be saved
task.draftattachments=Task draft attachments
task.edit=Edit task
task.error.noclient=You need to define the client related to the task client or change the status of the task for "Without client"
task.error.withoutclient=You cannot set the category of the task to "Without client"
task.expectedDate=Expected date
task.files=Files
task.filter.moreThan7days=More than 7 days
task.filter.next7days.late=Less than 7 days and late
task.internalOrWithoutClient=Internal communication / Task without a client
task.lastTaskYouCreated=Last task you created
task.late.warning={0} late!
task.late=Late
task.list=Task list
task.new=New task
task.not.saved.user=Please assign a user to the task.
task.not.updated=Problem while updating the task
task.saved.file=File saved to task 
task.saved=Task saved
task.soon.warning.body={0} to do for {1}
task.soon.warning=A task has been assigned to you!
task.soon=To complete
task.status=Status
task.subscribe.error=Task or user not found
task.subscribe.success=Subscription updated successfully
task.subscribe=Subscribe to the task
task.subscribers=Subscribers
task.subscription.count={0} subscriptions
task.subscriptions=Task subscriptions
task.types=Types of tasks
task.unsubscribe=Unsubscribe from the task
task.updated=Task successfully updated
task.userGroup=Department
task.users=Assigned users
task=Task
taskassigned.assigned=Assigned
taskassigned.status=Status
taskassigned.subscribed=Subscribed
taskassigned.type=Type
taskassigned.user=User
taskExpectedDateFormat=yyyy-MM-dd HH:mm
taskExpectedDateFormatjs=YYYY-MM-DD HH:mm
tasks.historylink=Go to history
tasks.noTasks=No tasks
tasks.selected.client=Tasks of selected client
tasks=My tasks
tax.abreviation=Abreviation
tax.add=Add tax
tax.amount=Amount
tax.config.title=Configuration of taxes
tax.edit=Edit tax
tax.name=Name
taxes.canada=Federal taxes
taxes.quebec=Provincial taxes
taxes=Taxes
team.assignmentrule=Assignment rule
team.department=Branch
team.facebookPage=Facebook page
team.Formation=Training
team.serviceresources=Service resources
team.WorkSchedules=Work schedules
Teams.title=Team
text.composition=Text composition
text.input.placeholder=Enter text
text.or=OR
text=Text
th=I
theme.dark=Dark
theme.light=Light
this.month=This month
this.years=This year
thisMonth=This Month
thismonth=This month
timeFormat=hh:mm a
timeline.systemcreated=Automatically created by the system
timeline.usercreated=Created by
title=Title
to.min=to
to=To
today.potential.opportunity.tooltip=Number of new potential opportunities today
today.potential.opportunity=Potential opportunities today
today=Today
tools.payments=Payments
tools.system=System
tools.task=Task
total.objectives=Total of objectives
total.sales=Total sales
totalcall=Total phone calls
traction.communication.CallData=Call
traction.communication.CallNotes=Call note
traction.communication.ChatMessage=Chat
traction.communication.CommunicationMeeting=Appointment
traction.communication.FacebookMessage=Facebook
traction.communication.FormClientData=Form
traction.communication.MailMessage=Email
traction.communication.SmsMessage=SMS
traction.communication.VideoRoom=Video call
traction.copyright=\u00a9 2024 Traction DK inc.
traction.mobile.key=Mobile application key
traction.mobile=Traction mobile:
traction.mobileapp.getapp=Get the app
traction.mobile.download.ios=Download on iOS
traction.mobile.download.android=Download on Android
traction.mobile.download.on=Download on
traction.mobile.get.it.on=Get it on
traction.version.none=No version
traction.version=Traction version
tractionhelp.addbrand=Add a brand
tractionhelp.addcategory=Add a category
tractionhelp.addcustomerexchangeproduct=Add a customer exchange/product
tractionhelp.addinterest=Add an interest
tractionhelp.addkijijicategory=Add a Kijiji category
tractionhelp.addlabelcustomerview=How to add a label to a customer view
tractionhelp.addtasksalesmodule=Add tasks to the sales module
tractionhelp.addtradercategory=Add a Trader category
tractionhelp.allopportunities=All opportunities
tractionhelp.appointment=Appointment
tractionhelp.assigningemployee=Assigning an employee
tractionhelp.associatevisit=Associate a visit
tractionhelp.bulksms=Bulk SMS / Mass Action
tractionhelp.calendartime=Configure calendar time
tractionhelp.callmanagement=Call management
tractionhelp.callnotes=Call notes
tractionhelp.callopportunities=Call opportunities
tractionhelp.changeopportunitystatus=Change opportunity status
tractionhelp.changesaledate=Change a sale date
tractionhelp.changestatustosale=Change status to SALE
tractionhelp.changingworkschedule=Changing a work schedule
tractionhelp.chatsystem=Chat system
tractionhelp.completeworkflowschedule=Complete workflow schedule
tractionhelp.contactcolumn=Contact column
tractionhelp.createcolor=How to create a color
tractionhelp.createcustomerinterest=Create a customer interest
tractionhelp.createcustomerview=Create a customer view
tractionhelp.createform=Create a form
tractionhelp.createinspectionsheet=Create an insepction sheet
tractionhelp.createlabel=Create a label
tractionhelp.createmodifyuser=Create and modify a user
tractionhelp.createopportunity=Create an opportunity
tractionhelp.createtechnicians=Create your technicians
tractionhelp.createview=Create a view
tractionhelp.createworkflow=Create a workflow
tractionhelp.createworkflowicon=Create a workflow icon
tractionhelp.customerattachments=Customer attachments
tractionhelp.customercardicons=Customer card icons
tractionhelp.dashboardsales=Sales dashboard 
tractionhelp.emailsubmission=Email submission
tractionhelp.emailvalidation=Email validation
tractionhelp.excludeanopportunity=Exclude an opportunity
tractionhelp.explanationcalendaropportunity=Explanation of the 4 calendars displayed on the opportunity card
tractionhelp.facebookpage=Facebook page
tractionhelp.filtersboxessalesmodule=Sales module filters and boxes
tractionhelp.formdata=Form data
tractionhelp.fullvideo=Not contacted
tractionhelp.groupsinterface=Groups interface
tractionhelp.identifymedia=Identify the media
tractionhelp.identifysource=Identify the source
tractionhelp.importimage=Import an image
tractionhelp.importpromopriceguarantees=Import Promotion / Price list / Guarantees
tractionhelp.instoreopp=In-store opportunities
tractionhelp.inventory=Inventory
tractionhelp.lastactivityicon=Icon of the last activity on the opportunity card
tractionhelp.listcommunications=List of communications
tractionhelp.listopportunities=List of opportunities
tractionhelp.mailbox=Mailbox
tractionhelp.meetingstore=In-store appointment
tractionhelp.mergeclient=Merge two clients
tractionhelp.mobileapp=Mobile application
tractionhelp.movingmapworkflow=Moving a card within the workflow
tractionhelp.newfeatures=New features
tractionhelp.notcontacted=Not contacted
tractionhelp.opportunityavailability=Opportunity availability
tractionhelp.opportunitypool=Opportunity pool
tractionhelp.opportunitystatusdirector=Opportunity status only available to the director
tractionhelp.paymenttraction=Payment via Traction
tractionhelp.pointsreport=Point reports
tractionhelp.productlocation=Product location
tractionhelp.producttable=Products table
tractionhelp.ratecommunication=Rate a communication
tractionhelp.recognizeemailerror=Recognizing an email ERROR
tractionhelp.recordedcalls=Roles on recorded calls
tractionhelp.redirectcategory=Redirect a category
tractionhelp.roleturns=Round robin
tractionhelp.routingschedule=Routing schedule
tractionhelp.saleposter=Sales display
tractionhelp.salescontractwithexchange=Sales contract with exchange
tractionhelp.salescontractwithoutexchange=Sales contract without exchange
tractionhelp.salesconversionratetable=Sales conversion rate table
tractionhelp.salesmasterchart=Sales master chart
tractionhelp.salesmodulestats=Sales module statistics
tractionhelp.sendcardworkflow=Send a card to the workflow
tractionhelp.stats=Statistics
tractionhelp.storevisit=Store visits
tractionhelp.summaryworkinghours=Summary of working hours
tractionhelp.supervisor=Supervisor
tractionhelp.taskaction=Tasks and actions
tractionhelp.taskschedule=Task Schedule
tractionhelp.technicianschedule=Technician schedule
tractionhelp.templateemailsms=Email/text template
tractionhelp.textinterface=Text interface
tractionhelp.tobump=TO and BUMP
tractionhelp.todolist=Task list
tractionhelp.usersguide=User's guide
tractionhelp.usersupervisor=User's supervisor
tractionhelp.voicemail=Voicemail with Traction
tractionhelp.webopportunities=Web opportunities
tractionhelp.workflow=Workflow
tractionhelp.workflowresources=Workflow resources
tractionhelp.workflowschedules=Workflow schedules
tractionlocalisation.date=Date
tractionlocalisation.name=Location
tractionlocalisation.user=User
trade.add=Add a trade
trade.askedPrice=Trade asking price
trade.clientproduct.name=Client product name
trade.clientproduct=Add a client product related to a trade
trade.delete=Delete
trade.description=Comments of the trade
trade.export=Export
trade.files=Files
trade.go.to=Go to trade vehicle
trade.histories=History
trade.hours=Hours
trade.images=Pictures
trade.imagesTab=Images
trade.infoTab=Information
trade.inspectionby=Inspected by
trade.interest=Interest
trade.kilometers=Kilometers
trade.link=Trade lien
trade.make=Make
trade.model=Model
trade.motor=Motor
trade.new=New trade
trade.no.id=No trade with this ID
trade.option.add=Add an option
trade.option.comment=Inspection comments
trade.option.name=Name
trade.option.pieceNumber=Part number to replace
trade.option.saved.error=A trade option must contain a name.
trade.option.status.error=The status must be valid
trade.option.status.success=The status has been saved
trade.option.status=Status
trade.option.technique=Technical adviser
trade.option.type=Part
trade.option.value.total=Total value
trade.option.web=Web
trade.options.subtotal.client=Client subtotal :
trade.options.subtotal.dealer=Dealer subtotal:
trade.options=Trade options
trade.optionsTab=Options / Inspection
trade.price=Trade value
trade.gross=Trade value
trade.priceHistoryTab=Real / display value changes
trade.print=Print
trade.remove=Remove this trade from the quote.
trade.removed.error=This trade does not belongs to a quote.
trade.removed=Trade removed from quotation
trade.reparationEstimated=Estimated repair
trade.saved.error=The trade must have a make, model, year and interest.
trade.saved=Saved successful
trade.send=Send to client
trade.serialNumber=Serial number
trade.stocknumber=Stock number
trade.undelete=Cancel deletion
trade.year=Year
trade=Trade
tradePrices=Price of trades
trades=Trades
training.doc.deleted=Training document successfully deleted.
training.doc.edit.title=Modification of a training document
training.doc.error.childs=Please empty the document of the files it contains before deleting.
training.doc.error.description=The description cannot be empty.
training.doc.error.notexist=Non-existent training document.
training.doc.error.title=The title cannot be empty.
training.doc.new.description=Description of the new training document
training.doc.new.title=New training document
training.doc.open=Open document
training.doc.saved=Training document saved
training.doc.title=Training document
training.edit.title=Editing a training section
training.file.browsersupport=Your browser does not support this video. Maybe you should try Chrome.
training.help.nohelp=No training document is associated with this page.
training.new.description=Description of the new training section
training.new.title=New section
training.open.section=Open section
training.section.deleted=Training section deleted successfully.
training.section.description=Description
training.section.error.childs=Please empty the section of its subsections and documents before deletion.
training.section.error.movetochild=Cannot move section {0} to one of its child sections.
training.section.error.notexist=Non-existent training section.
training.section.error.notsaved=Training section not saved.
training.section.error.parent=Parent training section invalid.
training.section.files=Files
training.section.parentandorder=Parent section and order:
training.section.saved=Training section saved
training.section.title=Title
training.title=Training
training.video.converting=The video is being converted to a compatible format. It will be available soon.
transfer.conversation=Transfer the conversation
true=True
tu=Tu
twilionumber.create=Create SMS number
twilionumber.delete=Delete SMS number
twilionumber.edit=Edit SMS number
twilionumber.list=SMS phone number list
twilionumber.no.longer.valid=Number no longer valid
twilionumber.number=Number
twilionumber.opportunity=Create potential opportunities
twilionumber.source=Source
twilionumber.userGroup.label=Department to use for dispatch for a customer's first message
twilionumber.userGroup=Dispatch
twilionumber=SMS phone number
typeMismatch.java.lang.Double=Property {0} must be a valid number
typeMismatch.java.lang.Integer=Property {0} must be a valid number
typeMismatch.java.lang.Long=Property {0} must be a valid number
typeMismatch.java.lang.Short=Property {0} must be a valid number
typeMismatch.java.math.BigDecimal=Property {0} must be a valid number
typeMismatch.java.math.BigInteger=Property {0} must be a valid number
typeMismatch.java.net.URI=Property {0} must be a valid URL
typeMismatch.java.net.URL=Property {0} must be a valid URL
typeMismatch.java.util.Date=Property {0} must be a valid date
typeMismatch=The property {0} does not correspond to the type
types=Types
ui.theme=Themes
undelete=Cancel deletion
unread.emails=Unread emails
unread=Unread
unsaved.change.in.page=Changes made on the page have not been saved. Are you sure you want to continue?
upload.alert=Invalid file type. Only JPG and JPEG are allowed.
use.fixed.date=Use a fixed date
use.mail.template=Use a template
used=Used
user.account=Account
user.address=Address
user.allManagerProductFilter=All product manager filters
user.allManagerProductProfiles=All product manager profiles
user.allManagerProductView=All product manager views
user.allPrivateCalls=All calls are private by default
user.allPrivateCommunications=All communications are private by default
user.authCode=Authorization code
user.birthday=Bitrhday
user.color=Color
user.community=Community
user.country=Country
user.create=Create a new user
user.default=Default routing user
user.defaultFromEmail=Traction email
user.deleted.button=Show deleted users
user.deleted.confirm=Do you want to recover this deleted user
user.deleted.title=Deleted users
user.department=Branch
user.departments=Branches
user.dropdown=User
user.edit.accountpbx=PBX account created
user.edit.activelead=Active lead
user.edit.businessUnit=Business unit
user.edit.department=Branch
user.edit.email=Email
user.edit.newemail=New Email
user.edit.popover.title=Display content
user.edit.usergroupchange.confirm.affected=Opportunities affected
user.edit.usergroupchange.confirm.conflict=Assignments that might be lost
user.edit.usergroupchange.confirm.title=Change the current assigned user to a different user group
user.edit.usergroupchange.confirm=Changing the user group will change the assignment for {0} opportunities.\n\n\n\n Since there are already users assigned for this User Group, this user might be removed from the assignment of {1} opportunities.\n\n\n\n This action is not reversable, you have to do it at your own risk and be sure that is what you want to do.
user.edit.usergroupchange.title=Change the current assigned user to the previous department
user.edit.usergroupchange.user=User who will replace the current user of the department for all assigned opportunities
user.edit.usergroupchange.warning=This process will take few minutes depending on how many opportunities has to be changed.
user.edit.voicemail=Voicemail
user.edit.informations=Informations
user.edit.work_schedule=Work schedule
user.edit.goals=Edit goals
user.edit.informations.personel=PERSONAL INFORMATION
user.edit.informations.work=WORK INFORMATION
user.edit.signature=SIGNATURE
user.edit.informations.autorisations=ROLES AND AUTHORIZATIONS
user.edit.informations.cookies=ALLOWED COOKIES
user.edit=Modify a user
user.email=Personal email
user.enabled=Enabled
user.expired=Expired
user.extensions=Extensions
user.extID.exists=User {0} already has this external ID for this DMS
user.firstname=First name
user.gender=Gender
user.goal.notsaved=User objectives not saved
user.goal.saved=User objectives saved
user.groupassignation=Group assignment
user.guide=User's guide
user.impersonate.resume=Resume as
user.impersonate=Switch user
user.language=Language
user.lastname=Last name
user.linked=User associated
user.locked=Locked
user.logout=Logout
user.new=New user
user.notdeleted.inAssignmentRule=Please remove user from any assignment rules before disabling it.
user.notdeleted.servicescheduleerror=This user is a workflow resource, delete this workflow resource before deleting this user.
user.notdeleted=User not deleted
user.notificationDepartmentService=All my branches service notifications
user.notifications=Sales manager notification report 
user.notificationService=Service advisor notifications
user.notrecovered=User not recovered
user.password.strength=Password strength
user.password.tooweak=Please enter a safer password
user.password=Password
user.passwordexpired=Password expired
user.permissionLevel=Level of permissions allowed
user.profiles=Profiles
user.province=Province
user.recovered=User recovered
user.region=Region
user.roles=Role
user.save.error.businessunit=Please select at least one business unit
user.save.error.department=Please select at least one branch
user.save.warning.businessunit=User saved but not removed form {0}. Make sure the user is not currently in {0} to remove them from this business unit.
user.saveError.email.exists=Error saving user. Traction email already exists.
user.saveError.user.inRoutingSchedule=Please remove the user from the email routing schedule before disabling it.
user.saveError.username.exists=Error saving user. Username already exists.
user.search=Search a user
user.signature=Signature created automatically
user.signSmsWithName=Add the name in the sms signature.
user.status=Status
user.title=Title
user.userGroup=Department
user.username=Username
user.users=Users
user.valid.button=Show valid users
user.valid.title=Valid users
user.visibleInSoldboard=Is visible in the Sold Board (if user is part of the sales department)
user.zipcode=Zip code
user=User
usernotificationsettings.callNotes=Call notes
usernotificationsettings.opportunityAssigned=Opportunities assigned
usernotificationsettings.commentMentions=Comments @Mentions
usernotificationsettings.comments=Comments
usernotificationsettings.communications=Communications
usernotificationsettings.event=Event
usernotificationsettings.mobile=Mobile
usernotificationsettings.pool=New pool opportunity
usernotificationsettings.taskAssigned=Tasks assigned
usernotificationsettings.taskReminder=Task reminders
usernotificationsettings.taskSubscriptions=Task subscriptions
usernotificationsettings.visits=In-store visits
usernotificationsettings.web=Web
usernotificationsettings=Notifications settings
users.goal.tooltip=Users objectives:
users=Users
value=Value
vehicle.accessories.options=Accessories/Options
vehicle.accessories=Accessories
vehicle.add.image=Add an image
vehicle.addedManually=Added manually
vehicle.additional.informations=Additional information
vehicle.aesthetic=Aesthetic
vehicle.ai.assistant=AI assistant
vehicle.all=All vehicles
vehicle.assign.addClient=Assign the vehicle to a client
vehicle.assign.changeOrRemoveClient=Change or remove associated client
vehicle.assign.removeClient=Remove association
vehicle.associated.client.remove=Are you sure you want to remove associated client?
vehicle.associated.client=Associated client
vehicle.attrSet=AttrSet
vehicle.availability.available=Available
vehicle.availability.late=Late
vehicle.availability=Availability
vehicle.barredPrice=Barred price
vehicle.base.data=Vehicle base data
vehicle.body=Body type
vehicle.branch=Branch
vehicle.category.null=No category
vehicle.category.nullable.error=Category is required
vehicle.category=Category
vehicle.categoryKijiji.null=No Kijiji category
vehicle.categoryKijiji=Kijiji Category
vehicle.categoryTrader.null=No Trader category
vehicle.categoryTrader=Trader Category
vehicle.changeGroup=Change group
vehicle.charge=Charge
vehicle.clearance=Clearance
vehicle.client=Client
vehicle.clientAssignment=Client assignment
vehicle.compiledData_listingStatusAll=Listing status: All
vehicle.compiledData_listingStatusInheritanceOnly=Listing status: Inheritance only
vehicle.compiledData_listingStatusWebsite=Listing status: Web Site
vehicle.compiledData_listingStatusDisabled=Listing status: Disabled
vehicle.compiledData_listingStatusTraction=Listing status: Traction
vehicle.compiledData_barredPrice=Barred price
vehicle.compiledData_costRealPrice=Real cost price
vehicle.compiledData_displayPrice=Display price
vehicle.compiledData_estimatedLoanAmount=Estimated loan amount
vehicle.compiledData_formulaBarredPrice=Barred price
vehicle.compiledData_formulaCategoryMagento=Magento Category Group
vehicle.compiledData_formulaDescription=Description of formula 
vehicle.compiledData_formulaDescriptionKijiji=Kijiji description
vehicle.compiledData_formulaDescriptionTrader=Trader description
vehicle.compiledData_formulaDisplayPrice=Display price
vehicle.compiledData_formulaFreightPrice=Freight price
vehicle.compiledData_formulaPrice1=Formula price 1
vehicle.compiledData_formulaPrice2=Formula price 2
vehicle.compiledData_formulaPrice3=Formula price 3
vehicle.compiledData_formulaPrice4=Formula price 4
vehicle.compiledData_formulaPrice5=Formula price 5
vehicle.compiledData_formulaPrice6=Formula price 6
vehicle.compiledData_formulaPrice7=Formula price 7
vehicle.compiledData_formulaPrice8=Formula price 8
vehicle.compiledData_formulaPdiPrice=PDI price
vehicle.compiledData_formulaShortDescription=Short description
vehicle.compiledData_formulaTitre=Title
vehicle.compiledData_minimumPrice=Minimum price
vehicle.compiledData_monthlyPayment=Monthly
vehicle.compiledData_optionsFormulaPrice=Price of options
vehicle.compiledData_pictureCountIncludingInheritance=Picture count including inheritance
vehicle.compiledData_tractionPrice=Traction price
vehicle.compiledData_weeklyPayment=Weekly
vehicle.configurable=Configurable
vehicle.configuration=Configuration
vehicle.configuratorData_color=Configurator - Color
vehicle.configuratorData_colorSet=Configurator - Color set
vehicle.configuratorData_configurable=Configurator - Configurable
vehicle.configuratorData_decor=Configurator - Decor
vehicle.configuratorData_genre=Configurator - Genre
vehicle.configuratorData_interior=Configurator - Interior
vehicle.configuratorData_material=Configurator - Material
vehicle.configuratorData_model=Configurator - Model
vehicle.configuratorData_size=Configurator - Size
vehicle.configuratorData_style=Configurator - Style
vehicle.contract=Contract
vehicle.cost=Cost
vehicle.costGL=GL Cost 
vehicle.costReal=Real price
vehicle.create.specifications=Create specifications
vehicle.create=Create a new vehicle
vehicle.creationdate=Creation date
vehicle.custom.field.active.hint=Field is visible in THE opportunity and inventory list
vehicle.custom.field.active=Active
vehicle.custom.field.adminOnly.hint=Field is visible in opportunity and inventory list only for users with [Administrative opportunities] permissions only
vehicle.custom.field.adminOnly=Restrained
vehicle.custom.field.label=Custom label
vehicle.custom.field=Custom field
vehicle.customPrice1=Custom price 1
vehicle.customPrice2=Custom price 2
vehicle.customPrice3=Custom price 3
vehicle.customText1=Custom text 1
vehicle.customText2=Custom text 2
vehicle.customText3=Custom text 3
vehicle.date=Date
vehicle.dateAvailability=Availability date
vehicle.dateAvailability.short=Availability
vehicle.dateCommissioning=Commissioning date
vehicle.dateCommissioning.short=Commissioning
vehicle.dateCreated=Created date
vehicle.dateCreated.short=Created
vehicle.dateDelivery=Delivery date
vehicle.dateDelivery.short=Delivery
vehicle.dateDue=Due date
vehicle.dateDue.short=Due
vehicle.dateImport=Import date
vehicle.dateImport.short=Import
vehicle.dateManufactured=Manufactured date
vehicle.dateManufactured.short=Manufactured
vehicle.dateModified=Modified date
vehicle.dateModified.short=Modified
vehicle.dateOrder=Order date
vehicle.dateOrder.short=Order
vehicle.datePossessed=Possessed date
vehicle.datePossessed.short=Possessed
vehicle.dateReceipted=Reception date
vehicle.dateReceipted.short=Reception
vehicle.dateReceipted.daysInStock=Reception date (Days in stock)
vehicle.dateRegistered=Registration date
vehicle.dateRegistered.short=Registered
vehicle.dateSold=Date sold
vehicle.dateSold.short=Sold
vehicle.daysInStock=Days in stock
vehicle.dealer=Dealer
vehicle.dealerInvoice=Dealer invoice
vehicle.dealerNet=Dealer net
vehicle.default.group=Default group
vehicle.delete.confirm=Are you sure you want to delete this vehicle? This action is irreversible.
vehicle.deleted.warning=Warning! This vehicle is deleted.
vehicle.deleted=Deleted
vehicle.demoDiscountAmount=Demo discount
vehicle.department.nullable.error=Branch is required
vehicle.department=Branch
vehicle.description=Description
vehicle.diameter=Diameter
vehicle.discount=Discount
vehicle.displayPrice.preview=Preview display price
vehicle.displayPrice=Display price
vehicle.displayPrices=Display prices
vehicle.displayYear.custom=Custom
vehicle.displayYear.origin=Origin
vehicle.displayYear=Year displayed
vehicle.dms.active.error=Cannot deactivate this DMS import...
vehicle.dms.active.success=DMS import activated!
vehicle.dms.cadealercsv=Cadealer
vehicle.dms.client.lautopak=Client vehicle Lautopak
vehicle.dms.client.lars=Client vehicle LARS
vehicle.dms.dealervucsv=Dealervu
vehicle.dms.delete.error=Cannot delete this DMS import...
vehicle.dms.delete.success=DMS import deleted!
vehicle.dms.delete=Are you sure you want to delete this DMS import
vehicle.dms.error=Cannot save this DMS import...
vehicle.dms.lars=Lars
vehicle.dms.lautopakraspberry=Lautopak raspberry
vehicle.dms.lautopakxml=Lautopak
vehicle.dms.lightspeed=Lightspeed
vehicle.dms.nostock=Enter a stock number
vehicle.dms.pbs=Pbs
vehicle.dms.reorder.error=Cannot reorder DMS import...
vehicle.dms.reorder.success=DMS import reordered!
vehicle.dms.rules.column=Preview updated columns
vehicle.dms.rules.condition.priority=Priority
vehicle.dms.rules.create=ADD OR MODIFY DMS import
vehicle.dms.rules.step.1.name=Name a new DMS import
vehicle.dms.rules.step.1=1. Select or create a new DMS import
vehicle.dms.rules.step.2.filepath=Type a file name
vehicle.dms.rules.step.2.mdp=Type a password
vehicle.dms.rules.step.2.token=Type a token
vehicle.dms.rules.step.2.url=Type a URL
vehicle.dms.rules.step.2.user=Type a user
vehicle.dms.rules.step.2=2. Add or modify addtional data
vehicle.dms.rules.step.3.inscrire=Type a value
vehicle.dms.rules.step.3=3. Create or modify DMS import mapping
vehicle.dms.rules.step.4.replaced=Replace
vehicle.dms.rules.step.4.flush=Flush
vehicle.dms.rules.step.4.actif=Enabled
vehicle.dms.rules.step.4.actif_all=Enabled/Disabled
vehicle.dms.rules.step.4.all.select=Select all
vehicle.dms.rules.step.4.all.unselect=Unselect all
vehicle.dms.rules.step.4.inactif=Disabled
vehicle.dms.rules.step.4.page_all=Page selected (all)
vehicle.dms.rules.step.4.page_data=Page selected (data)
vehicle.dms.rules.step.4.page_date=Page selected (date)
vehicle.dms.rules.step.4.page_info=Page selected (info)
vehicle.dms.rules.step.4.page_price=Page selected (price)
vehicle.dms.rules.step.4.page_specs=Page selected (specs)
vehicle.dms.rules.step.4.search=Search a column
vehicle.dms.rules.step.4=4. Modify updated columns
vehicle.dms.rules=Import DMS rules
vehicle.dms.search.stock=Search stock number in DMS file
vehicle.dms.search.stock.find=Stock number found !
vehicle.dms.search.stock.error=Stock number not found in DMS file !
vehicle.dms.sei=Sei
vehicle.dms.success=DMS import saved!
vehicle.dms.zii=Zii
vehicle.document=Documents
vehicle.dueDate=Due date
vehicle.enter.exteriorColor=Enter the exterior color
vehicle.evaluation.button.tooltip=You cannot modify the evaluation prices for a blocked quote
vehicle.evaluation.price=Price per dealer
vehicle.evaluation=Evaluation
vehicle.evaluations=Evaluations
vehicle.extendedWarranty=Extended Warranty
vehicle.exteriorColor=Exterior color
vehicle.exteriorColorShort=Ext color
vehicle.externalIdentifiers=External identifiers
vehicle.fabrication=fabrication
vehicle.featured=Featured
vehicle.feed.add.action=2. Add an action to
vehicle.feed.cancel=Are you sure you want to delete this feed
vehicle.feed.choose.rule=1. Choose a rule
vehicle.feed.create.configuration=Create configuration
vehicle.feed.create.file.holder=Enter a file name with extension : *.csv
vehicle.feed.create.file=Name an export file
vehicle.feed.create.name.holder=Enter a configuration name
vehicle.feed.create.name=Name configuration
vehicle.feed.create.template=Create configuration from template
vehicle.feed.create=Modify or create configuration
vehicle.feed.data.action=Actions
vehicle.feed.data.config.filter=Configs & filters
vehicle.feed.data.duration=Duration
vehicle.feed.data.file=File
vehicle.feed.data.last.generated=Last generated
vehicle.feed.data.options.name=Name
vehicle.feed.data.status=Status
vehicle.feed.data.type=Type
vehicle.feed.data.vehicules.count=Vehicle count
vehicle.feed.delete=Deleted
vehicle.feed.duplicate=Duplicated
vehicle.feed.feeds=Feeds
vehicle.feed.filter=Filter
vehicle.feed.generate=Generate
vehicle.feed.map.new=New mapping
vehicle.feed.map.redirection=Mapping redirection
vehicle.feed.map.save=Save action
vehicle.feed.save=Apply configuration in the feed
vehicle.feed.select.vehicle=Choose a default vehicle for preview
vehicle.feed.task.active.at=Start at
vehicle.feed.task.active=Enable
vehicle.feed.task.ftpfortask=FTP enabled
vehicle.feed.task.ftpname=FTP URL
vehicle.feed.task.ftppassword=FTP password
vehicle.feed.task.ftpstate=FTP method
vehicle.feed.task.ftpuser=FTP user
vehicle.feed.task.kill.at=Kill task after
vehicle.feed.task.kill=Kill task
vehicle.feed.task.offhour=Off hour
vehicle.feed.task.offhourend.at=Task restarts at
vehicle.feed.task.offhourstart.at=Task ends at
vehicle.feed.task.repeat.at=Repeat task every
vehicle.feed.task.repeat=Repeat task
vehicle.feed.task.run=Renew this task today
vehicle.feed.task.waitfortask=Wait for task
vehicle.feed.tasks=Tasks
vehicle.feed.template.autodealer=Autodealer template
vehicle.feed.template.kijiji=Kijiji template
vehicle.feed.template.powergo=Powergo template
vehicle.feed.template.trader=Trader template
vehicle.feed.upload=Configuration
vehicle.feeds=Feeds
vehicle.field.search.edit=Search a field to edit
vehicle.filters.dateCreatedLast30=Date created - Last 30 days
vehicle.filters.dateCreatedLast7=Date created - Last 7 days
vehicle.filters.dateModifiedLast30=Date modified - Last 30 days
vehicle.filters.dateModifiedLast7=Date modified - Last 7 days
vehicle.firstPicture=First image
vehicle.floorPlanPicture=Floor plan
vehicle.formula.select.vehicle=Select a vehicle
vehicle.formula.select=Add variables
vehicle.formula=Formula
vehicle.formulaPrice1=Formula price 1
vehicle.formulaPrice2=Formula price 2
vehicle.freight=Freight
vehicle.general.import.cancel=Cancel
vehicle.general.import.columns=Modify columns for update
vehicle.general.import.config=Import configuration
vehicle.general.import.create.file.holder=Enter a file name with extension : *.csv
vehicle.general.import.create.file=Enter an import configuration name
vehicle.general.import.create.name.holder=Enter an import configuration name
vehicle.general.import.create.name=Name import configuration
vehicle.general.import.create=Add new import profile
vehicle.general.import.duplicate=Duplicate
vehicle.general.import.importexcelfile=Please select an excel file with at least one column named stock number.
vehicle.general.import.importfromexcel=Import from excel file
vehicle.general.import.save=Apply this import configuration
vehicle.general.import.status=Importation status
vehicle.general.import.updated.column=Updated columns
vehicle.general.import.updated=Auto update modifications
vehicle.general.import=General import
vehicle.general.informations=General information
vehicle.general=General
vehicle.group.link=Go to group
vehicle.group.manage=Manage groups
vehicle.group=Group
vehicle.groupFormulas=Group formulas
vehicle.height=Height
vehicle.hideSelectedList=Show all
vehicle.holdback=Holdback
vehicle.id=ID
vehicle.identification=Identification
vehicle.information=Information
vehicle.infos=Info
vehicle.inherit.duplicate=Duplicate
vehicle.inheritance.column=Preview inherited columns
vehicle.inheritance.condition.priority=Priority
vehicle.inheritance.create=Add or modify inheritance
vehicle.inheritance.rule=Define inheritance rules
vehicle.inheritance.step.1.name=Name a new inheritance
vehicle.inheritance.step.1=1. Select or create a new inheritance
vehicle.inheritance.step.2.inscrire=Enter a value
vehicle.inheritance.step.2=2. Create or modify inherit conditions
vehicle.inheritance.step.3.actif=Enabled
vehicle.inheritance.step.3.actif_all=Enabled/Disabled
vehicle.inheritance.step.3.all.select=Select all
vehicle.inheritance.step.3.all.unselect=Unselect all
vehicle.inheritance.step.3.default.select=Select default
vehicle.inheritance.step.3.inactif=Disabled
vehicle.inheritance.step.3.page_all=Page selected (all)
vehicle.inheritance.step.3.page_data=Page selected (data)
vehicle.inheritance.step.3.page_date=Page selected (date)
vehicle.inheritance.step.3.page_price=Page selected (price)
vehicle.inheritance.step.3.page_specs=Page selected (specs)
vehicle.inheritance.step.3.search=Search a column
vehicle.inheritance.step.3=3. Modify inherited columns
vehicle.inheritance=Inherit
vehicle.interests= Client interests
vehicle.invdesc=Inventory description
vehicle.iprofile=Import profile
vehicle.isUsed.false=New
vehicle.isUsed.true=Used
vehicle.isUsed=Used
vehicle.key.location=Keys
vehicle.keyLocation=Key location
Vehicle.lautopak.status.addedoptions=Added options
Vehicle.lautopak.status.brokerage=Brokerage
Vehicle.lautopak.status.confirmed=Confirmed
Vehicle.lautopak.status.consignment=Consignment
Vehicle.lautopak.status.crate=Crate
Vehicle.lautopak.status.instock=In stock
Vehicle.lautopak.status.location=Location
Vehicle.lautopak.status.modifiedstock=Modified stock
Vehicle.lautopak.status.mounted=Mounted
Vehicle.lautopak.status.ordered=Ordered
Vehicle.lautopak.status.packedup=Packed up
Vehicle.lautopak.status.packeduppdicompleted=Packed up PDI completed
Vehicle.lautopak.status.pdicompleted=PDI completed
Vehicle.lautopak.status.solddelivered=Sold delivered
Vehicle.lautopak.status.soldnondelivered=Sold not delivered
Vehicle.lautopak.status.trade=Trade
Vehicle.lautopak.status.transit=Transit
vehicle.length=Length
vehicle.licenseNumber=License
vehicle.line.null=No line
vehicle.line=Line
vehicle.list=Vehicle list
Vehicle.ListingStatus.ALL=All
Vehicle.ListingStatus.INHERITANCE_ONLY=Inheritance only
Vehicle.ListingStatus.NO=Disabled
Vehicle.ListingStatus.TRACTION=Traction
Vehicle.ListingStatus.WEBSITE=Website
vehicle.listingStatus=Listing status
vehicle.loanRate=Rate
vehicle.loanTermMonths=Loan term in months
vehicle.loanTermWeeks=Loan term in weeks
vehicle.localisation=Location
vehicle.location=Location
vehicle.locationhistory=Location history
vehicle.magento=Magento
vehicle.magentoData_attrSet=Attribute set
vehicle.magentoData_category.none=No Magento category
vehicle.magentoData_category.select=Select magento categories
vehicle.magentoData_category=Magento category
vehicle.magentoData_crsell=Cross-sell products
vehicle.magentoData_htmlH1=Html H1
vehicle.magentoData_htmlH2=Html H2
vehicle.magentoData_onWebsite=On website
vehicle.magentoData_sku=Magento sku
vehicle.magentoData_upsell=Up-sell products
vehicle.magentoData_vehicleNavAll=All vehicle navigation
vehicle.magentoData_vehicleNavCur=Current vehicle navigation
vehicle.magentoData_visibility=Magento visibility
vehicle.make.null=No make
vehicle.make.nullable.error=Make is required
vehicle.make=Make
vehicle.manage.custom.fields=Manage custom field
vehicle.manage.pictures=Manage pictures
vehicle.manageDisplayPrice=Manage display price
vehicle.manageGroupFormulas=Details of group formulas
vehicle.manageImages.allimages=All images
vehicle.manageImages.imageslist=List of images 
vehicle.manageImages.imagesupdate=Update image
vehicle.manageImages.original=Original
vehicle.manageImages.root=Root
vehicle.manageImages=Manage images
vehicle.manageInfo=Manage information
vehicle.manageMagentoCategory=Manage categories
vehicle.manageOptionsAccessories=Manage options and accessories
vehicle.managePromotions=Manage promotions
vehicle.manageSpecifications=Manage specifications
vehicle.manageWatermarks=Manage watermarks
vehicle.manufacturing=Manufacturer
vehicle.marketing=Marketing
vehicle.measures=Measures
vehicle.memo=Memo
vehicle.minimumPrice=Minimum price
vehicle.model.null=No model
vehicle.model.nullable.error=Model is required
vehicle.model=Model
vehicle.modelCode.nullable.error=Model code is required
vehicle.modelCode=Model code
vehicle.monthlyPayment=Monthly payment
vehicle.msrp=MSRP
vehicle.new.evaluation=New evaluation
vehicle.new=New vehicle
vehicle.no.watermark=No watermark
vehicle.noInterests=No interest
vehicle.not.found=Vehicle not found
vehicle.odometer.type=Odometer type
vehicle.odometer=Odometer
vehicle.opportunities.active=Opportunities active with this product
vehicle.opportunities.other=Other opportunities with this product
vehicle.opportunities.sold=Opportunities sold with this product
vehicle.options2=Options 2
vehicle.options=Options
vehicle.optionsPrice2=Options price 2
vehicle.optionsPrice=Options price
vehicle.original.base.data=Original vehicle data base 
vehicle.other=Other
vehicle.parts=Parts
vehicle.payment=Payment
vehicle.pdfs=PDFs
vehicle.pdi=PDI
vehicle.picture.delete.confirm=Do you want to delete all pictures of this vehicle
vehicle.pictures.add=Add
vehicle.pictures.ajust=Adjust
vehicle.pictures.delete.all=Delete all
vehicle.pictures.details=Details
vehicle.pictures.download.all=Download all
vehicle.pictures.metadata.authors=Authors
vehicle.pictures.metadata.credit=Credit
vehicle.pictures.metadata.location=Location
vehicle.pictures.metadata.notes=Notes
vehicle.pictures.metadata.texts=Texts
vehicle.pictures.metadata.titles=Titles
vehicle.pictures.metadata=Meta data
vehicle.pictures.number=#Images:
vehicle.pictures.reorder=Reorder
vehicle.pictures.transform=Transform
vehicle.pictures=Images
vehicle.pixelguru=Pixel Guru
vehicle.potentialPrice=Potential price
vehicle.powergoid=PowerGo ID
vehicle.preparationCost=Preparation cost
vehicle.preparationDetail=Preparation details
vehicle.preview=Vehicle preview
vehicle.priceAccepted=Trade value
vehicleevaluation.priceAccepted=Accepted price
vehicleevaluation.priceLink=Linked price
vehicleevaluation.priceReal=Real price
vehicleevaluation.priceAsked=Asked price
vehicleevaluation.priceDisplay=Displayed price
category.history.vehicleevaluation=Evaluation
vehicle.priceAsked=Price requested
vehicle.priceDisplay=Display price
vehicle.priceLink=Lien price
vehicle.priceReal=Real price
vehicle.priceRepair=Estimated repair cost
vehicle.prices=Prices
vehicle.priceSpecial=Special price
vehicle.priceTotal=Price with options
vehicle.profile=Profile
vehicle.profitMargin=Profit margin
vehicle.promo.state=State
vehicle.promo=Promotion
vehicle.promoEndDate=Expiration date
vehicle.promoInternal=Internal
vehicle.promoInternalAmount=Internal promotion
vehicle.promoInternalEndDate=Internal promotion end date
vehicle.promoInternalStartDate=Internal promotion start date
vehicle.promoManufacturer=Manufacturer
vehicle.promoManufacturerAmount=Manufacturer promotion
vehicle.promoManufacturerEndDate=Manufacturer promotion end date
vehicle.promoManufacturerStartDate=Manufacturer promotion start date
vehicle.promoSpecialPrice=Special price
vehicle.promoStartDate=Start date
vehicle.promoText=Text promotion
vehicle.promoTextEndDate=Text promotion end date
vehicle.promoTextStartDate=Text promotion start date
vehicle.promoTextual=Textual
vehicle.promotion.expired=Expired
vehicle.promotion.not.set=No promotion set
vehicle.promotions=Promotions
vehicle.promovalid=Promotion valid
vehicle.promoWatermark=Watermark promotion
vehicle.qr.location.associate=Associate a QR code
vehicle.qr.location=Location QR code
vehicle.qr=QR code
vehicle.registration=Registration
vehicle.reserved=Reserved
vehicle.root.children.zero=This root vehicle has no child.
vehicle.root.children=This root vehicle has {0} child.
vehicle.root.childrens=This root vehicle has {0} children.
vehicle.rootChilds=Children
vehicle.rootVehicle.pictures=Root images
vehicle.rootVehicle=Root vehicle
vehicle.sale=Sale
vehicle.salePrice=Sale price
vehicle.salePrices=Sale prices
vehicle.sales=Sales
vehicle.save=Save
vehicle.search.confirm.change=Are you sure you want to change the vehicle?
vehicle.search.stock=Search for a stock number
vehicle.search=Search for a vehicle
vehicle.seeGroup=See group
vehicle.selectBarredPrice=Select barred price
vehicle.selectDisplayPrice=Select display price
vehicle.selected=products selected
vehicle.serialNumber.nullable.error=Serial number is required
vehicle.serialNumber.number=# Serial
vehicle.serialNumber2.number=# Serial 2
vehicle.serialNumber2=Serial number 2
vehicle.serialNumber=Serial number
vehicle.services.inspections=Services/Inspections
vehicle.shortDescription=Short description
vehicle.showroomUrl.add=Add a showroom URL
vehicle.showroomUrl=Showroom URL
vehicle.showSelectedList=Show selected only
vehicle.spec.language.en=English
vehicle.spec.language.fr=French
vehicle.spec.language=Language of displayed data
vehicle.specialPrice2=Special price 2
vehicle.specification.bulk.delete.category=Are you sure you want to delete this category? (All names and values related will also be deleted)
vehicle.specification.bulk.delete.name=Are you sure you want to delete this name? (All values related will also be deleted)
vehicle.specification.bulk.delete.value=Are you sure you want to delete this value?
vehicle.specification.categories.select=Add an attribute category
vehicle.specification.categories=Categories
vehicle.specification.names.select=Add an attribute to the category
vehicle.specification.names=Names
vehicle.specification.selected.vehicles=Specified vehicles specification
vehicle.specification.title=Category
vehicle.specification.values.select=Add a value to the attribute
vehicle.specification.values=Values
vehicle.specification=Specification
vehicle.specifications.preview=Preview
vehicle.specifications=Specifications
vehicle.stat.180days=180 days
vehicle.stat.30days=30 days
vehicle.stat.90days=90 days
vehicle.stat.alltime=All time
vehicle.stat.avgDaysInStock.tooltip=Average number of days in stock (after reception date)
vehicle.stat.avgDaysInStock=Average number of days in stock
vehicle.stat.newVehicleListedNoOpportunity=New vehicle listed for less than 30 days and has not received any leads
vehicle.stat.usedVehicleListedNoOpportunity=Used vehicle listed for less than 30 days and has not received any leads
vehicle.stat.usedVehicleNoOpportunityLast30=Used vehicle has not received an opportunity in the last 30 days
vehicle.stat.vehicleAvgOpportunity.tooltip=Average of new opportunities per vehicle
vehicle.stat.vehicleAvgOpportunity=Average of opportunities
vehicle.stat.vehicleAvgOpportunityLast180.tooltip=Average of new opportunities in the Last 180 days per vehicle
vehicle.stat.vehicleAvgOpportunityLast180=Average of opportunities - 180 days
vehicle.stat.vehicleAvgOpportunityLast30.tooltip=Average of new opportunities in the last 30 days per vehicle
vehicle.stat.vehicleAvgOpportunityLast30=Average of opportunities - 30 days
vehicle.stat.vehicleAvgOpportunityLast90.tooltip=Average of new opportunities in the last 90 days per vehicle
vehicle.stat.vehicleAvgOpportunityLast90=Average of opportunities - 90 days
vehicle.stat.vehicleNoOpportunity.tooltip=Vehicle that has not received an opportunity
vehicle.stat.vehicleNoOpportunity=No opportunity
vehicle.stat.vehicleNoOpportunityLast180.tooltip=Vehicles that has not received an opportunity in the last 180 days
vehicle.stat.vehicleNoOpportunityLast180=No opportunity - 180 days
vehicle.stat.vehicleNoOpportunityLast30.tooltip=Vehicles that have not received an opportunity in the last 30 days
vehicle.stat.vehicleNoOpportunityLast30=No Opportunity - 30 days
vehicle.stat.vehicleNoOpportunityLast90.tooltip=Vehicles that have not received an opportunity in the last 90 days
vehicle.stat.vehicleNoOpportunityLast90=No opportunity - 90 days
vehicle.state=State
vehicle.stats=Statistics
vehicle.statsopp.compare=Compare data
vehicle.statsopp.legend=Legend
vehicle.statsopp.nbopportunity=Number of opportunities according to the displayed price
vehicle.statsopp.time=Choose a time increment
vehicle.statsopp=Statistics price/opportunity
vehicle.status=Status
vehicle.stockNumber.nullable.error=Stock number is required
vehicle.stockNumber.number=# Stock
vehicle.stockNumber.required.for.this.type=Stock number is required for this type of vehicle
vehicle.stockNumber=Stock number
vehicle.subCategory.null=No sub-category
vehicle.subCategory=Sub-category
vehicle.suffix=Suffix
vehicle.sumarry=Summary
vehicle.supplierCode=No supplier code for these vehicles
vehicle.surchargeAmount=Surcharge
vehicle.text.actions=Actions
vehicle.text.description=Description
vehicle.text.name=Name
vehicle.text.preview=Preview
vehicle.text.querybuilder=Create your text conditions
vehicle.text.saveandcontinue=Save and continue
vehicle.text.saveandquit=Save and exit
vehicle.text.select=Choose a variable to add to your text
vehicle.text.translated.default=Default Text
vehicle.text.translated.edit=Modify a translated text 
vehicle.text.translated.show=Show translated texts with language:
vehicle.text.translated.translate=Translate automatically
vehicle.text.translated.translated=Text translated
vehicle.text.translated.update=Update translated text table
vehicle.text.write=Write a text
vehicle.texts.edition=Texts edition
vehicle.texts=Texts
vehicle.topstat.twoPicturesAndLess.tooltip=Vehicles with only two picture or less
vehicle.topstat.twoPicturesAndLess=Two pictures or less
vehicle.trade.prices=Trade Price
vehicle.traderTrim=Trader trim 
vehicle.transportCost=Transport cost
vehicle.transportDetail=Transport details
vehicle.type.category=For category
vehicle.type.cell=Cell to use
Vehicle.Type.CLIENT.unassigned=Client - Unassigned
Vehicle.Type.CLIENT=Client
vehicle.type.conditions=Conditions
Vehicle.Type.CONSIGNMENT=Consignment
vehicle.type.contains=Contains
vehicle.type.date=Date format
vehicle.type.insert=Text to insert
Vehicle.Type.INVENTORY=Inventory
vehicle.type.isused=Used vehicle value
Vehicle.Type.LOCATION=Location
vehicle.type.max=Maximum
vehicle.type.prdduplic=prdduplic
vehicle.type.remove=Text to remove
vehicle.type.replace=Text to replace
Vehicle.Type.ROOT=Root
vehicle.type.separator=Text separator
Vehicle.Type.SERVICE=Service
vehicle.type.specs=Specification name
vehicle.type.url=URL
vehicle.type=Type
vehicle.unique.serialNumber=Serial number must be unique
vehicle.unique.stockNumber=Stock number must be unique
vehicle.url=Enter or copy the video URL
vehicle.videos=Video and URL
vehicle.videoUrl.add=Add a video URL
vehicle.videoUrl=Video URL
vehicle.warranty=Warranty
vehicle.warrantyDescription=Warranty description
vehicle.warrantyKm=Number of kilometers
vehicle.warrantyPrice=Warranty price
vehicle.warrantyTerm=Warranty term
vehicle.watermark.condition.has.image=Vehicle has at least one image
vehicle.watermark.condition.has.valid.promo=Vehicle has a valid manufacturer promotion
vehicle.watermark.condition.reserved=Vehicle is reserved
vehicle.watermark.diagram=Watermark selection diagram
vehicle.watermark.explained=Explanation of watermark assignment
vehicle.watermark.nomerge.invalid=The watermark chosen by the conditions is not used, because the other children of the current vehicle's root vehicle does not choose the same watermark.
vehicle.watermark=Watermark
vehicle.websitesExport=Website export
vehicle.weeklyPayment=Weekly payment
vehicle.weight=Weight
vehicle.width=Width
vehicle.workOrders=Ongoing work order
vehicle.year.null=No year
vehicle.year.nullable.error=Year is required
vehicle.year=Year
vehicle=Vehicle
vehicleevaluation.department.null.error=You must select a branch
vehicleinspection.base=Base information
vehicleinspection.client=Client
vehicleinspection.create=Create vehicle inspection
vehicleinspection.creator=Created by
vehicleinspection.dateClientInspection=Client inspection date
vehicleinspection.dateCreated=Created on
vehicleinspection.dateDeliveryInspection=Delivery inspection date
vehicleinspection.dateTechInspection=Technician inspection date
vehicleinspection.form=Form
vehicleinspection.inspector=Inspected by
vehicleinspection.list=Vehicle inspection list
vehicleInspection.not.updatable=Impossible to modify inspection because there are already items
vehicleinspection.odometer=Odometer
vehicleinspection.page.title=Technical inspection form
vehicleinspection.resultClient=Client inspection
vehicleinspection.resultDelivery=Delivery inspection
vehicleinspection.resultTech=Technician inspection
vehicleInspection.select.interest=Select an interest
vehicleinspection.status=Status
vehicleinspection.tags=Tags
vehicleinspection.title=Inspections
vehicleinspection.vehicle=Vehicle
vehicleinspection.view.client=Client
vehicleinspection.view.delivery=Delivery
vehicleinspection.view.summary=Summary
vehicleinspection.view.tech=Technician
vehicleinspection=Vehicle inspection
vehicleinspectionitem.addedManually=Added manually
vehicleinspectionitem.attach.files.here=Attach files here
vehicleinspectionitem.attach.files=Attach files
vehicleinspectionitem.clientChoice=Client accepted
vehicleinspectionitem.comment.placeholder=Enter a comment
vehicleinspectionitem.commentClient=Client comment
vehicleinspectionitem.commentDelivery=Delivery comment
vehicleinspectionitem.commentTech=Technician comment
vehicleinspectionitem.files=Files
vehicleinspectionitem.labourHours=Labour hours
vehicleinspectionitem.labourValue=Labour value
vehicleinspectionitem.name.error=Specify item name
vehicleinspectionitem.name=Name
vehicleinspectionitem.new=Name inspection
vehicleinspectionitem.partNumber.placeholder=Enter the part number to be replaced
vehicleinspectionitem.partNumber=Part number
vehicleinspectionitem.partValue=Part value
vehicleinspectionitem.salesManagerChoice=Sales director
vehicleinspectionitem.tech=Technician
vehiclelist.add.pictures.to.selected.confirm=Are you sure you want to add images to {0} selected vehicles ?
vehiclelist.add.pictures.to.selected=Add images to selected vehicles
vehiclelist.clone.confirm=Are you sure you want to clone {0} selected vehicles?
vehiclelist.clone.yearplusone=Clone vehicles (year + 1)
vehiclelist.clone=Clone vehicles
vehiclelist.deleted.selected.confirm=Are you sure you want to delete all {0} selected vehicles ?
vehiclelist.deleted.selected=Delete selected vehicles
vehiclelist.pictures.download.all=Download images
vehicleoption.actions=Actions
vehicleoption.add=Add an option
vehicleoption.description=Description
vehicleoption.images=Image
vehicleoption.name=Name
vehicleoption.optional=Optional
vehicleoption.order=Order
vehicleoption.price=Price
vehicleoption.title=Options and accessories
vehicleselectionoption.add.label=Text option
vehicleselectionoption.add.url=Option URL
vehicleselectionoption.add.value=Option value
vehicleselectionoption.add=Add a new option to
vehicleselectionoption.search.label=Search label
vehicleselectionoption.search.value=Search value
vehicleselectionoption.manage=Managing selection options
vehicleselectionoption.select.property=Select a property
vehiclespecification.add.name=Write a specification
vehiclespecification.add.value=Write a value
vehiclespecification.add=Add a specification
vehiclespecification.category.add.name=Name the category
vehiclespecification.category.add=Add a category
vehiclespecification.category.delete.text=Are you sure you want to delete this category?
vehiclespecification.category.delete=Delete the category
vehiclespecification.category.edit=Rename the category
vehiclespecification.category=Category
vehiclespecification.delete=Are you sure you want to delete this spec?
vehiclespecification.enter.category=Enter category
vehiclespecification.inherited=Inherited
vehiclespecification.initial=Initial
vehiclespecification.name=Name
vehiclespecification.preview=Website preview
vehiclespecification.total=Total
vehiclespecification.value=Value
vehiclewatermark.add.child.select=Select child
vehiclewatermark.add.child=Add child to this watermark
vehiclewatermark.alpha=Alpha
vehiclewatermark.config=Watermarks configuration
vehiclewatermark.create=Create new watermark
vehiclewatermark.downloadPreviewButton=Download image and watermark
vehiclewatermark.downloadWatermarkButton=Download watermark image only
vehiclewatermark.information=Information
vehiclewatermark.maintainImageAspectRatio=Maintain image aspect ratio
vehiclewatermark.make.selection=Please select the watermark to edit.
vehiclewatermark.name=Name
vehiclewatermark.noimages=Use this watermark for all products without images
vehiclewatermark.noimageswatermark=Use this watermark for all products without images and reserved
vehiclewatermark.positions=Position
vehiclewatermark.preview=Watermark preview
vehiclewatermark.Ratio=Ratio
vehiclewatermark.replaceImage=Replace image with watermark
vehiclewatermark.scale=Scale
vehiclewatermark.search.placeholder=Search for a watermark
vehiclewatermark.transformer=Transformer
vehiclewatermark.upload=Upload watermark image
vehiclewatermark.visual=Visual
vehicule.specification.category.name=Category name
vehicule.specification.category=Add a category
video.apply=Apply
video.call=Video Call
video.cancel=Cancel
video.flip=Flip camera
video.manager=Video Manager
video.room.apply=Apply
video.room.cancel=Cancel
video.room.flip=Flip
video.room.settings=Settings
video.safe.close=The video room is over, you can now close your browser safely
video.settings=Settings
videoRoom.create=Create a conference video
videoRoom.date=Date created
videoRoom.duration=Duration
videoRoom.participantConnected=Participants connected
videoRoom.room=Room
videoRoom.save=Video conference created!
videoRoom.subject=Join me in my Traction video conference
videoRoom.user=User
videoroom=Video conference
visit.in.store=In-store visit 
visitlog.add=Add visit log
visitlog.cannot.edit=You need to be a sales manager in order to edit a visit log created by another user
visitlog.categorize.irrelevant=Categorize as irrelevant
visitlog.category.associated=Associated
visitlog.category.irrelevant=Irrelevant
visitlog.category.new=New
visitlog.category=Category
visitlog.client.hint=Find the client or enter a note about him
visitlog.creator=Creator
visitlog.edit=Edit visit log
visitlog.list=Visit log
visitlog.note=Comment
visitlog.noteOnClient=Note about client
visitlog.notification.body={0} has logged a visit. \nNote about the client: {1}
visitlog.notification.title=New in-store visit 
visitlog.nulluser=Without user assigned
visitlog.save.error=Please make sure you enter a note about the client and a user or branch
visitlog.saved=Visit log saved
visitlog.status=Type of visit
walkin.added=Walk-in added
walkin.not.added=Walk-in not added. Please ensure that the date entered is not in the future.
warning=Warning
watermark.default.group=Default watermark of the group
watermark.default.vehicle=Default watermark of the vehicle
watermark.noimage.default=No image watermark by default
watermark.noimage.group=No image watermark of the group
watermark.noimage.reserved.default=Reserved without image watermark by default
watermark.product.search=Search for
watermark.promo.group=Promo watermark of the group
watermark.promo.vehicle=Promo watermark of the vehicle
watermark.reserved.group=Reserved watermark of the group
watermark.status.group=Status watermark of the group
we=We
webinar.recording=Click here to see the recording
webinar.sessionId=ZOHO Session ID
webinar.title=Title
webinar=Webinar
webinars=Webinars
websession.action.open=Open the communication
websession.action.supervise=Supervise the communication
websession.action.transfer=Transfer the chat
websession.actions=Actions
websession.browser=Browser
websession.cameFrom=Came from
websession.city=City
websession.client.information=Client information
websession.client=Client
websession.country=Country
websession.date=Creation date
websession.device=Device
websession.group=Group
websession.ip=IP address
websession.lastGreeting=Last greeting
websession.lastWebVisit=Last viewed page
WebSession.list=List of web sessions
websession.numberOfSession=Number of sessions
websession.operatingSystem=Operating system
websession.pageViewed=Page viewed
websession.returning.client=Returning client
websession.state=State
WebSession.Status.BROWSING=Browsing
WebSession.Status.CHATTING=Chat in progress
WebSession.Status.INVITED=Invited
WebSession.Status.LEFT_WEBSITE=Left the website
WebSession.Status.WAITING_FOR_REPLY=Waiting for a reply
websession.status=Status
websession.technology=Technology
websession.totalTime=Total time
websession.traffic=Website traffic
webSession.traffic=Website traffic
websession.user=User
websession.visit.list=Page visited
websession.visit.start=Start
week.of=Week of
week=week
weekday.1=Sunday
weekday.2=Monday
weekday.3=Tuesday
weekday.4=Wednesday
weekday.5=Thursday
weekday.6=Friday
weekday.7=Saturday
Weight.Unit.GRAMS=g
Weight.Unit.KILOGRAMS=kg
Weight.Unit.POUNDS=lb
welcome=Welcome
with.appointment.canceled=Cancelled
with.appointment.createdByUserBdc=Created by BDC user 
with.appointment.createdByUserSales=Created by sales user 
with.appointment.done=Done
with.appointment.noshow=No show
with.appointment.todo.later=To complete later
with.appointment.todo.now=Late
with.late.task=With a late task
with.task=With task
with=With
without.appointment.done=Without appt. done
without.task=Without task
without=Without
workcshedule=Work schedule
workflow.achiveall.confirm=Are you sure you want to archive all selected opportunities? This action is not reversible.
workflow.activate.drop=Select destination
workflow.activate.selection=Activate selection mode (press [S])
workflow.addBoard=New board
workflow.addWorkflow=New workflow
workflow.archiveWorkflow=Archive workflow
workflow.archiveWorkflowByDate=Archive by date
workflow.assigned.filter=Filter
workflow.board.archive=Archive
workflow.board.begin=Begin
workflow.board.end=End
workflow.cancel.select.destination=Cancel selected destination 
workflow.cannot.find.opportunity=Cannot find the opportunity. Please make sure that it is visible depending on the filters applied.
workflow.categories.to.send=Opportunity categories allowed to send to the workflow in the client view
workflow.data.noend=Cards without an end date
workflow.data=Workflow data
workflow.deleteWorkflow=Delete workflow
workflow.drop.wrong.sort=The selection of a destination when displaying sorted by something other than [Order / Ascending] is imprecise.
workflow.duplicateWorkflow=Duplicate workflow
workflow.editWorkflow=Edit workflow
workflow.hook.delete.confirm=Are you sure you want to remove the hook, {0}?
workflow.hook.error.alreadyExist=Hook already exists
workflow.hook.error.invalidBoard=Invalid board
workflow.hook.error.invalidDestination=Invalid destination
workflow.hook.error.invalidType=Invalid type
workflow.hook.error.noBoard=Board is missing
workflow.hook.error.noDelete=This hook cannot be removed
workflow.hook.error.noDestination=Destination missing
workflow.hook.error.noName=Name is missing
workflow.hook.error.noType=Type is missing
workflow.hook.success.deleted=The hook has been removed
workflow.hook.success.saved=Hook saved successfully
workflow.mastboard.save=Master board saved
workflow.menu.calendar=Workflow calendar
workflow.menu.filter=Filter by user
workflow.menu.find.opportunity=Find an opportunity
workflow.menu.reset.tooltips=Reset user filter
workflow.menu.reset=Reset
workflow.modal.archive.date=Will archive all cards in archive board older than
workflow.modal.archive.save=Archive
workflow.modal.board.delete=Delete
workflow.modal.board.height=Height
workflow.modal.board.hooks=Hooks
workflow.modal.board.move=Move
workflow.modal.board.name=Name
workflow.modal.board.order=Order
workflow.modal.board.parent=Parent
workflow.modal.board.save=Save
workflow.modal.board.width=Width
workflow.modal.data.addboard=Add board
workflow.modal.data.assigned=Assigned
workflow.modal.data.attachments=Files
workflow.modal.data.none=NONE
workflow.modal.data.order=Move the card before
workflow.modal.data.subscribe=Subscribe
workflow.modal.data.unsubscribe=Unsubscribe
workflow.modal.master.delete=Delete
workflow.modal.master.duplicate=Duplicate
workflow.modal.master.label1=Are you sure you want to delete this workflow?
workflow.modal.master.label2=This is NOT reversible
workflow.modal.master.label3=Are you sure you want to duplicate this workflow?
workflow.modal.master.name.duplicate=New name
workflow.modal.master.name=Name
workflow.modal.product.customer=Customer product
workflow.modal.product.new=New product
workflow.note.deleted=Workflow calendar note was deleted
workflow.note.not.deleted=Workflow calendar note was not deleted
workflow.note.not.saved=Workflow calendar note not saved
workflow.note.saved=Workflow calendar note saved
workflow.press.d=press on [D]
workflow.priority.critical=Critical
workflow.priority.high=High
workflow.priority.low=Low
workflow.priority.normal=Normal
workflow.salesmanageronly.properties=Properties that can only be changed by a user with "Edit workflows" permission
workflow.save.client.error.email=Client with this email exists : {0}
workflow.save.client.error.extid=Client with this external ID exists : {0}
workflow.select.edit=Select cards to modify
workflow.select.location=Select a location
Workflow.servicecalendars=Service calendars
workflow.serviceresources=Workflow resources
workflow.smsTemplate.delete.confirm=Are you sure you want to delete the following SMS template: {0}?
workflow.task.delivery=Creation of delivery tasks with an end date on the workflow cards
Workflow.title=Workflow
workflow.view=Workflow view
Workflow.Workflowschedule=Workflow schedule
workflow.zoom=Zoom in / Zoom out
workflow=Workflow
workflowboard.description.click=Click to open the description
workflowboard.description=Description
workflowboard.name=Name
workflowboard.selectWorkflow=Select a workflow to enable this column
workflowboard.workflowMaster=Workflow
workflowboard=Workflow board
workflowboardsubscription.type.all=Subscribe to all
workflowboardsubscription.type.mine=Subscribe to my data
workflowboardsubscription.type.none=No subscription
workflowcard.elements.to.hide=Elements to hide
workflowdata.assigned=Assigned users
workflowdata.classOfParts.short=Parts
workflowdata.classOfParts=Parts class
workflowdata.classOfSale.short=Sale
workflowdata.classOfSale=Sales class
workflowdata.classOfService.short=Service
workflowdata.classOfService=Service class
workflowdata.classOfShipping.short=Shipping
workflowdata.classOfShipping=Shipping class
workflowdata.comments=Comments
workflowdata.create.search.client=Find a client to create the workflow data
workflowdata.date=Creation date
workflowdata.dateEnd.short=End
workflowdata.dateEnd=End date 
workflowdata.dateParts.short=Parts
workflowdata.dateParts=Parts date 
workflowdata.datePreparation.short=Preparation
workflowdata.datePreparation=Preparation date 
workflowdata.dates=Dates
workflowdata.dateStart.short=Start
workflowdata.dateStart=Start date 
workflowdata.delete.text=Are you sure you want to delete this workflow data?
workflowdata.delete=Delete workflow data
workflowdata.duration.minutes=minutes
workflowdata.duration=Duration
workflowdata.edit.cancel=Cancel modifications and return to workflow
workflowdata.for=Workflow data for {0}
workflowdata.info=Information
workflowdata.lastBoardChange=Last board change
workflowdata.list=Workflow data list
workflowdata.order=Order
workflowdata.otherInfo=Other information
workflowdata.priority=Priority
workflowdata.timers=Workflow: Time since last move / since creation
workflowdata.typeOfCard=Card type
workflowdata.workflowBoard=Workflow board
workflowdata.workOrder.already.linked=Workflow data already linked to another work order
workflowdata.workOrder.close=Close in workflow schedule
workflowdata.workOrder=Work order
workflowdata=Workflow data
workflowmaster.dateEndNotification=Cards without an end date notification enabled
workorder.all.departments=All branches
workorder.already.linked.workflowdata=Work order not saved. The selected work order is already linked to another workflow data.
workorder.appointmentDate=Appointment date
workorder.archive=Archive
workorder.assignmentStatus.toggle=Enable / Disable assignment status for the work order
workorder.assignmentStatus=Assignment status
workorder.authorizations.view=View authorizations
workorder.authorizations=Authorizations
workorder.clientComment=Client comment
workorder.closed=Closed
workorder.closingAdvisor=Closing advisor
workorder.closingAdvisorExtId=Closing advisor
workorder.comment=Comment
workorder.date=Date
workorder.delete=Delete
workorder.department=Branch
workorder.duplicate=Duplicate
workorder.estimatedTime=Estimated time
workorder.estimatedTimeLeft=Estimated time remaining
workorder.exceeding=Exceeding
workorder.grandTotal=Grand total
workorder.icon=Icon
workorder.inspectionsTab=Inspections
workorder.invoiceType.majorUnit=Major unit
workorder.invoiceType.service=Service
workorder.invoiceType=Work order type
workorder.jobs.opened=Opened jobs
workorder.jobs=Jobs
workorder.jobsTab=Jobs
workorder.lautopakPartReservationStatus=Parts status
workorder.lautopakPartReservationStatusLastChange=Parts reservations status last changed
workorder.list.advanced=Advanced work order list
workorder.list=Work order list
workorder.new.job=New job
workorder.no.details=Job has no details
workorder.opened=Opened
workorder.openingAdvisor=Opening advisor
workorder.openingAdvisorExtId=Opening advisor
workorder.paid=Paid
workorder.part=Parts
workorder.percentCompleted=Percent completed
workorder.print.estimate=Print estimate
workorder.print.options=Work order options
workorder.print.workorder=Print work order
workorder.print=Print
workorder.priority=Priority
workorder.priorityLautopak=Priority L.
workorder.promisedDate=Date promised 
workorder.recommendations=Recommendations
workorder.remainingBalance=Remaining balance
workorder.roID=#RO
workorder.search.codemenu=Search code menu
workorder.search=Search client work order
workorder.send=Send
workorder.serialnumber=Serial number
workorder.short=WO
workorder.status.accounted10=Accounted 10
workorder.status.accounted1=Accounted 1
workorder.status.accounted2=Accounted 2
workorder.status.accounted3=Accounted 3
workorder.status.accounted5=Accounted 5
workorder.status.accounted9=Accounted 9
workorder.status.billgenerated=Bill of sale generated
workorder.status.billprinted=Bill of sale printed
workorder.status.close=Closed
workorder.status.completed=Completed
workorder.status.estimatedcancel=Estimate cancelled
workorder.status.inactive=Inactive
workorder.status.open=Open
workorder.status.paid=Paid
workorder.status.rogenerated=R.O. generated
workorder.status.roopen=R.O. open
workorder.status.roprinted=R.O. printed
workorder.status.rvgenerated=RV generated
workorder.status=Status
workorder.subtotal=Subtotal
workorder.syncPromisedDate.false=Not synched with the DMS
workorder.tags=Tags
workorder.taxes=Tax
workorder.timeLate=Time late
workorder.timeLeft=Time left
workorder.timeSheetsTab=Time clocked
workorder.timeWorked=Time worked
workorder.title=Title
workorder.totalLabor=Total labor
workorder.totalParts=Total parts
workorder.type.APPOINTMENT=Appointment
workorder.type.BILL=Bill
workorder.type.ESTIMATE=Estimate
workorder.type=Type
workorder.unread.false=Read
workorder.unread.markas.false=Mark as read
workorder.unread.markas.true=Mark as unread
workorder.unread.true=Unread
workorder.unread=Unread
workorder.with.same.serialnumber=Work orders with the same serial number
workorder=Work order
workOrderEvent=Work order
workorderjob.add.labor=Add labor
workorderjob.add.part=Add a part
workorderjob.add=Add
workorderjob.assignmentstatus.assigned=Technician assigned
workorderjob.assignmentstatus.jobcomplete=Job complete
workorderjob.assignmentstatus.jobestimate=Job estimate
workorderjob.assignmentstatus.nolabor=No labor
workorderjob.assignmentstatus.notassigned=Technician not assigned
workorderjob.assignmentstatus.scheduled=Job scheduled
workorderjob.assignmentstatus.scheduledlate=Scheduled late
workorderjob.assignmentstatus.scheduleduncompletedbeforetoday=Schedule incomplete before today
workorderjob.assignmentstatus.scheduledwarranty=Job scheduled - Warranty
workorderjob.assignmentstatus.techclockedin=Technician clocked-in
workorderjob.assignmentstatus.techclockedout=Technician clocked-out
workorderjob.assignmentstatus.techdone=Technician done
workorderjob.assignmentstatus.toassign=To assign
workorderjob.assignmentstatus.toscheduled=Job not scheduled
workorderjob.assignmentstatus.toscheduledwarranty=Job not scheduled - Warranty
workorderjob.assignmentstatus.waitingonclientauthorization=Waiting on client authorization
workorderjob.assignmentstatus.waitingonsubletlabor=Waiting on sublet labor
workorderjob.assignmentstatus.waitingonwarrantyauthorization=Waiting on warranty authorization
workorderjob.assignmentStatus=Assignment status
workorderjob.authorization.null=Not authorized yet
workorderjob.change.authorization=Change authorization
workorderjob.closed=Closed
workorderjob.codeMenu.filter.info=6 methods to filter menu codes:<br/><br/>1) Enter the exact menu code<br/>2) To search with menu codes starting with characters xyz, enter S:xyz<br/>3) To search with menu codes ending with characters xyz, enter E:xyz<br/>4) To search with menu codes that contain characters xyz, enter C:yxz<br />5) To search with menu codes that are in a range of numbers, for example between 1000 and 1100 inclusive, enter B:100-1100<br/>6) To search excluding menu code xyz, enter N:xyz
workorderjob.codeMenu=Menu code 
workorderjob.description=Description
workorderjob.divide.late.time=Divide the late time over
workorderjob.drop.to.remove=Drop here to remove
workorderjob.drop.to.schedule=Drop here to schedule
workorderjob.estimatedTime=Estimated time
workorderjob.estimatedTimeLeft=Estimated time remaining
workorderjob.exceeding=Exceeding
workorderjob.followUpCode=Follow up code
workorderjob.icon=Icon
workorderjob.item.discount=Discount
workorderjob.item.epa=EPA
workorderjob.item.price=Price
workorderjob.item.qtyhours=Qty / Hrs
workorderjob.item.shopsuplies=Shop supplies
workorderjob.item.subtotal=Subtotal
workorderjob.item.tag=Tags
workorderjob.item.tax=Tax
workorderjob.items=Items
workorderjob.job=Job#
workorderjob.labors=Labor
workorderjob.laborStatusTech=Labor status tech.
workorderjob.lautopakPartReservationStatus=Parts status
workorderjob.list=Work order job list
workorderjob.noschedules=No schedules
workorderjob.note.add=Add note
workorderjob.note=Note
workorderjob.nothing.to.place=Nothing to schedule
workorderjob.percentCompleted=% completed
workorderjob.preAssignedTechs=Pre-assigned technicians
workorderjob.priority=Priority
workorderjob.priorityLautopak=Priority L.
workorderjob.proposal.for.user=Proposal for the user
workorderjob.qualifiedTechs=Qualified technicians
workorderjob.remarkCause=Cause
workorderjob.remarkCorrection=Correction
workorderjob.scheduledDate=Scheduled date
workorderjob.select.from.table=Select from table to schedule
workorderjob.selected=jobs selected
workorderjob.status=Status
workorderjob.statustech.assigned=Assigned
workorderjob.statustech.completedinvoiced=Completed invoiced
workorderjob.statustech.completedmecanic=Completed mechanic
workorderjob.statustech.inprogress=In progress
workorderjob.statustech.interrupted=Interrupted
workorderjob.statustech.pending=Pending
workorderjob.statustech.repending=Pending reset
workorderjob.statustech.toresume=To resume
workorderjob.subtotal=Subtotal
workorderjob.techs=Technicians
workorderjob.timeLate=Time late
workorderjob.timeLeft=Time left
workorderjob.timeWorked=Time stamped
workorderjob.to.place=To schedule
workorderjob=Job
workorderjobdetail.codeMD=MD code 
workorderjobdetail.codeMenu=Menu code 
workorderjobdetail.dateMod=Modification date
workorderjobdetail.description=Description
workorderjobdetail.extId=External ID
workorderjobdetail.remark=Remark
workorderjobdetail.remarkCause=Remark cause
workorderjobdetail.remarkCorrection=Remark correction
workorderjobdetail.sequence=Sequence
workorderjoblabor.add.schedule=New schedule
workorderjoblabor.category=Category
workorderjoblabor.cost=Labor cost
workorderjoblabor.description=Labor description
workorderjoblabor.discount=Discount
workorderjoblabor.displayHours=Display hours on estimate and invoice
workorderjoblabor.displayNote=Display note on estimate and invoice
workorderjoblabor.hours=Hours
workorderjoblabor.hoursCost=Hours
workorderjoblabor.hoursMultiplier=Hours multiplier
workorderjoblabor.matrix=Labor matrix
workorderjoblabor.note=Labor note
workorderjoblabor.rate=Rate
workorderjoblabor.rateCost=Rate
workorderjoblabor.retail=Labor retail
workorderjoblabor.schedules=Schedules
workorderjoblabor.subtotal=Subtotal retail
workorderjoblabor.subtotalCost=Subtotal cost
workorderjoblabor.taxed=Should this be taxed?
workorderjoblabor.tech=Technician
workorderjoblabor=Labor
workorderjobpart.cost=Cost
workorderjobpart.description=Description
workorderjobpart.displayNote=Display note on estimate and invoice
workorderjobpart.displayPartNumber=Display by number on estimate and invoice
workorderjobpart.displayPriceAndQty=Display price quantity on estimate and invoice
workorderjobpart.note.add=Add note
workorderjobpart.note.hide=Hide note
workorderjobpart.note=Part note
workorderjobpart.partNumber=Part number
workorderjobpart.qty=Qty
workorderjobpart.retail=Retail
workorderjobpart.taxed=Should this be taxed?
workorderjobparts=Parts
workorders=Work orders
workorderunit.linestatus.customerwaiting=Customer waiting
workorderunit.linestatus.newunit=New unit
workorderunit.linestatus.nolinestatus=No line status
workorderunit.linestatus.unitnothere=Unit not here
workorderunit.linestatus.unitonsite=Unit on site
workorderunit.linestatus.usedunit=Used unit
workorderunit.linestatus=Line status
workschedule.copied.success=Work schedule copied successfully
workschedule.copy.department.warning=Copy work schedule is only available for users schedules
workschedule.copy.for=Copy {0} work schedule for the following users
workschedule.copy=Copy work schedule
workschedule.create=Create work schedule
workschedule.delete.confirm=Are you sure you want to delete the entire work schedule?
workschedule.delete=Delete work schedule
workschedule.edit=Edit work schedule
workschedule.error.empty=The work schedule cannot be empty
workschedule.error.name=The name must be unique
workschedule.hours=Hours
workschedule.myschedule=My work schedule
workschedule.name=Name
workschedule.overlay.error=Unable to overlay schedules
workschedule.week.hours=Hours of the week
workschedule=Work schedule
workscheduleday.delete.confirm=Are you sure you want to remove this part of the schedule?
workschedulerule.delete=Delete recurring event
workschedulerule.edit=Edit recurring event
workschedulerule.edittype.all=All events
workschedulerule.edittype.following=This and following events
workschedulerule.edittype.this=This event
workschedules.summary=Work schedules summary
workschedules=Work schedules
x.maximum=X Maximum
y.maximum=Y Maximum
yamaha=Yamaha
year.first.letter=y
year=year
Year=Year
years.ago.short={0}y ago
yeartodate=Year to date
yes=Yes
yesterday=Yesterday
you=You
zero_motorcycle=Zero Motorcycle
zii=Global DMS
zoho.announcements.new=New announcements
zoho.documents.new=New training documents
zoho.documents=Training documents
schedule.visit=Schedule a visit
marked.message.as=Marked as {0}
unread=unread
read=read
failed.to.update.message=Failed to update message
user.mismatch=User mismatch
soldboard.goto.cotation=Go to quotation
cotation.sold.on=Sold on
badge=Badge
client.list=Client list
sms.sent.error.no.content=Message text or attached file is required.
sms.sent.error.no.twilio.number=No sending number, please verify message settings.
email.parser=Email parser
email.parsers=Email parsers
email.parser.fields=Parsed fields
email.parser.edit=Edit email parser
email.parser.create=Create email parser
default.saved=Saved successfully
default.deleted=Deleted successfully
emailParser.warn.linked.origins=Cannot delete the email parser because it is used by source(s)
cotation.department.confirm=Select a department from this cotation
lars.categories=Lars categories
lars.category.add=Add a Lars category
lars.category.edit=Edit a Lars category
lars.category=Lars category
product.is.sold=This product is already sold
document.preview=Document preview
click.on.preview.to.download=Click on the preview to download the document
TOP_CENTER=Top center
TOP_LEFT=Top left
TOP_RIGHT=Top right
BOTTOM_CENTER=Bottom center
BOTTOM_LEFT=Bottom left
BOTTOM_RIGHT=Bottom right
CENTER=Center
CENTER_LEFT=Center left
CENTER_RIGHT=Center right
client.purchase=purchase
related.config=Related
print.format=Print format
vehicle.inventory=Inventory
copy.copy=Click to copy
copy.copied=Copied
vehicle.compiledData_magentoNotPublished=Not published on Magento
vehicle.compiledData_magentoSynchronized=Magento is synchronized
vehicle.compiledData_magentoNotSynchronized=Magento is not synchronized
vehicle.compiledData_buildPending=Initial build pending
vehicle.compiledData_buildSuccess=Build successful
vehicle.compiledData_buildFailed=Build failed
vehicle.compiledData_updateSynchronized=Modification synchronized
vehicle.compiledData_updateNotSynchronized=Modification not synchronized
vehicle.compiledData_inventoryPicturesMissing=Inventory pictures missing
vehicle.compiledData_picturesTwoOrLess=2 or fewer pictures
vehicle.compiledData_picturesMissing=Pictures missing
vehicle.compiledData_categoryMagentoMissing=Magento category missing
vehicle.compiledData_categoryOther=Category is "Other" or miscellaneous
vehicle.compiledData_modelCodeMissing=Model code missing
vehicle.compiledData_categoryMissing=Category missing
vehicle.compiledData_makeMissing=Make missing
vehicle.compiledData_modelMissing=Model missing
vehicle.compiledData_categoryKijijiMissing=Kijiji category missing
vehicle.compiledData_categoryTraderMissing=Trader category missing
vehicle.compiledData_interestMissing=Interest missing
vehicle.compiledData_rootVehicleGroupMissing=Group missing for root vehicle
vehicle.compiledData_rootVehicleWgetPriceZero=W-GET price is zero for root vehicle
vehicle.compiledData_kawasakiPriceZero=Kawasaki price is zero
vehicle.compiledData_listingStatusNotAll=Listing visibility not set to ALL
vehicle.compiledData_promoInternalExpired=Internal promo expired
vehicle.compiledData_promoManufacturerExpired=Manufacturer promo expired
vehicle.compiledData_promoTextExpired=Text promo expired
vehicle.compiledData_buildCustomRuleFailed1=Custom build rule #1 failed
vehicle.compiledData_buildCustomRuleFailed2=Custom build rule #2 failed
vehicle.compiledData_buildCustomRuleFailed3=Custom build rule #3 failed
vehicle.compiledData_buildCustomRuleFailed4=Custom build rule #4 failed
vehicle.compiledData_buildCustomRuleFailed5=Custom build rule #5 failed
vehicle.compiledData_buildCustomRuleFailed6=Custom build rule #6 failed
vehicle.stateList=Error list
technician.signature=Technician signature
changelog=Last modifications on Traction
vehicle.categoryMagentoRoot=Root Magento Category
harley=Harley-Davidson
