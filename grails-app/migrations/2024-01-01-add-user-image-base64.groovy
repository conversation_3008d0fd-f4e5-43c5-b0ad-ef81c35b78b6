databaseChangeLog = {

    changeSet(author: "system", id: "2024-01-01-add-user-image-base64-1") {
        addColumn(tableName: "user") {
            column(name: "image_base64", type: "LONGTEXT") {
                constraints(nullable: "true")
            }
        }
    }

    changeSet(author: "system", id: "2024-01-01-add-user-image-base64-2") {
        comment("Add index on image_base64 column for performance")
        sql("ALTER TABLE user ADD INDEX idx_user_image_base64 (image_base64(100))")
    }
}
