environments {
    development {
        grails.plugin.console.enabled = true
    }
    test {
        grails.plugin.console.enabled = true
    }
    production {
        grails.plugin.console.enabled = false
    }
    track_dev {
        grails.plugin.console.enabled = false
    }
}

// Added by the Spring Security Core plugin:
grails.plugin.springsecurity.userLookup.userDomainClassName = 'traction.security.User'
grails.plugin.springsecurity.userLookup.authorityJoinClassName = 'traction.security.UserRole'
grails.plugin.springsecurity.authority.className = 'traction.security.Role'
grails.plugin.springsecurity.useSwitchUserFilter = true
grails.plugin.springsecurity.controllerAnnotations.staticRules = [
    [pattern: '/',               access: ['permitAll']],
    [pattern: '/error',          access: ['permitAll']],
    [pattern: '/index',          access: ['permitAll']],
    [pattern: '/_stats.gsp',      access: ['permitAll']],
    [pattern: '/shutdown',       access: ['permitAll']],
    [pattern: '/assets/**',      access: ['permitAll']],
    [pattern: '/**/js/**',       access: ['permitAll']],
    [pattern: '/**/css/**',      access: ['permitAll']],
    [pattern: '/**/images/**',   access: ['permitAll']],
    [pattern: '/**/favicon.ico', access: ['permitAll']],
    [pattern: "/console/**",     access: ['ROLE_DEV']],
    [pattern: "/static/console/**", access: ['ROLE_DEV']],
    [pattern: "/static/**", access: ['permitAll']], // Grails 3.x
    [pattern: "/stomp/**", access: ['permitAll']],
    [pattern: '/login/impersonate', access: ['ROLE_DEV']],
    [pattern: '/logout/impersonate', access: ['permitAll']]
]

grails.plugin.springsecurity.filterChain.chainMap = [
    [pattern: '/assets/**',      filters: 'none'],
    [pattern: '/**/js/**',       filters: 'none'],
    [pattern: '/**/css/**',      filters: 'none'],
    [pattern: '/**/images/**',   filters: 'none'],
    [pattern: '/**/favicon.ico', filters: 'none'],
    [pattern: '/**', filters: 'JOINED_FILTERS'],
    [pattern: '/api/**', filters: 'JOINED_FILTERS,-restTokenValidationFilter,-restExceptionTranslationFilter']
]

grails.plugin.springsecurity.rememberMe.cookieName = 'TractionRememberMe'
grails.plugin.springsecurity.rememberMe.key = '8M@qeMhwUNTpW#rz&'

grails.plugin.springsecurity.rest.token.storage.jwt.secret = "qrD6h8K6S9503Q06Y6Rfk21TErImPYqa"
grails.plugin.springsecurity.rest.token.storage.jwt.useSignedJwt = true
grails.plugin.springsecurity.rest.token.storage.useJwt = true
grails.plugin.springsecurity.rest.token.storage.jwt.expiration = 3600

// need to be different then ehcache.xml cause conflict with hibernate ehcache plugin
grails {
    cache {
        ehcache {
            ehcacheXmlLocation = 'ehcache-3.xml'
            lockTimeout = 200 // In milliseconds
        }
    }
}

// Database migration
grails.plugin.databasemigration.updateOnStartFileName = 'changelog.groovy'
grails.plugin.databasemigration.databaseChangeLogTableName = 'database_change_log'
grails.plugin.databasemigration.databaseChangeLogLockTableName = 'database_change_log_lock'
